<?php
/**
 * WSDL Documentation Analyzer
 * Examines the SOAP WSDL for pagination parameters and method documentation
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';

echo "<h1>WSDL Documentation Analysis</h1>";
echo "<p><strong>WSDL URL:</strong> " . SOAP_WSDL . "</p>";
echo "<p><strong>Purpose:</strong> Examine available methods and parameters for pagination/limiting</p>";
echo "<hr>";

try {
    // Create SOAP client to analyze WSDL
    $options = [
        'soap_version' => SOAP_1_2,
        'trace' => true,
        'exceptions' => true,
        'cache_wsdl' => WSDL_CACHE_NONE,
        'connection_timeout' => 10
    ];
    
    $client = new SoapClient(SOAP_WSDL, $options);
    
    echo "<h2>1. Available SOAP Methods</h2>";
    $functions = $client->__getFunctions();
    
    echo "<h3>All Available Methods:</h3>";
    echo "<ul>";
    foreach ($functions as $function) {
        echo "<li><code>" . htmlspecialchars($function) . "</code></li>";
    }
    echo "</ul>";
    
    echo "<h2>2. SOAP Types</h2>";
    $types = $client->__getTypes();
    
    echo "<h3>All Available Types:</h3>";
    echo "<div style='max-height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px;'>";
    foreach ($types as $type) {
        echo "<pre>" . htmlspecialchars($type) . "</pre>";
        echo "<hr>";
    }
    echo "</div>";
    
    echo "<h2>3. Method Parameter Analysis</h2>";
    
    // Look for methods that might support pagination
    $searchMethods = [];
    foreach ($functions as $function) {
        if (stripos($function, 'cautare') !== false || stripos($function, 'search') !== false) {
            $searchMethods[] = $function;
        }
    }
    
    echo "<h3>Search-related Methods:</h3>";
    if (!empty($searchMethods)) {
        echo "<ul>";
        foreach ($searchMethods as $method) {
            echo "<li><code>" . htmlspecialchars($method) . "</code></li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No search-related methods found</p>";
    }
    
    // Analyze CautareDosare2 method specifically
    echo "<h3>CautareDosare2 Method Analysis:</h3>";
    $cautareMethod = null;
    foreach ($functions as $function) {
        if (stripos($function, 'CautareDosare2') !== false) {
            $cautareMethod = $function;
            break;
        }
    }
    
    if ($cautareMethod) {
        echo "<p><strong>Method signature:</strong></p>";
        echo "<code>" . htmlspecialchars($cautareMethod) . "</code>";
        
        // Parse parameters from method signature
        if (preg_match('/CautareDosare2\((.*?)\)/', $cautareMethod, $matches)) {
            $params = $matches[1];
            echo "<h4>Parameters:</h4>";
            echo "<pre>" . htmlspecialchars($params) . "</pre>";
            
            // Check for pagination-related parameters
            $paginationKeywords = ['page', 'limit', 'max', 'count', 'size', 'offset', 'start', 'end', 'top', 'skip'];
            $foundPaginationParams = false;
            
            foreach ($paginationKeywords as $keyword) {
                if (stripos($params, $keyword) !== false) {
                    echo "<p style='color: green;'>✓ Found potential pagination parameter containing: <strong>{$keyword}</strong></p>";
                    $foundPaginationParams = true;
                }
            }
            
            if (!$foundPaginationParams) {
                echo "<p style='color: orange;'>⚠️ No obvious pagination parameters found in CautareDosare2 method</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>CautareDosare2 method not found in WSDL</p>";
    }
    
    echo "<h2>4. Raw WSDL Content Analysis</h2>";
    
    // Fetch and analyze raw WSDL
    $wsdlContent = file_get_contents(SOAP_WSDL);
    
    if ($wsdlContent) {
        echo "<p><strong>WSDL Size:</strong> " . strlen($wsdlContent) . " bytes</p>";
        
        // Search for pagination-related terms in WSDL
        $paginationTerms = [
            'maxResults', 'pageSize', 'limit', 'offset', 'page', 'count', 'total',
            'hasMore', 'nextPage', 'pagination', 'resultLimit', 'maxRecords',
            'top', 'skip', 'first', 'last', 'size'
        ];
        
        echo "<h3>Searching for Pagination Terms in WSDL:</h3>";
        $foundTerms = [];
        
        foreach ($paginationTerms as $term) {
            if (stripos($wsdlContent, $term) !== false) {
                $foundTerms[] = $term;
            }
        }
        
        if (!empty($foundTerms)) {
            echo "<p style='color: green;'>✓ Found pagination-related terms:</p>";
            echo "<ul>";
            foreach ($foundTerms as $term) {
                echo "<li><strong>{$term}</strong></li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠️ No pagination-related terms found in WSDL</p>";
        }
        
        // Look for any documentation or comments about limits
        if (preg_match_all('/<!--(.*?)-->/s', $wsdlContent, $comments)) {
            echo "<h3>WSDL Comments/Documentation:</h3>";
            foreach ($comments[1] as $comment) {
                $comment = trim($comment);
                if (!empty($comment)) {
                    echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border-left: 3px solid #007bff;'>";
                    echo "<pre>" . htmlspecialchars($comment) . "</pre>";
                    echo "</div>";
                }
            }
        } else {
            echo "<p>No comments or documentation found in WSDL</p>";
        }
        
        // Show relevant WSDL sections
        echo "<h3>CautareDosare2 Definition in WSDL:</h3>";
        if (preg_match('/<.*?name="CautareDosare2".*?<\/.*?>/s', $wsdlContent, $methodDef)) {
            echo "<textarea style='width: 100%; height: 200px;'>" . htmlspecialchars($methodDef[0]) . "</textarea>";
        } else {
            echo "<p>Could not extract CautareDosare2 definition from WSDL</p>";
        }
        
    } else {
        echo "<p style='color: red;'>Could not fetch WSDL content</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
    echo "<h4 style='color: #721c24;'>Exception:</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Analysis completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
