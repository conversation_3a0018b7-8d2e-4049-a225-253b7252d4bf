<?php
/**
 * Step-by-step test of hybrid party extraction process
 * Tests each component individually to identify the exact issue
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<h1>🔧 Step-by-Step Hybrid Extraction Test</h1>\n";
echo "<p><strong>Testing:</strong> 130/98/2022 from Curtea de Apel BUCURESTI</p>\n";

try {
    $dosarService = new DosarService();
    $numarDosar = '130/98/2022';
    $institutie = 'CurteadeApelBUCURESTI';
    
    // Get reflection access to private methods
    $reflection = new ReflectionClass($dosarService);
    $executeSoapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $executeSoapMethod->setAccessible(true);
    $extractMethod = $reflection->getMethod('extractPartiesFromDecisionText');
    $extractMethod->setAccessible(true);
    $mergeMethod = $reflection->getMethod('mergeAndDeduplicateParties');
    $mergeMethod->setAccessible(true);
    
    echo "<h2>Step 1: Get Raw SOAP Data</h2>\n";
    
    $searchParams = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $response = $executeSoapMethod->invoke($dosarService, 'CautareDosare2', $searchParams, "Step-by-step test");
    
    if (!isset($response->CautareDosare2Result->Dosar)) {
        echo "<p style='color: red;'>❌ No SOAP data found</p>\n";
        exit;
    }
    
    $rawDosar = is_array($response->CautareDosare2Result->Dosar) ? $response->CautareDosare2Result->Dosar[0] : $response->CautareDosare2Result->Dosar;
    echo "<p>✅ Raw SOAP data retrieved</p>\n";
    
    echo "<h2>Step 2: Extract SOAP Parties</h2>\n";
    
    $soapParties = [];
    if (isset($rawDosar->parti) && isset($rawDosar->parti->DosarParte)) {
        $parti = $rawDosar->parti->DosarParte;
        if (is_array($parti)) {
            foreach ($parti as $parte) {
                if (isset($parte->nume)) {
                    $soapParties[] = [
                        'nume' => $parte->nume ?? '',
                        'calitate' => $parte->calitateParte ?? '',
                        'source' => 'soap_api'
                    ];
                }
            }
        } elseif (isset($parti->nume)) {
            $soapParties[] = [
                'nume' => $parti->nume ?? '',
                'calitate' => $parti->calitateParte ?? '',
                'source' => 'soap_api'
            ];
        }
    }
    
    echo "<p><strong>SOAP Parties Extracted:</strong> " . count($soapParties) . "</p>\n";
    
    if (count($soapParties) >= 100) {
        echo "<p style='color: orange;'>⚠️ SOAP API limit reached - decision text extraction is critical</p>\n";
    }
    
    // Show first few SOAP parties
    echo "<h4>Sample SOAP Parties:</h4>\n";
    echo "<ul>\n";
    for ($i = 0; $i < min(5, count($soapParties)); $i++) {
        echo "<li>" . htmlspecialchars($soapParties[$i]['nume']) . " (" . $soapParties[$i]['calitate'] . ")</li>\n";
    }
    echo "</ul>\n";
    
    echo "<h2>Step 3: Extract Decision Text Parties</h2>\n";
    
    $decisionParties = $extractMethod->invoke($dosarService, $rawDosar);
    echo "<p><strong>Decision Text Parties Extracted:</strong> " . count($decisionParties) . "</p>\n";
    
    if (count($decisionParties) > 0) {
        echo "<p style='color: green;'>✅ Decision text extraction working</p>\n";
        
        // Show first few decision text parties
        echo "<h4>Sample Decision Text Parties:</h4>\n";
        echo "<ul>\n";
        for ($i = 0; $i < min(10, count($decisionParties)); $i++) {
            echo "<li>" . htmlspecialchars($decisionParties[$i]['nume']) . " (" . $decisionParties[$i]['calitate'] . ")</li>\n";
        }
        echo "</ul>\n";
    } else {
        echo "<p style='color: red;'>❌ No parties extracted from decision text</p>\n";
        
        // Debug decision text content
        echo "<h4>Debug: Decision Text Content</h4>\n";
        if (isset($rawDosar->sedinte) && isset($rawDosar->sedinte->DosarSedinta)) {
            $sedinte = $rawDosar->sedinte->DosarSedinta;
            if (!is_array($sedinte)) {
                $sedinte = [$sedinte];
            }
            
            foreach ($sedinte as $index => $sedinta) {
                echo "<h5>Session " . ($index + 1) . ":</h5>\n";
                
                if (isset($sedinta->solutie) && !empty($sedinta->solutie)) {
                    $solutieLength = strlen($sedinta->solutie);
                    echo "<p><strong>Solution text:</strong> {$solutieLength} characters</p>\n";
                    
                    // Show first 500 characters
                    $preview = substr($sedinta->solutie, 0, 500);
                    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0; background: #f9f9f9;'>\n";
                    echo "<strong>Preview:</strong><br>\n";
                    echo htmlspecialchars($preview) . "...\n";
                    echo "</div>\n";
                } else {
                    echo "<p>No solution text</p>\n";
                }
                
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $sumarLength = strlen($sedinta->solutieSumar);
                    echo "<p><strong>Summary text:</strong> {$sumarLength} characters</p>\n";
                } else {
                    echo "<p>No summary text</p>\n";
                }
            }
        } else {
            echo "<p>No court sessions found</p>\n";
        }
    }
    
    echo "<h2>Step 4: Merge and Deduplicate</h2>\n";
    
    $mergedParties = $mergeMethod->invoke($dosarService, $soapParties, $decisionParties);
    echo "<p><strong>Merged Parties Total:</strong> " . count($mergedParties) . "</p>\n";
    
    $finalSoapCount = 0;
    $finalDecisionCount = 0;
    foreach ($mergedParties as $party) {
        if ($party['source'] === 'soap_api') $finalSoapCount++;
        if ($party['source'] === 'decision_text') $finalDecisionCount++;
    }
    
    echo "<p><strong>Final Breakdown:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>SOAP API: {$finalSoapCount}</li>\n";
    echo "<li>Decision Text: {$finalDecisionCount}</li>\n";
    echo "<li>Total: " . count($mergedParties) . "</li>\n";
    echo "</ul>\n";
    
    echo "<h2>Step 5: Compare with getDetaliiDosar Result</h2>\n";
    
    $finalDosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    $finalPartyCount = count($finalDosar->parti ?? []);
    
    echo "<p><strong>getDetaliiDosar() Result:</strong> {$finalPartyCount} parties</p>\n";
    
    if ($finalPartyCount === count($mergedParties)) {
        echo "<p style='color: green;'>✅ Consistent results between manual extraction and getDetaliiDosar()</p>\n";
    } else {
        echo "<p style='color: red;'>❌ Inconsistent results - manual: " . count($mergedParties) . ", getDetaliiDosar: {$finalPartyCount}</p>\n";
    }
    
    echo "<h2>📊 Summary</h2>\n";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>Step</th><th>Count</th><th>Status</th></tr>\n";
    echo "<tr><td>SOAP Parties</td><td>" . count($soapParties) . "</td><td>" . (count($soapParties) >= 100 ? "⚠️ Limit reached" : "✅ Normal") . "</td></tr>\n";
    echo "<tr><td>Decision Text Parties</td><td>" . count($decisionParties) . "</td><td>" . (count($decisionParties) > 0 ? "✅ Working" : "❌ Not working") . "</td></tr>\n";
    echo "<tr><td>Merged Total</td><td>" . count($mergedParties) . "</td><td>" . (count($mergedParties) >= 500 ? "✅ Target met" : "❌ Below target") . "</td></tr>\n";
    echo "<tr><td>Final Result</td><td>{$finalPartyCount}</td><td>" . ($finalPartyCount >= 500 ? "✅ Success" : "❌ Needs fix") . "</td></tr>\n";
    echo "</table>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
