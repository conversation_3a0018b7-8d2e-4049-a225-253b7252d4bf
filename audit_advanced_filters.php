<?php
/**
 * Comprehensive audit and fix script for all advanced search filters
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<h1>🔍 Comprehensive Advanced Filters Audit</h1>\n";
echo "<style>
    .audit-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 300px; overflow-y: auto; }
    .filter-test { border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 5px; }
    .test-result { margin: 5px 0; padding: 5px; border-radius: 3px; }
    .pass { background-color: #d4edda; color: #155724; }
    .fail { background-color: #f8d7da; color: #721c24; }
    .skip { background-color: #fff3cd; color: #856404; }
</style>\n";

echo "<div class='audit-section info'>";
echo "<h2>🎯 Audit Overview</h2>\n";
echo "<p>This comprehensive audit will test all advanced search filters:</p>";
echo "<ul>";
echo "<li><strong>Institution Filter:</strong> Dropdown population and null handling</li>";
echo "<li><strong>Institution Category Filter:</strong> Client-side filtering logic</li>";
echo "<li><strong>Case Category Filter:</strong> Enhanced terminology matching</li>";
echo "<li><strong>Date Range Filters:</strong> Romanian format conversion and validation</li>";
echo "<li><strong>Integration Testing:</strong> Multiple filter combinations</li>";
echo "</ul>";
echo "</div>";

try {
    // Test 1: Institution Filter Audit
    echo "<div class='audit-section'>";
    echo "<h2>Test 1: Institution Filter (Instanță judecătorească)</h2>\n";
    
    echo "<div class='filter-test'>";
    echo "<h4>1.1 Institution List Population</h4>\n";
    
    $institutii = getInstanteList();
    $institutionCount = count($institutii);
    
    echo "<div class='test-result " . ($institutionCount > 0 ? 'pass' : 'fail') . "'>";
    echo "Institution list loaded: <strong>{$institutionCount}</strong> institutions";
    echo "</div>";
    
    if ($institutionCount > 0) {
        // Test specific institution codes
        $testCodes = ['CurteadeApelBUCURESTI', 'TribunalulBucuresti', 'JudecatoriaSector1'];
        foreach ($testCodes as $code) {
            $exists = isset($institutii[$code]);
            echo "<div class='test-result " . ($exists ? 'pass' : 'skip') . "'>";
            echo "Code '{$code}': " . ($exists ? "✅ Found - " . htmlspecialchars($institutii[$code]) : "⚠️ Not found");
            echo "</div>";
        }
        
        // Show sample institutions
        echo "<h5>Sample institutions (first 5):</h5>";
        echo "<pre>";
        $sample = array_slice($institutii, 0, 5, true);
        foreach ($sample as $code => $name) {
            echo htmlspecialchars($code) . " => " . htmlspecialchars($name) . "\n";
        }
        echo "</pre>";
    }
    echo "</div>";
    
    echo "<div class='filter-test'>";
    echo "<h4>1.2 Null Value Handling Test</h4>\n";
    
    // Test SOAP API with null institution
    $dosarService = new \App\Services\DosarService();
    
    $testParams = [
        'numarDosar' => '',
        'numeParte' => '',
        'obiectDosar' => '',
        'institutie' => null, // Test null value
        'dataStart' => '01.01.2023',
        'dataStop' => '31.12.2023',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
        '_maxResults' => 5
    ];
    
    echo "Testing SOAP API with institutie=null...<br>\n";
    try {
        $nullResults = $dosarService->cautareAvansata($testParams);
        echo "<div class='test-result pass'>";
        echo "✅ SOAP API accepts null institution: " . count($nullResults) . " results";
        echo "</div>";
    } catch (Exception $e) {
        echo "<div class='test-result fail'>";
        echo "❌ SOAP API error with null institution: " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
    echo "</div>";
    
    echo "</div>";
    
    // Test 2: Institution Category Filter Audit
    echo "<div class='audit-section'>";
    echo "<h2>Test 2: Institution Category Filter (Categorie instanță)</h2>\n";
    
    $categories = [
        'curtea_suprema' => ['InaltaCurte', 'Înalta Curte', 'ICCJ'],
        'curte_apel' => ['CurteadeApel', 'Curtea de Apel'],
        'tribunal' => ['Tribunalul'],
        'judecatorie' => ['Judecatoria']
    ];
    
    foreach ($categories as $category => $patterns) {
        echo "<div class='filter-test'>";
        echo "<h4>2." . (array_search($category, array_keys($categories)) + 1) . " Category: {$category}</h4>\n";
        
        echo "Patterns to match: " . implode(', ', $patterns) . "<br>\n";
        
        // Find matching institutions
        $matches = [];
        foreach ($institutii as $code => $name) {
            foreach ($patterns as $pattern) {
                if (stripos($code, $pattern) !== false || stripos($name, $pattern) !== false) {
                    $matches[] = $name;
                    break;
                }
            }
        }
        
        echo "<div class='test-result " . (count($matches) > 0 ? 'pass' : 'warning') . "'>";
        echo "Found " . count($matches) . " matching institutions";
        echo "</div>";
        
        if (count($matches) > 0) {
            echo "<h5>Sample matches (first 3):</h5>";
            echo "<pre>" . htmlspecialchars(implode("\n", array_slice($matches, 0, 3))) . "</pre>";
        }
        echo "</div>";
    }
    
    echo "</div>";
    
    // Test 3: Case Category Filter Audit
    echo "<div class='audit-section'>";
    echo "<h2>Test 3: Case Category Filter (Categorie caz)</h2>\n";
    
    echo "<div class='filter-test'>";
    echo "<h4>3.1 Labor Law (munca) Enhanced Filtering</h4>\n";
    
    $laborTerms = ['munca', 'muncă', 'salarial', 'salariat', 'angajat', 'angajator', 
                   'contract de munca', 'contract de muncă', 'individual de munca', 
                   'individual de muncă', 'concediere', 'licențiere', 'despăgubiri', 
                   'daune morale', 'discriminare', 'hărțuire', 'overtime', 'ore suplimentare'];
    
    echo "Enhanced labor law terms (" . count($laborTerms) . " terms):<br>\n";
    echo "<pre>" . htmlspecialchars(implode(', ', $laborTerms)) . "</pre>";
    
    // Test with sample data
    $sampleCases = [
        (object)['obiect' => 'Contract individual de muncă', 'categorieCaz' => '', 'categorieCazNume' => ''],
        (object)['obiect' => 'Despăgubiri pentru concediere', 'categorieCaz' => '', 'categorieCazNume' => ''],
        (object)['obiect' => 'Divorț', 'categorieCaz' => '', 'categorieCazNume' => ''],
        (object)['obiect' => 'Litigiu comercial', 'categorieCaz' => '', 'categorieCazNume' => '']
    ];
    
    $laborMatches = 0;
    foreach ($sampleCases as $case) {
        $isLaborCase = false;
        $obiect = strtolower($case->obiect);
        
        foreach ($laborTerms as $term) {
            if (stripos($obiect, $term) !== false) {
                $isLaborCase = true;
                break;
            }
        }
        
        if ($isLaborCase) $laborMatches++;
        
        echo "<div class='test-result " . ($isLaborCase ? 'pass' : 'skip') . "'>";
        echo "'" . htmlspecialchars($case->obiect) . "': " . ($isLaborCase ? '✅ Labor case' : '⚠️ Not labor');
        echo "</div>";
    }
    
    echo "<div class='test-result " . ($laborMatches >= 2 ? 'pass' : 'fail') . "'>";
    echo "Labor law filtering accuracy: {$laborMatches}/4 correct matches";
    echo "</div>";
    
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='audit-section error'>";
    echo "<h2>❌ Critical Error</h2>\n";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "<br>\n";
    echo "<strong>File:</strong> " . $e->getFile() . "<br>\n";
    echo "<strong>Line:</strong> " . $e->getLine() . "<br>\n";
    echo "</div>";
}

try {
    // Test 4: Date Range Filter Audit
    echo "<div class='audit-section'>";
    echo "<h2>Test 4: Date Range Filters</h2>\n";

    echo "<div class='filter-test'>";
    echo "<h4>4.1 Romanian Date Format Conversion</h4>\n";

    $testDates = [
        '12.06.2022' => '2022-06-12',
        '01.01.2023' => '2023-01-01',
        '31.12.2025' => '2025-12-31',
        '29.02.2024' => '2024-02-29', // Leap year
        'invalid' => false
    ];

    foreach ($testDates as $input => $expected) {
        $timestamp = strtotime($input);
        $converted = $timestamp ? date('Y-m-d', $timestamp) : false;

        $isCorrect = ($expected === false && $converted === false) || ($converted === $expected);

        echo "<div class='test-result " . ($isCorrect ? 'pass' : 'fail') . "'>";
        echo "'{$input}' => " . ($converted ?: 'invalid') . " " . ($isCorrect ? '✅' : '❌');
        echo "</div>";
    }
    echo "</div>";

    echo "<div class='filter-test'>";
    echo "<h4>4.2 Date Range Validation</h4>\n";

    $dateRanges = [
        ['12.06.2022', '15.06.2025', true],  // Valid range
        ['01.01.2023', '31.12.2022', false], // Invalid: start > end
        ['29.02.2024', '01.03.2024', true],  // Valid leap year
        ['', '15.06.2025', true],            // Valid: only end date
        ['12.06.2022', '', true]             // Valid: only start date
    ];

    foreach ($dateRanges as [$start, $end, $shouldBeValid]) {
        $startTs = !empty($start) ? strtotime($start) : null;
        $endTs = !empty($end) ? strtotime($end) : null;

        $isValid = true;
        if ($startTs && $endTs && $startTs > $endTs) {
            $isValid = false;
        }

        $testPassed = ($isValid === $shouldBeValid);

        echo "<div class='test-result " . ($testPassed ? 'pass' : 'fail') . "'>";
        echo "Range '{$start}' to '{$end}': " . ($isValid ? 'Valid' : 'Invalid') . " " . ($testPassed ? '✅' : '❌');
        echo "</div>";
    }
    echo "</div>";

    echo "</div>";

    // Test 5: Integration Testing
    echo "<div class='audit-section'>";
    echo "<h2>Test 5: Filter Integration Testing</h2>\n";

    echo "<div class='filter-test'>";
    echo "<h4>5.1 Multiple Filter Combination Test</h4>\n";

    $integrationTests = [
        [
            'name' => 'Court of Appeal + Labor Law + Date Range',
            'params' => [
                'categorieInstanta' => 'curte_apel',
                'categorieCaz' => 'munca',
                'dataStart' => '01.01.2023',
                'dataStop' => '31.12.2023'
            ]
        ],
        [
            'name' => 'Specific Institution + Date Range',
            'params' => [
                'institutie' => 'CurteadeApelBUCURESTI',
                'dataStart' => '01.06.2023',
                'dataStop' => '30.06.2023'
            ]
        ],
        [
            'name' => 'Case Category Only',
            'params' => [
                'categorieCaz' => 'civil'
            ]
        ]
    ];

    foreach ($integrationTests as $test) {
        echo "<h5>" . htmlspecialchars($test['name']) . "</h5>";

        // Prepare full parameters
        $fullParams = array_merge([
            'numarDosar' => '',
            'numeParte' => '',
            'obiectDosar' => '',
            'institutie' => null,
            'categorieInstanta' => '',
            'categorieCaz' => '',
            'dataStart' => '',
            'dataStop' => '',
            'dataUltimaModificareStart' => '',
            'dataUltimaModificareStop' => '',
            '_maxResults' => 10
        ], $test['params']);

        // Special handling for institution parameter
        if ($fullParams['institutie'] === '') {
            $fullParams['institutie'] = null;
        }

        echo "<pre>Parameters: " . print_r($test['params'], true) . "</pre>";

        try {
            $results = $dosarService->cautareAvansata($fullParams);
            echo "<div class='test-result pass'>";
            echo "✅ Search successful: " . count($results) . " results";
            echo "</div>";

            if (!empty($results)) {
                $sample = $results[0];
                echo "<div class='test-result info'>";
                echo "Sample result: " . htmlspecialchars($sample->numar ?? 'N/A') . " - " . htmlspecialchars($sample->institutie ?? 'N/A');
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='test-result fail'>";
            echo "❌ Search failed: " . htmlspecialchars($e->getMessage());
            echo "</div>";
        }
    }
    echo "</div>";

    echo "</div>";

    // Test 6: hasSearchCriteria Validation
    echo "<div class='audit-section'>";
    echo "<h2>Test 6: Search Criteria Validation</h2>\n";

    $criteriaTests = [
        ['numarDosar' => '123', 'expected' => true],
        ['numeParte' => 'popescu', 'expected' => true],
        ['categorieInstanta' => 'curte_apel', 'expected' => true],
        ['categorieCaz' => 'munca', 'expected' => true],
        ['dataStart' => '01.01.2023', 'expected' => true],
        ['dataStop' => '31.12.2023', 'expected' => true],
        ['institutie' => 'CurteadeApelBUCURESTI', 'expected' => true],
        [/* empty */ 'expected' => false]
    ];

    foreach ($criteriaTests as $i => $test) {
        $expected = $test['expected'];
        unset($test['expected']);

        // Simulate the hasSearchCriteria logic from search.php
        $hasSearchCriteria = !empty($test['numarDosar'] ?? '') ||
                            !empty($test['numeParte'] ?? '') ||
                            !empty($test['obiectDosar'] ?? '') ||
                            !empty($test['institutie'] ?? '') ||
                            !empty($test['categorieInstanta'] ?? '') ||
                            !empty($test['categorieCaz'] ?? '') ||
                            !empty($test['dataStart'] ?? '') ||
                            !empty($test['dataStop'] ?? '') ||
                            !empty($test['dataUltimaModificareStart'] ?? '') ||
                            !empty($test['dataUltimaModificareStop'] ?? '');

        $testPassed = ($hasSearchCriteria === $expected);

        echo "<div class='test-result " . ($testPassed ? 'pass' : 'fail') . "'>";
        echo "Test " . ($i + 1) . ": " . ($hasSearchCriteria ? 'Has criteria' : 'No criteria') . " " . ($testPassed ? '✅' : '❌');
        if (!empty($test)) {
            echo " (" . implode(', ', array_keys($test)) . ")";
        }
        echo "</div>";
    }

    echo "</div>";

} catch (Exception $e) {
    echo "<div class='audit-section error'>";
    echo "<h2>❌ Critical Error in Additional Tests</h2>\n";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "<br>\n";
    echo "<strong>File:</strong> " . $e->getFile() . "<br>\n";
    echo "<strong>Line:</strong> " . $e->getLine() . "<br>\n";
    echo "</div>";
}

echo "<div class='audit-section info'>";
echo "<h2>🏁 Comprehensive Audit Complete</h2>\n";
echo "<p><strong>Summary of tests performed:</strong></p>";
echo "<ul>";
echo "<li>✅ Institution filter dropdown population and null handling</li>";
echo "<li>✅ Institution category client-side filtering patterns</li>";
echo "<li>✅ Case category enhanced terminology matching (labor law)</li>";
echo "<li>✅ Date range format conversion and validation</li>";
echo "<li>✅ Multiple filter combination integration testing</li>";
echo "<li>✅ Search criteria validation logic</li>";
echo "</ul>";
echo "<p>Review the results above to identify any issues that need to be fixed.</p>";
echo "</div>";
?>
