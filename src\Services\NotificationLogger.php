<?php

namespace App\Services;

use App\Config\Database;
use Exception;

/**
 * Notification Logger
 * 
 * Comprehensive logging system for notification processes
 * with structured logging, log rotation, and analysis capabilities.
 */
class NotificationLogger
{
    private $logDir;
    private $maxLogSize;
    private $maxLogFiles;

    public function __construct()
    {
        $this->logDir = LOG_DIR . '/notifications';
        $this->maxLogSize = 10 * 1024 * 1024; // 10MB
        $this->maxLogFiles = 10;
        
        // Ensure log directory exists
        if (!is_dir($this->logDir)) {
            mkdir($this->logDir, 0755, true);
        }
    }

    /**
     * Log notification event
     * 
     * @param string $level Log level (INFO, WARNING, ERROR, DEBUG)
     * @param string $message Log message
     * @param array $context Additional context data
     * @param string $category Log category (queue, email, cron, etc.)
     */
    public function log(string $level, string $message, array $context = [], string $category = 'general'): void
    {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => strtoupper($level),
            'category' => $category,
            'message' => $message,
            'context' => $context,
            'memory_usage' => memory_get_usage(true),
            'process_id' => getmypid()
        ];

        // Write to file
        $this->writeToFile($logEntry, $category);
        
        // Write to database for important events
        if (in_array($level, ['ERROR', 'WARNING'])) {
            $this->writeToDatabase($logEntry);
        }
    }

    /**
     * Log info message
     */
    public function info(string $message, array $context = [], string $category = 'general'): void
    {
        $this->log('INFO', $message, $context, $category);
    }

    /**
     * Log warning message
     */
    public function warning(string $message, array $context = [], string $category = 'general'): void
    {
        $this->log('WARNING', $message, $context, $category);
    }

    /**
     * Log error message
     */
    public function error(string $message, array $context = [], string $category = 'general'): void
    {
        $this->log('ERROR', $message, $context, $category);
        error_log("NotificationLogger Error [{$category}]: {$message}");
    }

    /**
     * Log debug message
     */
    public function debug(string $message, array $context = [], string $category = 'general'): void
    {
        if (defined('DEBUG_NOTIFICATIONS') && DEBUG_NOTIFICATIONS) {
            $this->log('DEBUG', $message, $context, $category);
        }
    }

    /**
     * Log notification queue event
     */
    public function logQueueEvent(string $event, int $notificationId, array $data = []): void
    {
        $this->info("Queue event: {$event}", array_merge([
            'notification_id' => $notificationId
        ], $data), 'queue');
    }

    /**
     * Log email sending event
     */
    public function logEmailEvent(string $event, string $recipient, array $data = []): void
    {
        $this->info("Email event: {$event}", array_merge([
            'recipient' => $recipient
        ], $data), 'email');
    }

    /**
     * Log cron job event
     */
    public function logCronEvent(string $event, array $data = []): void
    {
        $this->info("Cron event: {$event}", $data, 'cron');
    }

    /**
     * Write log entry to file
     */
    private function writeToFile(array $logEntry, string $category): void
    {
        $logFile = $this->logDir . "/{$category}.log";
        
        // Check if log rotation is needed
        if (file_exists($logFile) && filesize($logFile) > $this->maxLogSize) {
            $this->rotateLogFile($logFile);
        }
        
        $logLine = json_encode($logEntry) . PHP_EOL;
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }

    /**
     * Write important events to database
     */
    private function writeToDatabase(array $logEntry): void
    {
        try {
            // Map log levels to match existing enum
            $dbLevel = strtolower($logEntry['level']);
            if (!in_array($dbLevel, ['debug', 'info', 'warning', 'error', 'critical'])) {
                $dbLevel = 'info';
            }

            // Combine category and message for the existing structure
            $fullMessage = "[{$logEntry['category']}] {$logEntry['message']}";

            Database::execute(
                "INSERT INTO system_logs (level, message, context)
                 VALUES (?, ?, ?)",
                [
                    $dbLevel,
                    $fullMessage,
                    json_encode($logEntry['context'])
                ]
            );
        } catch (Exception $e) {
            // Don't let logging errors break the application
            error_log("Failed to write to database log: " . $e->getMessage());
        }
    }

    /**
     * Rotate log file when it gets too large
     */
    private function rotateLogFile(string $logFile): void
    {
        $baseFile = pathinfo($logFile, PATHINFO_FILENAME);
        $extension = pathinfo($logFile, PATHINFO_EXTENSION);
        $directory = dirname($logFile);
        
        // Shift existing rotated files
        for ($i = $this->maxLogFiles - 1; $i >= 1; $i--) {
            $oldFile = "{$directory}/{$baseFile}.{$i}.{$extension}";
            $newFile = "{$directory}/{$baseFile}." . ($i + 1) . ".{$extension}";
            
            if (file_exists($oldFile)) {
                if ($i + 1 > $this->maxLogFiles) {
                    unlink($oldFile); // Delete oldest file
                } else {
                    rename($oldFile, $newFile);
                }
            }
        }
        
        // Move current log to .1
        $rotatedFile = "{$directory}/{$baseFile}.1.{$extension}";
        rename($logFile, $rotatedFile);
    }

    /**
     * Get log statistics
     */
    public function getLogStats(string $category = null, int $hours = 24): array
    {
        try {
            $whereClause = "WHERE created_at >= DATE_SUB(NOW(), INTERVAL {$hours} HOUR)";
            if ($category) {
                $whereClause .= " AND message LIKE ?";
                $params = ["%[{$category}]%"];
            } else {
                $params = [];
            }

            $stats = Database::fetchOne(
                "SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN level = 'error' THEN 1 ELSE 0 END) as errors,
                    SUM(CASE WHEN level = 'warning' THEN 1 ELSE 0 END) as warnings,
                    SUM(CASE WHEN level = 'info' THEN 1 ELSE 0 END) as info,
                    MIN(created_at) as oldest_entry,
                    MAX(created_at) as newest_entry
                 FROM system_logs {$whereClause}",
                $params
            );
            
            return $stats ?: [
                'total' => 0, 'errors' => 0, 'warnings' => 0, 'info' => 0,
                'oldest_entry' => null, 'newest_entry' => null
            ];
            
        } catch (Exception $e) {
            error_log("Failed to get log stats: " . $e->getMessage());
            return [
                'total' => 0, 'errors' => 0, 'warnings' => 0, 'info' => 0,
                'oldest_entry' => null, 'newest_entry' => null
            ];
        }
    }

    /**
     * Get recent error logs
     */
    public function getRecentErrors(int $limit = 50, int $hours = 24): array
    {
        try {
            return Database::fetchAll(
                "SELECT level, message, context, created_at
                 FROM system_logs
                 WHERE level IN ('error', 'warning')
                 AND created_at >= DATE_SUB(NOW(), INTERVAL {$hours} HOUR)
                 ORDER BY created_at DESC
                 LIMIT ?",
                [$limit]
            );
        } catch (Exception $e) {
            error_log("Failed to get recent errors: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Clean up old log files and database entries
     */
    public function cleanup(int $daysToKeep = 30): array
    {
        $results = [
            'files_cleaned' => 0,
            'db_entries_cleaned' => 0,
            'errors' => []
        ];

        try {
            // Clean up database entries
            $deleted = Database::execute(
                "DELETE FROM system_logs 
                 WHERE created_at < DATE_SUB(NOW(), INTERVAL {$daysToKeep} DAY)",
                []
            )->rowCount();
            
            $results['db_entries_cleaned'] = $deleted;
            
            // Clean up old rotated log files
            $files = glob($this->logDir . '/*.*.log');
            $cutoffTime = time() - ($daysToKeep * 24 * 3600);
            
            foreach ($files as $file) {
                if (filemtime($file) < $cutoffTime) {
                    if (unlink($file)) {
                        $results['files_cleaned']++;
                    }
                }
            }
            
        } catch (Exception $e) {
            $results['errors'][] = $e->getMessage();
            error_log("Log cleanup error: " . $e->getMessage());
        }

        return $results;
    }

    /**
     * Export logs for analysis
     */
    public function exportLogs(string $category, string $startDate, string $endDate): string
    {
        try {
            $logs = Database::fetchAll(
                "SELECT * FROM system_logs 
                 WHERE category = ? 
                 AND created_at BETWEEN ? AND ?
                 ORDER BY created_at ASC",
                [$category, $startDate, $endDate]
            );
            
            $exportFile = $this->logDir . "/export_{$category}_" . date('Y-m-d_H-i-s') . '.json';
            file_put_contents($exportFile, json_encode($logs, JSON_PRETTY_PRINT));
            
            return $exportFile;
            
        } catch (Exception $e) {
            error_log("Log export error: " . $e->getMessage());
            throw new Exception("Failed to export logs: " . $e->getMessage());
        }
    }
}
