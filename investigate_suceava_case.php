<?php
/**
 * Investigate why Suceava case 2177/40/2019 is found in party search but not direct search
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';

use App\Services\DosarService;

echo "=== INVESTIGATING SUCEAVA CASE ===" . PHP_EOL;
echo "Case: CurteadeApelSUCEAVA - 2177/40/2019**" . PHP_EOL;
echo "Found in party search but not in direct case search" . PHP_EOL;
echo PHP_EOL;

try {
    $dosarService = new DosarService();
    
    // Test 1: Direct case search with exact parameters
    echo "=== TEST 1: Direct Case Search ===" . PHP_EOL;
    
    $variations = [
        ['institutie' => 'CurteadeApelSUCEAVA', 'numar' => '2177/40/2019'],
        ['institutie' => 'CurteadeApelSUCEAVA', 'numar' => '2177/40/2019**'],
        ['institutie' => 'CURTEA DE APEL SUCEAVA', 'numar' => '2177/40/2019'],
        ['institutie' => 'CURTEA DE APEL SUCEAVA', 'numar' => '2177/40/2019**'],
        ['institutie' => null, 'numar' => '2177/40/2019'],
        ['institutie' => null, 'numar' => '2177/40/2019**']
    ];
    
    foreach ($variations as $index => $params) {
        echo "Variation " . ($index + 1) . ": institutie='" . ($params['institutie'] ?? 'null') . "', numar='{$params['numar']}'" . PHP_EOL;
        
        $searchParams = [
            'numarDosar' => $params['numar'],
            'institutie' => $params['institutie'],
            'obiectDosar' => '',
            'numeParte' => '',
            'dataStart' => null,
            'dataStop' => null,
            'dataUltimaModificareStart' => null,
            'dataUltimaModificareStop' => null
        ];
        
        try {
            $results = $dosarService->cautareAvansata($searchParams);
            echo "  Results: " . count($results) . PHP_EOL;
            
            if (!empty($results)) {
                foreach ($results as $dosar) {
                    echo "  Found: " . ($dosar->institutie ?? 'Unknown') . " - " . ($dosar->numar ?? 'Unknown') . PHP_EOL;
                }
            }
        } catch (Exception $e) {
            echo "  Error: " . $e->getMessage() . PHP_EOL;
        }
        echo PHP_EOL;
    }
    
    // Test 2: Party search to see how it appears
    echo "=== TEST 2: Party Search (how it was originally found) ===" . PHP_EOL;
    
    $partySearchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => 'Saragea Tudorita',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $partyResults = $dosarService->cautareAvansata($partySearchParams);
    echo "Party search results: " . count($partyResults) . PHP_EOL;
    
    foreach ($partyResults as $dosar) {
        $institutie = $dosar->institutie ?? 'Unknown';
        $numar = $dosar->numar ?? 'Unknown';
        
        echo "Found: $institutie - $numar" . PHP_EOL;
        
        if (stripos($institutie, 'SUCEAVA') !== false) {
            echo "  ✅ This is the Suceava case!" . PHP_EOL;
            echo "  Institution: '$institutie'" . PHP_EOL;
            echo "  Case number: '$numar'" . PHP_EOL;
            
            $parti = $dosar->parti ?? [];
            echo "  Parties: " . count($parti) . PHP_EOL;
            
            foreach ($parti as $party) {
                $partyName = is_object($party) ? ($party->nume ?? '') : ($party['nume'] ?? '');
                if (stripos($partyName, 'SARAGEA') !== false || stripos($partyName, 'TUDORITA') !== false) {
                    echo "  Target party: $partyName" . PHP_EOL;
                }
            }
        }
    }
    
    echo PHP_EOL;
    echo "=== ANALYSIS ===" . PHP_EOL;
    echo "The case number might have special characters or formatting that affects direct search." . PHP_EOL;
    echo "The party search works because it searches across all cases without strict case number matching." . PHP_EOL;
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
}
