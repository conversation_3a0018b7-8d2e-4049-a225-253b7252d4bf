<?php
/**
 * Debug filtering to see what happens to SARAGEA TUDORIŢA
 */

require_once 'config/config.php';

echo "=== DEBUGGING FILTERING ===" . PHP_EOL;

try {
    // Read the SOAP response file
    $responseFile = 'soap_response_2025-07-04_17-14-14.xml';
    $xmlContent = file_get_contents($responseFile);
    
    // Extract solutieSumar content
    if (preg_match('/<solutieSumar>(.*?)<\/solutieSumar>/s', $xmlContent, $matches)) {
        $solutieSumar = $matches[1];
        
        // Apply the same pattern as in the service
        $pattern = '/formulate de creditorii ([^;]+(?:;[^;]+)*)/';
        if (preg_match($pattern, $solutieSumar, $matches)) {
            $creditorsText = $matches[1];
            
            // Apply the cleaning
            $creditorsText = preg_replace('/\.\s*Suma de 200 de lei.*$/s', '', $creditorsText);
            
            // Split into names
            $creditorNames = explode(';', $creditorsText);
            
            echo "Total names after split: " . count($creditorNames) . PHP_EOL;
            echo PHP_EOL;
            
            // Find and analyze the specific parties
            $targetParties = ['SARAGEA TUDORIŢA', 'ZAMFIR NICOLETA'];
            
            foreach ($targetParties as $target) {
                echo "=== ANALYZING: $target ===" . PHP_EOL;
                
                $found = false;
                foreach ($creditorNames as $index => $name) {
                    if (strpos($name, $target) !== false) {
                        $found = true;
                        $originalName = $name;
                        
                        echo "Found at index $index: '$originalName'" . PHP_EOL;
                        echo "Original length: " . strlen($originalName) . PHP_EOL;
                        
                        // Apply the same cleaning as in the service
                        $name = trim($name);
                        echo "After trim: '$name' (length: " . strlen($name) . ")" . PHP_EOL;
                        
                        // Remove common suffixes
                        $name = preg_replace('/\s*\(date\)\s*$/', '', $name);
                        echo "After (date) removal: '$name' (length: " . strlen($name) . ")" . PHP_EOL;
                        
                        $name = preg_replace('/^\s*și\s+/', '', $name);
                        echo "After 'și' removal: '$name' (length: " . strlen($name) . ")" . PHP_EOL;
                        
                        $name = trim($name);
                        echo "After final trim: '$name' (length: " . strlen($name) . ")" . PHP_EOL;
                        
                        // Check length filter
                        if (!empty($name) && strlen($name) > 2 && strlen($name) <= 100) {
                            echo "✅ Passes length filter" . PHP_EOL;
                            
                            // Check regex filter
                            if (preg_match('/^[A-ZĂÂÎȘȚŢ][A-ZĂÂÎȘȚŢ\s\-\.\(\)]+$/u', $name)) {
                                echo "✅ Passes regex filter" . PHP_EOL;
                                
                                // Check blacklist filter
                                if (!preg_match('/\b(suma|lei|reprezentând|ajutor|public|judiciar|conform|pronunţată|dosarul|rămâne|sarcina|statului|drept|apel|termen|zile|comunicare|depune|tribunalul|secţia|civilă|punerea|soluţiei|dispoziţia|părţilor|mijlocirea|grefei|instanţei|astăzi|camera|consiliu)\b/i', $name)) {
                                    echo "✅ Passes blacklist filter" . PHP_EOL;
                                    echo "🎉 WOULD BE INCLUDED" . PHP_EOL;
                                } else {
                                    echo "❌ BLOCKED by blacklist filter" . PHP_EOL;
                                }
                            } else {
                                echo "❌ BLOCKED by regex filter" . PHP_EOL;
                            }
                        } else {
                            echo "❌ BLOCKED by length filter" . PHP_EOL;
                        }
                        
                        break;
                    }
                }
                
                if (!$found) {
                    echo "❌ NOT FOUND in split names" . PHP_EOL;
                }
                
                echo PHP_EOL;
            }
            
        }
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
}

echo PHP_EOL . "=== DEBUG COMPLETE ===" . PHP_EOL;
?>
