<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 INVESTIGATING PARTY SEARCH DISPLAY ISSUES\n";
echo "=============================================\n\n";

$dosarService = new DosarService();

// Test cases with known party issues
$testCases = [
    [
        'numar' => '130/98/2022',
        'institutie' => 'CurteadeApelBUCURESTI',
        'searchParty' => 'BURDUŞELU',
        'description' => 'Case with 400+ parties, search for BURDUŞELU'
    ],
    [
        'numar' => '130/98/2022', 
        'institutie' => 'TribunalulIALOMITA',
        'searchParty' => 'NORDIS',
        'description' => 'Case with 527+ parties, search for NORDIS'
    ]
];

foreach ($testCases as $i => $testCase) {
    echo "🔍 TEST CASE " . ($i + 1) . ": {$testCase['description']}\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    try {
        // Get case details
        $dosar = $dosarService->getDetaliiDosar($testCase['numar'], $testCase['institutie']);
        
        if (!$dosar) {
            echo "❌ Case not found\n\n";
            continue;
        }
        
        echo "✅ Case found\n";
        echo "Total parties loaded: " . count($dosar->parti) . "\n";
        
        // Test party search functionality
        echo "\n🔍 TESTING PARTY SEARCH:\n";
        echo "------------------------\n";
        
        $searchTerm = $testCase['searchParty'];
        $foundParties = [];
        $exactMatches = [];
        $partialMatches = [];
        
        foreach ($dosar->parti as $index => $party) {
            $partyName = is_array($party) ? $party['nume'] : $party->nume;
            $partyQuality = is_array($party) ? ($party['calitate'] ?? '') : ($party->calitate ?? '');
            $partySource = is_array($party) ? ($party['source'] ?? 'soap_api') : ($party->source ?? 'soap_api');
            
            // Test exact match (case insensitive)
            if (stripos($partyName, $searchTerm) !== false) {
                $foundParties[] = [
                    'index' => $index + 1,
                    'name' => $partyName,
                    'quality' => $partyQuality,
                    'source' => $partySource,
                    'match_type' => 'partial'
                ];
                
                // Check if it's an exact match
                if (strcasecmp($partyName, $searchTerm) === 0) {
                    $exactMatches[] = $foundParties[count($foundParties) - 1];
                } else {
                    $partialMatches[] = $foundParties[count($foundParties) - 1];
                }
            }
        }
        
        echo "Search term: '{$searchTerm}'\n";
        echo "Total matches found: " . count($foundParties) . "\n";
        echo "Exact matches: " . count($exactMatches) . "\n";
        echo "Partial matches: " . count($partialMatches) . "\n\n";
        
        // Display first 5 matches
        echo "🔍 FIRST 5 MATCHES:\n";
        echo "-------------------\n";
        $displayCount = min(5, count($foundParties));
        for ($j = 0; $j < $displayCount; $j++) {
            $party = $foundParties[$j];
            echo "#{$party['index']}: '{$party['name']}' ({$party['quality']}) [Source: {$party['source']}]\n";
        }
        
        if (count($foundParties) > 5) {
            echo "... and " . (count($foundParties) - 5) . " more matches\n";
        }
        
        // Test the search algorithm functions
        echo "\n🔍 TESTING SEARCH ALGORITHM FUNCTIONS:\n";
        echo "--------------------------------------\n";
        
        // Test findMatchingParty function (if it exists)
        if (function_exists('findMatchingParty')) {
            $matchingParty = findMatchingParty($dosar->parti, $searchTerm);
            if ($matchingParty) {
                $matchedName = is_array($matchingParty) ? $matchingParty['nume'] : $matchingParty->nume;
                echo "✅ findMatchingParty found: '{$matchedName}'\n";
            } else {
                echo "❌ findMatchingParty returned null\n";
            }
        } else {
            echo "⚠️ findMatchingParty function not available in this context\n";
        }
        
        // Test Romanian diacritics normalization
        echo "\n🔍 TESTING ROMANIAN DIACRITICS:\n";
        echo "-------------------------------\n";
        
        $diacriticsTests = [
            'BURDUŞELU' => 'burduselu',
            'TUDORIŢA' => 'tudorita',
            'ŞTEFAN' => 'stefan',
            'ŢÎMBALARIU' => 'timbalariu'
        ];
        
        foreach ($diacriticsTests as $original => $expected) {
            // Simple normalization test
            $normalized = strtolower(str_replace(
                ['ă', 'â', 'î', 'ș', 'ț', 'ş', 'ţ', 'Ă', 'Â', 'Î', 'Ș', 'Ț', 'Ş', 'Ţ'],
                ['a', 'a', 'i', 's', 't', 's', 't', 'a', 'a', 'i', 's', 't', 's', 't'],
                $original
            ));
            
            $status = ($normalized === $expected) ? '✅' : '❌';
            echo "{$status} '{$original}' -> '{$normalized}' (expected: '{$expected}')\n";
        }
        
        // Test party extraction completeness
        echo "\n🔍 TESTING PARTY EXTRACTION COMPLETENESS:\n";
        echo "-----------------------------------------\n";
        
        $soapParties = 0;
        $decisionTextParties = 0;
        
        foreach ($dosar->parti as $party) {
            $source = is_array($party) ? ($party['source'] ?? 'soap_api') : ($party->source ?? 'soap_api');
            if ($source === 'soap_api') {
                $soapParties++;
            } else {
                $decisionTextParties++;
            }
        }
        
        echo "SOAP API parties: {$soapParties}\n";
        echo "Decision text parties: {$decisionTextParties}\n";
        echo "Total parties: " . ($soapParties + $decisionTextParties) . "\n";
        
        if ($soapParties >= 100) {
            echo "⚠️ SOAP API at limit (100), decision text extraction should be active\n";
        }
        
        if ($decisionTextParties === 0 && $soapParties >= 100) {
            echo "❌ CRITICAL: No decision text parties found despite SOAP API limit\n";
        } else if ($decisionTextParties > 0) {
            echo "✅ Decision text extraction is working\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat("=", 70) . "\n\n";
}

echo "✅ Investigation complete\n";
