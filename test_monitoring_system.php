<?php
/**
 * Portal Judiciar România - Monitoring System Test
 * 
 * Comprehensive test script for the case monitoring and notification system.
 * Tests database connectivity, SOAP API integration, notification services,
 * and overall system functionality.
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */

// Load bootstrap
require_once __DIR__ . '/bootstrap.php';

use App\Config\Database;
use App\Services\CaseMonitoringService;
use App\Services\NotificationManager;
use App\Services\DosarService;
use App\Services\UserPreferencesManager;
use App\Security\CSRFProtection;
use App\Security\RateLimiter;
use App\Security\GDPRCompliance;

class MonitoringSystemTest
{
    private $results = [];
    private $errors = [];
    private $warnings = [];
    
    public function __construct()
    {
        echo "🚀 Portal Judiciar România - Monitoring System Test\n";
        echo "=" . str_repeat("=", 60) . "\n\n";
    }
    
    /**
     * Run all tests
     */
    public function runAllTests()
    {
        $this->testDatabaseConnection();
        $this->testDatabaseTables();
        $this->testSOAPAPIConnection();
        $this->testNotificationServices();
        $this->testSecurityFeatures();
        $this->testMonitoringServices();
        $this->testEmailConfiguration();
        $this->testCronJobSetup();
        $this->testPerformance();
        
        $this->displayResults();
    }
    
    /**
     * Test database connection
     */
    private function testDatabaseConnection()
    {
        echo "📊 Testing Database Connection...\n";
        
        try {
            $connection = Database::getConnection();
            $version = $connection->getAttribute(PDO::ATTR_SERVER_VERSION);
            
            $this->results['database_connection'] = [
                'status' => 'PASS',
                'message' => "Connected to MySQL {$version}",
                'details' => [
                    'host' => DB_HOST,
                    'database' => DB_NAME,
                    'charset' => DB_CHARSET
                ]
            ];
            
            echo "   ✅ Database connection successful\n";
            echo "   📋 MySQL Version: {$version}\n";
            
        } catch (Exception $e) {
            $this->results['database_connection'] = [
                'status' => 'FAIL',
                'message' => 'Database connection failed: ' . $e->getMessage()
            ];
            $this->errors[] = 'Database connection failed';
            echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test database tables
     */
    private function testDatabaseTables()
    {
        echo "🗄️ Testing Database Tables...\n";
        
        $requiredTables = [
            'users',
            'monitored_cases',
            'case_snapshots',
            'case_changes',
            'notification_queue',
            'user_sessions',
            'gdpr_requests',
            'system_logs'
        ];
        
        $existingTables = [];
        $missingTables = [];
        
        try {
            foreach ($requiredTables as $table) {
                try {
                    $result = Database::fetchOne("SHOW TABLES LIKE ?", [$table]);
                    if ($result) {
                        $existingTables[] = $table;
                        echo "   ✅ Table '{$table}' exists\n";
                    } else {
                        $missingTables[] = $table;
                        echo "   ❌ Table '{$table}' missing\n";
                    }
                } catch (Exception $e) {
                    $missingTables[] = $table;
                    echo "   ❌ Table '{$table}' missing (error: " . $e->getMessage() . ")\n";
                }
            }
            
            if (empty($missingTables)) {
                $this->results['database_tables'] = [
                    'status' => 'PASS',
                    'message' => 'All required tables exist',
                    'details' => ['existing_tables' => $existingTables]
                ];
            } else {
                $this->results['database_tables'] = [
                    'status' => 'FAIL',
                    'message' => 'Missing tables: ' . implode(', ', $missingTables),
                    'details' => [
                        'existing_tables' => $existingTables,
                        'missing_tables' => $missingTables
                    ]
                ];
                $this->errors[] = 'Missing database tables';
            }
            
        } catch (Exception $e) {
            $this->results['database_tables'] = [
                'status' => 'FAIL',
                'message' => 'Error checking tables: ' . $e->getMessage()
            ];
            $this->errors[] = 'Database table check failed';
            echo "   ❌ Error checking tables: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test SOAP API connection
     */
    private function testSOAPAPIConnection()
    {
        echo "🌐 Testing SOAP API Connection...\n";
        
        try {
            $dosarService = new DosarService();
            
            // Test with a simple case search
            $testResult = $dosarService->cautareDupaNumarDosar('1/1/2024', 'JudecatoriaSECTORUL1BUCURESTI');
            
            if ($testResult !== false) {
                $this->results['soap_api'] = [
                    'status' => 'PASS',
                    'message' => 'SOAP API connection successful',
                    'details' => [
                        'wsdl_url' => SOAP_WSDL,
                        'test_case' => '1/1/2024',
                        'test_institution' => 'JudecatoriaSECTORUL1BUCURESTI'
                    ]
                ];
                echo "   ✅ SOAP API connection successful\n";
                echo "   📋 WSDL URL: " . SOAP_WSDL . "\n";
            } else {
                $this->results['soap_api'] = [
                    'status' => 'WARN',
                    'message' => 'SOAP API connected but test search returned no results'
                ];
                $this->warnings[] = 'SOAP API test search returned no results';
                echo "   ⚠️ SOAP API connected but test search returned no results\n";
            }
            
        } catch (Exception $e) {
            $this->results['soap_api'] = [
                'status' => 'FAIL',
                'message' => 'SOAP API connection failed: ' . $e->getMessage()
            ];
            $this->errors[] = 'SOAP API connection failed';
            echo "   ❌ SOAP API connection failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test notification services
     */
    private function testNotificationServices()
    {
        echo "📧 Testing Notification Services...\n";
        
        try {
            $notificationManager = new NotificationManager();
            
            // Test email configuration
            $emailConfigValid = defined('CONTACT_EMAIL') && !empty(CONTACT_EMAIL);
            
            if ($emailConfigValid) {
                echo "   ✅ Email configuration found\n";
                echo "   📋 Contact Email: " . CONTACT_EMAIL . "\n";
                
                $this->results['notification_services'] = [
                    'status' => 'PASS',
                    'message' => 'Notification services configured',
                    'details' => [
                        'contact_email' => CONTACT_EMAIL,
                        'smtp_configured' => defined('SMTP_HOST') && !empty(SMTP_HOST)
                    ]
                ];
            } else {
                $this->results['notification_services'] = [
                    'status' => 'WARN',
                    'message' => 'Email configuration incomplete'
                ];
                $this->warnings[] = 'Email configuration incomplete';
                echo "   ⚠️ Email configuration incomplete\n";
            }
            
        } catch (Exception $e) {
            $this->results['notification_services'] = [
                'status' => 'FAIL',
                'message' => 'Notification services test failed: ' . $e->getMessage()
            ];
            $this->errors[] = 'Notification services test failed';
            echo "   ❌ Notification services test failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test security features
     */
    private function testSecurityFeatures()
    {
        echo "🔒 Testing Security Features...\n";
        
        try {
            // Test CSRF protection
            $csrfToken = CSRFProtection::generateToken('test');
            $csrfValid = CSRFProtection::validateToken($csrfToken, 'test');
            
            if ($csrfValid) {
                echo "   ✅ CSRF protection working\n";
            } else {
                echo "   ❌ CSRF protection failed\n";
                $this->errors[] = 'CSRF protection failed';
            }
            
            // Test rate limiting
            $rateLimitCheck = RateLimiter::checkLimit('test', 'test_key');
            echo "   ✅ Rate limiting system operational\n";
            
            // Test GDPR compliance
            $gdprFeatures = [
                'consent_management' => method_exists(GDPRCompliance::class, 'recordConsent'),
                'data_export' => method_exists(GDPRCompliance::class, 'exportUserData'),
                'data_deletion' => method_exists(GDPRCompliance::class, 'deleteUserData')
            ];
            
            if (array_sum($gdprFeatures) === count($gdprFeatures)) {
                echo "   ✅ GDPR compliance features available\n";
                
                $this->results['security_features'] = [
                    'status' => 'PASS',
                    'message' => 'All security features operational',
                    'details' => [
                        'csrf_protection' => $csrfValid,
                        'rate_limiting' => true,
                        'gdpr_compliance' => $gdprFeatures
                    ]
                ];
            } else {
                $this->results['security_features'] = [
                    'status' => 'WARN',
                    'message' => 'Some GDPR features missing'
                ];
                $this->warnings[] = 'Some GDPR features missing';
                echo "   ⚠️ Some GDPR features missing\n";
            }
            
        } catch (Exception $e) {
            $this->results['security_features'] = [
                'status' => 'FAIL',
                'message' => 'Security features test failed: ' . $e->getMessage()
            ];
            $this->errors[] = 'Security features test failed';
            echo "   ❌ Security features test failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * Test monitoring services
     */
    private function testMonitoringServices()
    {
        echo "👁️ Testing Monitoring Services...\n";

        try {
            $monitoringService = new CaseMonitoringService();
            $userPreferencesManager = new UserPreferencesManager();

            // Test service initialization
            echo "   ✅ CaseMonitoringService initialized\n";
            echo "   ✅ UserPreferencesManager initialized\n";

            $this->results['monitoring_services'] = [
                'status' => 'PASS',
                'message' => 'Monitoring services operational',
                'details' => [
                    'case_monitoring_service' => true,
                    'user_preferences_manager' => true
                ]
            ];

        } catch (Exception $e) {
            $this->results['monitoring_services'] = [
                'status' => 'FAIL',
                'message' => 'Monitoring services test failed: ' . $e->getMessage()
            ];
            $this->errors[] = 'Monitoring services test failed';
            echo "   ❌ Monitoring services test failed: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * Test email configuration
     */
    private function testEmailConfiguration()
    {
        echo "📬 Testing Email Configuration...\n";

        try {
            $emailSettings = [
                'CONTACT_EMAIL' => defined('CONTACT_EMAIL') ? CONTACT_EMAIL : null,
                'CONTACT_NAME' => defined('CONTACT_NAME') ? CONTACT_NAME : null,
                'SMTP_HOST' => defined('SMTP_HOST') ? SMTP_HOST : null,
                'SMTP_PORT' => defined('SMTP_PORT') ? SMTP_PORT : null,
                'SMTP_USERNAME' => defined('SMTP_USERNAME') ? SMTP_USERNAME : null,
                'SMTP_PASSWORD' => defined('SMTP_PASSWORD') ? (SMTP_PASSWORD ? '***configured***' : null) : null
            ];

            $configuredSettings = array_filter($emailSettings);
            $missingSettings = array_diff_key($emailSettings, $configuredSettings);

            foreach ($configuredSettings as $setting => $value) {
                echo "   ✅ {$setting}: {$value}\n";
            }

            foreach ($missingSettings as $setting => $value) {
                echo "   ⚠️ {$setting}: Not configured\n";
            }

            if (count($configuredSettings) >= 2) { // At least CONTACT_EMAIL and CONTACT_NAME
                $this->results['email_configuration'] = [
                    'status' => 'PASS',
                    'message' => 'Email configuration sufficient',
                    'details' => [
                        'configured_settings' => array_keys($configuredSettings),
                        'missing_settings' => array_keys($missingSettings)
                    ]
                ];
            } else {
                $this->results['email_configuration'] = [
                    'status' => 'WARN',
                    'message' => 'Email configuration incomplete',
                    'details' => [
                        'configured_settings' => array_keys($configuredSettings),
                        'missing_settings' => array_keys($missingSettings)
                    ]
                ];
                $this->warnings[] = 'Email configuration incomplete';
            }

        } catch (Exception $e) {
            $this->results['email_configuration'] = [
                'status' => 'FAIL',
                'message' => 'Email configuration test failed: ' . $e->getMessage()
            ];
            $this->errors[] = 'Email configuration test failed';
            echo "   ❌ Email configuration test failed: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * Test cron job setup
     */
    private function testCronJobSetup()
    {
        echo "⏰ Testing Cron Job Setup...\n";

        try {
            $cronFiles = [
                'monitor_cases.php' => __DIR__ . '/cron/monitor_cases.php',
                'send_scheduled_notifications.php' => __DIR__ . '/cron/send_scheduled_notifications.php',
                'monitor_system.php' => __DIR__ . '/cron/monitor_system.php'
            ];

            $existingCronFiles = [];
            $missingCronFiles = [];

            foreach ($cronFiles as $name => $path) {
                if (file_exists($path)) {
                    $existingCronFiles[] = $name;
                    echo "   ✅ Cron file '{$name}' exists\n";
                } else {
                    $missingCronFiles[] = $name;
                    echo "   ❌ Cron file '{$name}' missing\n";
                }
            }

            // Check if logs directory exists
            $logsDir = __DIR__ . '/logs';
            if (is_dir($logsDir) && is_writable($logsDir)) {
                echo "   ✅ Logs directory exists and is writable\n";
            } else {
                echo "   ⚠️ Logs directory missing or not writable\n";
                $this->warnings[] = 'Logs directory issues';
            }

            if (empty($missingCronFiles)) {
                $this->results['cron_job_setup'] = [
                    'status' => 'PASS',
                    'message' => 'All cron job files exist',
                    'details' => [
                        'existing_files' => $existingCronFiles,
                        'logs_directory' => is_dir($logsDir) && is_writable($logsDir)
                    ]
                ];
            } else {
                $this->results['cron_job_setup'] = [
                    'status' => 'WARN',
                    'message' => 'Some cron job files missing',
                    'details' => [
                        'existing_files' => $existingCronFiles,
                        'missing_files' => $missingCronFiles
                    ]
                ];
                $this->warnings[] = 'Some cron job files missing';
            }

        } catch (Exception $e) {
            $this->results['cron_job_setup'] = [
                'status' => 'FAIL',
                'message' => 'Cron job setup test failed: ' . $e->getMessage()
            ];
            $this->errors[] = 'Cron job setup test failed';
            echo "   ❌ Cron job setup test failed: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * Test performance
     */
    private function testPerformance()
    {
        echo "⚡ Testing Performance...\n";

        try {
            // Test database query performance
            $start = microtime(true);
            Database::fetchAll("SELECT 1");
            $dbTime = microtime(true) - $start;

            echo "   📊 Database query time: " . number_format($dbTime * 1000, 2) . "ms\n";

            // Test memory usage
            $memoryUsage = memory_get_usage(true);
            $memoryPeak = memory_get_peak_usage(true);

            echo "   💾 Memory usage: " . $this->formatBytes($memoryUsage) . "\n";
            echo "   📈 Peak memory: " . $this->formatBytes($memoryPeak) . "\n";

            // Performance thresholds
            $dbTimeOk = $dbTime < 0.1; // 100ms
            $memoryOk = $memoryUsage < 50 * 1024 * 1024; // 50MB

            if ($dbTimeOk && $memoryOk) {
                $this->results['performance'] = [
                    'status' => 'PASS',
                    'message' => 'Performance within acceptable limits',
                    'details' => [
                        'db_query_time_ms' => number_format($dbTime * 1000, 2),
                        'memory_usage_mb' => number_format($memoryUsage / 1024 / 1024, 2),
                        'peak_memory_mb' => number_format($memoryPeak / 1024 / 1024, 2)
                    ]
                ];
                echo "   ✅ Performance acceptable\n";
            } else {
                $this->results['performance'] = [
                    'status' => 'WARN',
                    'message' => 'Performance issues detected',
                    'details' => [
                        'db_query_time_ms' => number_format($dbTime * 1000, 2),
                        'memory_usage_mb' => number_format($memoryUsage / 1024 / 1024, 2),
                        'db_time_ok' => $dbTimeOk,
                        'memory_ok' => $memoryOk
                    ]
                ];
                $this->warnings[] = 'Performance issues detected';
                echo "   ⚠️ Performance issues detected\n";
            }

        } catch (Exception $e) {
            $this->results['performance'] = [
                'status' => 'FAIL',
                'message' => 'Performance test failed: ' . $e->getMessage()
            ];
            $this->errors[] = 'Performance test failed';
            echo "   ❌ Performance test failed: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }

    /**
     * Display test results
     */
    private function displayResults()
    {
        echo "📋 TEST RESULTS SUMMARY\n";
        echo "=" . str_repeat("=", 60) . "\n\n";

        $totalTests = count($this->results);
        $passedTests = 0;
        $failedTests = 0;
        $warningTests = 0;

        foreach ($this->results as $testName => $result) {
            $status = $result['status'];
            $message = $result['message'];

            switch ($status) {
                case 'PASS':
                    echo "✅ {$testName}: {$message}\n";
                    $passedTests++;
                    break;
                case 'WARN':
                    echo "⚠️ {$testName}: {$message}\n";
                    $warningTests++;
                    break;
                case 'FAIL':
                    echo "❌ {$testName}: {$message}\n";
                    $failedTests++;
                    break;
            }
        }

        echo "\n" . str_repeat("-", 60) . "\n";
        echo "📊 STATISTICS:\n";
        echo "   Total Tests: {$totalTests}\n";
        echo "   ✅ Passed: {$passedTests}\n";
        echo "   ⚠️ Warnings: {$warningTests}\n";
        echo "   ❌ Failed: {$failedTests}\n";

        if ($failedTests === 0 && $warningTests === 0) {
            echo "\n🚀 SYSTEM READY FOR PRODUCTION!\n";
            echo "All tests passed successfully. The monitoring system is fully operational.\n";
        } elseif ($failedTests === 0) {
            echo "\n✅ SYSTEM OPERATIONAL WITH WARNINGS\n";
            echo "The monitoring system is functional but has some configuration warnings.\n";
        } else {
            echo "\n❌ SYSTEM NEEDS ATTENTION\n";
            echo "Critical issues found that need to be resolved before production use.\n";
        }

        if (!empty($this->errors)) {
            echo "\n🔴 CRITICAL ERRORS:\n";
            foreach ($this->errors as $error) {
                echo "   • {$error}\n";
            }
        }

        if (!empty($this->warnings)) {
            echo "\n🟡 WARNINGS:\n";
            foreach ($this->warnings as $warning) {
                echo "   • {$warning}\n";
            }
        }

        echo "\n" . str_repeat("=", 60) . "\n";
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

// Run the test if called directly
if (php_sapi_name() === 'cli' || basename($_SERVER['SCRIPT_NAME']) === 'test_monitoring_system.php') {
    $test = new MonitoringSystemTest();
    $test->runAllTests();
}
