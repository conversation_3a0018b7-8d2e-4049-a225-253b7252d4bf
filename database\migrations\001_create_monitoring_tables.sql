-- <PERSON> Judiciar - Database Schema for Case Monitoring System
-- Created: 2025-07-03
-- Description: Comprehensive database schema for case monitoring and email notifications

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS portal_judiciar 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE portal_judiciar;

-- =====================================================
-- USERS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    phone VARCHAR(20) NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(64) NULL,
    email_verification_expires DATETIME NULL,
    password_reset_token VARCHAR(64) NULL,
    password_reset_expires DATETIME NULL,
    gdpr_consent BOOLEAN DEFAULT FALSE,
    gdpr_consent_date DATETIME NULL,
    gdpr_consent_ip VARCHAR(45) NULL,
    notification_preferences JSON NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_email_verified (email_verified),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- MONITORED CASES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS monitored_cases (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    case_number VARCHAR(50) NOT NULL,
    institution_code VARCHAR(100) NOT NULL,
    institution_name VARCHAR(255) NOT NULL,
    case_object TEXT NULL,
    monitoring_reason TEXT NULL,
    notification_frequency ENUM('immediate', 'daily', 'weekly') DEFAULT 'daily',
    last_checked DATETIME NULL,
    last_notification_sent DATETIME NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_case (user_id, case_number, institution_code),
    INDEX idx_user_id (user_id),
    INDEX idx_case_number (case_number),
    INDEX idx_institution_code (institution_code),
    INDEX idx_is_active (is_active),
    INDEX idx_last_checked (last_checked),
    INDEX idx_notification_frequency (notification_frequency)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- CASE SNAPSHOTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS case_snapshots (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    monitored_case_id INT UNSIGNED NOT NULL,
    snapshot_data JSON NOT NULL,
    snapshot_hash VARCHAR(64) NOT NULL,
    case_status VARCHAR(100) NULL,
    case_stage VARCHAR(100) NULL,
    next_hearing_date DATE NULL,
    last_modification_date DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (monitored_case_id) REFERENCES monitored_cases(id) ON DELETE CASCADE,
    INDEX idx_monitored_case_id (monitored_case_id),
    INDEX idx_snapshot_hash (snapshot_hash),
    INDEX idx_case_status (case_status),
    INDEX idx_next_hearing_date (next_hearing_date),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- CASE CHANGES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS case_changes (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    monitored_case_id INT UNSIGNED NOT NULL,
    old_snapshot_id INT UNSIGNED NULL,
    new_snapshot_id INT UNSIGNED NOT NULL,
    change_type ENUM('hearing_date', 'status', 'stage', 'parties', 'judge', 'solution', 'other') NOT NULL,
    change_description TEXT NOT NULL,
    change_details JSON NULL,
    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (monitored_case_id) REFERENCES monitored_cases(id) ON DELETE CASCADE,
    FOREIGN KEY (old_snapshot_id) REFERENCES case_snapshots(id) ON DELETE SET NULL,
    FOREIGN KEY (new_snapshot_id) REFERENCES case_snapshots(id) ON DELETE CASCADE,
    INDEX idx_monitored_case_id (monitored_case_id),
    INDEX idx_change_type (change_type),
    INDEX idx_detected_at (detected_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- NOTIFICATION QUEUE TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS notification_queue (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    monitored_case_id INT UNSIGNED NOT NULL,
    case_change_id INT UNSIGNED NULL,
    notification_type ENUM('immediate', 'daily_digest', 'weekly_summary') NOT NULL,
    email_subject VARCHAR(255) NOT NULL,
    email_body TEXT NOT NULL,
    email_html_body TEXT NULL,
    priority TINYINT UNSIGNED DEFAULT 5,
    status ENUM('pending', 'processing', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
    attempts TINYINT UNSIGNED DEFAULT 0,
    max_attempts TINYINT UNSIGNED DEFAULT 3,
    scheduled_for DATETIME NOT NULL,
    sent_at DATETIME NULL,
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (monitored_case_id) REFERENCES monitored_cases(id) ON DELETE CASCADE,
    FOREIGN KEY (case_change_id) REFERENCES case_changes(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_scheduled_for (scheduled_for),
    INDEX idx_notification_type (notification_type),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- USER SESSIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT UNSIGNED NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    payload TEXT NOT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- GDPR DATA REQUESTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS gdpr_requests (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    request_type ENUM('export', 'delete') NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME NULL,
    file_path VARCHAR(500) NULL,
    expires_at DATETIME NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_requested_at (requested_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- SYSTEM LOGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS system_logs (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    level ENUM('debug', 'info', 'warning', 'error', 'critical') NOT NULL,
    message TEXT NOT NULL,
    context JSON NULL,
    user_id INT UNSIGNED NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_level (level),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
