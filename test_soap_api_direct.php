<?php
/**
 * Test SOAP API Direct
 * Testează direct SOAP API-ul pentru a vedea câte părți returnează
 */

echo "<!DOCTYPE html>";
echo "<html><head><title>Test SOAP API Direct</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .highlight { background: yellow; font-weight: bold; }
    pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
</style></head><body>";

echo "<h1>🔍 Test SOAP API Direct</h1>";
echo "<p>Testează direct SOAP API-ul pentru a vedea câte părți returnează</p>";
echo "<hr>";

try {
    echo "<div class='section'>";
    echo "<h2>📡 Apel Direct SOAP API</h2>";
    
    // Configurare SOAP client
    $wsdl = 'http://portalquery.just.ro/Query.asmx?WSDL';
    $options = [
        'soap_version' => SOAP_1_1,
        'exceptions' => true,
        'trace' => 1,
        'cache_wsdl' => WSDL_CACHE_NONE
    ];
    
    $soapClient = new SoapClient($wsdl, $options);
    
    echo "<p class='info'>✅ SOAP Client conectat la: {$wsdl}</p>";
    
    // Parametrii pentru căutare
    $params = [
        'numarDosar' => '130/98/2022',
        'obiectDosar' => '',
        'numeParte' => '',
        'institutie' => 'TribunalulIALOMITA',
        'dataStart' => null,
        'dataStop' => null
    ];
    
    echo "<h3>Parametrii apel:</h3>";
    echo "<pre>" . print_r($params, true) . "</pre>";
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🔄 Executare Apel SOAP</h2>";
    
    // Apel SOAP
    $startTime = microtime(true);
    $response = $soapClient->CautareDosare($params);
    $endTime = microtime(true);
    
    $executionTime = round(($endTime - $startTime) * 1000, 2);
    
    echo "<p class='success'>✅ Apel SOAP executat cu succes în {$executionTime}ms</p>";
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>📊 Analiza Răspuns SOAP</h2>";
    
    if (isset($response->CautareDosareResult) && !empty($response->CautareDosareResult)) {
        $dosare = $response->CautareDosareResult;
        
        if (!is_array($dosare)) {
            $dosare = [$dosare];
        }
        
        echo "<table>";
        echo "<tr><th>Metric</th><th>Valoare</th><th>Status</th></tr>";
        echo "<tr><td>Dosare găsite</td><td>" . count($dosare) . "</td><td class='success'>✅ OK</td></tr>";
        
        if (count($dosare) > 0) {
            $dosar = $dosare[0]; // Primul dosar
            
            echo "<tr><td>Numărul dosarului</td><td>" . htmlspecialchars($dosar->numar ?? 'N/A') . "</td><td class='info'>ℹ️ Info</td></tr>";
            echo "<tr><td>Instituția</td><td>" . htmlspecialchars($dosar->institutie ?? 'N/A') . "</td><td class='info'>ℹ️ Info</td></tr>";
            
            // Analizăm părțile
            $totalParti = 0;
            if (isset($dosar->parti)) {
                if (is_array($dosar->parti)) {
                    $totalParti = count($dosar->parti);
                } elseif (is_object($dosar->parti)) {
                    // Verificăm dacă este un singur obiect sau un array în obiect
                    if (isset($dosar->parti->DosarParte)) {
                        if (is_array($dosar->parti->DosarParte)) {
                            $totalParti = count($dosar->parti->DosarParte);
                        } else {
                            $totalParti = 1;
                        }
                    }
                }
            }
            
            echo "<tr><td>Părți în SOAP răspuns</td><td>{$totalParti}</td><td class='" . ($totalParti > 0 ? 'success' : 'warning') . "'>" . ($totalParti > 0 ? '✅ Găsite' : '⚠️ Niciuna') . "</td></tr>";
            
            if ($totalParti >= 100) {
                echo "<tr><td>Posibilă limitare la 100</td><td>" . ($totalParti == 100 ? 'Posibil' : 'Nu') . "</td><td class='" . ($totalParti == 100 ? 'warning' : 'success') . "'>" . ($totalParti == 100 ? '⚠️ Exact 100' : '✅ Nu pare limitat') . "</td></tr>";
            }
        }
        
        echo "</table>";
        
        echo "</div>";
        
        if (count($dosare) > 0 && $totalParti > 0) {
            echo "<div class='section'>";
            echo "<h2>📋 Lista Părților din SOAP</h2>";
            
            $dosar = $dosare[0];
            $parti = [];
            
            if (isset($dosar->parti->DosarParte)) {
                if (is_array($dosar->parti->DosarParte)) {
                    $parti = $dosar->parti->DosarParte;
                } else {
                    $parti = [$dosar->parti->DosarParte];
                }
            }
            
            echo "<table>";
            echo "<tr><th>#</th><th>Nume</th><th>Calitate</th><th>Observații</th></tr>";
            
            $displayLimit = min(50, count($parti)); // Afișăm primele 50
            for ($i = 0; $i < $displayLimit; $i++) {
                $parte = $parti[$i];
                $nume = $parte->nume ?? 'N/A';
                $calitate = $parte->calitateParte ?? 'N/A';
                
                // Verificăm dacă este SARAGEA TUDORIŢA
                $isSaragea = (stripos($nume, 'SARAGEA') !== false && stripos($nume, 'TUDORI') !== false);
                $rowClass = $isSaragea ? 'style="background-color: yellow;"' : '';
                
                echo "<tr {$rowClass}>";
                echo "<td>" . ($i + 1) . "</td>";
                echo "<td>" . htmlspecialchars($nume) . "</td>";
                echo "<td>" . htmlspecialchars($calitate) . "</td>";
                echo "<td>" . ($isSaragea ? 'SARAGEA TUDORIŢA găsită!' : '') . "</td>";
                echo "</tr>";
            }
            
            if (count($parti) > 50) {
                echo "<tr><td colspan='4' class='info'>... și încă " . (count($parti) - 50) . " părți</td></tr>";
            }
            
            echo "</table>";
            
            // Căutăm specific SARAGEA TUDORIŢA
            $saragea_found = false;
            $saragea_position = -1;
            
            foreach ($parti as $index => $parte) {
                $nume = $parte->nume ?? '';
                if (stripos($nume, 'SARAGEA') !== false && stripos($nume, 'TUDORI') !== false) {
                    $saragea_found = true;
                    $saragea_position = $index + 1;
                    break;
                }
            }
            
            echo "<h3>🔍 Verificare SARAGEA TUDORIŢA:</h3>";
            echo "<table>";
            echo "<tr><th>Test</th><th>Rezultat</th><th>Status</th></tr>";
            echo "<tr><td>SARAGEA TUDORIŢA în SOAP</td><td>" . ($saragea_found ? "Da" : "Nu") . "</td><td class='" . ($saragea_found ? 'success' : 'error') . "'>" . ($saragea_found ? '✅ Găsită' : '❌ Nu este în SOAP') . "</td></tr>";
            
            if ($saragea_found) {
                echo "<tr><td>Poziția în SOAP</td><td>{$saragea_position}</td><td class='info'>ℹ️ Info</td></tr>";
                echo "<tr><td>În primele 100</td><td>" . ($saragea_position <= 100 ? 'Da' : 'Nu') . "</td><td class='" . ($saragea_position <= 100 ? 'success' : 'warning') . "'>" . ($saragea_position <= 100 ? '✅ În primele 100' : '⚠️ După poziția 100') . "</td></tr>";
            }
            
            echo "</table>";
            
            echo "</div>";
        }
        
    } else {
        echo "<p class='error'>❌ Nu s-au găsit dosare în răspunsul SOAP</p>";
    }
    
    echo "<div class='section'>";
    echo "<h2>🔧 Răspuns SOAP Raw (Primele 2000 caractere)</h2>";
    
    $lastResponse = $soapClient->__getLastResponse();
    echo "<pre>" . htmlspecialchars(substr($lastResponse, 0, 2000)) . "</pre>";
    
    if (strlen($lastResponse) > 2000) {
        echo "<p class='info'>... (răspunsul complet are " . strlen($lastResponse) . " caractere)</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🎯 Concluzie</h2>";
    
    if (isset($totalParti) && $totalParti > 0) {
        if ($totalParti == 100) {
            echo "<p class='warning'>⚠️ SOAP API returnează exact 100 de părți - posibilă limitare în API</p>";
            echo "<p class='info'>Aceasta ar putea fi o limitare în SOAP API-ul oficial, nu în codul nostru</p>";
        } elseif ($totalParti < 100) {
            echo "<p class='success'>✅ SOAP API returnează {$totalParti} părți - nu pare să fie limitare</p>";
        } else {
            echo "<p class='success'>✅ SOAP API returnează {$totalParti} părți - nu există limitare la 100</p>";
        }
        
        if (isset($saragea_found)) {
            if ($saragea_found) {
                echo "<p class='success'>✅ SARAGEA TUDORIŢA este o parte oficială în SOAP API</p>";
            } else {
                echo "<p class='info'>ℹ️ SARAGEA TUDORIŢA nu este în SOAP API - nu este parte oficială</p>";
            }
        }
    } else {
        echo "<p class='error'>❌ Nu s-au găsit părți în răspunsul SOAP</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2 class='error'>❌ Error Occurred</h2>";
    echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p class='info'>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Test completat la " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
