<?php

namespace App\Security;

/**
 * CSRF Protection for Case Monitoring System
 * 
 * Provides comprehensive CSRF token generation and validation
 * with session-based storage and automatic cleanup.
 * 
 * Portal Judiciar România - Case Monitoring System
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */
class CSRFProtection
{
    private const TOKEN_LENGTH = 32;
    private const TOKEN_LIFETIME = 3600; // 1 hour
    private const MAX_TOKENS_PER_SESSION = 10;
    
    /**
     * Generate a new CSRF token
     * 
     * @param string $action Action identifier (e.g., 'add_case', 'update_frequency')
     * @return string Generated token
     */
    public static function generateToken(string $action = 'default'): string
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Initialize CSRF tokens array if not exists
        if (!isset($_SESSION['csrf_tokens'])) {
            $_SESSION['csrf_tokens'] = [];
        }
        
        // Clean up expired tokens
        self::cleanupExpiredTokens();
        
        // Limit number of tokens per session
        if (count($_SESSION['csrf_tokens']) >= self::MAX_TOKENS_PER_SESSION) {
            // Remove oldest token
            $oldestKey = array_key_first($_SESSION['csrf_tokens']);
            unset($_SESSION['csrf_tokens'][$oldestKey]);
        }
        
        // Generate new token
        $token = bin2hex(random_bytes(self::TOKEN_LENGTH));
        $tokenData = [
            'token' => $token,
            'action' => $action,
            'created_at' => time(),
            'expires_at' => time() + self::TOKEN_LIFETIME,
            'used' => false
        ];
        
        $_SESSION['csrf_tokens'][$token] = $tokenData;
        
        return $token;
    }
    
    /**
     * Validate a CSRF token
     * 
     * @param string $token Token to validate
     * @param string $action Action identifier
     * @param bool $singleUse Whether token should be invalidated after use
     * @return bool True if token is valid
     */
    public static function validateToken(string $token, string $action = 'default', bool $singleUse = true): bool
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Check if tokens array exists
        if (!isset($_SESSION['csrf_tokens']) || !is_array($_SESSION['csrf_tokens'])) {
            return false;
        }
        
        // Check if token exists
        if (!isset($_SESSION['csrf_tokens'][$token])) {
            return false;
        }
        
        $tokenData = $_SESSION['csrf_tokens'][$token];
        
        // Check if token has expired
        if (time() > $tokenData['expires_at']) {
            unset($_SESSION['csrf_tokens'][$token]);
            return false;
        }
        
        // Check if token has already been used (for single-use tokens)
        if ($singleUse && $tokenData['used']) {
            return false;
        }
        
        // Check if action matches
        if ($tokenData['action'] !== $action) {
            return false;
        }
        
        // Mark token as used if single-use
        if ($singleUse) {
            $_SESSION['csrf_tokens'][$token]['used'] = true;
        }
        
        return true;
    }
    
    /**
     * Get CSRF token for forms
     * 
     * @param string $action Action identifier
     * @return string HTML hidden input with CSRF token
     */
    public static function getTokenField(string $action = 'default'): string
    {
        $token = self::generateToken($action);
        return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
    }
    
    /**
     * Get CSRF token for AJAX requests
     * 
     * @param string $action Action identifier
     * @return array Token data for AJAX
     */
    public static function getTokenForAjax(string $action = 'default'): array
    {
        $token = self::generateToken($action);
        return [
            'token' => $token,
            'action' => $action,
            'expires_at' => time() + self::TOKEN_LIFETIME
        ];
    }
    
    /**
     * Validate CSRF token from request
     * 
     * @param array $request Request data ($_POST, $_GET, etc.)
     * @param string $action Action identifier
     * @return bool True if token is valid
     */
    public static function validateRequest(array $request, string $action = 'default'): bool
    {
        if (!isset($request['csrf_token'])) {
            return false;
        }
        
        return self::validateToken($request['csrf_token'], $action);
    }
    
    /**
     * Clean up expired tokens
     */
    private static function cleanupExpiredTokens(): void
    {
        if (!isset($_SESSION['csrf_tokens'])) {
            return;
        }
        
        $currentTime = time();
        foreach ($_SESSION['csrf_tokens'] as $token => $data) {
            if ($currentTime > $data['expires_at']) {
                unset($_SESSION['csrf_tokens'][$token]);
            }
        }
    }
    
    /**
     * Clear all CSRF tokens for current session
     */
    public static function clearAllTokens(): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $_SESSION['csrf_tokens'] = [];
    }
    
    /**
     * Get token statistics for debugging
     * 
     * @return array Token statistics
     */
    public static function getTokenStats(): array
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['csrf_tokens'])) {
            return [
                'total_tokens' => 0,
                'active_tokens' => 0,
                'expired_tokens' => 0,
                'used_tokens' => 0
            ];
        }
        
        $total = count($_SESSION['csrf_tokens']);
        $active = 0;
        $expired = 0;
        $used = 0;
        $currentTime = time();
        
        foreach ($_SESSION['csrf_tokens'] as $data) {
            if ($currentTime > $data['expires_at']) {
                $expired++;
            } elseif ($data['used']) {
                $used++;
            } else {
                $active++;
            }
        }
        
        return [
            'total_tokens' => $total,
            'active_tokens' => $active,
            'expired_tokens' => $expired,
            'used_tokens' => $used
        ];
    }
    
    /**
     * Middleware function to protect routes
     * 
     * @param string $action Action identifier
     * @param callable $callback Function to execute if CSRF is valid
     * @param array $request Request data
     * @return mixed Result of callback or false if CSRF invalid
     */
    public static function protect(string $action, callable $callback, array $request = null)
    {
        $request = $request ?? $_REQUEST;
        
        if (!self::validateRequest($request, $action)) {
            http_response_code(403);
            return [
                'success' => false,
                'error' => 'Invalid CSRF token. Please refresh the page and try again.',
                'error_code' => 'CSRF_INVALID'
            ];
        }
        
        return $callback();
    }
    
    /**
     * Generate JavaScript code for AJAX CSRF protection
     * 
     * @param string $action Action identifier
     * @return string JavaScript code
     */
    public static function getAjaxScript(string $action = 'default'): string
    {
        $tokenData = self::getTokenForAjax($action);
        
        return "
        <script>
        window.csrfToken = {
            token: '{$tokenData['token']}',
            action: '{$tokenData['action']}',
            expiresAt: {$tokenData['expires_at']}
        };
        
        // Add CSRF token to all AJAX requests
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (settings.type === 'POST' || settings.type === 'PUT' || settings.type === 'DELETE') {
                    if (settings.data) {
                        if (typeof settings.data === 'string') {
                            settings.data += '&csrf_token=' + encodeURIComponent(window.csrfToken.token);
                        } else {
                            settings.data.csrf_token = window.csrfToken.token;
                        }
                    } else {
                        settings.data = { csrf_token: window.csrfToken.token };
                    }
                }
            }
        });
        
        // Check if token is about to expire
        function checkTokenExpiry() {
            if (Date.now() / 1000 > window.csrfToken.expiresAt - 300) { // 5 minutes before expiry
                console.warn('CSRF token will expire soon. Consider refreshing the page.');
            }
        }
        
        // Check token expiry every 5 minutes
        setInterval(checkTokenExpiry, 300000);
        </script>";
    }
}
