<?php
/**
 * Online Hosting Setup Verification Script
 * 
 * Run this script on your online hosting to verify that
 * the database import and configuration are working correctly.
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔍 PORTAL JUDICIAR ROMÂNIA - ONLINE SETUP VERIFICATION\n";
echo "======================================================\n\n";

$errors = [];
$warnings = [];
$success = [];

// Test 1: Check if bootstrap file exists
echo "1. Checking file structure...\n";
if (file_exists('bootstrap.php')) {
    $success[] = "bootstrap.php found";
    echo "   ✅ bootstrap.php found\n";
} else {
    $errors[] = "bootstrap.php not found";
    echo "   ❌ bootstrap.php not found\n";
}

if (is_dir('src')) {
    $success[] = "src/ directory found";
    echo "   ✅ src/ directory found\n";
} else {
    $errors[] = "src/ directory not found";
    echo "   ❌ src/ directory not found\n";
}

if (is_dir('logs')) {
    $success[] = "logs/ directory found";
    echo "   ✅ logs/ directory found\n";
    
    if (is_writable('logs')) {
        $success[] = "logs/ directory is writable";
        echo "   ✅ logs/ directory is writable\n";
    } else {
        $warnings[] = "logs/ directory is not writable";
        echo "   ⚠️ logs/ directory is not writable\n";
    }
} else {
    $warnings[] = "logs/ directory not found";
    echo "   ⚠️ logs/ directory not found (will be created automatically)\n";
}

// Test 2: Check PHP extensions
echo "\n2. Checking PHP extensions...\n";
$requiredExtensions = ['pdo', 'pdo_mysql', 'curl', 'json', 'mbstring'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        $success[] = "PHP extension {$ext} loaded";
        echo "   ✅ {$ext} extension loaded\n";
    } else {
        $errors[] = "PHP extension {$ext} not loaded";
        echo "   ❌ {$ext} extension not loaded\n";
    }
}

// Test 3: Try to load bootstrap and test database
echo "\n3. Testing database connection...\n";
try {
    if (file_exists('bootstrap.php')) {
        require_once 'bootstrap.php';
        
        if (class_exists('App\Config\Database')) {
            $db = App\Config\Database::getConnection();
            $success[] = "Database connection successful";
            echo "   ✅ Database connection successful\n";
            
            // Test basic queries
            $result = $db->query("SELECT COUNT(*) as count FROM users")->fetch();
            $userCount = $result['count'];
            $success[] = "Users table accessible ({$userCount} users)";
            echo "   ✅ Users table accessible ({$userCount} users)\n";
            
            // Check for required tables
            $requiredTables = ['users', 'monitored_cases', 'notification_queue', 'system_logs'];
            foreach ($requiredTables as $table) {
                try {
                    $result = $db->query("SELECT COUNT(*) as count FROM {$table}")->fetch();
                    $count = $result['count'];
                    $success[] = "Table {$table} accessible ({$count} rows)";
                    echo "   ✅ Table {$table} accessible ({$count} rows)\n";
                } catch (Exception $e) {
                    $errors[] = "Table {$table} not accessible: " . $e->getMessage();
                    echo "   ❌ Table {$table} not accessible\n";
                }
            }
            
            // Check for views
            try {
                $result = $db->query("SELECT COUNT(*) as count FROM active_users")->fetch();
                $count = $result['count'];
                $success[] = "View active_users accessible ({$count} active users)";
                echo "   ✅ View active_users accessible ({$count} active users)\n";
            } catch (Exception $e) {
                $warnings[] = "View active_users not accessible: " . $e->getMessage();
                echo "   ⚠️ View active_users not accessible (will be created automatically)\n";
            }
            
        } else {
            $errors[] = "Database class not found";
            echo "   ❌ Database class not found\n";
        }
    } else {
        $errors[] = "Cannot test database - bootstrap.php not found";
        echo "   ❌ Cannot test database - bootstrap.php not found\n";
    }
} catch (Exception $e) {
    $errors[] = "Database connection failed: " . $e->getMessage();
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
}

// Test 4: Check notification system components
echo "\n4. Testing notification system components...\n";
try {
    if (class_exists('App\Services\NotificationManager')) {
        $success[] = "NotificationManager class loaded";
        echo "   ✅ NotificationManager class loaded\n";
    } else {
        $warnings[] = "NotificationManager class not found";
        echo "   ⚠️ NotificationManager class not found\n";
    }
    
    if (class_exists('App\Services\UserPreferencesManager')) {
        $success[] = "UserPreferencesManager class loaded";
        echo "   ✅ UserPreferencesManager class loaded\n";
    } else {
        $warnings[] = "UserPreferencesManager class not found";
        echo "   ⚠️ UserPreferencesManager class not found\n";
    }
} catch (Exception $e) {
    $warnings[] = "Error testing notification components: " . $e->getMessage();
    echo "   ⚠️ Error testing notification components\n";
}

// Test 5: Check configuration
echo "\n5. Checking configuration...\n";
if (defined('CONTACT_EMAIL')) {
    $success[] = "CONTACT_EMAIL configured: " . CONTACT_EMAIL;
    echo "   ✅ CONTACT_EMAIL configured: " . CONTACT_EMAIL . "\n";
} else {
    $warnings[] = "CONTACT_EMAIL not configured";
    echo "   ⚠️ CONTACT_EMAIL not configured\n";
}

if (defined('BASE_URL')) {
    $success[] = "BASE_URL configured: " . BASE_URL;
    echo "   ✅ BASE_URL configured: " . BASE_URL . "\n";
} else {
    $warnings[] = "BASE_URL not configured";
    echo "   ⚠️ BASE_URL not configured\n";
}

// Generate summary
echo "\n📊 VERIFICATION SUMMARY\n";
echo "======================\n";
echo "✅ Successful checks: " . count($success) . "\n";
echo "⚠️ Warnings: " . count($warnings) . "\n";
echo "❌ Errors: " . count($errors) . "\n\n";

if (empty($errors)) {
    echo "🎉 SETUP VERIFICATION PASSED!\n";
    echo "Your Portal Judiciar România is ready for use.\n\n";
    
    echo "🚀 NEXT STEPS:\n";
    echo "1. Set up cron jobs for automated processing\n";
    echo "2. Test the web interface by accessing your domain\n";
    echo "3. Test user login and search functionality\n";
    echo "4. Configure email settings if needed\n";
    echo "5. Monitor logs for any issues\n";
} else {
    echo "❌ SETUP VERIFICATION FAILED!\n";
    echo "Please fix the following errors:\n\n";
    
    foreach ($errors as $error) {
        echo "❌ {$error}\n";
    }
    
    echo "\n🔧 TROUBLESHOOTING:\n";
    echo "1. Check database credentials in src/Config/Database.php\n";
    echo "2. Ensure all files were uploaded correctly\n";
    echo "3. Verify PHP extensions are installed\n";
    echo "4. Check file permissions on logs directory\n";
    echo "5. Import the SQL file if database tables are missing\n";
}

if (!empty($warnings)) {
    echo "\n⚠️ WARNINGS TO ADDRESS:\n";
    foreach ($warnings as $warning) {
        echo "⚠️ {$warning}\n";
    }
}

echo "\n📋 SYSTEM INFORMATION:\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo "Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Unknown') . "\n";
echo "Current Directory: " . getcwd() . "\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
echo "Max Execution Time: " . ini_get('max_execution_time') . "s\n";

echo "\n✅ VERIFICATION COMPLETED!\n";
?>
