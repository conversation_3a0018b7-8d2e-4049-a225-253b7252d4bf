<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 CHECKING EXTRACTION OF: Burduşelu Tudoriţa\n";
echo "==============================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    
    // Get all parties from the case
    $allParties = [];
    
    // SOAP API parties
    if (isset($dosar->parti)) {
        if (is_array($dosar->parti)) {
            foreach ($dosar->parti as $party) {
                $allParties[] = [
                    'nume' => is_array($party) ? $party['nume'] : $party->nume,
                    'calitate' => is_array($party) ? ($party['calitate'] ?? 'Unknown') : ($party->calitate ?? 'Unknown'),
                    'source' => 'soap_api'
                ];
            }
        } elseif (is_object($dosar->parti)) {
            // Single party object
            $allParties[] = [
                'nume' => $dosar->parti->nume,
                'calitate' => $dosar->parti->calitate ?? 'Unknown',
                'source' => 'soap_api'
            ];
        }
    }
    
    // Decision text parties (get from solutieSumar)
    $solutieSumarText = '';
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $i => $sedinta) {
            if (!empty($sedinta['solutieSumar'])) {
                $solutieSumarText = $sedinta['solutieSumar'];
                break;
            }
        }
    }
    
    if (!empty($solutieSumarText)) {
        // We need to manually extract decision text parties since the method is private
        // Let's check if the name appears in the current party list
        
        echo "Total SOAP API parties: " . count($allParties) . "\n\n";
        
        // Check if "Burduşelu Tudoriţa" is in SOAP API parties
        $foundInSoap = false;
        foreach ($allParties as $party) {
            if (stripos($party['nume'], 'Burduşelu') !== false && stripos($party['nume'], 'Tudoriţa') !== false) {
                echo "✅ Found in SOAP API: '{$party['nume']}' (Quality: {$party['calitate']})\n";
                $foundInSoap = true;
            }
        }
        
        if (!$foundInSoap) {
            echo "❌ NOT found in SOAP API parties\n";
        }
        
        // Now let's get the full party list from the case details page to see if it's extracted from decision text
        echo "\n🔍 CHECKING CURRENT EXTRACTION STATUS:\n";
        echo "======================================\n\n";
        
        // Search for variations in the text
        $variations = [
            'Burduşelu Tudoriţa',
            'BURDUŞELU TUDORIŢA',
            'Burduşelu Tudorita',
            'BURDUŞELU TUDORITA'
        ];
        
        foreach ($variations as $variation) {
            $pos = stripos($solutieSumarText, $variation);
            echo "'{$variation}' in text: ";
            if ($pos !== false) {
                echo "✅ FOUND at position {$pos}\n";
                
                // Get context
                $start = max(0, $pos - 100);
                $length = min(200, strlen($solutieSumarText) - $start);
                $context = substr($solutieSumarText, $start, $length);
                echo "  Context: " . trim($context) . "\n";
            } else {
                echo "❌ NOT FOUND\n";
            }
        }
        
        // Let's also check if there are any parties with "Burduşelu" or "Tudoriţa" in the current extraction
        echo "\n🔍 MANUAL PATTERN TESTING:\n";
        echo "==========================\n\n";
        
        // Test comma separation around the found position
        $tudoritaPos = stripos($solutieSumarText, 'Tudoriţa');
        if ($tudoritaPos !== false) {
            // Get the comma-separated segment containing this name
            $start = max(0, $tudoritaPos - 200);
            $end = min(strlen($solutieSumarText), $tudoritaPos + 200);
            $segment = substr($solutieSumarText, $start, $end - $start);
            
            echo "Segment around 'Tudoriţa':\n";
            echo $segment . "\n\n";
            
            // Split by comma and find the exact name
            $commaNames = explode(',', $segment);
            foreach ($commaNames as $name) {
                $name = trim($name);
                if (stripos($name, 'Tudoriţa') !== false) {
                    echo "Comma-separated name containing 'Tudoriţa': '{$name}'\n";
                    
                    // Test if this would pass our validation
                    if (strlen($name) >= 3 && preg_match('/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+$/u', $name)) {
                        echo "  ✅ Would pass validation\n";
                    } else {
                        echo "  ❌ Would fail validation\n";
                        echo "  Length: " . strlen($name) . "\n";
                        echo "  Regex match: " . (preg_match('/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+$/u', $name) ? 'YES' : 'NO') . "\n";
                    }
                }
            }
        }
    }
    
    echo "\n✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
