<?php
/**
 * Test simplu pentru verificarea funcționării
 */

error_reporting(E_ALL);
ini_set("display_errors", 1);

echo "<h1>Test Portal Judiciar</h1>";

try {
    require_once __DIR__ . "/bootstrap.php";
    echo "<p style=\"color: green;\">✅ Bootstrap încărcat cu succes</p>";
    
    $db = App\Config\Database::getConnection();
    echo "<p style=\"color: green;\">✅ Conexiune baza de date OK</p>";
    
    echo "<p><a href=\"public/admin/\">Testează Admin</a></p>";
    echo "<p><a href=\"public/monitor.php\">Testează Monitor</a></p>";
    
} catch (Exception $e) {
    echo "<p style=\"color: red;\">❌ Eroare: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>