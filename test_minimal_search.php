<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

// Test foarte simplu pentru a vedea dacă rezultatele se returnează corect
$dosarService = new DosarService();

echo "<!DOCTYPE html>
<html lang='ro'>
<head>
    <meta charset='UTF-8'>
    <title>Test Minimal - Portal Judiciar România</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { border: 1px solid #ccc; padding: 10px; margin: 10px 0; }
        .success { background: #d4edda; }
        .warning { background: #fff3cd; }
        .error { background: #f8d7da; }
    </style>
</head>
<body>
    <h1>Test Minimal Căutare</h1>";

// Test 1: Căutare simplă
echo "<h2>Test 1: Căutare pentru 'POPESCU'</h2>";

try {
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'numeParte' => 'POPESCU',
        'obiectDosar' => '',
        'dataStart' => '',
        'dataStop' => '',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => ''
    ];
    
    $results = $dosarService->cautareAvansata($searchParams);
    $count = count($results);
    
    echo "<div class='result success'>";
    echo "<strong>Rezultate găsite:</strong> $count<br>";
    
    if ($count > 0) {
        echo "<strong>Primul rezultat:</strong><br>";
        $first = $results[0];
        echo "Număr: " . htmlspecialchars($first->numar ?? 'N/A') . "<br>";
        echo "Instanță: " . htmlspecialchars($first->institutie ?? 'N/A') . "<br>";
        echo "Părți: " . count($first->parti ?? []) . " părți<br>";
        
        if (!empty($first->parti)) {
            echo "<strong>Prima parte:</strong><br>";
            $firstParty = $first->parti[0];
            if (is_object($firstParty)) {
                echo "Nume: " . htmlspecialchars($firstParty->nume ?? 'N/A') . "<br>";
                echo "Calitate: " . htmlspecialchars($firstParty->calitate ?? 'N/A') . "<br>";
            } elseif (is_array($firstParty)) {
                echo "Nume: " . htmlspecialchars($firstParty['nume'] ?? 'N/A') . "<br>";
                echo "Calitate: " . htmlspecialchars($firstParty['calitate'] ?? 'N/A') . "<br>";
            }
        }
        
        if ($count > 1) {
            echo "<br><strong>Ultimul rezultat:</strong><br>";
            $last = $results[$count - 1];
            echo "Număr: " . htmlspecialchars($last->numar ?? 'N/A') . "<br>";
            echo "Instanță: " . htmlspecialchars($last->institutie ?? 'N/A') . "<br>";
            echo "Părți: " . count($last->parti ?? []) . " părți<br>";
        }
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>";
    echo "<strong>Eroare:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

// Test 2: Căutare cu limită
echo "<h2>Test 2: Căutare pentru 'IONESCU' cu limită 10</h2>";

try {
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'numeParte' => 'IONESCU',
        'obiectDosar' => '',
        'dataStart' => '',
        'dataStop' => '',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
        '_maxResults' => 10
    ];
    
    $results = $dosarService->cautareAvansata($searchParams);
    $count = count($results);
    
    echo "<div class='result success'>";
    echo "<strong>Rezultate găsite:</strong> $count (limită: 10)<br>";
    
    if ($count === 10) {
        echo "<div class='warning'>S-a atins limita de 10 rezultate.</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>";
    echo "<strong>Eroare:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

// Test 3: Căutare cu limită mare
echo "<h2>Test 3: Căutare pentru 'IONESCU' cu limită 2000</h2>";

try {
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'numeParte' => 'IONESCU',
        'obiectDosar' => '',
        'dataStart' => '',
        'dataStop' => '',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
        '_maxResults' => 2000
    ];
    
    $results = $dosarService->cautareAvansata($searchParams);
    $count = count($results);
    
    echo "<div class='result success'>";
    echo "<strong>Rezultate găsite:</strong> $count (limită: 2000)<br>";
    
    if ($count === 1000) {
        echo "<div class='warning'>S-a atins limita default de 1000 rezultate din DosarService.</div>";
    } elseif ($count === 2000) {
        echo "<div class='warning'>S-a atins limita specificată de 2000 rezultate.</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='result error'>";
    echo "<strong>Eroare:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

// Test 4: Simulare performBulkSearchWithFilters
echo "<h2>Test 4: Simulare performBulkSearchWithFilters</h2>";

$searchTermsData = [
    ['term' => 'POPESCU', 'type' => 'numeParte'],
    ['term' => 'IONESCU', 'type' => 'numeParte']
];

$bulkResults = [];

foreach ($searchTermsData as $termData) {
    $term = $termData['term'];
    $searchType = $termData['type'];
    
    try {
        $searchParams = [
            'numarDosar' => '',
            'institutie' => null,
            'numeParte' => $term,
            'obiectDosar' => '',
            'dataStart' => '',
            'dataStop' => '',
            'dataUltimaModificareStart' => '',
            'dataUltimaModificareStop' => ''
        ];
        
        $termResults = $dosarService->cautareAvansata($searchParams);
        
        $bulkResults[] = [
            'term' => $term,
            'type' => $searchType,
            'results' => $termResults ?: [],
            'count' => count($termResults ?: []),
            'error' => null
        ];
        
    } catch (Exception $e) {
        $bulkResults[] = [
            'term' => $term,
            'type' => $searchType,
            'results' => [],
            'count' => 0,
            'error' => $e->getMessage()
        ];
    }
}

echo "<div class='result success'>";
echo "<strong>Rezultate bulk search:</strong><br>";

foreach ($bulkResults as $index => $result) {
    echo "<br><strong>Termen " . ($index + 1) . ":</strong> " . htmlspecialchars($result['term']) . "<br>";
    echo "Tip: " . htmlspecialchars($result['type']) . "<br>";
    echo "Rezultate: " . $result['count'] . "<br>";
    
    if (!empty($result['error'])) {
        echo "Eroare: " . htmlspecialchars($result['error']) . "<br>";
    }
    
    if ($result['count'] > 0) {
        echo "Primul rezultat: " . htmlspecialchars($result['results'][0]->numar ?? 'N/A') . "<br>";
    }
}

echo "</div>";

// Test 5: Verificare afișare în bucla foreach
echo "<h2>Test 5: Simulare bucla de afișare din index.php</h2>";

echo "<div class='result success'>";
echo "<strong>Simulare afișare rezultate:</strong><br>";

foreach ($bulkResults as $index => $result) {
    echo "<br><strong>Secțiunea " . ($index + 1) . " - " . htmlspecialchars($result['term']) . ":</strong><br>";
    
    if ($result['count'] > 0) {
        echo "Afișez primele 3 rezultate:<br>";
        
        $displayResults = array_slice($result['results'], 0, 3);
        foreach ($displayResults as $dosar) {
            echo "- Dosar: " . htmlspecialchars($dosar->numar ?? 'N/A');
            echo " | Părți: " . count($dosar->parti ?? []);
            echo " | Instanță: " . htmlspecialchars($dosar->institutie ?? 'N/A') . "<br>";
        }
    } else {
        echo "Nu există rezultate pentru afișare.<br>";
    }
}

echo "</div>";

echo "
    <br>
    <a href='index.php'>Înapoi la Portal</a>
</body>
</html>";
?>
