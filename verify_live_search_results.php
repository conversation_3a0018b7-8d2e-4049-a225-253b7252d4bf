<?php
require_once 'config/config.php';
require_once 'src/Services/DosarService.php';

use App\Services\DosarService;

echo "=== LIVE SEARCH VERIFICATION FOR 14096/3/2024* ===" . PHP_EOL;
echo "Testing the actual search results that users see in the web interface" . PHP_EOL;
echo PHP_EOL;

try {
    // Step 1: Test the exact search that users perform
    echo "=== STEP 1: Simulating User Search Through Web Interface ===" . PHP_EOL;
    
    // Simulate the exact POST request from the web form
    $_POST['bulkSearchTerms'] = "14096/3/2024*";
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    // Capture what the web interface actually returns
    ob_start();
    
    // Include the main search logic from index.php
    require_once 'includes/functions.php';

    // Add the required functions that might be missing
    if (!function_exists('detectSearchType')) {
        function detectSearchType($term) {
            $cleanTerm = trim($term, '"\'');

            // Pattern pentru numărul de dosar cu asterisk wildcard
            if (preg_match('/^\d+\/\d+(?:\/\d+)?\*$/', $cleanTerm)) {
                return 'numarDosar';
            }

            // Pattern pentru numărul de dosar cu sufixe suplimentare
            if (preg_match('/^\d+\/\d+(?:\/\d+)?\/[a-zA-Z0-9]+$/', $cleanTerm)) {
                return 'numarDosar';
            }

            // Pattern standard pentru numărul de dosar
            if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
                return 'numarDosar';
            }

            // Pattern cu prefixe (nr., dosar, număr)
            if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
                return 'numarDosar';
            }

            return 'numeParte';
        }
    }

    if (!function_exists('performBulkSearchWithFilters')) {
        function performBulkSearchWithFilters($searchTermsData, $advancedFilters = []) {
            $dosarService = new DosarService();
            $results = [];

            foreach ($searchTermsData as $termData) {
                $term = $termData['term'];
                $searchType = $termData['type'];

                $searchParams = [
                    'institutie' => null,
                    'dataStart' => null,
                    'dataStop' => null,
                    'dataUltimaModificareStart' => null,
                    'dataUltimaModificareStop' => null
                ];

                // Set the appropriate parameter based on detected type
                switch ($searchType) {
                    case 'numarDosar':
                        $searchParams['numarDosar'] = $term;
                        $searchParams['obiectDosar'] = '';
                        $searchParams['numeParte'] = '';
                        break;
                    case 'obiectDosar':
                        $searchParams['numarDosar'] = '';
                        $searchParams['obiectDosar'] = $term;
                        $searchParams['numeParte'] = '';
                        break;
                    case 'numeParte':
                    default:
                        $searchParams['numarDosar'] = '';
                        $searchParams['obiectDosar'] = '';
                        $searchParams['numeParte'] = $term;
                        break;
                }

                try {
                    $termResults = $dosarService->cautareAvansata($searchParams);

                    $results[] = [
                        'term' => $term,
                        'type' => $searchType,
                        'results' => $termResults,
                        'count' => count($termResults)
                    ];
                } catch (Exception $e) {
                    $results[] = [
                        'term' => $term,
                        'type' => $searchType,
                        'results' => [],
                        'count' => 0,
                        'error' => $e->getMessage()
                    ];
                }
            }

            return $results;
        }
    }

    // Parse search terms like index.php does
    $bulkSearchTerms = trim($_POST['bulkSearchTerms'] ?? '');
    echo "User input: '{$bulkSearchTerms}'" . PHP_EOL;

    if (!empty($bulkSearchTerms)) {
        // Parse terms exactly like index.php
        $input = str_replace(',', "\n", $bulkSearchTerms);
        $terms = explode("\n", $input);
        $searchTermsData = [];

        foreach ($terms as $term) {
            $term = trim($term);
            if (!empty($term) && strlen($term) >= 2) {
                $searchType = detectSearchType($term);
                $searchTermsData[] = [
                    'term' => $term,
                    'type' => $searchType
                ];
            }
        }
        
        echo "Parsed search terms: " . json_encode($searchTermsData, JSON_UNESCAPED_UNICODE) . PHP_EOL;
        
        // Perform the search exactly like index.php
        $searchResults = performBulkSearchWithFilters($searchTermsData, []);
        
        echo "Search completed. Results:" . PHP_EOL;
        echo "Number of search terms processed: " . count($searchResults) . PHP_EOL;
        
        $totalResults = 0;
        $foundLiteralCase = false;
        $allCases = [];
        
        foreach ($searchResults as $termResult) {
            $term = $termResult['term'];
            $type = $termResult['type'];
            $results = $termResult['results'];
            $count = count($results);
            $totalResults += $count;
            
            echo PHP_EOL;
            echo "Term: '{$term}' (Type: {$type})" . PHP_EOL;
            echo "Results: {$count} cases" . PHP_EOL;
            
            foreach ($results as $index => $dosar) {
                $caseNumber = $dosar->numar ?? '';
                $institution = $dosar->institutie ?? '';
                $date = $dosar->data ?? '';
                $object = $dosar->obiect ?? '';
                $category = $dosar->categorieCazNume ?? '';
                $stage = $dosar->stadiuProcesualNume ?? '';
                
                // Format date for display
                $formattedDate = '';
                if (!empty($date)) {
                    try {
                        $dateObj = new DateTime($date);
                        $formattedDate = $dateObj->format('d.m.Y');
                    } catch (Exception $e) {
                        $formattedDate = $date;
                    }
                }
                
                echo "  " . ($index + 1) . ". Case: '{$caseNumber}'" . PHP_EOL;
                echo "     Institution: '{$institution}'" . PHP_EOL;
                echo "     Date: '{$formattedDate}'" . PHP_EOL;
                echo "     Object: " . substr($object, 0, 80) . "..." . PHP_EOL;
                echo "     Category: '{$category}'" . PHP_EOL;
                echo "     Stage: '{$stage}'" . PHP_EOL;
                
                // Check if this is the literal asterisk case
                if ($caseNumber === '14096/3/2024*') {
                    $foundLiteralCase = true;
                    echo "     🎯 THIS IS THE LITERAL ASTERISK CASE!" . PHP_EOL;
                    
                    // Verify the expected details
                    $expectedDate = '03.10.2024';
                    $expectedObject = 'procedura insolvenței – societăți pe acțiuni Rejudecare';
                    $expectedCategory = 'Faliment';
                    $expectedStage = 'Fond';
                    
                    echo "     VERIFICATION:" . PHP_EOL;
                    echo "     - Date match: " . ($formattedDate === $expectedDate ? "✅ YES" : "❌ NO (expected: {$expectedDate}, got: {$formattedDate})") . PHP_EOL;
                    echo "     - Object match: " . (strpos($object, 'Rejudecare') !== false ? "✅ YES" : "❌ NO") . PHP_EOL;
                    echo "     - Category match: " . ($category === $expectedCategory ? "✅ YES" : "❌ NO (expected: {$expectedCategory}, got: {$category})") . PHP_EOL;
                    echo "     - Stage match: " . ($stage === $expectedStage ? "✅ YES" : "❌ NO (expected: {$expectedStage}, got: {$stage})") . PHP_EOL;
                }
                
                $allCases[] = $dosar;
                echo PHP_EOL;
            }
        }
        
        echo "=== SEARCH SUMMARY ===" . PHP_EOL;
        echo "Total results: {$totalResults}" . PHP_EOL;
        echo "Literal asterisk case found: " . ($foundLiteralCase ? "✅ YES" : "❌ NO") . PHP_EOL;
        echo PHP_EOL;
        
    } else {
        echo "❌ No search terms provided" . PHP_EOL;
    }
    
    $webOutput = ob_get_clean();
    
    // Step 2: Test direct SOAP API calls to verify the case exists
    echo "=== STEP 2: Direct SOAP API Verification ===" . PHP_EOL;
    
    $dosarService = new DosarService();
    $reflection = new ReflectionClass($dosarService);
    $executeSoapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $executeSoapMethod->setAccessible(true);
    
    // Test 1: Direct search for literal asterisk case
    echo "Testing direct SOAP call for '14096/3/2024*':" . PHP_EOL;
    
    $literalParams = [
        'numarDosar' => '14096/3/2024*',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    try {
        $literalResponse = $executeSoapMethod->invoke($dosarService, 'CautareDosare2', $literalParams, "Direct literal test");
        
        if ($literalResponse && isset($literalResponse->CautareDosare2Result->Dosar)) {
            $literalCase = $literalResponse->CautareDosare2Result->Dosar;
            
            echo "✅ SOAP API found literal case!" . PHP_EOL;
            echo "Raw SOAP response details:" . PHP_EOL;
            echo "  Case Number: " . ($literalCase->numar ?? 'N/A') . PHP_EOL;
            echo "  Institution: " . ($literalCase->institutie ?? 'N/A') . PHP_EOL;
            echo "  Date: " . ($literalCase->data ?? 'N/A') . PHP_EOL;
            echo "  Object: " . ($literalCase->obiect ?? 'N/A') . PHP_EOL;
            echo "  Category: " . ($literalCase->categorieCazNume ?? 'N/A') . PHP_EOL;
            echo "  Stage: " . ($literalCase->stadiuProcesualNume ?? 'N/A') . PHP_EOL;
            echo "  Last Modified: " . ($literalCase->dataUltimaModificare ?? 'N/A') . PHP_EOL;
            
            // Check all available fields
            echo PHP_EOL . "All available fields in SOAP response:" . PHP_EOL;
            foreach ($literalCase as $field => $value) {
                echo "  {$field}: " . (is_string($value) ? $value : json_encode($value)) . PHP_EOL;
            }
            
        } else {
            echo "❌ SOAP API did not find literal case" . PHP_EOL;
        }
    } catch (Exception $e) {
        echo "❌ SOAP API error: " . $e->getMessage() . PHP_EOL;
    }
    
    echo PHP_EOL;
    
    // Step 3: Test the enhanced search method directly
    echo "=== STEP 3: Testing Enhanced Search Method ===" . PHP_EOL;
    
    $enhancedParams = [
        'numarDosar' => '14096/3/2024*',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    echo "Testing cautareAvansata with '14096/3/2024*':" . PHP_EOL;
    
    $enhancedResults = $dosarService->cautareAvansata($enhancedParams);
    echo "Enhanced search returned: " . count($enhancedResults) . " cases" . PHP_EOL;
    
    $foundInEnhanced = false;
    foreach ($enhancedResults as $index => $dosar) {
        $caseNumber = $dosar->numar ?? '';
        $institution = $dosar->institutie ?? '';
        
        echo "  " . ($index + 1) . ". '{$caseNumber}' at '{$institution}'" . PHP_EOL;
        
        if ($caseNumber === '14096/3/2024*') {
            $foundInEnhanced = true;
            echo "    🎯 FOUND LITERAL CASE IN ENHANCED SEARCH!" . PHP_EOL;
        }
    }
    
    echo "Literal case found in enhanced search: " . ($foundInEnhanced ? "✅ YES" : "❌ NO") . PHP_EOL;
    echo PHP_EOL;
    
    // Step 4: Final diagnosis
    echo "=== STEP 4: FINAL DIAGNOSIS ===" . PHP_EOL;
    
    echo "Search Pipeline Analysis:" . PHP_EOL;
    echo "1. User input processing: " . (!empty($searchTermsData) ? "✅ Working" : "❌ Failed") . PHP_EOL;
    echo "2. Search type detection: " . (isset($searchTermsData[0]['type']) ? "✅ Working (" . $searchTermsData[0]['type'] . ")" : "❌ Failed") . PHP_EOL;
    echo "3. SOAP API literal search: " . (isset($literalCase) ? "✅ Working" : "❌ Failed") . PHP_EOL;
    echo "4. Enhanced search method: " . ($foundInEnhanced ? "✅ Working" : "❌ Failed") . PHP_EOL;
    echo "5. Web interface results: " . ($foundLiteralCase ? "✅ Working" : "❌ Failed") . PHP_EOL;
    echo PHP_EOL;
    
    if (!$foundLiteralCase) {
        echo "🔍 ISSUE IDENTIFIED:" . PHP_EOL;
        
        if (!isset($literalCase)) {
            echo "❌ The literal case '14096/3/2024*' does not exist in the SOAP API" . PHP_EOL;
            echo "   This means either:" . PHP_EOL;
            echo "   - The case number is different than expected" . PHP_EOL;
            echo "   - The case is in a different institution" . PHP_EOL;
            echo "   - The case data has changed" . PHP_EOL;
        } elseif (!$foundInEnhanced) {
            echo "❌ The case exists in SOAP API but enhanced search is not finding it" . PHP_EOL;
            echo "   This indicates a problem with the dual search implementation" . PHP_EOL;
        } else {
            echo "❌ The case is found by enhanced search but not displayed in web results" . PHP_EOL;
            echo "   This indicates a problem with result processing or display" . PHP_EOL;
        }
    } else {
        echo "✅ SUCCESS: The literal asterisk case is found and displayed correctly!" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ Verification Error: " . $e->getMessage() . PHP_EOL;
    echo "Stack trace: " . $e->getTraceAsString() . PHP_EOL;
}
?>
