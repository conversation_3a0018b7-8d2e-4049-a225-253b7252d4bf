{% extends "layouts/main.twig" %}

{% block title %}{{ app.name }} - Detalii dosar {{ dosar.numar }}{% endblock %}

{% block seo_meta %}
    {% set caseTitle = "Dosar " ~ dosar.numar ~ " - " ~ (instante[dosar.institutie] ?? dosar.institutie) %}
    {% set caseDescription = "Detalii dosar: " ~ dosar.numar ~ " | Instanță: " ~ (instante[dosar.institutie] ?? dosar.institutie) ~ " | Obiect: " ~ dosar.obiect ~ " | Data: " ~ dosar.data %}

    <title>{{ caseTitle }} - {{ app.name }}</title>
    <meta name="description" content="{{ caseDescription }}">
    <meta name="keywords" content="dosar {{ dosar.numar }}, {{ instante[dosar.institutie] ?? dosar.institutie }}, {{ dosar.categorieCazNume }}, portal judiciar românia">
    <meta name="robots" content="index, follow">
    <meta name="author" content="Portal Judiciar România">
    <link rel="canonical" href="{{ app.base_url }}/detalii_dosar.php?numar={{ dosar.numar|url_encode }}&institutie={{ dosar.institutie|url_encode }}">

    <!-- Enhanced Open Graph for Case Details -->
    <meta property="og:title" content="{{ caseTitle }}">
    <meta property="og:description" content="{{ caseDescription }}">
    <meta property="og:type" content="article">
    <meta property="og:url" content="{{ app.base_url }}/detalii_dosar.php?numar={{ dosar.numar|url_encode }}&institutie={{ dosar.institutie|url_encode }}">
    <meta property="og:site_name" content="Portal Judiciar România">
    <meta property="og:locale" content="ro_RO">
    <meta property="og:image" content="{{ app.base_url }}/images/logo.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="Portal Judiciar România - Logo">

    <!-- Article specific Open Graph -->
    <meta property="article:published_time" content="{{ dosar.data }}">
    <meta property="article:modified_time" content="{{ dosar.dataModificare }}">
    <meta property="article:section" content="Dosare Judecătorești">
    <meta property="article:tag" content="{{ dosar.categorieCazNume }}">

    <!-- Enhanced Twitter Cards -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ caseTitle }}">
    <meta name="twitter:description" content="{{ caseDescription }}">
    <meta name="twitter:image" content="{{ app.base_url }}/images/logo.jpg">
    <meta name="twitter:image:alt" content="Portal Judiciar România - Logo">
{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
/* Loading overlay pentru detalii dosar */
.page-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(44, 62, 80, 0.9);
    z-index: 9998;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 1;
    transition: opacity 0.5s ease-out;
}

.page-loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

.page-loading-content {
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 350px;
    width: 90%;
    border-top: 4px solid #007bff;
}

.page-loading-spinner {
    width: 50px;
    height: 50px;
    margin: 0 auto 1rem;
    border: 4px solid #e9ecef;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: pageLoadingSpin 1s linear infinite;
}

.page-loading-message {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
    line-height: 1.4;
}

.page-loading-submessage {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    margin-bottom: 0;
}

@keyframes pageLoadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design pentru mobile */
@media (max-width: 767.98px) {
    .page-loading-content {
        padding: 1.5rem;
        max-width: 300px;
    }

    .page-loading-spinner {
        width: 40px;
        height: 40px;
    }

    .page-loading-message {
        font-size: 0.9rem;
    }

    .page-loading-submessage {
        font-size: 0.8rem;
    }
}

/* Asigură că conținutul principal este ascuns inițial */
.main-content {
    opacity: 0;
    transition: opacity 0.5s ease-in;
}

.main-content.loaded {
    opacity: 1;
}

/* Social Sharing Styles - Optimized for Compact Layout */
.social-sharing-section {
    margin: 0.75rem 0;
}

.social-sharing-section .card {
    border: 1px solid #e3f2fd;
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
    transition: all 0.2s ease;
}

.social-sharing-section .card:hover {
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.12);
    transform: translateY(-0.5px);
}

.social-sharing-section h6 {
    color: #2c3e50;
    font-weight: 600;
    font-size: 0.85rem;
}

.social-buttons {
    gap: 0.375rem;
}

.social-btn {
    border: 1px solid #007bff;
    color: #007bff;
    background-color: #fff;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    min-width: 100px;
    justify-content: center;
}

/* Compact Social Buttons */
.social-btn-compact {
    border: 1px solid #007bff;
    color: #007bff;
    background-color: #fff;
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    font-weight: 500;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    min-width: 80px;
    justify-content: center;
}

.social-btn:hover {
    background-color: #007bff;
    color: #fff;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.social-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 123, 255, 0.3);
}

.social-btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.social-btn i {
    font-size: 0.875rem;
}

/* Compact Button Hover States */
.social-btn-compact:hover {
    background-color: #007bff;
    color: #fff;
    border-color: #0056b3;
    transform: translateY(-0.5px);
    box-shadow: 0 1px 6px rgba(0, 123, 255, 0.25);
}

.social-btn-compact:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 123, 255, 0.25);
}

.social-btn-compact:focus {
    outline: none;
    box-shadow: 0 0 0 0.15rem rgba(0, 123, 255, 0.25);
}

.social-btn-compact i {
    font-size: 0.8rem;
}

/* Success state for buttons */
.social-btn.btn-success,
.social-btn-compact.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: #fff;
}

.social-btn.btn-success:hover,
.social-btn-compact.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

/* Specific social platform colors on hover */
.social-btn#facebookShareBtn:hover,
.social-btn-compact#facebookShareBtn:hover {
    background-color: #1877f2;
    border-color: #1877f2;
}

.social-btn#whatsappShareBtn:hover,
.social-btn-compact#whatsappShareBtn:hover {
    background-color: #25d366;
    border-color: #25d366;
}

.social-btn#emailShareBtn:hover,
.social-btn-compact#emailShareBtn:hover {
    background-color: #6c757d;
    border-color: #6c757d;
}

.social-btn#copyLinkBtn:hover,
.social-btn-compact#copyLinkBtn:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
}

/* Mobile responsive adjustments - Optimized for Compact Layout */
@media (max-width: 768px) {
    .social-sharing-section .row {
        text-align: center;
    }

    .social-sharing-section .col-md-3 {
        margin-bottom: 0.5rem;
    }

    .social-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .social-btn,
    .social-btn-compact {
        flex: 1 1 calc(50% - 0.2rem);
        min-width: 100px;
        margin-bottom: 0.25rem;
    }
}

@media (max-width: 480px) {
    .social-btn,
    .social-btn-compact {
        flex: 1 1 100%;
        min-width: auto;
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
    }

    .social-sharing-section h6 {
        font-size: 0.8rem;
    }

    .social-sharing-section {
        margin: 0.5rem 0;
    }
}
</style>
{% endblock %}

{% block content %}

<!-- Loading overlay -->
<div id="pageLoadingOverlay" class="page-loading-overlay" role="status" aria-live="polite" aria-label="Se încarcă detaliile dosarului">
    <div class="page-loading-content">
        <div class="page-loading-spinner" aria-hidden="true"></div>
        <p class="page-loading-message">Se încarcă detaliile dosarului...</p>
        <p class="page-loading-submessage">Vă rugăm să așteptați</p>
    </div>
</div>

<div class="main-content" id="mainContent">

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h2 class="card-title h5 mb-0">
                        <i class="fas fa-folder-open me-2"></i>Informații dosar {{ dosar.numar }}
                    </h2>
                    <div class="d-flex">
                        <button id="downloadPdfBtn" class="btn btn-sm btn-success me-2" type="button"
                                data-numar="{{ dosar.numar }}" data-institutie="{{ dosar.institutie }}">
                            <i class="fas fa-file-pdf me-1"></i>Descarcă PDF
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-share-alt me-1"></i>Distribuie
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#emailModal">
                                        <i class="fas fa-envelope me-1"></i>Email
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url('export.php', {'format': 'pdf', 'numar': dosar.numar, 'institutie': dosar.institutie}) }}" target="_blank">
                                        <i class="fas fa-file-pdf me-1"></i>PDF
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url('export.php', {'format': 'html', 'numar': dosar.numar, 'institutie': dosar.institutie}) }}">
                                        <i class="fas fa-file-code me-1"></i>HTML
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ url('export.php', {'format': 'pdf', 'numar': dosar.numar, 'institutie': dosar.institutie}) }}" target="_blank">
                                        <i class="fas fa-file-pdf me-1"></i>Descarcă PDF (din meniu)
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Număr dosar:</strong> {{ dosar.numar }}</p>
                            <p><strong>Număr vechi:</strong> {{ dosar.numarVechi ?: '-' }}</p>
                            <p><strong>Data înregistrării:</strong> {{ dosar.data }}</p>
                            <p><strong>Instanță:</strong> {{ instante[dosar.institutie] ?? dosar.institutie }}</p>
                            <p><strong>Departament:</strong> {{ dosar.departament ?: '-' }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Categorie caz:</strong> {{ dosar.categorieCazNume ?: '-' }}</p>
                            <p><strong>Stadiu procesual:</strong> {{ dosar.stadiuProcesualNume ?: '-' }}</p>
                            <p><strong>Obiect:</strong> {{ dosar.obiect ?: '-' }}</p>
                            <p><strong>Data ultimei modificări:</strong> {{ dosar.dataModificare }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Social Sharing Section - Compact -->
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="social-sharing-section">
                <div class="card border-0 bg-light">
                    <div class="card-body py-2 px-3">
                        <div class="row align-items-center">
                            <div class="col-md-3 col-sm-12 mb-1 mb-md-0">
                                <h6 class="mb-0 text-muted small">
                                    <i class="fas fa-share-alt me-1"></i>Distribuie
                                </h6>
                            </div>
                            <div class="col-md-9 col-sm-12">
                                <div class="social-buttons d-flex flex-wrap gap-1">
                                    <button type="button" class="btn btn-outline-primary btn-sm social-btn-compact" id="copyLinkBtn" title="Copiază link-ul în clipboard">
                                        <i class="fas fa-copy me-1"></i>Link
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm social-btn-compact" id="facebookShareBtn" title="Distribuie pe Facebook">
                                        <i class="fab fa-facebook-f me-1"></i>Facebook
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm social-btn-compact" id="whatsappShareBtn" title="Distribuie pe WhatsApp">
                                        <i class="fab fa-whatsapp me-1"></i>WhatsApp
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm social-btn-compact" id="emailShareBtn" title="Trimite prin email">
                                        <i class="fas fa-envelope me-1"></i>Email
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title h5 mb-0">
                        <i class="fas fa-users me-2"></i>Părți implicate
                    </h3>
                </div>
                <div class="card-body">
                    {% if dosar.parti|length > 0 %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nume</th>
                                        <th>Calitate</th>
                                        <th>Acțiuni</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for parte in dosar.parti %}
                                        <tr>
                                            <td>{{ parte.nume }}</td>
                                            <td>{{ parte.calitate }}</td>
                                            <td>
                                                <a href="#" class="btn btn-sm btn-outline-primary quick-search" data-search="{{ parte.nume }}">
                                                    <i class="fas fa-search me-1"></i>Caută
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>Nu există informații despre părțile implicate.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title h5 mb-0">
                        <i class="fas fa-gavel me-2"></i>Ședințe de judecată
                    </h3>
                </div>
                <div class="card-body">
                    {% if dosar.sedinte|length > 0 %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Data</th>
                                        <th>Ora</th>
                                        <th>Complet</th>
                                        <th>Soluție</th>
                                        <th>Document</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sedinta in dosar.sedinte %}
                                        <tr>
                                            <td>{{ sedinta.data }}</td>
                                            <td>{{ sedinta.ora ?: '-' }}</td>
                                            <td>{{ sedinta.complet ?: '-' }}</td>
                                            <td>
                                                {% if sedinta.solutie %}
                                                    <button type="button" class="btn btn-sm btn-link p-0" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ sedinta.solutie }}">
                                                        {{ truncate(sedinta.solutie, 50) }}
                                                    </button>
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if sedinta.documentSedinta %}
                                                    <a href="{{ sedinta.documentSedinta }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-file-alt me-1"></i>Document
                                                    </a>
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>Nu există informații despre ședințele de judecată.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title h5 mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>Căi de atac
                    </h3>
                </div>
                <div class="card-body">
                    {% if dosar.caiAtac|length > 0 %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Data declarare</th>
                                        <th>Tip cale atac</th>
                                        <th>Parte declaratoare</th>
                                        <th>Dosar instanță superioară</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for caleAtac in dosar.caiAtac %}
                                        <tr>
                                            <td>{{ caleAtac.dataDeclarare }}</td>
                                            <td>{{ caleAtac.tipCaleAtac ?: '-' }}</td>
                                            <td>{{ caleAtac.parteDeclaratoare ?: '-' }}</td>
                                            <td>
                                                {% if caleAtac.numarDosarInstantaSuperior %}
                                                    <a href="{{ url('detalii_dosar.php', {'numar': caleAtac.numarDosarInstantaSuperior, 'institutie': caleAtac.instantaSuperior}) }}">
                                                        {{ caleAtac.numarDosarInstantaSuperior }}
                                                    </a>
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>Nu există informații despre căile de atac.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

</div> <!-- End main-content -->

    <!-- Modal pentru trimitere email -->
    <div class="modal fade" id="emailModal" tabindex="-1" aria-labelledby="emailModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form action="send_email.php" method="post" id="emailForm">
                    <div class="modal-header">
                        <h5 class="modal-title" id="emailModalLabel">Trimite detalii dosar prin email</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="numarDosar" value="{{ dosar.numar }}">
                        <input type="hidden" name="institutie" value="{{ dosar.institutie }}">

                        <div class="mb-3">
                            <label for="emailTo" class="form-label">Adresă email destinatar:</label>
                            <input type="email" class="form-control" id="emailTo" name="emailTo" required>
                        </div>

                        <div class="mb-3">
                            <label for="emailFrom" class="form-label">Adresa ta de email:</label>
                            <input type="email" class="form-control" id="emailFrom" name="emailFrom" required>
                        </div>

                        <div class="mb-3">
                            <label for="emailSubject" class="form-label">Subiect:</label>
                            <input type="text" class="form-control" id="emailSubject" name="emailSubject" value="Detalii dosar {{ dosar.numar }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="emailMessage" class="form-label">Mesaj:</label>
                            <textarea class="form-control" id="emailMessage" name="emailMessage" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-1"></i>Trimite
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
<script>
    /**
     * Gestionează loading overlay-ul pentru pagina de detalii dosar
     */
    function initPageLoadingOverlay() {
        const loadingOverlay = document.getElementById('pageLoadingOverlay');
        const mainContent = document.getElementById('mainContent');

        if (!loadingOverlay || !mainContent) {
            console.error('Elementele pentru loading overlay nu au fost găsite!');
            return;
        }

        // Funcție pentru ascunderea loading overlay-ului
        function hideLoadingOverlay() {
            // Adăugăm clasa loaded la conținutul principal
            mainContent.classList.add('loaded');

            // Ascundem overlay-ul cu animație
            loadingOverlay.classList.add('fade-out');

            // Eliminăm complet overlay-ul după animație
            setTimeout(() => {
                if (loadingOverlay.parentNode) {
                    loadingOverlay.parentNode.removeChild(loadingOverlay);
                }
            }, 500);

            console.log('Loading overlay ascuns cu succes.');
        }

        // Verificăm dacă pagina are conținut
        const hasContent = document.querySelector('.card');

        // Calculăm timpul minim de afișare (1-2 secunde conform cerințelor)
        const minDisplayTime = 1000; // 1 secundă
        const startTime = Date.now();

        // Funcție pentru ascunderea cu respectarea timpului minim
        function hideWithMinTime() {
            const elapsedTime = Date.now() - startTime;
            const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

            setTimeout(hideLoadingOverlay, remainingTime);
        }

        // Ascundem loading-ul după ce conținutul este gata
        if (hasContent) {
            // Dacă avem conținut, ascundem loading-ul
            hideWithMinTime();
        } else {
            // Fallback: ascundem după 2 secunde maxim
            setTimeout(hideLoadingOverlay, 2000);
        }

        console.log('Loading overlay inițializat pentru detalii dosar.');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Inițializăm loading overlay-ul
        initPageLoadingOverlay();

        // Inițializăm tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Implementăm funcționalitatea de căutare rapidă
        const quickSearchLinks = document.querySelectorAll('.quick-search');
        quickSearchLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const searchTerm = this.getAttribute('data-search');
                window.location.href = 'index.php#cautare-avansata?numeParte=' + encodeURIComponent(searchTerm);
            });
        });

        // Validare formular email
        const emailForm = document.getElementById('emailForm');
        if (emailForm) {
            emailForm.addEventListener('submit', function(e) {
                const emailTo = document.getElementById('emailTo').value;
                const emailFrom = document.getElementById('emailFrom').value;

                if (!emailTo || !emailFrom) {
                    e.preventDefault();
                    showNotification('Te rugăm să completezi toate câmpurile obligatorii.', 'warning');
                }
            });
        }

        // Funcționalitatea butonului de descărcare PDF
        const downloadPdfBtn = document.getElementById('downloadPdfBtn');
        if (downloadPdfBtn) {
            downloadPdfBtn.addEventListener('click', function() {
                const numarDosar = this.getAttribute('data-numar');
                const institutie = this.getAttribute('data-institutie');

                if (numarDosar && institutie) {
                    // Construiește URL-ul pentru ruta de export PDF
                    const exportUrl = `export.php?format=pdf&numar=${encodeURIComponent(numarDosar)}&institutie=${encodeURIComponent(institutie)}`;

                    // Deschide URL-ul într-o fereastră nouă pentru a declanșa descărcarea
                    window.open(exportUrl, '_blank');

                    // Notificarea poate fi afișată după inițierea descărcării
                    showNotification('Descărcarea PDF-ului a început.', 'success');
                } else {
                    showNotification('Nu s-au putut obține informațiile necesare pentru descărcarea PDF.', 'warning');
                }
            });
        }

        // Social Sharing Functionality
        initSocialSharing();
    });

    // Social Sharing Functions
    function initSocialSharing() {
        // Case information for sharing
        const caseInfo = {
            number: '{{ dosar.numar|e('js') }}',
            institution: '{{ instante[dosar.institutie] ?? dosar.institutie|e('js') }}',
            object: '{{ dosar.obiect|e('js') }}',
            date: '{{ dosar.data|e('js') }}',
            url: window.location.href
        };

        // Generate sharing content
        const shareTitle = `Dosar ${caseInfo.number} - ${caseInfo.institution}`;
        const shareText = `Detalii dosar: ${caseInfo.number}\nInstanță: ${caseInfo.institution}\nObiect: ${caseInfo.object}\nData: ${caseInfo.date}`;
        const shareUrl = caseInfo.url;

        // Copy Link functionality
        const copyLinkBtn = document.getElementById('copyLinkBtn');
        if (copyLinkBtn) {
            copyLinkBtn.addEventListener('click', function() {
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(shareUrl).then(function() {
                        showNotification('Link copiat în clipboard!', 'success');
                        updateButtonState(copyLinkBtn, 'success');
                    }).catch(function() {
                        fallbackCopyToClipboard(shareUrl);
                    });
                } else {
                    fallbackCopyToClipboard(shareUrl);
                }
            });
        }

        // Facebook Share
        const facebookBtn = document.getElementById('facebookShareBtn');
        if (facebookBtn) {
            facebookBtn.addEventListener('click', function() {
                const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
                openShareWindow(facebookUrl, 'Facebook');
                updateButtonState(facebookBtn, 'shared');
            });
        }

        // WhatsApp Share
        const whatsappBtn = document.getElementById('whatsappShareBtn');
        if (whatsappBtn) {
            whatsappBtn.addEventListener('click', function() {
                const whatsappText = `${shareText}\n\n${shareUrl}`;
                const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(whatsappText)}`;

                // Check if mobile device for better WhatsApp experience
                if (isMobileDevice()) {
                    window.location.href = whatsappUrl;
                } else {
                    openShareWindow(whatsappUrl, 'WhatsApp');
                }
                updateButtonState(whatsappBtn, 'shared');
            });
        }

        // Email Share
        const emailBtn = document.getElementById('emailShareBtn');
        if (emailBtn) {
            emailBtn.addEventListener('click', function() {
                const emailSubject = encodeURIComponent(shareTitle);
                const emailBody = encodeURIComponent(`${shareText}\n\nAccesează dosarul: ${shareUrl}`);
                const emailUrl = `mailto:?subject=${emailSubject}&body=${emailBody}`;

                window.location.href = emailUrl;
                updateButtonState(emailBtn, 'shared');
            });
        }
    }

    // Fallback copy to clipboard for older browsers
    function fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            showNotification('Link copiat în clipboard!', 'success');
            updateButtonState(document.getElementById('copyLinkBtn'), 'success');
        } catch (err) {
            showNotification('Nu s-a putut copia link-ul. Vă rugăm să copiați manual URL-ul din bara de adrese.', 'warning');
        }

        document.body.removeChild(textArea);
    }

    // Open share window
    function openShareWindow(url, platform) {
        const width = 600;
        const height = 400;
        const left = (window.innerWidth - width) / 2;
        const top = (window.innerHeight - height) / 2;

        window.open(
            url,
            `share-${platform}`,
            `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
        );
    }

    // Check if mobile device
    function isMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    // Update button state after sharing
    function updateButtonState(button, state) {
        if (!button) return;

        const originalContent = button.innerHTML;
        const originalClass = button.className;

        if (state === 'success') {
            button.innerHTML = '<i class="fas fa-check me-1"></i>Copiat!';
            button.className = button.className.replace('btn-outline-primary', 'btn-success');
        } else if (state === 'shared') {
            button.innerHTML = '<i class="fas fa-check me-1"></i>Distribuit!';
            button.className = button.className.replace('btn-outline-primary', 'btn-success');
        }

        setTimeout(function() {
            button.innerHTML = originalContent;
            button.className = originalClass;
        }, 2000);
    }

    // Inițializează loading overlay-ul imediat (înainte de DOMContentLoaded)
    // pentru a fi siguri că este vizibil de la început
    (function() {
        // Verificăm dacă loading overlay-ul există
        function checkAndInitLoading() {
            const loadingOverlay = document.getElementById('pageLoadingOverlay');
            if (loadingOverlay) {
                // Overlay-ul este deja vizibil prin CSS
                console.log('Loading overlay detectat și activ.');
            } else {
                // Reîncercăm după un scurt interval
                setTimeout(checkAndInitLoading, 50);
            }
        }

        // Începem verificarea
        if (document.readyState === 'loading') {
            checkAndInitLoading();
        }
    })();
</script>
{% endblock %}
