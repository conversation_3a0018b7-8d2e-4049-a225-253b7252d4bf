# 🚀 Portal Judiciar România - Case Monitoring Quick Start Guide

## 📋 Overview

The case monitoring system is **100% implemented and ready for use**. This guide will help you get it running in production.

## ✅ What's Already Working

- ✅ **User Registration & Authentication**
- ✅ **Case Monitoring Dashboard** (`/monitor.php`)
- ✅ **Email Notifications** (immediate, daily, weekly)
- ✅ **Background Processing** (cron jobs)
- ✅ **GDPR Compliance** & Security
- ✅ **Professional Email Templates**
- ✅ **Rate Limiting** & Abuse Prevention

## 🔧 Setup Instructions

### Step 1: Verify System Requirements

```bash
# Check PHP version (7.4+ required)
php --version

# Check required PHP extensions
php -m | grep -E "(pdo|soap|json|mbstring|openssl)"

# Check if Composer is installed
composer --version
```

### Step 2: Install Dependencies

```bash
# Install PHP dependencies
composer install

# Ensure directories are writable
chmod 755 logs/ cache/ temp/
```

### Step 3: Configure Database

1. **Update database settings** in `includes/config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'portal_judiciar');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

2. **Run database setup**:
```bash
php setup_monitoring_database.php
```

### Step 4: Configure Email Settings

Update email settings in `src/Config/constants.php`:
```php
define('SMTP_HOST', 'your-smtp-server.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('CONTACT_EMAIL', '<EMAIL>');
```

### Step 5: Test the System

```bash
# Run comprehensive system test
php test_monitoring_system.php

# Should show: "🚀 SYSTEM READY FOR PRODUCTION!"
```

### Step 6: Set Up Cron Jobs

```bash
# Get cron job commands
php cron/setup_cron.php

# Add to crontab (run: crontab -e)
*/30 * * * * /usr/bin/php /path/to/just/cron/monitor_cases.php >> /path/to/just/logs/cron.log 2>&1
0 8 * * * /usr/bin/php /path/to/just/cron/send_scheduled_notifications.php >> /path/to/just/logs/notifications.log 2>&1
0 */6 * * * /usr/bin/php /path/to/just/cron/monitor_system.php >> /path/to/just/logs/system_monitor.log 2>&1
```

## 🎯 How to Use

### For Users:

1. **Access monitoring dashboard**: `http://your-domain.com/just/monitor.php`
2. **Register/Login** with email verification
3. **Add cases to monitor**: Enter case number and institution
4. **Configure notifications**: Choose immediate, daily, or weekly
5. **Receive email alerts** when cases change

### For Administrators:

1. **Monitor system health**: Check `/logs/` directory
2. **View cron job status**: `tail -f logs/cron.log`
3. **Check email queue**: Monitor `notification_queue` table
4. **System statistics**: Available in admin dashboard

## 📊 System Features

### Case Monitoring
- **Up to 50 cases per user** (configurable)
- **Real-time change detection** via SOAP API
- **Detailed change tracking** (status, dates, parties, etc.)
- **Smart notifications** (avoid spam, batch processing)

### Email Notifications
- **Professional HTML templates** with Romanian language
- **Immediate alerts** for urgent changes
- **Daily digest** summaries
- **Weekly reports** for regular updates
- **Delivery tracking** and retry logic

### Security & Compliance
- **GDPR compliant** data handling
- **Rate limiting** to prevent abuse
- **CSRF protection** on all forms
- **Secure password hashing**
- **Session management**
- **Audit logging**

## 🔍 Troubleshooting

### Common Issues:

1. **"Database connection failed"**
   - Check database credentials in `includes/config.php`
   - Ensure MySQL server is running
   - Verify database exists and user has permissions

2. **"SOAP API connection failed"**
   - Check internet connection
   - Verify SOAP_WSDL URL is accessible
   - Check if judicial portal is online

3. **"Email sending failed"**
   - Verify SMTP settings in `constants.php`
   - Check if email provider allows app passwords
   - Test with a simple email client first

4. **"Cron jobs not running"**
   - Check crontab: `crontab -l`
   - Verify PHP path: `which php`
   - Check file permissions: `ls -la cron/`
   - Monitor logs: `tail -f logs/cron.log`

### Debug Commands:

```bash
# Test database connection
php -r "require 'bootstrap.php'; use App\Config\Database; echo 'DB OK: ' . Database::getConnection()->getAttribute(PDO::ATTR_SERVER_VERSION);"

# Test SOAP API
php -r "require 'bootstrap.php'; use App\Services\DosarService; $ds = new DosarService(); var_dump($ds->getDetaliiDosar('1/1/2024', 'JudecatoriaSECTORUL1BUCURESTI'));"

# Test email sending
php cron/send_scheduled_notifications.php

# Manual case monitoring
php cron/monitor_cases.php
```

## 📈 Monitoring & Maintenance

### Daily Checks:
- Monitor log files for errors
- Check email delivery rates
- Verify cron job execution

### Weekly Maintenance:
- Review system performance
- Clean up old log files
- Check database growth
- Update case monitoring statistics

### Monthly Tasks:
- Review GDPR compliance
- Update security settings
- Backup monitoring data
- Performance optimization

## 🆘 Support

For technical support:
1. Check logs in `/logs/` directory
2. Run `php test_monitoring_system.php`
3. Review this documentation
4. Contact development team with specific error messages

---

**🎉 The system is ready for production use!**

All core functionality is implemented and tested. Users can start monitoring cases immediately after setup.
