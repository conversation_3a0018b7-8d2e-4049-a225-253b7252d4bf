<?php
// Test how htmlspecialchars handles asterisks
echo "<h1>Testing htmlspecialchars with asterisks</h1>";

$testCases = [
    "14096/3/2024",
    "14096/3/2024*",
    "14096/3/2024**",
    "test*case",
    "*asterisk*"
];

echo "<h2>htmlspecialchars() behavior:</h2>";

foreach ($testCases as $test) {
    $escaped = htmlspecialchars($test);
    echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
    echo "<strong>Original:</strong> '$test'<br>";
    echo "<strong>htmlspecialchars():</strong> '$escaped'<br>";
    echo "<strong>Are they equal?:</strong> " . ($test === $escaped ? "YES" : "NO") . "<br>";
    echo "</div>";
}

echo "<h2>HTML Attribute Test:</h2>";

$caseNumber = "14096/3/2024*";
echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
echo "<strong>Case Number:</strong> $caseNumber<br>";
echo "<strong>In HTML attribute:</strong><br>";
echo "<code>data-numar=\"" . htmlspecialchars($caseNumber) . "\"</code><br>";
echo "</div>";

echo "<h2>JavaScript Access Test:</h2>";
echo "<div id='testElement' data-numar='" . htmlspecialchars($caseNumber) . "'>Test Element</div>";

echo "<script>";
echo "const element = document.getElementById('testElement');";
echo "const dataNumar = element.getAttribute('data-numar');";
echo "console.log('data-numar value:', dataNumar);";
echo "console.log('Contains asterisk:', dataNumar.includes('*'));";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "  const resultDiv = document.createElement('div');";
echo "  resultDiv.style.cssText = 'background: #d4edda; padding: 10px; margin: 10px 0; border: 1px solid #c3e6cb;';";
echo "  resultDiv.innerHTML = '<strong>JavaScript Result:</strong><br>' +";
echo "    'data-numar value: ' + dataNumar + '<br>' +";
echo "    'Contains asterisk: ' + dataNumar.includes('*') + '<br>' +";
echo "    'Length: ' + dataNumar.length;";
echo "  document.body.appendChild(resultDiv);";
echo "});";
echo "</script>";

echo "<h2>Conclusion:</h2>";
echo "<p>If htmlspecialchars() doesn't change asterisks, then the issue is elsewhere.</p>";
echo "<p>If it does change them, that could explain why JavaScript filtering fails.</p>";
?>
