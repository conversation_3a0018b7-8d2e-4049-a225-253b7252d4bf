<?php
/**
 * Test Service Fix Verification
 * Verifies that detalii_dosar.php now uses the enhanced service and displays all parties
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Service Fix Verification</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style></head><body>";

echo "<h1>🔧 Test Service Fix Verification</h1>";
echo "<p>Verifies that detalii_dosar.php now uses the enhanced service and displays all parties</p>";
echo "<hr>";

try {
    echo "<div class='section'>";
    echo "<h2>📊 Step 1: Enhanced Service Test</h2>";
    
    $dosarService = new \App\Services\DosarService();
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');
    
    if (!$dosar || !isset($dosar->parti)) {
        echo "<p class='error'>❌ Enhanced service failed to retrieve case data</p>";
        exit;
    }
    
    $totalParties = count($dosar->parti);
    
    echo "<table>";
    echo "<tr><th>Metric</th><th>Value</th><th>Status</th></tr>";
    echo "<tr><td>Enhanced service working</td><td>Yes</td><td class='success'>✅ OK</td></tr>";
    echo "<tr><td>Total parties retrieved</td><td>{$totalParties}</td><td class='" . ($totalParties > 400 ? 'success' : 'error') . "'>" . ($totalParties > 400 ? '✅ OK' : '❌ Too few') . "</td></tr>";
    
    // Check party sources
    $soapCount = 0;
    $decisionCount = 0;
    $unknownCount = 0;
    
    foreach ($dosar->parti as $party) {
        $source = $party->source ?? 'unknown';
        switch ($source) {
            case 'soap_api': $soapCount++; break;
            case 'decision_text': $decisionCount++; break;
            default: $unknownCount++; break;
        }
    }
    
    echo "<tr><td>SOAP API parties</td><td>{$soapCount}</td><td class='info'>ℹ️ Info</td></tr>";
    echo "<tr><td>Decision text parties</td><td>{$decisionCount}</td><td class='info'>ℹ️ Info</td></tr>";
    echo "<tr><td>Unknown source parties</td><td>{$unknownCount}</td><td class='info'>ℹ️ Info</td></tr>";
    echo "<tr><td>Hybrid extraction working</td><td>" . ($decisionCount > 0 ? 'Yes' : 'No') . "</td><td class='" . ($decisionCount > 0 ? 'success' : 'warning') . "'>" . ($decisionCount > 0 ? '✅ OK' : '⚠️ Limited') . "</td></tr>";
    
    echo "</table>";
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🔍 Step 2: SARAGEA TUDORIŢA Verification</h2>";
    
    $saragea_found = false;
    $saragea_position = -1;
    $saragea_source = '';
    
    foreach ($dosar->parti as $index => $party) {
        if (isset($party->nume) && 
            stripos($party->nume, 'SARAGEA') !== false && 
            stripos($party->nume, 'TUDORI') !== false) {
            $saragea_found = true;
            $saragea_position = $index + 1;
            $saragea_source = $party->source ?? 'unknown';
            break;
        }
    }
    
    echo "<table>";
    echo "<tr><th>Test</th><th>Result</th><th>Status</th></tr>";
    echo "<tr><td>SARAGEA TUDORIŢA found</td><td>" . ($saragea_found ? "Yes" : "No") . "</td><td class='" . ($saragea_found ? 'success' : 'error') . "'>" . ($saragea_found ? '✅ OK' : '❌ Missing') . "</td></tr>";
    
    if ($saragea_found) {
        echo "<tr><td>Position in list</td><td>{$saragea_position}</td><td class='info'>ℹ️ Info</td></tr>";
        echo "<tr><td>Data source</td><td>{$saragea_source}</td><td class='info'>ℹ️ Info</td></tr>";
        echo "<tr><td>Position > 100</td><td>" . ($saragea_position > 100 ? 'Yes' : 'No') . "</td><td class='" . ($saragea_position > 100 ? 'success' : 'info') . "'>" . ($saragea_position > 100 ? '✅ Beyond SOAP limit' : 'ℹ️ Within SOAP limit') . "</td></tr>";
    }
    
    echo "</table>";
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🌐 Step 3: Production Page Test Simulation</h2>";
    
    // Simulate what detalii_dosar.php should now do
    echo "<p>Simulating the exact logic that detalii_dosar.php should now use:</p>";
    
    $simulated_count = 0;
    $simulated_errors = [];
    
    foreach ($dosar->parti as $index => $party) {
        if (!is_object($party)) {
            $simulated_errors[] = "Party at index {$index} is not an object";
            continue;
        }
        
        if (!isset($party->nume) || empty(trim($party->nume))) {
            $simulated_errors[] = "Party at index {$index} has no valid name";
            continue;
        }
        
        $simulated_count++;
    }
    
    echo "<table>";
    echo "<tr><th>Metric</th><th>Value</th><th>Status</th></tr>";
    echo "<tr><td>Parties that would be rendered</td><td>{$simulated_count}</td><td class='" . ($simulated_count > 400 ? 'success' : 'error') . "'>" . ($simulated_count > 400 ? '✅ OK' : '❌ Too few') . "</td></tr>";
    echo "<tr><td>Rendering errors</td><td>" . count($simulated_errors) . "</td><td class='" . (count($simulated_errors) === 0 ? 'success' : 'warning') . "'>" . (count($simulated_errors) === 0 ? '✅ OK' : '⚠️ Issues') . "</td></tr>";
    echo "<tr><td>Expected vs Actual</td><td>{$simulated_count} / {$totalParties}</td><td class='" . ($simulated_count === $totalParties ? 'success' : 'warning') . "'>" . ($simulated_count === $totalParties ? '✅ Perfect' : '⚠️ Some filtered') . "</td></tr>";
    echo "</table>";
    
    if (!empty($simulated_errors)) {
        echo "<h4>Rendering Issues:</h4>";
        echo "<ul>";
        foreach (array_slice($simulated_errors, 0, 5) as $error) {
            echo "<li>{$error}</li>";
        }
        if (count($simulated_errors) > 5) {
            echo "<li>... and " . (count($simulated_errors) - 5) . " more issues</li>";
        }
        echo "</ul>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🎯 Final Assessment</h2>";
    
    $service_ok = $totalParties > 400;
    $saragea_ok = $saragea_found;
    $rendering_ok = $simulated_count > 400;
    $hybrid_ok = $decisionCount > 0;
    
    $total_score = 0;
    if ($service_ok) $total_score++;
    if ($saragea_ok) $total_score++;
    if ($rendering_ok) $total_score++;
    if ($hybrid_ok) $total_score++;
    
    $percentage = round(($total_score / 4) * 100);
    
    echo "<table>";
    echo "<tr><th>Component</th><th>Status</th></tr>";
    echo "<tr><td>Enhanced service (>400 parties)</td><td class='" . ($service_ok ? 'success' : 'error') . "'>" . ($service_ok ? '✅ OK' : '❌ Issues') . "</td></tr>";
    echo "<tr><td>SARAGEA TUDORIŢA found</td><td class='" . ($saragea_ok ? 'success' : 'error') . "'>" . ($saragea_ok ? '✅ OK' : '❌ Missing') . "</td></tr>";
    echo "<tr><td>Rendering simulation (>400)</td><td class='" . ($rendering_ok ? 'success' : 'error') . "'>" . ($rendering_ok ? '✅ OK' : '❌ Issues') . "</td></tr>";
    echo "<tr><td>Hybrid extraction working</td><td class='" . ($hybrid_ok ? 'success' : 'warning') . "'>" . ($hybrid_ok ? '✅ OK' : '⚠️ Limited') . "</td></tr>";
    echo "</table>";
    
    echo "<h3>Overall Score: {$total_score}/4 ({$percentage}%)</h3>";
    
    if ($percentage >= 75) {
        echo "<p class='success'>🎉 SUCCESS! The service fix should resolve the 100-party limitation!</p>";
        echo "<p class='success'>✅ Enhanced service is working correctly</p>";
        echo "<p class='success'>✅ All parties should now be displayed in detalii_dosar.php</p>";
    } elseif ($percentage >= 50) {
        echo "<p class='warning'>⚠️ PARTIAL: Most components working but some issues remain</p>";
    } else {
        echo "<p class='error'>❌ ISSUES: Additional fixes needed</p>";
    }
    
    echo "<h3>🔗 Test Links</h3>";
    echo "<ul>";
    echo "<li><a href='detalii_dosar.php?numar=130/98/2022&institutie=TribunalulIALOMITA' target='_blank'>Production page (should now show all parties)</a></li>";
    echo "<li><a href='detalii_dosar.php?numar=130/98/2022&institutie=TribunalulIALOMITA&debug=1' target='_blank'>Production page with debug</a></li>";
    echo "<li><a href='test_party_limit_comparison.php?limit=0' target='_blank'>Test page comparison (all parties)</a></li>";
    echo "</ul>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2 class='error'>❌ Error Occurred</h2>";
    echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p class='info'>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Service fix verification completed at " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
