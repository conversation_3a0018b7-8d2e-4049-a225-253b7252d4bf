<?php
/**
 * Enhanced wildcard search with better user feedback
 * This enhancement provides clearer messaging when wildcard searches return no results
 */

// Add this function to provide better user feedback for wildcard searches
function enhanceWildcardSearchFeedback($searchTerm, $results, $dosarService) {
    // Check if this is a wildcard search
    if (strpos($searchTerm, '*') !== false) {
        $basePattern = str_replace('*', '', $searchTerm);
        
        if (empty($results)) {
            // Check if the base pattern exists
            try {
                $baseResults = $dosarService->cautareAvansata(['numarDosar' => $basePattern]);
                
                if (empty($baseResults)) {
                    return [
                        'type' => 'info',
                        'message' => "Nu au fost găsite dosare pentru pattern-ul '$searchTerm'. " .
                                   "Cazul de bază '$basePattern' nu există în baza de date. " .
                                   "Încercați un pattern mai general (ex: '" . substr($basePattern, 0, -4) . "*')."
                    ];
                } else {
                    return [
                        'type' => 'warning', 
                        'message' => "Cazul de bază '$basePattern' există, dar nu au fost găsite " .
                                   "dosare suplimentare cu sufixe pentru pattern-ul '$searchTerm'."
                    ];
                }
            } catch (Exception $e) {
                return [
                    'type' => 'error',
                    'message' => "Eroare la verificarea pattern-ului: " . $e->getMessage()
                ];
            }
        } else {
            // Wildcard search found results - provide helpful info
            $exactMatches = 0;
            $wildcardMatches = 0;
            
            foreach ($results as $dosar) {
                if ($dosar->numar === $basePattern) {
                    $exactMatches++;
                } else {
                    $wildcardMatches++;
                }
            }
            
            $message = "Găsite " . count($results) . " dosare pentru pattern-ul '$searchTerm'.";
            if ($exactMatches > 0) {
                $message .= " Includ $exactMatches potriviri exacte";
            }
            if ($wildcardMatches > 0) {
                $message .= ($exactMatches > 0 ? " și " : " Includ ") . "$wildcardMatches dosare cu sufixe";
            }
            $message .= ".";
            
            return [
                'type' => 'success',
                'message' => $message
            ];
        }
    }
    
    return null; // Not a wildcard search
}

// Example usage in search results display:
function displaySearchResultsWithWildcardFeedback($searchTerm, $results, $dosarService) {
    $feedback = enhanceWildcardSearchFeedback($searchTerm, $results, $dosarService);
    
    if ($feedback) {
        echo "<div class='alert alert-{$feedback['type']}' role='alert'>";
        echo "<i class='fas fa-info-circle'></i> ";
        echo htmlspecialchars($feedback['message']);
        echo "</div>";
    }
    
    // Display regular results...
    if (!empty($results)) {
        foreach ($results as $dosar) {
            // Display dosar information
            echo "<div class='result-item'>";
            echo "<strong>Dosar:</strong> " . htmlspecialchars($dosar->numar ?? '');
            echo "<br><strong>Obiect:</strong> " . htmlspecialchars($dosar->obiect ?? '');
            echo "</div>";
        }
    } else if (!$feedback) {
        echo "<div class='alert alert-warning'>Nu au fost găsite rezultate pentru '$searchTerm'.</div>";
    }
}

// Enhanced search suggestions for wildcard patterns
function suggestWildcardPatterns($searchTerm) {
    if (strpos($searchTerm, '*') !== false) {
        $basePattern = str_replace('*', '', $searchTerm);
        $suggestions = [];
        
        // Suggest broader patterns
        $parts = explode('/', $basePattern);
        if (count($parts) >= 2) {
            $suggestions[] = $parts[0] . "/*";  // e.g., "14096/*"
            if (count($parts) >= 3) {
                $suggestions[] = $parts[0] . "/" . $parts[1] . "/*";  // e.g., "14096/3/*"
            }
        }
        
        // Suggest shorter patterns
        if (strlen($basePattern) > 4) {
            $suggestions[] = substr($basePattern, 0, -1) . "*";
            $suggestions[] = substr($basePattern, 0, -2) . "*";
        }
        
        return array_unique($suggestions);
    }
    
    return [];
}

/**
 * JavaScript enhancement for real-time wildcard feedback
 */
?>
<script>
function validateWildcardPattern(input) {
    const value = input.value.trim();
    const feedbackDiv = document.getElementById('wildcard-feedback');
    
    if (value.includes('*')) {
        const basePattern = value.replace('*', '');
        
        if (basePattern.length < 3) {
            showWildcardFeedback('warning', 
                'Pattern-ul este prea scurt. Folosiți cel puțin 3 caractere înainte de asterisk.');
        } else if (basePattern.match(/^\d+\/\d+/)) {
            showWildcardFeedback('info', 
                'Căutare wildcard pentru dosare care încep cu "' + basePattern + '".');
        } else {
            showWildcardFeedback('info', 
                'Căutare wildcard pentru "' + basePattern + '*".');
        }
    } else {
        hideWildcardFeedback();
    }
}

function showWildcardFeedback(type, message) {
    const feedbackDiv = document.getElementById('wildcard-feedback');
    if (feedbackDiv) {
        feedbackDiv.className = `alert alert-${type}`;
        feedbackDiv.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
        feedbackDiv.style.display = 'block';
    }
}

function hideWildcardFeedback() {
    const feedbackDiv = document.getElementById('wildcard-feedback');
    if (feedbackDiv) {
        feedbackDiv.style.display = 'none';
    }
}

// Add event listener to search input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="numarDosar"]');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            validateWildcardPattern(this);
        });
    }
});
</script>

<!-- Add this HTML where the search form is displayed -->
<div id="wildcard-feedback" class="alert" style="display: none;"></div>
