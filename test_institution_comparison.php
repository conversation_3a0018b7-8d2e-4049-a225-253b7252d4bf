<?php
/**
 * Institution Comparison Test
 * Compares loading behavior across different institutions
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Institution Comparison Test - Romanian Judicial Portal</title>";
echo "<meta charset='UTF-8'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
.container { max-width: 1400px; margin: 0 auto; }
.section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.header { background: linear-gradient(135deg, #007bff, #0056b3); color: white; text-align: center; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
.comparison-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
.comparison-table th, .comparison-table td { padding: 12px; border: 1px solid #ddd; text-align: left; vertical-align: top; }
.comparison-table th { background-color: #007bff; color: white; }
.status-success { background-color: #d4edda; color: #155724; }
.status-warning { background-color: #fff3cd; color: #856404; }
.status-error { background-color: #f8d7da; color: #721c24; }
.test-link { display: inline-block; background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; margin: 2px; font-size: 12px; }
.test-link:hover { background: #0056b3; color: white; text-decoration: none; }
.performance-info { font-family: monospace; font-size: 11px; background: #f8f9fa; padding: 8px; border-radius: 4px; margin: 5px 0; }
</style></head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🏛️ Institution Comparison Test</h1>";
echo "<p>Testing case loading behavior across different institutions</p>";
echo "<p><strong>Focus:</strong> Verify TribunalulIALOMITA fix and compare with other institutions</p>";
echo "</div>";

// Test institutions and cases
$testCases = [
    [
        'institution' => 'CurteadeApelBUCURESTI',
        'case' => '130/98/2022',
        'description' => 'Known working case (recently optimized)',
        'expected' => 'Should work perfectly'
    ],
    [
        'institution' => 'TribunalulIALOMITA',
        'case' => '130/98/2022',
        'description' => 'Previously problematic case (parameter fix applied)',
        'expected' => 'Should now work after parameter fix'
    ],
    [
        'institution' => 'TribunalulBUCURESTI',
        'case' => '130/98/2022',
        'description' => 'Test case for comparison',
        'expected' => 'May or may not exist'
    ],
    [
        'institution' => 'TribunalulIALOMITA',
        'case' => '100/2022',
        'description' => 'Different case from same institution',
        'expected' => 'Test institution behavior'
    ],
    [
        'institution' => 'CurteadeApelBUCURESTI',
        'case' => '100/2022',
        'description' => 'Different case from working institution',
        'expected' => 'Test case availability'
    ]
];

echo "<div class='section'>";
echo "<h2>📊 Institution & Case Comparison</h2>";

echo "<table class='comparison-table'>";
echo "<tr>";
echo "<th>Institution</th>";
echo "<th>Case Number</th>";
echo "<th>Description</th>";
echo "<th>SOAP API Test</th>";
echo "<th>Performance</th>";
echo "<th>Page Loading Prediction</th>";
echo "<th>Test Links</th>";
echo "</tr>";

foreach ($testCases as $testCase) {
    echo "<tr>";
    echo "<td><strong>{$testCase['institution']}</strong></td>";
    echo "<td><code>{$testCase['case']}</code></td>";
    echo "<td>{$testCase['description']}<br><small><em>{$testCase['expected']}</em></small></td>";
    
    // Test SOAP API
    $startTime = microtime(true);
    $startMemory = memory_get_usage(true);
    
    try {
        $dosarService = new DosarService();
        $dosare = $dosarService->cautareDupaNumarDosar(
            $testCase['case'],
            $testCase['institution'],
            '', '', ''
        );
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        
        $loadTime = ($endTime - $startTime) * 1000;
        $memoryUsed = ($endMemory - $startMemory) / 1024 / 1024;
        
        if (!empty($dosare)) {
            $dosar = $dosare[0];
            $partiesCount = count($dosar->parti ?? []);
            
            echo "<td class='status-success'>";
            echo "✅ <strong>SUCCESS</strong><br>";
            echo "Data found<br>";
            echo "Parties: $partiesCount<br>";
            
            // Check for potential performance issues
            if ($partiesCount > 300) {
                echo "<small>⚠️ Large dataset</small>";
            } elseif ($partiesCount > 100) {
                echo "<small>📊 Medium dataset</small>";
            } else {
                echo "<small>📄 Small dataset</small>";
            }
            echo "</td>";
            
            echo "<td>";
            echo "<div class='performance-info'>";
            echo "Load: " . round($loadTime, 1) . "ms<br>";
            echo "Memory: " . round($memoryUsed, 1) . "MB<br>";
            if ($loadTime < 500) {
                echo "Status: ✅ Fast";
            } elseif ($loadTime < 1000) {
                echo "Status: ⚠️ Moderate";
            } else {
                echo "Status: ❌ Slow";
            }
            echo "</div>";
            echo "</td>";
            
            echo "<td class='status-success'>";
            echo "✅ <strong>WILL LOAD</strong><br>";
            echo "• Parameters: Valid<br>";
            echo "• Data: Available<br>";
            echo "• .dosar-header: Will render<br>";
            echo "• Loading overlay: Will hide<br>";
            echo "• Page: Will display normally";
            echo "</td>";
            
        } else {
            echo "<td class='status-warning'>";
            echo "⚠️ <strong>NO DATA</strong><br>";
            echo "API responded but<br>";
            echo "no case found";
            echo "</td>";
            
            echo "<td>";
            echo "<div class='performance-info'>";
            echo "Load: " . round($loadTime, 1) . "ms<br>";
            echo "Memory: " . round($memoryUsed, 1) . "MB<br>";
            echo "Status: ✅ Fast (no data)";
            echo "</div>";
            echo "</td>";
            
            echo "<td class='status-warning'>";
            echo "⚠️ <strong>NO CONTENT</strong><br>";
            echo "• Parameters: Valid<br>";
            echo "• Data: Not found<br>";
            echo "• .dosar-header: Won't render<br>";
            echo "• Loading overlay: Timeout (2s)<br>";
            echo "• Page: 'Nu s-au găsit dosare'";
            echo "</td>";
        }
        
    } catch (Exception $e) {
        $endTime = microtime(true);
        $loadTime = ($endTime - $startTime) * 1000;
        
        echo "<td class='status-error'>";
        echo "❌ <strong>ERROR</strong><br>";
        echo "Exception caught<br>";
        echo "<small>" . htmlspecialchars(substr($e->getMessage(), 0, 50)) . "...</small>";
        echo "</td>";
        
        echo "<td>";
        echo "<div class='performance-info'>";
        echo "Load: " . round($loadTime, 1) . "ms<br>";
        echo "Status: ❌ Error";
        echo "</div>";
        echo "</td>";
        
        echo "<td class='status-error'>";
        echo "❌ <strong>ERROR PAGE</strong><br>";
        echo "• Parameters: Valid<br>";
        echo "• Data: Error<br>";
        echo "• .alert-danger: Will render<br>";
        echo "• Loading overlay: Will hide<br>";
        echo "• Page: Will show error";
        echo "</td>";
    }
    
    // Test links
    $encodedCase = urlencode($testCase['case']);
    $testUrl = "http://localhost/just/detalii_dosar.php?numar={$encodedCase}&institutie={$testCase['institution']}";
    
    echo "<td>";
    echo "<a href='$testUrl' target='_blank' class='test-link'>Test Page</a><br>";
    echo "<a href='debug_loading_issue.php' target='_blank' class='test-link'>Debug Tool</a>";
    echo "</td>";
    
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Summary and recommendations
echo "<div class='section'>";
echo "<h2>📋 Summary & Recommendations</h2>";

echo "<div style='background: #d4edda; border-left: 4px solid #28a745; padding: 15px; margin: 10px 0;'>";
echo "<h4>✅ Parameter Fix Status</h4>";
echo "<p><strong>Applied Fix:</strong> Modified detalii_dosar.php line 17 to support both 'numar' and 'numar_dosar' parameters.</p>";
echo "<p><strong>Expected Result:</strong> TribunalulIALOMITA case should now load properly without getting stuck on loading message.</p>";
echo "</div>";

echo "<div style='background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 10px 0;'>";
echo "<h4>🔍 Loading Mechanism Explanation</h4>";
echo "<p><strong>How it works:</strong></p>";
echo "<ul>";
echo "<li>Page loads with loading overlay visible</li>";
echo "<li>PHP processes parameters and loads case data</li>";
echo "<li>If data found: renders .dosar-header element</li>";
echo "<li>JavaScript detects .dosar-header and hides loading overlay</li>";
echo "<li>If no data: shows 'Nu s-au găsit dosare' after 2-second timeout</li>";
echo "<li>If error: renders .alert-danger and hides loading overlay</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0;'>";
echo "<h4>⚠️ If Issues Persist</h4>";
echo "<p>If the TribunalulIALOMITA case still doesn't load after the parameter fix:</p>";
echo "<ul>";
echo "<li>Check browser console for JavaScript errors</li>";
echo "<li>Verify network requests in browser developer tools</li>";
echo "<li>Check if the case actually exists in that institution</li>";
echo "<li>Test with different browsers to rule out browser-specific issues</li>";
echo "</ul>";
echo "</div>";

echo "</div>";

echo "</div>";
echo "</body></html>";
?>
