<?php
/**
 * Privacy Settings and GDPR Management
 * 
 * Allows users to manage their privacy settings, view/export data,
 * and exercise their GDPR rights.
 * 
 * Portal Judiciar România - Case Monitoring System
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */

require_once dirname(__DIR__) . '/bootstrap.php';
require_once dirname(__DIR__) . '/includes/config.php';

use App\Helpers\TemplateEngine;
use App\Security\CSRFProtection;
use App\Security\RateLimiter;
use App\Security\GDPRCompliance;

// Session is already started in bootstrap.php

// Initialize services
$templateEngine = new TemplateEngine();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // For development, create a demo user session
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Demo User';
    $_SESSION['user_email'] = '<EMAIL>';
}

$userId = $_SESSION['user_id'];
$userName = $_SESSION['user_name'] ?? 'Utilizator';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'];
        
        // Check rate limiting
        $rateLimitCheck = RateLimiter::checkLimit($action, "user_{$userId}");
        if (!$rateLimitCheck['allowed']) {
            echo json_encode([
                'success' => false,
                'error' => 'Prea multe cereri. Vă rugăm să încercați din nou mai târziu.',
                'rate_limit' => $rateLimitCheck
            ]);
            exit;
        }
        
        // Verify CSRF token
        if (!CSRFProtection::validateRequest($_POST, $action)) {
            RateLimiter::recordAttempt($action, "user_{$userId}", false, ['reason' => 'csrf_invalid']);
            throw new Exception('Token CSRF invalid');
        }

        // Execute action with rate limiting
        $result = RateLimiter::enforce($action, function() use ($action, $userId) {
            switch ($action) {
                case 'update_consent':
                    return handleUpdateConsent($userId, $_POST);
                    
                case 'export_data':
                    return handleDataExport($userId);
                    
                case 'delete_data':
                    return handleDataDeletion($userId, $_POST);
                    
                case 'update_privacy_settings':
                    return handlePrivacySettings($userId, $_POST);
                    
                default:
                    throw new Exception('Acțiune necunoscută');
            }
        }, "user_{$userId}");
        
        echo json_encode($result);
        
    } catch (Exception $e) {
        RateLimiter::recordAttempt($_POST['action'] ?? 'unknown', "user_{$userId}", false, [
            'error' => $e->getMessage()
        ]);
        
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// Get user data for template
$consents = GDPRCompliance::getUserConsents($userId);
$privacyReport = GDPRCompliance::generatePrivacyReport($userId);

// Prepare template data
$data = [
    'user_id' => $userId,
    'user_name' => $userName,
    'user_email' => $_SESSION['user_email'] ?? '',
    'consents' => $consents,
    'privacy_report' => $privacyReport,
    'csrf_tokens' => [
        'update_consent' => CSRFProtection::generateToken('update_consent'),
        'export_data' => CSRFProtection::generateToken('export_data'),
        'delete_data' => CSRFProtection::generateToken('delete_data'),
        'update_privacy_settings' => CSRFProtection::generateToken('update_privacy_settings')
    ],
    'rate_limits' => [
        'export_data' => RateLimiter::checkLimit('export_data', "user_{$userId}"),
        'delete_data' => RateLimiter::checkLimit('delete_data', "user_{$userId}")
    ]
];

// Render template
echo $templateEngine->render('privacy/settings.twig', $data);

/**
 * Handle consent updates
 */
function handleUpdateConsent($userId, $postData)
{
    $consentType = $postData['consent_type'] ?? '';
    $granted = isset($postData['granted']) && $postData['granted'] === '1';
    
    if (empty($consentType)) {
        throw new Exception('Tipul de consimțământ este obligatoriu');
    }
    
    $success = GDPRCompliance::recordConsent($userId, $consentType, $granted);
    
    if ($success) {
        return [
            'success' => true,
            'message' => 'Consimțământul a fost actualizat cu succes'
        ];
    } else {
        throw new Exception('Eroare la actualizarea consimțământului');
    }
}

/**
 * Handle data export requests
 */
function handleDataExport($userId)
{
    try {
        $exportData = GDPRCompliance::exportUserData($userId);
        
        // Create export file
        $filename = "user_data_export_{$userId}_" . date('Y-m-d_H-i-s') . '.json';
        $filepath = dirname(__DIR__) . '/exports/' . $filename;
        
        // Ensure exports directory exists
        $exportDir = dirname($filepath);
        if (!is_dir($exportDir)) {
            mkdir($exportDir, 0755, true);
        }
        
        // Write export data to file
        file_put_contents($filepath, json_encode($exportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        return [
            'success' => true,
            'message' => 'Datele au fost exportate cu succes',
            'download_url' => '/exports/' . $filename,
            'file_size' => filesize($filepath),
            'export_date' => date('Y-m-d H:i:s')
        ];
        
    } catch (Exception $e) {
        throw new Exception('Eroare la exportul datelor: ' . $e->getMessage());
    }
}

/**
 * Handle data deletion requests
 */
function handleDataDeletion($userId, $postData)
{
    $confirmDeletion = isset($postData['confirm_deletion']) && $postData['confirm_deletion'] === '1';
    $keepLegalData = isset($postData['keep_legal_data']) && $postData['keep_legal_data'] === '1';
    
    if (!$confirmDeletion) {
        throw new Exception('Confirmarea ștergerii este obligatorie');
    }
    
    try {
        $deletionSummary = GDPRCompliance::deleteUserData($userId, $keepLegalData);
        
        return [
            'success' => true,
            'message' => 'Datele au fost șterse cu succes',
            'deletion_summary' => $deletionSummary
        ];
        
    } catch (Exception $e) {
        throw new Exception('Eroare la ștergerea datelor: ' . $e->getMessage());
    }
}

/**
 * Handle privacy settings updates
 */
function handlePrivacySettings($userId, $postData)
{
    // This would integrate with a user preferences system
    // For now, we'll just return success
    
    return [
        'success' => true,
        'message' => 'Setările de confidențialitate au fost actualizate'
    ];
}
