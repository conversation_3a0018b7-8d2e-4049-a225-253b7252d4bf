<?php
/**
 * Cron Job Setup Script
 * 
 * This script helps set up the cron job for case monitoring.
 * It can be run from command line or web interface (for development).
 * 
 * Portal Judiciar România - Case Monitoring System
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */

// Include required files
require_once dirname(__DIR__) . '/bootstrap.php';
require_once dirname(__DIR__) . '/includes/config.php';

/**
 * Cron job setup and management class
 */
class CronSetup
{
    private $projectPath;
    private $phpPath;
    private $cronFile;
    private $logFile;
    
    public function __construct()
    {
        $this->projectPath = dirname(__DIR__);
        $this->phpPath = $this->findPhpPath();
        $this->cronFile = $this->projectPath . '/cron/monitor_cases.php';
        $this->logFile = $this->projectPath . '/logs/cron.log';
        
        // Ensure directories exist
        $this->ensureDirectories();
    }
    
    /**
     * Display cron job setup instructions
     */
    public function showInstructions()
    {
        echo "=== Portal Judiciar - Case Monitoring Cron Job Setup ===\n\n";
        
        echo "1. CRON JOB COMMAND:\n";
        echo "   Add this line to your crontab (run 'crontab -e'):\n\n";
        echo "   */30 * * * * {$this->phpPath} {$this->cronFile} >> {$this->logFile} 2>&1\n\n";
        
        echo "2. ALTERNATIVE CRON COMMAND (with error logging):\n";
        echo "   */30 * * * * {$this->phpPath} {$this->cronFile} >> {$this->logFile} 2>> {$this->projectPath}/logs/cron_errors.log\n\n";
        
        echo "3. MANUAL TEST:\n";
        echo "   Run this command to test the cron job manually:\n";
        echo "   {$this->phpPath} {$this->cronFile}\n\n";
        
        echo "4. CRON JOB FREQUENCY OPTIONS:\n";
        echo "   Every 30 minutes: */30 * * * *\n";
        echo "   Every 15 minutes: */15 * * * *\n";
        echo "   Every hour:       0 * * * *\n";
        echo "   Every 2 hours:    0 */2 * * *\n\n";
        
        echo "5. LOG FILES:\n";
        echo "   Main log:  {$this->logFile}\n";
        echo "   Error log: {$this->projectPath}/logs/cron_errors.log\n\n";
        
        echo "6. MONITORING:\n";
        echo "   Check logs regularly: tail -f {$this->logFile}\n";
        echo "   Check for errors:     tail -f {$this->projectPath}/logs/cron_errors.log\n\n";
        
        $this->showSystemInfo();
    }
    
    /**
     * Test the cron job
     */
    public function testCronJob()
    {
        echo "=== Testing Cron Job ===\n\n";
        
        // Check if the cron file exists and is executable
        if (!file_exists($this->cronFile)) {
            echo "❌ ERROR: Cron file not found: {$this->cronFile}\n";
            return false;
        }
        
        if (!is_readable($this->cronFile)) {
            echo "❌ ERROR: Cron file is not readable: {$this->cronFile}\n";
            return false;
        }
        
        echo "✅ Cron file exists and is readable\n";
        
        // Test PHP path
        $phpVersion = shell_exec("{$this->phpPath} -v 2>/dev/null");
        if (empty($phpVersion)) {
            echo "❌ ERROR: PHP not found at: {$this->phpPath}\n";
            echo "   Please update the PHP path in this script\n";
            return false;
        }
        
        echo "✅ PHP found: " . trim(explode("\n", $phpVersion)[0]) . "\n";
        
        // Test database connection
        try {
            $db = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            echo "✅ Database connection successful\n";
        } catch (Exception $e) {
            echo "❌ ERROR: Database connection failed: " . $e->getMessage() . "\n";
            return false;
        }
        
        // Test log directory
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            echo "❌ ERROR: Log directory does not exist: {$logDir}\n";
            return false;
        }
        
        if (!is_writable($logDir)) {
            echo "❌ ERROR: Log directory is not writable: {$logDir}\n";
            return false;
        }
        
        echo "✅ Log directory is writable\n";
        
        // Run a test execution
        echo "\n--- Running test execution ---\n";
        $command = "{$this->phpPath} {$this->cronFile}";
        $output = shell_exec($command . " 2>&1");
        
        if ($output) {
            echo "Cron job output:\n";
            echo $output . "\n";
        } else {
            echo "❌ No output from cron job (this might indicate an error)\n";
            return false;
        }
        
        echo "✅ Test completed successfully\n";
        return true;
    }
    
    /**
     * Show system information
     */
    private function showSystemInfo()
    {
        echo "=== System Information ===\n";
        echo "Project path: {$this->projectPath}\n";
        echo "PHP path:     {$this->phpPath}\n";
        echo "PHP version:  " . PHP_VERSION . "\n";
        echo "OS:           " . PHP_OS . "\n";
        echo "User:         " . get_current_user() . "\n";
        
        if (function_exists('posix_getuid')) {
            echo "UID:          " . posix_getuid() . "\n";
            echo "GID:          " . posix_getgid() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Find PHP executable path
     */
    private function findPhpPath()
    {
        // Common PHP paths
        $possiblePaths = [
            '/usr/bin/php',
            '/usr/local/bin/php',
            '/opt/php/bin/php',
            '/usr/bin/php8.1',
            '/usr/bin/php8.0',
            '/usr/bin/php7.4',
            'php' // System PATH
        ];
        
        foreach ($possiblePaths as $path) {
            $version = shell_exec("{$path} -v 2>/dev/null");
            if (!empty($version)) {
                return $path;
            }
        }
        
        // Fallback to 'php' if nothing found
        return 'php';
    }
    
    /**
     * Ensure required directories exist
     */
    private function ensureDirectories()
    {
        $directories = [
            dirname($this->logFile),
            dirname($this->cronFile)
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }
    
    /**
     * Generate cron job entry
     */
    public function generateCronEntry($frequency = '*/30')
    {
        return "{$frequency} * * * * {$this->phpPath} {$this->cronFile} >> {$this->logFile} 2>&1";
    }
    
    /**
     * Show log files
     */
    public function showLogs($lines = 50)
    {
        echo "=== Recent Cron Log Entries (last {$lines} lines) ===\n\n";
        
        if (file_exists($this->logFile)) {
            $logContent = shell_exec("tail -{$lines} {$this->logFile}");
            if ($logContent) {
                echo $logContent;
            } else {
                echo "Log file is empty or cannot be read.\n";
            }
        } else {
            echo "Log file does not exist yet: {$this->logFile}\n";
        }
        
        echo "\n";
        
        $errorLogFile = $this->projectPath . '/logs/cron_errors.log';
        if (file_exists($errorLogFile)) {
            echo "=== Recent Error Log Entries (last {$lines} lines) ===\n\n";
            $errorContent = shell_exec("tail -{$lines} {$errorLogFile}");
            if ($errorContent) {
                echo $errorContent;
            } else {
                echo "Error log file is empty.\n";
            }
        }
    }
}

// Handle command line arguments
if (php_sapi_name() === 'cli') {
    $setup = new CronSetup();
    
    $command = $argv[1] ?? 'help';
    
    switch ($command) {
        case 'test':
            $setup->testCronJob();
            break;
            
        case 'logs':
            $lines = isset($argv[2]) ? (int)$argv[2] : 50;
            $setup->showLogs($lines);
            break;
            
        case 'entry':
            $frequency = $argv[2] ?? '*/30';
            echo $setup->generateCronEntry($frequency) . "\n";
            break;
            
        case 'help':
        default:
            $setup->showInstructions();
            echo "Usage: php setup_cron.php [command]\n";
            echo "Commands:\n";
            echo "  help  - Show setup instructions (default)\n";
            echo "  test  - Test the cron job\n";
            echo "  logs  - Show recent log entries\n";
            echo "  entry - Generate cron entry\n";
            break;
    }
} else {
    // Web interface for development
    header('Content-Type: text/plain; charset=UTF-8');
    $setup = new CronSetup();
    $setup->showInstructions();
    
    echo "\n=== Test Results ===\n";
    $setup->testCronJob();
}
