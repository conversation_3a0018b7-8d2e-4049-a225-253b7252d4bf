/**
 * Portal Judiciar - Script principal
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inițializăm tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Inițializăm popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Adăugăm atribute data-label pentru tabelele responsive
    makeTablesResponsive();

    // Implementăm funcționalitatea de căutare rapidă
    setupQuickSearch();

    // Implementăm validarea formularelor
    setupFormValidation();

    // Implementăm funcționalitatea de alertă automată
    setupAutoAlerts();
});

/**
 * Adaugă atribute data-label pentru tabelele responsive
 */
function makeTablesResponsive() {
    const tables = document.querySelectorAll('.table-responsive-card table');

    tables.forEach(table => {
        const headerCells = table.querySelectorAll('thead th');
        const headerTexts = Array.from(headerCells).map(cell => cell.textContent.trim());

        const bodyRows = table.querySelectorAll('tbody tr');
        bodyRows.forEach(row => {
            const cells = row.querySelectorAll('td');
            cells.forEach((cell, index) => {
                if (index < headerTexts.length) {
                    cell.setAttribute('data-label', headerTexts[index]);
                }
            });
        });
    });
}

/**
 * Implementează funcționalitatea de căutare rapidă
 */
function setupQuickSearch() {
    const quickSearchLinks = document.querySelectorAll('.quick-search');

    quickSearchLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const searchTerm = this.getAttribute('data-search');

            // Verificăm dacă suntem pe pagina de detalii dosar
            if (window.location.pathname.includes('detalii_dosar.php')) {
                window.location.href = 'index.php#cautare-avansata?numeParte=' + encodeURIComponent(searchTerm);
            } else {
                // Dacă suntem pe pagina de căutare, completăm direct formularul
                const searchForm = document.querySelector('form[action="search.php"]');
                if (searchForm) {
                    const numeParteInput = searchForm.querySelector('input[name="numeParte"]');
                    if (numeParteInput) {
                        numeParteInput.value = searchTerm;
                        searchForm.submit();
                    }
                }
            }
        });
    });
}

/**
 * Implementează validarea formularelor
 */
function setupFormValidation() {
    const forms = document.querySelectorAll('form');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            let isValid = true;

            // Validăm câmpurile obligatorii
            const requiredFields = form.querySelectorAll('[required]');
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            // Validăm email-urile
            const emailFields = form.querySelectorAll('input[type="email"]');
            emailFields.forEach(field => {
                if (field.value.trim() && !isValidEmail(field.value.trim())) {
                    isValid = false;
                    field.classList.add('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                showAlert('Vă rugăm să completați corect toate câmpurile obligatorii.', 'danger');
            }
        });
    });
}

/**
 * Verifică dacă un email este valid
 *
 * @param {string} email Email-ul de verificat
 * @return {boolean} True dacă email-ul este valid, false în caz contrar
 */
function isValidEmail(email) {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(email);
}

/**
 * Implementează funcționalitatea de alertă automată
 */
function setupAutoAlerts() {
    // Ascundem alertele după 5 secunde
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
}

/**
 * Afișează o alertă
 *
 * @param {string} message Mesajul alertei
 * @param {string} type Tipul alertei (success, danger, warning, info)
 */
function showAlert(message, type = 'info') {
    const alertsContainer = document.createElement('div');
    alertsContainer.className = 'alert-container position-fixed top-0 end-0 p-3';
    alertsContainer.style.zIndex = '9999';

    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    alertsContainer.appendChild(alert);
    document.body.appendChild(alertsContainer);

    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();

        // Eliminăm containerul după ce alerta dispare
        alert.addEventListener('closed.bs.alert', () => {
            if (alertsContainer.parentNode) {
                alertsContainer.parentNode.removeChild(alertsContainer);
            }
        });
    }, 5000);
}

/**
 * Afișează o notificare temporară pentru utilizator
 *
 * @param {string} message - Textul notificării care va fi afișat utilizatorului
 * @param {string} type - Tipul notificării care determină stilul (success, danger, warning, info)
 *
 * Exemplu de utilizare:
 * showNotification("Operațiune realizată cu succes!", "success");
 * showNotification("A apărut o eroare!", "danger");
 * showNotification("Atenție, verificați datele introduse!", "warning");
 * showNotification("Procesul este în curs de desfășurare...", "info");
 */
function showNotification(message, type) {
    // Verifică dacă containerul de notificări există
    const notificationContainer = $('#notificationContainer');
    const notification = $('#notification');

    if (notificationContainer.length === 0 || notification.length === 0) {
        console.error("Elementele pentru notificări nu au fost găsite în DOM");
        return;
    }

    // Validează tipul notificării
    const validTypes = ['success', 'danger', 'warning', 'info'];
    if (!validTypes.includes(type)) {
        type = 'info'; // Folosește tipul implicit dacă tipul specificat nu este valid
    }

    // Elimină toate clasele de tip alert și adaugă clasa corespunzătoare tipului
    notification.removeClass('alert-success alert-danger alert-warning alert-info')
        .addClass('alert-' + type)
        .html('<i class="fas fa-' + getIconForType(type) + ' me-2"></i>' + message);

    // Afișează containerul de notificări cu animație
    notificationContainer.slideDown();

    // Ascunde notificarea după 5 secunde
    setTimeout(function() {
        notificationContainer.slideUp();
    }, 5000);
}

/**
 * Returnează iconița corespunzătoare tipului de notificare
 *
 * @param {string} type - Tipul notificării
 * @return {string} - Numele iconului Font Awesome
 */
function getIconForType(type) {
    switch(type) {
        case 'success': return 'check-circle';
        case 'danger': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        case 'info': return 'info-circle';
        default: return 'info-circle';
    }
}
