<?php
/**
 * Portal Judiciar România - Database Setup Script
 * 
 * Creates all necessary tables for the case monitoring system.
 */

require_once __DIR__ . '/bootstrap.php';

use App\Config\Database;

echo "🚀 Portal Judiciar România - Database Setup\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    // Test database connection
    echo "📊 Testing database connection...\n";
    $connection = Database::getConnection();
    echo "✅ Database connection successful\n\n";
    
    // Create users table
    echo "👥 Creating users table...\n";
    $connection->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            phone VARCHAR(20) NULL,
            email_verified BOOLEAN DEFAULT FALSE,
            email_verification_token VARCHAR(64) NULL,
            email_verification_expires D<PERSON>ETIME NULL,
            password_reset_token VARCHAR(64) NULL,
            password_reset_expires DATETIME NULL,
            gdpr_consent BOOLEAN DEFAULT FALSE,
            gdpr_consent_date DATETIME NULL,
            gdpr_consent_ip VARCHAR(45) NULL,
            notification_preferences JSON NULL,
            is_active BOOLEAN DEFAULT TRUE,
            admin_role ENUM('super_admin', 'admin', 'moderator', 'viewer') NULL,
            locked_until DATETIME NULL,
            deleted_at DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_email (email),
            INDEX idx_email_verified (email_verified),
            INDEX idx_is_active (is_active),
            INDEX idx_created_at (created_at),
            INDEX idx_admin_role (admin_role)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Users table created\n";
    
    // Create monitored_cases table
    echo "📁 Creating monitored_cases table...\n";
    $connection->exec("
        CREATE TABLE IF NOT EXISTS monitored_cases (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            user_id INT UNSIGNED NOT NULL,
            case_number VARCHAR(100) NOT NULL,
            institution_code VARCHAR(100) NOT NULL,
            institution_name VARCHAR(255) DEFAULT NULL,
            case_object TEXT NULL,
            monitoring_reason TEXT NULL,
            notification_frequency ENUM('immediate', 'daily', 'weekly') DEFAULT 'daily',
            last_checked DATETIME NULL,
            last_notification_sent DATETIME NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_case (user_id, case_number, institution_code),
            INDEX idx_user_id (user_id),
            INDEX idx_case_number (case_number),
            INDEX idx_institution_code (institution_code),
            INDEX idx_is_active (is_active),
            INDEX idx_last_checked (last_checked),
            INDEX idx_notification_frequency (notification_frequency)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Monitored cases table created\n";
    
    // Create case_snapshots table
    echo "📸 Creating case_snapshots table...\n";
    $connection->exec("
        CREATE TABLE IF NOT EXISTS case_snapshots (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            monitored_case_id INT UNSIGNED NOT NULL,
            snapshot_data JSON NOT NULL,
            snapshot_hash VARCHAR(64) NOT NULL,
            case_status VARCHAR(100) NULL,
            case_stage VARCHAR(100) NULL,
            next_hearing_date DATE NULL,
            last_modification_date DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (monitored_case_id) REFERENCES monitored_cases(id) ON DELETE CASCADE,
            INDEX idx_monitored_case_id (monitored_case_id),
            INDEX idx_snapshot_hash (snapshot_hash),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Case snapshots table created\n";
    
    // Create case_changes table
    echo "🔄 Creating case_changes table...\n";
    $connection->exec("
        CREATE TABLE IF NOT EXISTS case_changes (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            monitored_case_id INT UNSIGNED NOT NULL,
            old_snapshot_id INT UNSIGNED NULL,
            new_snapshot_id INT UNSIGNED NOT NULL,
            change_type ENUM('hearing_date', 'status', 'stage', 'parties', 'judge', 'solution', 'other') NOT NULL,
            change_description TEXT NOT NULL,
            change_details JSON NULL,
            detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (monitored_case_id) REFERENCES monitored_cases(id) ON DELETE CASCADE,
            FOREIGN KEY (old_snapshot_id) REFERENCES case_snapshots(id) ON DELETE SET NULL,
            FOREIGN KEY (new_snapshot_id) REFERENCES case_snapshots(id) ON DELETE CASCADE,
            INDEX idx_monitored_case_id (monitored_case_id),
            INDEX idx_change_type (change_type),
            INDEX idx_detected_at (detected_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Case changes table created\n";
    
    // Create notification_queue table
    echo "📧 Creating notification_queue table...\n";
    $connection->exec("
        CREATE TABLE IF NOT EXISTS notification_queue (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            user_id INT UNSIGNED NOT NULL,
            monitored_case_id INT UNSIGNED NOT NULL,
            case_change_id INT UNSIGNED NULL,
            notification_type ENUM('immediate', 'daily_digest', 'weekly_summary') NOT NULL,
            email_subject VARCHAR(255) NOT NULL,
            email_body TEXT NOT NULL,
            email_html_body TEXT NULL,
            priority TINYINT UNSIGNED DEFAULT 5,
            status ENUM('pending', 'processing', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
            attempts TINYINT UNSIGNED DEFAULT 0,
            max_attempts TINYINT UNSIGNED DEFAULT 3,
            scheduled_for DATETIME NOT NULL,
            sent_at DATETIME NULL,
            error_message TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (monitored_case_id) REFERENCES monitored_cases(id) ON DELETE CASCADE,
            FOREIGN KEY (case_change_id) REFERENCES case_changes(id) ON DELETE SET NULL,
            INDEX idx_user_id (user_id),
            INDEX idx_status (status),
            INDEX idx_scheduled_for (scheduled_for),
            INDEX idx_notification_type (notification_type),
            INDEX idx_priority (priority),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Notification queue table created\n";
    
    // Create user_sessions table
    echo "🔐 Creating user_sessions table...\n";
    $connection->exec("
        CREATE TABLE IF NOT EXISTS user_sessions (
            id VARCHAR(128) PRIMARY KEY,
            user_id INT UNSIGNED NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT NULL,
            session_data TEXT NULL,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            expires_at DATETIME NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_last_activity (last_activity),
            INDEX idx_expires_at (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ User sessions table created\n";
    
    // Create gdpr_requests table
    echo "🛡️ Creating gdpr_requests table...\n";
    $connection->exec("
        CREATE TABLE IF NOT EXISTS gdpr_requests (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            user_id INT UNSIGNED NOT NULL,
            request_type ENUM('export', 'delete') NOT NULL,
            status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
            requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at DATETIME NULL,
            file_path VARCHAR(500) NULL,
            expires_at DATETIME NULL,
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            INDEX idx_user_id (user_id),
            INDEX idx_status (status),
            INDEX idx_requested_at (requested_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ GDPR requests table created\n";
    
    // Create system_logs table
    echo "📝 Creating system_logs table...\n";
    $connection->exec("
        CREATE TABLE IF NOT EXISTS system_logs (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            level ENUM('debug', 'info', 'warning', 'error', 'critical') NOT NULL,
            message TEXT NOT NULL,
            context JSON NULL,
            user_id INT UNSIGNED NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            INDEX idx_level (level),
            INDEX idx_user_id (user_id),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ System logs table created\n";
    
    // Create a test user
    echo "👤 Creating test user...\n";
    $testUserExists = Database::fetchOne("SELECT id FROM users WHERE email = '<EMAIL>'");
    
    if (!$testUserExists) {
        Database::insert('users', [
            'email' => '<EMAIL>',
            'password_hash' => password_hash('test123', PASSWORD_DEFAULT),
            'first_name' => 'Test',
            'last_name' => 'User',
            'email_verified' => 1,
            'gdpr_consent' => 1,
            'gdpr_consent_date' => date('Y-m-d H:i:s'),
            'gdpr_consent_ip' => '127.0.0.1',
            'notification_preferences' => json_encode([
                'immediate_notifications' => true,
                'daily_digest' => true,
                'weekly_summary' => false,
                'email_format' => 'html'
            ])
        ]);
        echo "✅ Test user created (email: <EMAIL>, password: test123)\n";
    } else {
        echo "ℹ️ Test user already exists\n";
    }
    
    echo "\n🎉 Database setup completed successfully!\n";
    echo "✅ All monitoring system tables are ready\n";
    echo "✅ Test user account created\n";
    echo "\nNext steps:\n";
    echo "1. Run: php test_monitoring_system.php\n";
    echo "2. Visit: http://localhost/just/monitor.php\n";
    echo "3. Login with: <EMAIL> / test123\n";
    
} catch (Exception $e) {
    echo "❌ Database setup failed: " . $e->getMessage() . "\n";
    echo "Please check your database configuration and try again.\n";
    exit(1);
}
