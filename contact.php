<?php
/**
 * Portal Judiciar - Pagina de Contact
 * 
 * Pagină pentru contactarea echipei portalului judiciar
 */

// Încărcăm bootstrap-ul aplicației
require_once 'bootstrap.php';

// Importăm clasele necesare
use App\Helpers\SecurityHelper;
use App\Helpers\SEOHelper;
use App\Helpers\BreadcrumbHelper;

// Inițializăm sesiunea dacă nu este deja inițializată
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Generăm token CSRF pentru formular
$csrfToken = SecurityHelper::generateCSRFToken();

// Verificăm dacă există mesaje flash în sesiune
$flashMessages = $_SESSION['flash'] ?? [];
unset($_SESSION['flash']);

// Breadcrumbs pentru structured data
$breadcrumbs = [
    ['name' => 'Acasă', 'url' => 'http://localhost/just/'],
    ['name' => 'Contact', 'url' => 'http://localhost/just/contact.php']
];
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

    <?php
    // Generăm meta tags SEO optimizate
    echo SEOHelper::renderMetaTags('contact');

    // Generăm structured data JSON-LD cu breadcrumbs
    echo SEOHelper::renderStructuredData('contact', $breadcrumbs);

    // Renderăm CSS-ul pentru breadcrumb-uri
    echo BreadcrumbHelper::renderBreadcrumbCSS();
    ?>

    <!-- CSS Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.min.css?v=1.0">
    <link rel="stylesheet" href="assets/css/responsive.min.css?v=1.0">
    <link rel="stylesheet" href="assets/css/buttons.min.css?v=1.0">
    <link rel="stylesheet" href="assets/css/footer.min.css?v=1.0">

    <style>
        /* Stiluri specifice pentru pagina de contact */
        :root {
            --primary-blue: #007bff;
            --secondary-blue: #2c3e50;
            --light-blue: #e3f2fd;
            --success-green: #28a745;
            --warning-orange: #ffc107;
            --danger-red: #dc3545;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .contact-header {
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }

        .contact-form-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .form-label {
            font-weight: 600;
            color: var(--secondary-blue);
            margin-bottom: 0.5rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 6px;
            padding: 0.75rem;
            font-size: 1rem;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .form-control.is-invalid {
            border-color: var(--danger-red);
        }

        .form-control.is-valid {
            border-color: var(--success-green);
        }

        .btn-primary {
            background-color: var(--primary-blue);
            border-color: var(--primary-blue);
            padding: 0.75rem 2rem;
            font-weight: 600;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
            transform: translateY(-1px);
        }

        .contact-info {
            background: var(--light-blue);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .contact-info h4 {
            color: var(--secondary-blue);
            margin-bottom: 1rem;
        }

        .contact-info p {
            margin-bottom: 0.5rem;
            color: #495057;
        }

        .breadcrumb {
            background-color: transparent;
            padding: 0;
            margin-bottom: 1rem;
        }

        .breadcrumb-item a {
            color: var(--primary-blue);
            text-decoration: none;
        }

        .breadcrumb-item.active {
            color: var(--secondary-blue);
        }

        /* Responsive design */
        @media (max-width: 767px) {
            .contact-header {
                padding: 2rem 0;
            }
            
            .contact-form-container {
                padding: 1.5rem;
            }
            
            .btn-primary {
                width: 100%;
                min-height: 44px;
            }
            
            .form-control {
                min-height: 44px;
            }
        }

        /* Loading overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-content {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
        }

        .spinner-border {
            color: var(--primary-blue);
        }

        /* Notification styles */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            max-width: 400px;
        }

        .notification-container .alert {
            margin-bottom: 0;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: none;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
        }

        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }

        @media (max-width: 767px) {
            .notification-container {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-content">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Se încarcă...</span>
            </div>
            <p class="mt-2 mb-0">Se trimite mesajul...</p>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container" style="display: none;">
        <div id="notification" class="alert" role="alert"></div>
    </div>

    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-gavel me-2"></i>
                DosareJust.ro - Portal Judiciar
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-search me-1"></i>
                            Căutare Dosare
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sedinte.php">
                            <i class="fas fa-calendar-alt me-1"></i>
                            Ședințe
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Contact Header -->
    <div class="contact-header">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="display-5 fw-bold mb-3">
                        <i class="fas fa-envelope me-3"></i>
                        Contactați-ne
                    </h1>
                    <p class="lead">
                        Aveți întrebări sau sugestii? Vă stăm la dispoziție.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Breadcrumb -->
        <?php echo BreadcrumbHelper::renderBreadcrumbs('contact'); ?>

        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8">
                <div class="contact-form-container">
                    <h3 class="mb-4">
                        <i class="fas fa-paper-plane me-2"></i>
                        Trimiteți-ne un mesaj
                    </h3>

                    <form id="contactForm" method="POST" action="process_contact.php" novalidate>
                        <!-- CSRF Token -->
                        <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrfToken); ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="nume" class="form-label">
                                    Nume <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="nume" name="nume" required maxlength="100">
                                <div class="invalid-feedback">
                                    Vă rugăm să introduceți numele dumneavoastră.
                                </div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    Adresa email <span class="text-danger">*</span>
                                </label>
                                <input type="email" class="form-control" id="email" name="email" required maxlength="255">
                                <div class="invalid-feedback">
                                    Vă rugăm să introduceți o adresă de email validă.
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="telefon" class="form-label">
                                Număr telefon
                            </label>
                            <input type="tel" class="form-control" id="telefon" name="telefon" maxlength="20" placeholder="Ex: 0721234567">
                            <div class="invalid-feedback">
                                Vă rugăm să introduceți un număr de telefon valid.
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="mesaj" class="form-label">
                                Mesaj <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control" id="mesaj" name="mesaj" rows="6" required maxlength="2000" placeholder="Descrieți întrebarea sau problema dumneavoastră..."></textarea>
                            <div class="invalid-feedback">
                                Vă rugăm să introduceți mesajul dumneavoastră.
                            </div>
                            <div class="form-text">
                                <span id="caracterCount">0</span>/2000 caractere
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>
                                Trimite mesajul
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="col-lg-4">
                <div class="contact-info">
                    <h4>
                        <i class="fas fa-question-circle me-2"></i>
                        Întrebări frecvente
                    </h4>
                    <p><strong>Cum pot căuta un dosar?</strong></p>
                    <p class="small">Utilizați formularul de căutare de pe pagina principală introducând numărul dosarului sau numele părții, cu sau fara ghilimele.</p>
                    
                    <p><strong>De ce nu găsesc dosarul căutat?</strong></p>
                    <p class="small">Verificați dacă ați introdus corect datele și dacă dosarul este public conform legislației în vigoare.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <!-- Back to Top Button -->
    <button id="backToTopBtn" class="back-to-top" title="Mergi sus" aria-label="Mergi la începutul paginii">
        <i class="fas fa-chevron-up" aria-hidden="true"></i>
    </button>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Inițializăm funcționalitățile
            initContactForm();
            initCharacterCount();
            initBackToTop();

            // Afișăm mesajele flash dacă există
            <?php if (!empty($flashMessages)): ?>
                <?php foreach ($flashMessages as $type => $messages): ?>
                    <?php foreach ($messages as $message): ?>
                        showNotification('<?php echo addslashes($message); ?>', '<?php echo $type; ?>');
                    <?php endforeach; ?>
                <?php endforeach; ?>
            <?php endif; ?>
        });

        /**
         * Inițializează validarea și trimiterea formularului de contact
         */
        function initContactForm() {
            const form = document.getElementById('contactForm');
            const submitBtn = form.querySelector('button[type="submit"]');

            form.addEventListener('submit', function(e) {
                e.preventDefault();

                if (validateContactForm()) {
                    submitContactForm();
                }
            });

            // Validare în timp real
            const inputs = form.querySelectorAll('input, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    validateField(this);
                });

                input.addEventListener('input', function() {
                    if (this.classList.contains('is-invalid')) {
                        validateField(this);
                    }
                });
            });
        }

        /**
         * Validează întregul formular de contact
         */
        function validateContactForm() {
            const form = document.getElementById('contactForm');
            const inputs = form.querySelectorAll('input[required], textarea[required]');
            let isValid = true;

            inputs.forEach(input => {
                if (!validateField(input)) {
                    isValid = false;
                }
            });

            // Validare specifică pentru telefon dacă este completat
            const telefonInput = document.getElementById('telefon');
            if (telefonInput.value.trim() && !validateRomanianPhone(telefonInput.value.trim())) {
                telefonInput.classList.add('is-invalid');
                isValid = false;
            }

            return isValid;
        }

        /**
         * Validează un câmp individual
         */
        function validateField(field) {
            const value = field.value.trim();
            let isValid = true;

            // Verificare câmpuri obligatorii
            if (field.hasAttribute('required') && !value) {
                field.classList.add('is-invalid');
                field.classList.remove('is-valid');
                return false;
            }

            // Validare specifică pentru email
            if (field.type === 'email' && value) {
                if (!validateEmail(value)) {
                    field.classList.add('is-invalid');
                    field.classList.remove('is-valid');
                    return false;
                }
            }

            // Validare specifică pentru telefon
            if (field.id === 'telefon' && value) {
                if (!validateRomanianPhone(value)) {
                    field.classList.add('is-invalid');
                    field.classList.remove('is-valid');
                    return false;
                }
            }

            // Câmpul este valid
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
            return true;
        }

        /**
         * Trimite formularul de contact
         */
        function submitContactForm() {
            const form = document.getElementById('contactForm');
            const formData = new FormData(form);
            const loadingOverlay = document.getElementById('loadingOverlay');

            // Afișăm loading overlay
            loadingOverlay.style.display = 'flex';

            // Dezactivăm butonul de submit
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Se trimite...';

            fetch('process_contact.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loadingOverlay.style.display = 'none';
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;

                if (data.success) {
                    showNotification(data.message, 'success');
                    form.reset();

                    // Eliminăm clasele de validare
                    const inputs = form.querySelectorAll('input, textarea');
                    inputs.forEach(input => {
                        input.classList.remove('is-valid', 'is-invalid');
                    });

                    // Resetăm contorul de caractere
                    updateCharacterCount();
                } else {
                    showNotification(data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Eroare:', error);
                loadingOverlay.style.display = 'none';
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
                showNotification('A apărut o eroare la trimiterea mesajului. Vă rugăm să încercați din nou.', 'danger');
            });
        }

        /**
         * Inițializează contorul de caractere pentru textarea
         */
        function initCharacterCount() {
            const textarea = document.getElementById('mesaj');
            const counter = document.getElementById('caracterCount');

            textarea.addEventListener('input', updateCharacterCount);
            updateCharacterCount(); // Inițializare
        }

        /**
         * Actualizează contorul de caractere
         */
        function updateCharacterCount() {
            const textarea = document.getElementById('mesaj');
            const counter = document.getElementById('caracterCount');
            const currentLength = textarea.value.length;
            const maxLength = 2000;

            counter.textContent = currentLength;

            if (currentLength > maxLength * 0.9) {
                counter.style.color = '#dc3545'; // Roșu
            } else if (currentLength > maxLength * 0.7) {
                counter.style.color = '#ffc107'; // Galben
            } else {
                counter.style.color = '#6c757d'; // Gri
            }
        }

        /**
         * Validează o adresă de email
         */
        function validateEmail(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }

        /**
         * Validează un număr de telefon românesc
         */
        function validateRomanianPhone(phone) {
            // Eliminăm spațiile și caracterele speciale
            const cleanPhone = phone.replace(/[^0-9+]/g, '');

            // Verificăm formatele românești comune
            const patterns = [
                /^(\+40|0040|40)?[0-9]{9}$/,  // Format internațional
                /^0[0-9]{9}$/,                // Format național
                /^[0-9]{10}$/                 // 10 cifre
            ];

            return patterns.some(pattern => pattern.test(cleanPhone));
        }

        /**
         * Afișează o notificare
         */
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.getElementById('notification');

            // Setăm mesajul și tipul
            notification.innerHTML = message;
            notification.className = `alert alert-${type}`;

            // Afișăm notificarea
            container.style.display = 'block';

            // Ascundem automat după 5 secunde
            setTimeout(() => {
                container.style.display = 'none';
            }, 5000);
        }

        /**
         * Inițializează butonul "Back to Top"
         */
        function initBackToTop() {
            const backToTopBtn = document.getElementById('backToTopBtn');

            if (backToTopBtn) {
                window.addEventListener('scroll', function() {
                    if (window.pageYOffset > 300) {
                        backToTopBtn.style.display = 'block';
                    } else {
                        backToTopBtn.style.display = 'none';
                    }
                });

                backToTopBtn.addEventListener('click', function() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }
        }
    </script>
</body>
</html>
