<?php
/**
 * Test HTML Output
 * Verifică exact câte rânduri HTML sunt generate pentru părți
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

// Obținem datele
$dosarService = new \App\Services\DosarService();
$dosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');

if (!$dosar || empty($dosar->parti)) {
    echo "ERROR: Nu s-au putut obține datele dosarului";
    exit;
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Test HTML Output</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    #tabelParti { margin-top: 20px; }
    .table-responsive { max-height: none !important; overflow: visible !important; }
</style></head><body>";

echo "<h1>🔍 Test HTML Output pentru Părți</h1>";
echo "<p>Verifică exact câte rânduri HTML sunt generate</p>";
echo "<hr>";

echo "<div class='section'>";
echo "<h2>📊 Informații Backend</h2>";
echo "<p><strong>Total părți în backend:</strong> " . count($dosar->parti) . "</p>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>🔄 Generare HTML Identică cu detalii_dosar.php</h2>";

// Simulăm exact codul din detalii_dosar.php
$loop_index = 0;
$totalPartiCount = count($dosar->parti);
$html_rows = [];

echo "<div class='table-responsive'>";
echo "<table class='table table-striped' id='tabelParti'>";
echo "<thead>";
echo "<tr>";
echo "<th>Nume</th>";
echo "<th>Calitate</th>";
echo "<th>Informații suplimentare</th>";
echo "</tr>";
echo "</thead>";
echo "<tbody>";

foreach ($dosar->parti as $parteIndex => $parte) {
    $loop_index++;
    
    // Verificăm dacă partea este declaratoare în vreo cale de atac
    $esteDeclaratoare = false;
    $tipuriCaleAtac = [];
    
    if (!empty($dosar->caiAtac)) {
        foreach ($dosar->caiAtac as $caleAtac) {
            if (!empty($caleAtac->parteDeclaratoare) &&
                (stripos($parte->nume, $caleAtac->parteDeclaratoare) !== false ||
                stripos($caleAtac->parteDeclaratoare, $parte->nume) !== false)) {
                $esteDeclaratoare = true;
                if (!empty($caleAtac->tipCaleAtac) && !in_array($caleAtac->tipCaleAtac, $tipuriCaleAtac)) {
                    $tipuriCaleAtac[] = $caleAtac->tipCaleAtac;
                }
            }
        }
    }
    
    // Generăm HTML-ul exact ca în detalii_dosar.php
    $rowClass = $esteDeclaratoare ? 'table-info parte-row' : 'parte-row';
    
    echo "<tr class='{$rowClass}' ";
    echo "data-nume='" . htmlspecialchars($parte->nume) . "' ";
    echo "data-calitate='" . htmlspecialchars($parte->calitate ?? '') . "' ";
    echo "data-info='" . ($esteDeclaratoare ? 'parte_declaratoare' : '') . "' ";
    echo "data-index='{$loop_index}' ";
    echo "data-party-id='{$parteIndex}'>";
    
    echo "<td class='nume-parte' data-original-nume='" . htmlspecialchars($parte->nume) . "'>";
    echo htmlspecialchars($parte->nume);
    echo "</td>";
    
    echo "<td class='calitate-parte'>";
    echo !empty($parte->calitate) ? htmlspecialchars($parte->calitate) : '<span class="text-muted">-</span>';
    echo "</td>";
    
    echo "<td>";
    if ($esteDeclaratoare) {
        echo '<div class="badge bg-primary text-white p-2">Parte declaratoare</div>';
        if (!empty($tipuriCaleAtac)) {
            echo '<div class="small mt-1">';
            foreach ($tipuriCaleAtac as $tipIndex => $tip) {
                echo '<span class="badge bg-info text-white">' . htmlspecialchars($tip) . '</span>';
                echo ($tipIndex < count($tipuriCaleAtac) - 1) ? ' ' : '';
            }
            echo '</div>';
        }
    } else {
        echo '<span class="text-muted">-</span>';
    }
    echo "</td>";
    
    echo "</tr>";
    
    // Salvăm informațiile pentru analiza ulterioară
    $html_rows[] = [
        'index' => $loop_index,
        'party_index' => $parteIndex,
        'nume' => $parte->nume,
        'calitate' => $parte->calitate ?? '',
        'declaratoare' => $esteDeclaratoare
    ];
}

echo "</tbody>";
echo "</table>";
echo "</div>";

echo "<script>";
echo "// Numărăm rândurile generate în DOM";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    const table = document.getElementById('tabelParti');";
echo "    const rows = table.querySelectorAll('tbody tr.parte-row');";
echo "    const totalRows = rows.length;";
echo "    ";
echo "    console.log('Total rânduri în DOM:', totalRows);";
echo "    ";
echo "    // Afișăm rezultatul";
echo "    const resultDiv = document.getElementById('dom-count-result');";
echo "    if (resultDiv) {";
echo "        resultDiv.innerHTML = '<strong>Rânduri găsite în DOM: ' + totalRows + '</strong>';";
echo "        if (totalRows === {$totalPartiCount}) {";
echo "            resultDiv.className = 'success';";
echo "        } else {";
echo "            resultDiv.className = 'error';";
echo "        }";
echo "    }";
echo "    ";
echo "    // Verificăm dacă SARAGEA TUDORIŢA este în DOM";
echo "    let saragea_found = false;";
echo "    rows.forEach((row, index) => {";
echo "        const nume = row.getAttribute('data-nume') || '';";
echo "        if (nume.includes('SARAGEA') && nume.includes('TUDORI')) {";
echo "            saragea_found = true;";
echo "            console.log('SARAGEA TUDORIŢA găsită în DOM la poziția:', index + 1);";
echo "        }";
echo "    });";
echo "    ";
echo "    const saragea_result = document.getElementById('saragea-result');";
echo "    if (saragea_result) {";
echo "        if (saragea_found) {";
echo "            saragea_result.innerHTML = '<strong>✅ SARAGEA TUDORIŢA găsită în DOM</strong>';";
echo "            saragea_result.className = 'success';";
echo "        } else {";
echo "            saragea_result.innerHTML = '<strong>❌ SARAGEA TUDORIŢA NU găsită în DOM</strong>';";
echo "            saragea_result.className = 'error';";
echo "        }";
echo "    }";
echo "});";
echo "</script>";

echo "</div>";

echo "<div class='section'>";
echo "<h2>📊 Rezultate Analiză</h2>";

echo "<table>";
echo "<tr><th>Metric</th><th>Valoare</th></tr>";
echo "<tr><td>Părți în backend</td><td>{$totalPartiCount}</td></tr>";
echo "<tr><td>Rânduri HTML generate (PHP)</td><td>{$loop_index}</td></tr>";
echo "<tr><td>Rânduri în DOM (JavaScript)</td><td><div id='dom-count-result'>Se calculează...</div></td></tr>";
echo "</table>";

echo "<h3>🔍 Verificări Specifice</h3>";
echo "<p><div id='saragea-result'>Se verifică...</div></p>";

if ($loop_index === $totalPartiCount) {
    echo "<p class='success'>✅ PHP a generat toate rândurile ({$loop_index}/{$totalPartiCount})</p>";
} else {
    echo "<p class='error'>❌ PHP a generat doar {$loop_index} din {$totalPartiCount} rânduri</p>";
}

// Verificăm dacă SARAGEA TUDORIŢA este în lista generată
$saragea_in_php = false;
foreach ($html_rows as $row) {
    if (stripos($row['nume'], 'SARAGEA') !== false && stripos($row['nume'], 'TUDORI') !== false) {
        $saragea_in_php = true;
        echo "<p class='success'>✅ SARAGEA TUDORIŢA găsită în HTML generat de PHP la poziția {$row['index']}</p>";
        break;
    }
}

if (!$saragea_in_php) {
    echo "<p class='error'>❌ SARAGEA TUDORIŢA NU găsită în HTML generat de PHP</p>";
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>🔍 Primele 10 Părți Generate</h2>";
echo "<table>";
echo "<tr><th>#</th><th>Nume</th><th>Calitate</th><th>Declaratoare</th></tr>";

$display_limit = min(10, count($html_rows));
for ($i = 0; $i < $display_limit; $i++) {
    $row = $html_rows[$i];
    echo "<tr>";
    echo "<td>{$row['index']}</td>";
    echo "<td>" . htmlspecialchars($row['nume']) . "</td>";
    echo "<td>" . htmlspecialchars($row['calitate']) . "</td>";
    echo "<td>" . ($row['declaratoare'] ? 'Da' : 'Nu') . "</td>";
    echo "</tr>";
}

if (count($html_rows) > 10) {
    echo "<tr><td colspan='4' class='info'>... și încă " . (count($html_rows) - 10) . " părți</td></tr>";
}

echo "</table>";
echo "</div>";

echo "<hr>";
echo "<p><em>Test completat la " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
