<?php
/**
 * Script de debug pentru funcționalitatea Expandează/Restrânge Toate
 * Verifică dacă funcțiile JavaScript sunt definite corect în index.php
 */

// Simulăm o căutare pentru a avea rezultate
$_POST['searchTerms'] = "test\nSaragea";
$_POST['searchType'] = 'auto';

// Include index.php pentru a obține rezultatele
ob_start();
include 'index.php';
$indexContent = ob_get_clean();

// Analizăm conținutul pentru funcțiile JavaScript
$jsPatterns = [
    'expandAllResults' => '/function\s+expandAllResults\s*\(\s*\)\s*\{/',
    'collapseAllResults' => '/function\s+collapseAllResults\s*\(\s*\)\s*\{/',
    'toggleTermResults' => '/function\s+toggleTermResults\s*\(\s*\w+\s*\)\s*\{/',
    'showNotification' => '/function\s+showNotification\s*\(\s*\w+.*?\)\s*\{/'
];

$buttonPatterns = [
    'expandButton' => '/onclick\s*=\s*["\']expandAllResults\(\)["\']/',
    'collapseButton' => '/onclick\s*=\s*["\']collapseAllResults\(\)["\']/',
    'toggleButton' => '/onclick\s*=\s*["\']toggleTermResults\(\s*\d+\s*\)["\']/'
];

$elementPatterns = [
    'termContent' => '/id\s*=\s*["\']termContent\d+["\']/',
    'toggleIcon' => '/id\s*=\s*["\']toggleIcon\d+["\']/'
];

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Expandează/Restrânge - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status-success { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-danger { color: #dc3545; }
        
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .pattern-match {
            background: #d4edda;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-bug me-2"></i>
            Debug Funcționalitate Expandează/Restrânge Toate
        </h1>
        
        <!-- JavaScript Functions Analysis -->
        <div class="debug-section">
            <h3><i class="fas fa-code me-2"></i>Analiza Funcții JavaScript</h3>
            
            <?php foreach ($jsPatterns as $funcName => $pattern): ?>
                <?php 
                $matches = preg_match($pattern, $indexContent);
                $status = $matches ? 'success' : 'danger';
                $icon = $matches ? 'fa-check' : 'fa-times';
                ?>
                <p>
                    <i class="fas <?php echo $icon; ?> status-<?php echo $status; ?> me-2"></i>
                    <strong><?php echo $funcName; ?>:</strong> 
                    <span class="status-<?php echo $status; ?>">
                        <?php echo $matches ? 'Găsită' : 'Nu a fost găsită'; ?>
                    </span>
                </p>
                
                <?php if ($matches): ?>
                    <?php
                    // Extract function content
                    preg_match($pattern . '.*?(?=function\s+\w+|$)/s', $indexContent, $funcMatches);
                    if (!empty($funcMatches[0])) {
                        $funcContent = substr($funcMatches[0], 0, 500) . (strlen($funcMatches[0]) > 500 ? '...' : '');
                        echo '<div class="code-snippet">' . htmlspecialchars($funcContent) . '</div>';
                    }
                    ?>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        
        <!-- Button Analysis -->
        <div class="debug-section">
            <h3><i class="fas fa-mouse-pointer me-2"></i>Analiza Butoane</h3>
            
            <?php foreach ($buttonPatterns as $buttonName => $pattern): ?>
                <?php 
                $matches = preg_match_all($pattern, $indexContent, $buttonMatches);
                $status = $matches > 0 ? 'success' : 'danger';
                $icon = $matches > 0 ? 'fa-check' : 'fa-times';
                ?>
                <p>
                    <i class="fas <?php echo $icon; ?> status-<?php echo $status; ?> me-2"></i>
                    <strong><?php echo $buttonName; ?>:</strong> 
                    <span class="status-<?php echo $status; ?>">
                        <?php echo $matches > 0 ? "Găsite {$matches} instanțe" : 'Nu a fost găsit'; ?>
                    </span>
                </p>
                
                <?php if ($matches > 0): ?>
                    <div class="code-snippet">
                        <?php foreach ($buttonMatches[0] as $match): ?>
                            <div class="pattern-match"><?php echo htmlspecialchars($match); ?></div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        
        <!-- DOM Elements Analysis -->
        <div class="debug-section">
            <h3><i class="fas fa-sitemap me-2"></i>Analiza Elemente DOM</h3>
            
            <?php foreach ($elementPatterns as $elementName => $pattern): ?>
                <?php 
                $matches = preg_match_all($pattern, $indexContent, $elementMatches);
                $status = $matches > 0 ? 'success' : 'warning';
                $icon = $matches > 0 ? 'fa-check' : 'fa-exclamation-triangle';
                ?>
                <p>
                    <i class="fas <?php echo $icon; ?> status-<?php echo $status; ?> me-2"></i>
                    <strong><?php echo $elementName; ?>:</strong> 
                    <span class="status-<?php echo $status; ?>">
                        <?php echo $matches > 0 ? "Găsite {$matches} elemente" : 'Nu au fost găsite elemente'; ?>
                    </span>
                </p>
                
                <?php if ($matches > 0): ?>
                    <div class="code-snippet">
                        <?php foreach (array_slice($elementMatches[0], 0, 5) as $match): ?>
                            <div class="pattern-match"><?php echo htmlspecialchars($match); ?></div>
                        <?php endforeach; ?>
                        <?php if (count($elementMatches[0]) > 5): ?>
                            <div class="text-muted">... și încă <?php echo count($elementMatches[0]) - 5; ?> elemente</div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        
        <!-- Search Results Check -->
        <div class="debug-section">
            <h3><i class="fas fa-search me-2"></i>Verificare Rezultate Căutare</h3>
            
            <?php
            $hasSearchResults = strpos($indexContent, 'searchResults') !== false;
            $hasTermResults = strpos($indexContent, 'term-results') !== false;
            $hasResultsSection = strpos($indexContent, 'Rezultate detaliate') !== false;
            ?>
            
            <p>
                <i class="fas <?php echo $hasSearchResults ? 'fa-check status-success' : 'fa-times status-danger'; ?> me-2"></i>
                <strong>Variabila searchResults:</strong> 
                <span class="status-<?php echo $hasSearchResults ? 'success' : 'danger'; ?>">
                    <?php echo $hasSearchResults ? 'Prezentă' : 'Absentă'; ?>
                </span>
            </p>
            
            <p>
                <i class="fas <?php echo $hasTermResults ? 'fa-check status-success' : 'fa-times status-danger'; ?> me-2"></i>
                <strong>Secțiuni term-results:</strong> 
                <span class="status-<?php echo $hasTermResults ? 'success' : 'danger'; ?>">
                    <?php echo $hasTermResults ? 'Prezente' : 'Absente'; ?>
                </span>
            </p>
            
            <p>
                <i class="fas <?php echo $hasResultsSection ? 'fa-check status-success' : 'fa-times status-danger'; ?> me-2"></i>
                <strong>Secțiunea "Rezultate detaliate":</strong> 
                <span class="status-<?php echo $hasResultsSection ? 'success' : 'danger'; ?>">
                    <?php echo $hasResultsSection ? 'Prezentă' : 'Absentă'; ?>
                </span>
            </p>
        </div>
        
        <!-- JavaScript Errors Check -->
        <div class="debug-section">
            <h3><i class="fas fa-exclamation-triangle me-2"></i>Verificare Erori JavaScript</h3>
            
            <?php
            // Check for common JavaScript issues
            $jsIssues = [];
            
            // Check for syntax errors in function definitions
            if (preg_match('/function\s+\w+\s*\([^)]*\)\s*\{[^}]*\}\s*function/', $indexContent)) {
                $jsIssues[] = 'Posibile funcții JavaScript concatenate incorect';
            }
            
            // Check for missing semicolons
            if (preg_match('/\}\s*(?!;|\s*function|\s*var|\s*let|\s*const|\s*if|\s*for|\s*while|\s*$)/', $indexContent)) {
                $jsIssues[] = 'Posibile puncte și virgule lipsă după funcții';
            }
            
            // Check for undefined variables
            if (preg_match('/console\.log\([^)]*undefined[^)]*\)/', $indexContent)) {
                $jsIssues[] = 'Variabile nedefinite detectate în console.log';
            }
            ?>
            
            <?php if (empty($jsIssues)): ?>
                <p class="status-success">
                    <i class="fas fa-check me-2"></i>
                    Nu au fost detectate probleme evidente în codul JavaScript
                </p>
            <?php else: ?>
                <?php foreach ($jsIssues as $issue): ?>
                    <p class="status-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($issue); ?>
                    </p>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <!-- Recommendations -->
        <div class="debug-section">
            <h3><i class="fas fa-lightbulb me-2"></i>Recomandări</h3>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>Pentru a testa funcționalitatea:</h5>
                <ol>
                    <li>Accesați <a href="index.php" target="_blank">pagina principală</a></li>
                    <li>Efectuați o căutare cu mai mulți termeni (ex: "test" pe o linie și "Saragea" pe alta)</li>
                    <li>Verificați dacă apar butoanele "Expandează toate" și "Restrânge toate"</li>
                    <li>Testați funcționalitatea butoanelor</li>
                    <li>Verificați consola browser-ului pentru erori JavaScript (F12)</li>
                </ol>
            </div>
            
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>Probleme posibile:</h5>
                <ul>
                    <li>Funcțiile JavaScript nu sunt definite în scope-ul global</li>
                    <li>Elementele DOM nu sunt generate când nu există rezultate de căutare</li>
                    <li>Conflicte între diferite versiuni ale funcțiilor</li>
                    <li>Erori de sintaxă JavaScript care blochează execuția</li>
                </ul>
            </div>
        </div>
        
        <!-- Quick Test Links -->
        <div class="debug-section">
            <h3><i class="fas fa-external-link-alt me-2"></i>Link-uri Test Rapid</h3>
            
            <div class="row">
                <div class="col-md-4">
                    <a href="test_expand_collapse_menu.php" class="btn btn-primary w-100 mb-2" target="_blank">
                        <i class="fas fa-vial me-2"></i>
                        Test Izolat
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="index.php" class="btn btn-secondary w-100 mb-2" target="_blank">
                        <i class="fas fa-home me-2"></i>
                        Pagina Principală
                    </a>
                </div>
                <div class="col-md-4">
                    <button type="button" class="btn btn-info w-100 mb-2" onclick="window.open('index.php', '_blank'); setTimeout(() => { alert('Efectuați o căutare cu mai mulți termeni pentru a vedea butoanele de expandare!'); }, 1000);">
                        <i class="fas fa-search me-2"></i>
                        Test cu Căutare
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
