<?php
require_once dirname(__DIR__) . '/bootstrap.php';
use App\Config\Database;

try {
    echo "Checking users table structure:\n";
    echo "==============================\n\n";
    
    $result = Database::fetchAll('DESCRIBE users');
    
    foreach ($result as $column) {
        echo sprintf("%-20s %-15s %-10s %-10s %-10s %s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'], 
            $column['Key'], 
            $column['Default'] ?? 'NULL',
            $column['Extra'] ?? ''
        );
    }
    
    echo "\n\nChecking existing foreign keys:\n";
    echo "===============================\n";
    
    $fks = Database::fetchAll("
        SELECT 
            CONSTRAINT_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    
    if (empty($fks)) {
        echo "No foreign keys found on users table.\n";
    } else {
        foreach ($fks as $fk) {
            echo sprintf("%-30s %-15s -> %s.%s\n",
                $fk['CONSTRAINT_NAME'],
                $fk['COLUMN_NAME'],
                $fk['REFERENCED_TABLE_NAME'],
                $fk['REFERENCED_COLUMN_NAME']
            );
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
