<?php
// Test search functionality for debugging
require_once 'includes/DosarService.php';

// Test the search term parsing
function parseBulkSearchTerms($input) {
    // Copy the function from index.php
    $input = str_replace(',', "\n", $input);
    $terms = explode("\n", $input);
    $cleanTerms = [];

    foreach ($terms as $term) {
        $term = trim($term);
        if (!empty($term) && strlen($term) >= 2) {
            $cleanTerms[] = [
                'term' => $term,
                'type' => detectSearchType($term)
            ];
        }
    }

    $uniqueTerms = [];
    $seenTerms = [];

    foreach ($cleanTerms as $termData) {
        $termKey = strtolower($termData['term']);
        if (!in_array($termKey, $seenTerms)) {
            $uniqueTerms[] = $termData;
            $seenTerms[] = $termKey;
        }
    }

    return $uniqueTerms;
}

function detectSearchType($term) {
    $cleanTerm = trim($term, '"\'');
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
        return 'numarDosar';
    }
    
    return 'numeParte';
}

// Test the search term
$searchTerm = "130/98/2022";
echo "<h2>Testing Search Term: $searchTerm</h2>";

$parsedTerms = parseBulkSearchTerms($searchTerm);
echo "<h3>Parsed Terms:</h3>";
echo "<pre>" . print_r($parsedTerms, true) . "</pre>";

// Test the actual search
try {
    $dosarService = new DosarService();
    
    $searchParams = [
        'numarDosar' => $searchTerm,
        'institutie' => null,
        'numeParte' => '',
        'obiectDosar' => '',
        'dataStart' => '',
        'dataStop' => '',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
        'categorieInstanta' => '',
        'categorieCaz' => ''
    ];
    
    echo "<h3>Search Parameters:</h3>";
    echo "<pre>" . print_r($searchParams, true) . "</pre>";
    
    $results = $dosarService->cautareAvansata($searchParams);
    
    echo "<h3>Search Results:</h3>";
    echo "<p>Number of results: " . count($results) . "</p>";
    
    if (!empty($results)) {
        echo "<h4>First Result:</h4>";
        echo "<pre>" . print_r($results[0], true) . "</pre>";
    } else {
        echo "<p><strong>No results found!</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Test with TribunalulIALOMITA specifically
echo "<hr><h2>Testing with TribunalulIALOMITA Institution</h2>";

try {
    $searchParams['institutie'] = 'TribunalulIALOMITA';
    
    echo "<h3>Search Parameters with Institution:</h3>";
    echo "<pre>" . print_r($searchParams, true) . "</pre>";
    
    $results = $dosarService->cautareAvansata($searchParams);
    
    echo "<h3>Search Results with Institution:</h3>";
    echo "<p>Number of results: " . count($results) . "</p>";
    
    if (!empty($results)) {
        echo "<h4>First Result:</h4>";
        echo "<pre>" . print_r($results[0], true) . "</pre>";
    } else {
        echo "<p><strong>No results found with institution filter!</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error with Institution:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>
