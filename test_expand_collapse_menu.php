<?php
/**
 * Test pentru funcționalitatea "Expandează Toate" / "Restrânge Toate"
 * Verifică dacă butoanele și funcțiile JavaScript funcționează corect
 */
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Expandează/Restrânge Toate - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .term-header {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .term-header:hover {
            background: #e9ecef;
            border-color: #007bff;
        }
        
        .term-content {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .toggle-icon {
            transition: transform 0.3s ease;
            color: #007bff;
        }
        
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-danger { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-expand-arrows-alt me-2"></i>
            Test Funcționalitate Expandează/Restrânge Toate
        </h1>
        
        <!-- Test Status -->
        <div class="test-section">
            <h3><i class="fas fa-clipboard-check me-2"></i>Status Test</h3>
            <div id="testStatus">
                <p><span class="status-indicator status-warning"></span>Inițializare test...</p>
            </div>
        </div>
        
        <!-- Control Buttons -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Rezultate Test (3 secțiuni)
            </h5>
            <div>
                <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="expandAllResults()">
                    <i class="fas fa-expand-alt me-1"></i>
                    Expandează toate
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAllResults()">
                    <i class="fas fa-compress-alt me-1"></i>
                    Restrânge toate
                </button>
                <button type="button" class="btn btn-sm btn-info ms-2" onclick="runDiagnostics()">
                    <i class="fas fa-stethoscope me-1"></i>
                    Diagnosticare
                </button>
            </div>
        </div>
        
        <!-- Test Sections -->
        <?php for ($i = 0; $i < 3; $i++): ?>
        <div class="term-results">
            <div class="term-header" onclick="toggleTermResults(<?php echo $i; ?>)">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">
                            <i class="fas fa-search me-2"></i>
                            Termen de căutare <?php echo $i + 1; ?>
                        </h6>
                        <small class="text-muted">
                            <i class="fas fa-file-alt me-1"></i>
                            Rezultate găsite: <?php echo rand(5, 25); ?>
                        </small>
                    </div>
                    <div>
                        <i class="fas fa-chevron-down toggle-icon" id="toggleIcon<?php echo $i; ?>"></i>
                    </div>
                </div>
            </div>
            
            <div class="term-content" id="termContent<?php echo $i; ?>" style="display: none;">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Nr. Dosar</th>
                                <th>Instanța</th>
                                <th>Obiect</th>
                                <th>Stadiu</th>
                                <th>Data</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php for ($j = 0; $j < 3; $j++): ?>
                            <tr>
                                <td><?php echo rand(1000, 9999) . '/' . rand(1, 999) . '/2024'; ?></td>
                                <td>Judecătoria Test <?php echo $i + 1; ?></td>
                                <td>Obiect test <?php echo $j + 1; ?></td>
                                <td>În curs</td>
                                <td><?php echo date('d.m.Y', strtotime('-' . rand(1, 365) . ' days')); ?></td>
                            </tr>
                            <?php endfor; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endfor; ?>
        
        <!-- Diagnostic Results -->
        <div class="test-section">
            <h3><i class="fas fa-bug me-2"></i>Rezultate Diagnosticare</h3>
            <div id="diagnosticResults">
                <p class="text-muted">Apăsați butonul "Diagnosticare" pentru a rula testele...</p>
            </div>
        </div>
        
        <!-- Console Log -->
        <div class="test-section">
            <h3><i class="fas fa-terminal me-2"></i>Console Log</h3>
            <div id="consoleLog" style="background: #000; color: #0f0; padding: 15px; border-radius: 4px; font-family: monospace; height: 200px; overflow-y: auto;">
                <div>Portal Judiciar Test Console - Ready</div>
            </div>
        </div>
    </div>
    
    <!-- Notification Container -->
    <div id="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 1050;"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Console logging function
        function logToConsole(message, type = 'info') {
            const consoleLog = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            const colorMap = {
                'info': '#0f0',
                'warning': '#ff0',
                'error': '#f00',
                'success': '#0f0'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.color = colorMap[type] || '#0f0';
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            consoleLog.appendChild(logEntry);
            consoleLog.scrollTop = consoleLog.scrollHeight;
        }
        
        // Notification system
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            
            const typeClasses = {
                'success': 'alert-success',
                'danger': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            };
            
            const icons = {
                'success': 'fa-check-circle',
                'danger': 'fa-exclamation-triangle',
                'warning': 'fa-exclamation-circle',
                'info': 'fa-info-circle'
            };
            
            notification.className = `alert ${typeClasses[type]} alert-dismissible fade show`;
            notification.innerHTML = `
                <i class="fas ${icons[type]} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            container.appendChild(notification);
            logToConsole(`Notification: ${message}`, type);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
        
        // Toggle individual term results
        function toggleTermResults(index) {
            try {
                const content = document.getElementById('termContent' + index);
                const icon = document.getElementById('toggleIcon' + index);
                
                logToConsole(`Toggling term results for index: ${index}`);
                
                if (!content) {
                    logToConsole(`ERROR: Content element not found for index: ${index}`, 'error');
                    showNotification('Eroare: Secțiunea nu a fost găsită.', 'danger');
                    return;
                }
                
                if (!icon) {
                    logToConsole(`WARNING: Icon element not found for index: ${index}`, 'warning');
                }
                
                const isVisible = content.style.display !== 'none';
                
                if (isVisible) {
                    content.style.display = 'none';
                    if (icon) icon.className = 'fas fa-chevron-down toggle-icon';
                    logToConsole(`Collapsed section ${index}`, 'success');
                } else {
                    content.style.display = 'block';
                    if (icon) icon.className = 'fas fa-chevron-up toggle-icon';
                    logToConsole(`Expanded section ${index}`, 'success');
                }
                
            } catch (error) {
                logToConsole(`ERROR in toggleTermResults: ${error.message}`, 'error');
                showNotification('Eroare la comutarea secțiunii.', 'danger');
            }
        }
        
        // Expand all results function
        function expandAllResults() {
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
                
                logToConsole(`Expanding all results - found ${termContents.length} content elements and ${toggleIcons.length} icon elements`);
                
                if (termContents.length === 0) {
                    showNotification('Nu există secțiuni de rezultate pentru expandare.', 'warning');
                    return;
                }
                
                termContents.forEach(content => {
                    content.style.display = 'block';
                });
                
                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-up toggle-icon';
                });
                
                showNotification('Toate secțiunile au fost expandate.', 'info');
                logToConsole(`Successfully expanded ${termContents.length} sections`, 'success');
                
            } catch (error) {
                logToConsole(`ERROR in expandAllResults: ${error.message}`, 'error');
                showNotification('Eroare la expandarea rezultatelor.', 'danger');
            }
        }
        
        // Collapse all results function
        function collapseAllResults() {
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
                
                logToConsole(`Collapsing all results - found ${termContents.length} content elements and ${toggleIcons.length} icon elements`);
                
                if (termContents.length === 0) {
                    showNotification('Nu există secțiuni de rezultate pentru restrângere.', 'warning');
                    return;
                }
                
                termContents.forEach(content => {
                    content.style.display = 'none';
                });
                
                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-down toggle-icon';
                });
                
                showNotification('Toate secțiunile au fost restrânse.', 'info');
                logToConsole(`Successfully collapsed ${termContents.length} sections`, 'success');
                
            } catch (error) {
                logToConsole(`ERROR in collapseAllResults: ${error.message}`, 'error');
                showNotification('Eroare la restrângerea rezultatelor.', 'danger');
            }
        }
        
        // Run comprehensive diagnostics
        function runDiagnostics() {
            logToConsole('Starting comprehensive diagnostics...', 'info');
            
            const diagnosticResults = document.getElementById('diagnosticResults');
            let results = '<h4>Rezultate Diagnosticare:</h4>';
            
            // Test 1: Check if functions exist
            const functions = ['expandAllResults', 'collapseAllResults', 'toggleTermResults'];
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    results += `<p><span class="status-indicator status-success"></span>Funcția <code>${func}</code> există și este accesibilă</p>`;
                    logToConsole(`✓ Function ${func} exists`, 'success');
                } else {
                    results += `<p><span class="status-indicator status-danger"></span>Funcția <code>${func}</code> nu există sau nu este accesibilă</p>`;
                    logToConsole(`✗ Function ${func} missing`, 'error');
                }
            });
            
            // Test 2: Check DOM elements
            const termContents = document.querySelectorAll('[id^="termContent"]');
            const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
            
            results += `<p><span class="status-indicator status-${termContents.length > 0 ? 'success' : 'danger'}"></span>Elemente termContent găsite: ${termContents.length}</p>`;
            results += `<p><span class="status-indicator status-${toggleIcons.length > 0 ? 'success' : 'danger'}"></span>Elemente toggleIcon găsite: ${toggleIcons.length}</p>`;
            
            logToConsole(`Found ${termContents.length} termContent elements`, termContents.length > 0 ? 'success' : 'error');
            logToConsole(`Found ${toggleIcons.length} toggleIcon elements`, toggleIcons.length > 0 ? 'success' : 'error');
            
            // Test 3: Check button onclick handlers
            const expandBtn = document.querySelector('button[onclick="expandAllResults()"]');
            const collapseBtn = document.querySelector('button[onclick="collapseAllResults()"]');
            
            results += `<p><span class="status-indicator status-${expandBtn ? 'success' : 'danger'}"></span>Buton "Expandează toate": ${expandBtn ? 'Găsit' : 'Nu a fost găsit'}</p>`;
            results += `<p><span class="status-indicator status-${collapseBtn ? 'success' : 'danger'}"></span>Buton "Restrânge toate": ${collapseBtn ? 'Găsit' : 'Nu a fost găsit'}</p>`;
            
            // Test 4: Test actual functionality
            try {
                expandAllResults();
                results += `<p><span class="status-indicator status-success"></span>Test expandAllResults(): Executat cu succes</p>`;
                
                setTimeout(() => {
                    try {
                        collapseAllResults();
                        results += `<p><span class="status-indicator status-success"></span>Test collapseAllResults(): Executat cu succes</p>`;
                        diagnosticResults.innerHTML = results;
                    } catch (error) {
                        results += `<p><span class="status-indicator status-danger"></span>Test collapseAllResults(): Eroare - ${error.message}</p>`;
                        diagnosticResults.innerHTML = results;
                    }
                }, 1000);
                
            } catch (error) {
                results += `<p><span class="status-indicator status-danger"></span>Test expandAllResults(): Eroare - ${error.message}</p>`;
            }
            
            diagnosticResults.innerHTML = results;
            logToConsole('Diagnostics completed', 'success');
        }
        
        // Initialize test
        document.addEventListener('DOMContentLoaded', function() {
            logToConsole('DOM loaded, initializing test...', 'info');
            
            const testStatus = document.getElementById('testStatus');
            testStatus.innerHTML = '<p><span class="status-indicator status-success"></span>Test inițializat cu succes. Toate funcțiile sunt disponibile.</p>';
            
            logToConsole('Test initialization complete', 'success');
            showNotification('Test de funcționalitate inițializat cu succes!', 'success');
        });
    </script>
</body>
</html>
