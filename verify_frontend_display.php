<?php
/**
 * Verify frontend displays all parties correctly
 */

require_once 'config/config.php';
require_once 'services/DosarService.php';

$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "=== FRONTEND DISPLAY VERIFICATION ===" . PHP_EOL;
echo "Case: $numarDosar" . PHP_EOL;
echo "Institution: $institutie" . PHP_EOL;
echo PHP_EOL;

try {
    // Get case details (same as frontend)
    $dosarService = new DosarService();
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if (!$dosar) {
        echo "❌ ERROR: Could not retrieve case details" . PHP_EOL;
        exit;
    }
    
    echo "=== BACKEND DATA VERIFICATION ===" . PHP_EOL;
    echo "Total parties in backend: " . count($dosar->parti) . PHP_EOL;
    echo PHP_EOL;
    
    // Simulate frontend HTML generation
    echo "=== SIMULATING FRONTEND HTML GENERATION ===" . PHP_EOL;
    
    $htmlRows = 0;
    $partiCounter = 0;
    
    // Header row
    $htmlRows++;
    
    // Party rows (simulate the foreach loop in detalii_dosar.php)
    foreach ($dosar->parti as $index => $parte) {
        $htmlRows++;
        $partiCounter++;
    }
    
    echo "HTML table rows generated: $htmlRows" . PHP_EOL;
    echo "Party rows generated: $partiCounter" . PHP_EOL;
    echo "Expected party rows: " . count($dosar->parti) . PHP_EOL;
    echo PHP_EOL;
    
    // Verification
    if ($partiCounter == count($dosar->parti)) {
        echo "✅ SUCCESS: Frontend should display all parties correctly" . PHP_EOL;
        echo "Backend provides: " . count($dosar->parti) . " parties" . PHP_EOL;
        echo "Frontend generates: $partiCounter party rows" . PHP_EOL;
    } else {
        echo "❌ ERROR: Mismatch between backend and frontend" . PHP_EOL;
        echo "Backend: " . count($dosar->parti) . " parties" . PHP_EOL;
        echo "Frontend: $partiCounter party rows" . PHP_EOL;
    }
    
    echo PHP_EOL;
    
    // Test search functionality with enhanced dataset
    echo "=== SEARCH FUNCTIONALITY TEST ===" . PHP_EOL;
    
    // Test search for a party that should be in the enhanced data
    $testSearchTerms = [
        'SARAGEA TUDORIŢA', // This was the originally missing party
        'ZAMFIR NICOLETA',  // Last party in decision text
        'ŞERBĂNESCU ELENA', // First party from SOAP API
        'MILITARU OLIMPIA'  // Party from decision text
    ];
    
    foreach ($testSearchTerms as $searchTerm) {
        $found = false;
        foreach ($dosar->parti as $parte) {
            if (stripos($parte['nume'], $searchTerm) !== false) {
                $found = true;
                break;
            }
        }
        
        echo "Search test '$searchTerm': " . ($found ? "✅ FOUND" : "❌ NOT FOUND") . PHP_EOL;
    }
    
    echo PHP_EOL;
    
    // Performance analysis
    echo "=== PERFORMANCE ANALYSIS ===" . PHP_EOL;
    echo "Dataset size: " . count($dosar->parti) . " parties" . PHP_EOL;
    echo "Estimated frontend load time: " . (count($dosar->parti) * 0.1) . "ms (JavaScript processing)" . PHP_EOL;
    echo "Search index size: ~" . (count($dosar->parti) * 50) . " bytes" . PHP_EOL;
    
    if (count($dosar->parti) > 300) {
        echo "⚠ RECOMMENDATION: Consider implementing pagination for very large datasets" . PHP_EOL;
    } else {
        echo "✅ PERFORMANCE: Dataset size is manageable for single-page display" . PHP_EOL;
    }
    
    echo PHP_EOL;
    
    // Data quality check
    echo "=== DATA QUALITY CHECK ===" . PHP_EOL;
    
    $emptyNames = 0;
    $longNames = 0;
    $duplicateCheck = [];
    $duplicates = 0;
    
    foreach ($dosar->parti as $parte) {
        $nume = trim($parte['nume']);
        
        if (empty($nume)) {
            $emptyNames++;
        }
        
        if (strlen($nume) > 100) {
            $longNames++;
        }
        
        $normalizedName = strtolower($nume);
        if (isset($duplicateCheck[$normalizedName])) {
            $duplicates++;
        } else {
            $duplicateCheck[$normalizedName] = true;
        }
    }
    
    echo "Empty names: $emptyNames" . PHP_EOL;
    echo "Very long names (>100 chars): $longNames" . PHP_EOL;
    echo "Potential duplicates: $duplicates" . PHP_EOL;
    
    if ($emptyNames == 0 && $longNames == 0 && $duplicates == 0) {
        echo "✅ DATA QUALITY: Excellent" . PHP_EOL;
    } else {
        echo "⚠ DATA QUALITY: Issues detected" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
}

echo PHP_EOL . "=== VERIFICATION COMPLETE ===" . PHP_EOL;
?>
