<?php
/**
 * Portal Judiciar - Gmail Setup Wizard
 * 
 * Ghid pas cu pas pentru configurarea Gmail SMTP
 */

// Încărcăm bootstrap-ul aplicației
require_once 'bootstrap.php';

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\SMTP;

// Inițializăm sesiunea
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$currentConfig = [
    'host' => SMTP_HOST,
    'port' => SMTP_PORT,
    'username' => SMTP_USERNAME,
    'password' => SMTP_PASSWORD,
    'contact_email' => CONTACT_EMAIL
];

// Verificăm dacă parola pare să fie o parolă de aplicație Gmail
$isAppPassword = preg_match('/^[a-z]{16}$/', strtolower(str_replace(' ', '', SMTP_PASSWORD)));
$isRegularPassword = !$isAppPassword && strlen(SMTP_PASSWORD) > 6;

$testResult = null;
$debugOutput = [];

// Test SMTP cu credentiale custom dacă formularul a fost trimis
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_smtp'])) {
    $testUsername = $_POST['test_username'] ?? SMTP_USERNAME;
    $testPassword = $_POST['test_password'] ?? SMTP_PASSWORD;
    try {
        $mail = new PHPMailer(true);
        
        // Capturăm debug output
        $mail->SMTPDebug = SMTP::DEBUG_CONNECTION;
        $mail->Debugoutput = function($str, $level) use (&$debugOutput) {
            $debugOutput[] = trim($str);
        };
        
        // Configurăm SMTP
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = $testUsername;
        $mail->Password = $testPassword;
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = SMTP_PORT;
        $mail->Timeout = 10;
        
        // Testăm doar conexiunea
        $mail->smtpConnect();
        $testResult = [
            'success' => true,
            'message' => 'Conexiunea SMTP a fost stabilită cu succes!'
        ];
        $mail->smtpClose();
        
    } catch (Exception $e) {
        $testResult = [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
}

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Setup Wizard - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .wizard-container {
            max-width: 900px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .step-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            padding: 1.5rem;
        }
        .step-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .step-number {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
        .step-number.success {
            background: #28a745;
        }
        .step-number.warning {
            background: #ffc107;
        }
        .step-number.error {
            background: #dc3545;
        }
        .config-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
        .password-analysis {
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        .password-regular {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .password-app {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .debug-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .instruction-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .instruction-box h5 {
            color: white;
            margin-bottom: 1rem;
        }
        .instruction-box ol {
            margin-bottom: 0;
        }
        .instruction-box ol li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="wizard-container">
        <div class="text-center mb-4">
            <h1 class="display-5">
                <i class="fab fa-google me-2"></i>
                Gmail Setup Wizard
            </h1>
            <p class="lead">Configurare pas cu pas pentru SMTP Gmail</p>
        </div>

        <!-- Step 1: Current Configuration Analysis -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number <?php echo $isAppPassword ? 'success' : 'warning'; ?>">1</div>
                <h3>Analiza Configurației Curente</h3>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>Configurația ta actuală:</h5>
                    <div class="config-display">
SMTP_HOST: <?php echo htmlspecialchars($currentConfig['host']); ?>
SMTP_PORT: <?php echo htmlspecialchars($currentConfig['port']); ?>
SMTP_USERNAME: <?php echo htmlspecialchars($currentConfig['username']); ?>
SMTP_PASSWORD: <?php echo str_repeat('*', strlen($currentConfig['password'])); ?>
CONTACT_EMAIL: <?php echo htmlspecialchars($currentConfig['contact_email']); ?>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Analiza parolei:</h5>
                    <?php if ($isAppPassword): ?>
                        <div class="password-analysis password-app">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Parolă de aplicație detectată!</strong><br>
                            Formatul pare corect (16 caractere lowercase).
                        </div>
                    <?php elseif ($isRegularPassword): ?>
                        <div class="password-analysis password-regular">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Parolă regulată detectată!</strong><br>
                            Gmail necesită o parolă de aplicație pentru SMTP.
                        </div>
                    <?php else: ?>
                        <div class="password-analysis password-regular">
                            <i class="fas fa-times-circle me-2"></i>
                            <strong>Parolă neconfigurata!</strong><br>
                            Încă folosești valoarea placeholder.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Step 2: Gmail App Password Setup -->
        <?php if (!$isAppPassword): ?>
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">2</div>
                <h3>Crearea Parolei de Aplicație Gmail</h3>
            </div>
            
            <div class="instruction-box">
                <h5><i class="fas fa-key me-2"></i>Pași pentru crearea parolei de aplicație:</h5>
                <ol>
                    <li><strong>Accesează</strong> <a href="https://myaccount.google.com/" target="_blank" class="text-white"><u>Google Account Settings</u></a></li>
                    <li><strong>Click pe "Security"</strong> în meniul din stânga</li>
                    <li><strong>Activează "2-Step Verification"</strong> dacă nu este deja activă</li>
                    <li><strong>După activarea 2FA</strong>, caută "App passwords" în secțiunea Security</li>
                    <li><strong>Click pe "App passwords"</strong></li>
                    <li><strong>Selectează "Mail"</strong> ca aplicație și "Windows Computer" ca dispozitiv</li>
                    <li><strong>Click "Generate"</strong></li>
                    <li><strong>Copiază parola de 16 caractere</strong> (ex: abcdefghijklmnop)</li>
                </ol>
            </div>

            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>Exemplu de parolă de aplicație:</h6>
                <code>abcdefghijklmnop</code> (16 caractere lowercase, fără spații)
            </div>

            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>Important:</h6>
                <ul class="mb-0">
                    <li>Parola de aplicație este diferită de parola contului Gmail</li>
                    <li>Trebuie să ai 2-Factor Authentication activat</li>
                    <li>Parola se afișează o singură dată - copiază-o imediat</li>
                </ul>
            </div>
        </div>
        <?php endif; ?>

        <!-- Step 3: Update Configuration -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number <?php echo $isAppPassword ? 'success' : '3'; ?>">3</div>
                <h3>Actualizarea Configurației</h3>
            </div>
            
            <?php if (!$isAppPassword): ?>
                <div class="alert alert-primary">
                    <h6><i class="fas fa-edit me-2"></i>Actualizează fișierul src/Config/constants.php:</h6>
                    <div class="config-display mt-2">
// Înlocuiește această linie:
define('SMTP_PASSWORD', '<?php echo htmlspecialchars(SMTP_PASSWORD); ?>');

// Cu parola de aplicație (16 caractere):
define('SMTP_PASSWORD', 'abcdefghijklmnop'); // Parola ta de aplicație
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>Configurația pare corectă!</strong> Poți testa conexiunea SMTP mai jos.
                </div>
            <?php endif; ?>
        </div>

        <!-- Step 4: Test SMTP Connection -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">4</div>
                <h3>Test Conexiune SMTP</h3>
            </div>
            
            <?php if ($testResult): ?>
                <div class="alert alert-<?php echo $testResult['success'] ? 'success' : 'danger'; ?> mb-3">
                    <h5>
                        <i class="fas fa-<?php echo $testResult['success'] ? 'check' : 'times'; ?> me-2"></i>
                        <?php echo $testResult['success'] ? 'Conexiune reușită!' : 'Conexiune eșuată!'; ?>
                    </h5>
                    <p class="mb-0"><?php echo htmlspecialchars($testResult['message']); ?></p>
                </div>
            <?php endif; ?>

            <form method="POST" class="mb-3">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="test_username" class="form-label">Email (Username):</label>
                        <input type="email" class="form-control" id="test_username" name="test_username"
                               value="<?php echo htmlspecialchars(SMTP_USERNAME); ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="test_password" class="form-label">Parolă (App Password):</label>
                        <input type="password" class="form-control" id="test_password" name="test_password"
                               value="<?php echo htmlspecialchars(SMTP_PASSWORD); ?>" required>
                        <div class="form-text">Pentru Gmail: parola de aplicație de 16 caractere</div>
                    </div>
                </div>
                <button type="submit" name="test_smtp" class="btn btn-primary">
                    <i class="fas fa-plug me-2"></i>
                    Testează Conexiunea SMTP
                </button>
            </form>

            <?php if (!empty($debugOutput)): ?>
                <div class="mt-3">
                    <h6>Debug Output SMTP:</h6>
                    <div class="debug-output"><?php echo htmlspecialchars(implode("\n", $debugOutput)); ?></div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Step 5: Alternative Solutions -->
        <div class="step-card">
            <div class="step-header">
                <div class="step-number">5</div>
                <h3>Soluții Alternative</h3>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-envelope me-2"></i>Outlook/Hotmail SMTP:</h6>
                    <div class="config-display">
define('SMTP_HOST', 'smtp-mail.outlook.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-password');
                    </div>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-server me-2"></i>SMTP Local (WAMP):</h6>
                    <div class="config-display">
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 25);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
                    </div>
                    <small class="text-muted">Necesită configurarea sendmail în WAMP</small>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="step-card text-center">
            <div class="d-grid gap-2 d-md-block">
                <a href="test_smtp.php" class="btn btn-info">
                    <i class="fas fa-vial me-2"></i>
                    Test SMTP Simplu
                </a>
                <a href="debug_contact.php" class="btn btn-secondary">
                    <i class="fas fa-bug me-2"></i>
                    Debug Complet
                </a>
                <a href="contact.php" class="btn btn-success">
                    <i class="fas fa-envelope me-2"></i>
                    Testează Formularul
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
