<?php
/**
 * Test script to investigate party search issues for "Saragea Tudorita"
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';

use App\Services\DosarService;

echo "=== TESTING PARTY SEARCH FOR 'Saragea Tudorita' ===" . PHP_EOL;
echo "Expected results:" . PHP_EOL;
echo "- <PERSON><PERSON><PERSON> de Apel BUCUREŞTI: 130/98/2022/a3 (2478/2022)" . PHP_EOL;
echo "- Curt<PERSON> de Apel BUCUREŞTI: 130/98/2022 (2351/2022)" . PHP_EOL;
echo "- <PERSON><PERSON><PERSON> de Apel SUCEAVA: 2177/40/2019" . PHP_EOL;
echo "- Tribunalul IALOMIŢA: 130/98/2022" . PHP_EOL;
echo PHP_EOL;

$searchTerm = 'Saragea Tudorita';
$dosarService = new DosarService();

// Test 1: Direct party search using the SOAP API
echo "=== TEST 1: Direct SOAP API party search ===" . PHP_EOL;
try {
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => $searchTerm,
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $results = $dosarService->cautareAvansata($searchParams);
    echo "Found " . count($results) . " results from SOAP API" . PHP_EOL;
    
    foreach ($results as $index => $dosar) {
        echo "Result " . ($index + 1) . ": " . ($dosar->institutie ?? 'Unknown') . " - " . ($dosar->numar ?? 'Unknown') . PHP_EOL;
        
        // Check parties in this case
        $parti = $dosar->parti ?? [];
        echo "  Parties count: " . count($parti) . PHP_EOL;
        
        foreach ($parti as $partyIndex => $party) {
            $partyName = is_object($party) ? ($party->nume ?? '') : ($party['nume'] ?? '');
            $partyQuality = is_object($party) ? ($party->calitate ?? '') : ($party['calitate'] ?? '');
            $partySource = is_object($party) ? ($party->source ?? 'soap_api') : ($party['source'] ?? 'soap_api');
            
            // Check if this party matches our search
            if (stripos($partyName, 'SARAGEA') !== false || stripos($partyName, 'TUDORITA') !== false) {
                echo "  MATCH: Party " . ($partyIndex + 1) . ": $partyName ($partyQuality) [Source: $partySource]" . PHP_EOL;
            }
        }
        echo PHP_EOL;
    }
} catch (Exception $e) {
    echo "Error in SOAP API search: " . $e->getMessage() . PHP_EOL;
}

// Test 2: Test with different variations of the name
echo "=== TEST 2: Testing name variations ===" . PHP_EOL;
$nameVariations = [
    'Saragea Tudorita',
    'SARAGEA TUDORITA',
    'Saragea Tudoriţa',
    'SARAGEA TUDORIŢA',
    'Tudorita Saragea',
    'TUDORITA SARAGEA',
    'Tudoriţa Saragea',
    'TUDORIŢA SARAGEA'
];

foreach ($nameVariations as $variation) {
    echo "Testing variation: '$variation'" . PHP_EOL;
    try {
        $searchParams = [
            'numarDosar' => '',
            'institutie' => null,
            'obiectDosar' => '',
            'numeParte' => $variation,
            'dataStart' => null,
            'dataStop' => null,
            'dataUltimaModificareStart' => null,
            'dataUltimaModificareStop' => null
        ];
        
        $results = $dosarService->cautareAvansata($searchParams);
        echo "  Found " . count($results) . " results" . PHP_EOL;
    } catch (Exception $e) {
        echo "  Error: " . $e->getMessage() . PHP_EOL;
    }
}

echo PHP_EOL;
echo "=== TEST 3: Testing specific known cases ===" . PHP_EOL;

// Test specific cases that should contain this party
$knownCases = [
    ['institutie' => 'CurteadeApelBUCURESTI', 'numar' => '130/98/2022/a3'],
    ['institutie' => 'CurteadeApelBUCURESTI', 'numar' => '130/98/2022'],
    ['institutie' => 'CurteadeApelSUCEAVA', 'numar' => '2177/40/2019'],
    ['institutie' => 'TribunalulIALOMITA', 'numar' => '130/98/2022']
];

foreach ($knownCases as $case) {
    echo "Testing case: {$case['institutie']} - {$case['numar']}" . PHP_EOL;
    try {
        $searchParams = [
            'numarDosar' => $case['numar'],
            'institutie' => $case['institutie'],
            'obiectDosar' => '',
            'numeParte' => '',
            'dataStart' => null,
            'dataStop' => null,
            'dataUltimaModificareStart' => null,
            'dataUltimaModificareStop' => null
        ];

        $results = $dosarService->cautareAvansata($searchParams);
        if (!empty($results)) {
            $dosar = $results[0];
            $parti = $dosar->parti ?? [];
            echo "  Found case with " . count($parti) . " parties" . PHP_EOL;

            $foundTarget = false;
            $allPartyNames = [];
            foreach ($parti as $partyIndex => $party) {
                $partyName = is_object($party) ? ($party->nume ?? '') : ($party['nume'] ?? '');
                $partyQuality = is_object($party) ? ($party->calitate ?? '') : ($party['calitate'] ?? '');
                $partySource = is_object($party) ? ($party->source ?? 'soap_api') : ($party['source'] ?? 'soap_api');

                $allPartyNames[] = $partyName;

                if (stripos($partyName, 'SARAGEA') !== false || stripos($partyName, 'TUDORITA') !== false) {
                    echo "  FOUND TARGET PARTY: $partyName ($partyQuality) [Source: $partySource]" . PHP_EOL;
                    $foundTarget = true;
                }
            }

            if (!$foundTarget) {
                echo "  TARGET PARTY NOT FOUND in this case" . PHP_EOL;
                echo "  Searching for similar names..." . PHP_EOL;

                // Look for similar names
                foreach ($allPartyNames as $name) {
                    if (stripos($name, 'SARA') !== false || stripos($name, 'TUDO') !== false) {
                        echo "    Similar name found: $name" . PHP_EOL;
                    }
                }

                // Show first 10 party names for debugging
                echo "  First 10 parties in this case:" . PHP_EOL;
                for ($i = 0; $i < min(10, count($allPartyNames)); $i++) {
                    echo "    " . ($i + 1) . ": " . $allPartyNames[$i] . PHP_EOL;
                }
            }
        } else {
            echo "  Case not found" . PHP_EOL;
        }
    } catch (Exception $e) {
        echo "  Error: " . $e->getMessage() . PHP_EOL;
    }
    echo PHP_EOL;
}

echo "=== TEST COMPLETE ===" . PHP_EOL;
