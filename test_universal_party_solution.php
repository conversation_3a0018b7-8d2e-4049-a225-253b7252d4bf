<?php
/**
 * Universal Party Solution Test
 * Tests the comprehensive party extraction and search solution across multiple cases
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';
require_once 'enhanced_party_search_solution.php';

// Test cases with different party counts
$testCases = [
    // Large case (700+ parties)
    ['numar' => '130/98/2022', 'institutie' => 'TribunalulIALOMITA', 'description' => 'Large case (700+ parties)', 'searchParty' => 'SARAGEA TUDORIŢA'],
    
    // Medium cases (try to find some with 50-200 parties)
    ['numar' => '1/2023', 'institutie' => 'TribunalulIALOMITA', 'description' => 'Medium case test 1', 'searchParty' => ''],
    ['numar' => '2/2023', 'institutie' => 'TribunalulIALOMITA', 'description' => 'Medium case test 2', 'searchParty' => ''],
    
    // Small cases (try to find some with <20 parties)
    ['numar' => '100/2023', 'institutie' => 'TribunalulIALOMITA', 'description' => 'Small case test 1', 'searchParty' => ''],
    ['numar' => '200/2023', 'institutie' => 'TribunalulIALOMITA', 'description' => 'Small case test 2', 'searchParty' => '']
];

echo "<!DOCTYPE html>";
echo "<html><head><title>Universal Party Solution Test</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    .test-case { border: 2px solid #007bff; margin: 20px 0; padding: 15px; }
    .stats { background: #f8f9fa; padding: 10px; margin: 10px 0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style></head><body>";

echo "<h1>🔍 Universal Party Solution Test</h1>";
echo "<p>Testing the comprehensive party extraction and search solution across multiple cases with different party counts.</p>";
echo "<hr>";

$dosarService = new \App\Services\DosarService();
$overallStats = [
    'total_cases_tested' => 0,
    'successful_cases' => 0,
    'total_parties_across_all_cases' => 0,
    'cases_with_enhanced_extraction' => 0,
    'performance_metrics' => []
];

foreach ($testCases as $index => $testCase) {
    $numarDosar = $testCase['numar'];
    $institutie = $testCase['institutie'];
    $description = $testCase['description'];
    $searchParty = $testCase['searchParty'];
    
    echo "<div class='test-case'>";
    echo "<h2>🧪 Test Case " . ($index + 1) . ": {$description}</h2>";
    echo "<p><strong>Case:</strong> {$numarDosar}</p>";
    echo "<p><strong>Institution:</strong> {$institutie}</p>";
    
    $overallStats['total_cases_tested']++;
    
    try {
        $startTime = microtime(true);
        $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
        $endTime = microtime(true);
        
        $processingTime = round(($endTime - $startTime) * 1000, 2);
        $overallStats['performance_metrics'][] = $processingTime;
        
        if (!$dosar) {
            echo "<p class='warning'>⚠️ Case not found or no data available</p>";
            echo "</div>";
            continue;
        }
        
        $overallStats['successful_cases']++;
        
        $totalParties = count($dosar->parti ?? []);
        $overallStats['total_parties_across_all_cases'] += $totalParties;
        
        // Analyze party sources
        $soapParties = 0;
        $decisionParties = 0;
        
        foreach ($dosar->parti as $party) {
            if (isset($party->source)) {
                if ($party->source === 'soap_api') {
                    $soapParties++;
                } elseif ($party->source === 'decision_text') {
                    $decisionParties++;
                }
            }
        }
        
        if ($soapParties >= 100 && $decisionParties > 0) {
            $overallStats['cases_with_enhanced_extraction']++;
        }
        
        echo "<div class='section'>";
        echo "<h3>📊 Case Analysis</h3>";
        echo "<div class='stats'>";
        echo "<table>";
        echo "<tr><th>Metric</th><th>Value</th></tr>";
        echo "<tr><td>Total Parties</td><td>{$totalParties}</td></tr>";
        echo "<tr><td>SOAP API Parties</td><td>{$soapParties}</td></tr>";
        echo "<tr><td>Decision Text Parties</td><td>{$decisionParties}</td></tr>";
        echo "<tr><td>Processing Time</td><td>{$processingTime}ms</td></tr>";
        echo "<tr><td>Memory Usage</td><td>" . round(memory_get_usage() / 1024 / 1024, 2) . " MB</td></tr>";
        echo "</table>";
        echo "</div>";
        
        // Categorize case size
        $caseSize = 'Unknown';
        if ($totalParties < 20) {
            $caseSize = 'Small (<20 parties)';
        } elseif ($totalParties < 100) {
            $caseSize = 'Medium (20-99 parties)';
        } elseif ($totalParties < 500) {
            $caseSize = 'Large (100-499 parties)';
        } else {
            $caseSize = 'Very Large (500+ parties)';
        }
        
        echo "<p><strong>Case Size Category:</strong> <span class='info'>{$caseSize}</span></p>";
        
        // Check if enhanced extraction was used
        if ($soapParties >= 100) {
            echo "<p class='success'>✅ Enhanced extraction activated (SOAP API limit reached)</p>";
            echo "<p class='info'>📄 Decision text extraction added {$decisionParties} additional parties</p>";
        } else {
            echo "<p class='info'>📋 Standard extraction (SOAP API sufficient)</p>";
        }
        
        // Performance assessment
        if ($processingTime < 1000) {
            echo "<p class='success'>✅ Excellent performance ({$processingTime}ms)</p>";
        } elseif ($processingTime < 3000) {
            echo "<p class='info'>ℹ️ Good performance ({$processingTime}ms)</p>";
        } else {
            echo "<p class='warning'>⚠️ Slower performance ({$processingTime}ms) - acceptable for large datasets</p>";
        }
        
        echo "</div>";
        
        // Test search functionality if search party is specified
        if (!empty($searchParty)) {
            echo "<div class='section'>";
            echo "<h3>🔍 Search Functionality Test</h3>";
            echo "<p><strong>Searching for:</strong> <span class='highlight'>{$searchParty}</span></p>";
            
            $searchResults = enhancedPartySearch($dosar->parti, $searchParty);
            
            echo "<div class='stats'>";
            echo "<table>";
            echo "<tr><th>Match Type</th><th>Count</th></tr>";
            echo "<tr><td>Exact Matches</td><td>" . count($searchResults['exact_matches']) . "</td></tr>";
            echo "<tr><td>Partial Matches</td><td>" . count($searchResults['partial_matches']) . "</td></tr>";
            echo "<tr><td>Fuzzy Matches</td><td>" . count($searchResults['fuzzy_matches']) . "</td></tr>";
            echo "<tr><td>Suggestions</td><td>" . count($searchResults['suggestions']) . "</td></tr>";
            echo "</table>";
            echo "</div>";
            
            $totalMatches = count($searchResults['exact_matches']) + count($searchResults['partial_matches']) + count($searchResults['fuzzy_matches']);
            
            if ($totalMatches > 0) {
                echo "<p class='success'>✅ Search functionality working - found {$totalMatches} matches</p>";
            } else {
                echo "<p class='info'>ℹ️ No matches found for this search term</p>";
            }
            
            echo "</div>";
        }
        
        // Sample parties display
        echo "<div class='section'>";
        echo "<h3>👥 Sample Parties (First 5)</h3>";
        
        if ($totalParties > 0) {
            echo "<table>";
            echo "<tr><th>#</th><th>Name</th><th>Quality</th><th>Source</th></tr>";
            
            $displayCount = min(5, $totalParties);
            for ($i = 0; $i < $displayCount; $i++) {
                $party = $dosar->parti[$i];
                $rowClass = ($party->source ?? '') === 'soap_api' ? 'style="background-color: #e8f5e8;"' : 'style="background-color: #fff3cd;"';
                echo "<tr {$rowClass}>";
                echo "<td>" . ($i + 1) . "</td>";
                echo "<td>" . htmlspecialchars($party->nume ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($party->calitate ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($party->source ?? 'unknown') . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
            
            if ($totalParties > 5) {
                echo "<p class='info'>... and " . ($totalParties - 5) . " more parties</p>";
            }
        } else {
            echo "<p class='warning'>No parties found in this case</p>";
        }
        
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='section'>";
        echo "<h3 class='error'>❌ Error Processing Case</h3>";
        echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
    echo "</div>";
}

// Overall statistics
echo "<div class='section'>";
echo "<h2>📈 Overall Test Results</h2>";

$avgProcessingTime = !empty($overallStats['performance_metrics']) ? 
    round(array_sum($overallStats['performance_metrics']) / count($overallStats['performance_metrics']), 2) : 0;

echo "<div class='stats'>";
echo "<table>";
echo "<tr><th>Metric</th><th>Value</th></tr>";
echo "<tr><td>Total Cases Tested</td><td>{$overallStats['total_cases_tested']}</td></tr>";
echo "<tr><td>Successful Cases</td><td>{$overallStats['successful_cases']}</td></tr>";
echo "<tr><td>Success Rate</td><td>" . round(($overallStats['successful_cases'] / max($overallStats['total_cases_tested'], 1)) * 100, 1) . "%</td></tr>";
echo "<tr><td>Total Parties Across All Cases</td><td>{$overallStats['total_parties_across_all_cases']}</td></tr>";
echo "<tr><td>Cases with Enhanced Extraction</td><td>{$overallStats['cases_with_enhanced_extraction']}</td></tr>";
echo "<tr><td>Average Processing Time</td><td>{$avgProcessingTime}ms</td></tr>";
echo "</table>";
echo "</div>";

// Final assessment
if ($overallStats['successful_cases'] >= $overallStats['total_cases_tested'] * 0.8) {
    echo "<p class='success'>🎉 UNIVERSAL SOLUTION SUCCESS: The party extraction and search system works reliably across different case sizes!</p>";
} else {
    echo "<p class='warning'>⚠️ Some issues detected - may need further optimization</p>";
}

echo "</div>";

echo "<hr>";
echo "<p><em>Universal test completed at " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
