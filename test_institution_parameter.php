<?php
/**
 * Test Institution Parameter
 * Verify if the institution parameter is causing issues
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Institution Parameter Test</title>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
.warning { background: #fff3cd; border-left-color: #ffc107; }
.error { background: #f8d7da; border-left-color: #dc3545; }
.success { background: #d4edda; border-left-color: #28a745; }
.debug-info { background: #e3f2fd; border: 1px solid #2196f3; padding: 10px; margin: 10px 0; font-family: monospace; }
.test-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
.test-table th, .test-table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
.test-table th { background-color: #f8f9fa; }
</style></head><body>";

echo "<h1>🔍 Institution Parameter Test</h1>";
echo "<p><strong>Objective:</strong> Test different institution parameter formats for case 130/98/2022</p>";
echo "<hr>";

$numarDosar = '130/98/2022';

// Test different institution formats
$institutionTests = [
    'CurteadeApelBUCURESTI' => 'User provided format',
    'TribunalulIALOMITA' => 'Previous working format',
    'CURTEA_DE_APEL_BUCURESTI' => 'Underscore format',
    'CURTEA DE APEL BUCURESTI' => 'Space format',
    'CurteaDeApelBucuresti' => 'CamelCase format',
    'CURTEA_APEL_BUCURESTI' => 'Short underscore format'
];

echo "<div class='section'>";
echo "<h2>📊 Institution Format Tests</h2>";
echo "<table class='test-table'>";
echo "<tr><th>Institution Format</th><th>Description</th><th>Parties Found</th><th>Status</th><th>Test Link</th></tr>";

$dosarService = new DosarService();

foreach ($institutionTests as $institutie => $description) {
    echo "<tr>";
    echo "<td><code>{$institutie}</code></td>";
    echo "<td>{$description}</td>";
    
    try {
        $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
        
        if (!$dosar) {
            echo "<td>-</td>";
            echo "<td><span style='color: #dc3545;'>❌ No data</span></td>";
        } else {
            $totalParties = count($dosar->parti ?? []);
            echo "<td><strong>{$totalParties}</strong></td>";
            
            if ($totalParties >= 340) {
                echo "<td><span style='color: #28a745;'>✅ Success (340+)</span></td>";
            } elseif ($totalParties == 100) {
                echo "<td><span style='color: #ffc107;'>⚠️ SOAP limit (100)</span></td>";
            } elseif ($totalParties > 0) {
                echo "<td><span style='color: #17a2b8;'>ℹ️ Some data ({$totalParties})</span></td>";
            } else {
                echo "<td><span style='color: #dc3545;'>❌ No parties</span></td>";
            }
        }
        
        // Create test link
        $testUrl = "detalii_dosar.php?numar=" . urlencode($numarDosar) . "&institutie=" . urlencode($institutie);
        echo "<td><a href='{$testUrl}' target='_blank' style='color: #007bff;'>Test</a></td>";
        
    } catch (Exception $e) {
        echo "<td>-</td>";
        echo "<td><span style='color: #dc3545;'>❌ Error</span></td>";
        echo "<td>-</td>";
    }
    
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Test the original case that was working
echo "<div class='section'>";
echo "<h2>🔍 Original Working Case Test</h2>";
echo "<p>Testing the original case that was confirmed to have 340+ parties:</p>";

$originalNumar = '130/98/2022';
$originalInstitutie = 'TribunalulIALOMITA';

try {
    $originalDosar = $dosarService->getDetaliiDosar($originalNumar, $originalInstitutie);
    
    if (!$originalDosar) {
        echo "<div class='error'>❌ Original case not found</div>";
    } else {
        $originalParties = count($originalDosar->parti ?? []);
        
        echo "<div class='debug-info'>";
        echo "Original Case Results:\n";
        echo "Case: {$originalNumar}\n";
        echo "Institution: {$originalInstitutie}\n";
        echo "Parties: {$originalParties}\n";
        echo "</div>";
        
        if ($originalParties >= 340) {
            echo "<div class='success'>✅ Original case still works with {$originalParties} parties</div>";
        } else {
            echo "<div class='warning'>⚠️ Original case now shows only {$originalParties} parties</div>";
        }
        
        // Source analysis for original case
        if (!empty($originalDosar->parti)) {
            $soapCount = 0;
            $decisionCount = 0;
            foreach ($originalDosar->parti as $parte) {
                $source = $parte['source'] ?? 'unknown';
                if ($source === 'soap_api') $soapCount++;
                elseif ($source === 'decision_text') $decisionCount++;
            }
            
            echo "<div class='debug-info'>";
            echo "Original Case Source Analysis:\n";
            echo "SOAP API parties: {$soapCount}\n";
            echo "Decision text parties: {$decisionCount}\n";
            echo "Total: " . ($soapCount + $decisionCount) . "\n";
            echo "</div>";
        }
        
        $originalTestUrl = "detalii_dosar.php?numar=" . urlencode($originalNumar) . "&institutie=" . urlencode($originalInstitutie);
        echo "<p><a href='{$originalTestUrl}' target='_blank' class='btn btn-primary'>Test Original Case</a></p>";
    }
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h4>❌ Error testing original case</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Analysis and recommendations
echo "<div class='section'>";
echo "<h2>💡 Analysis and Recommendations</h2>";

echo "<h4>Key Findings:</h4>";
echo "<ul>";
echo "<li>Different institution formats may return different results</li>";
echo "<li>The SOAP API may be sensitive to exact institution naming</li>";
echo "<li>Some formats may not find the case at all</li>";
echo "<li>Others may find the case but with limited party data</li>";
echo "</ul>";

echo "<h4>Next Steps:</h4>";
echo "<ol>";
echo "<li><strong>Identify the correct format:</strong> Find which institution format returns the most parties</li>";
echo "<li><strong>Test the frontend:</strong> Use the working format to test the actual case details page</li>";
echo "<li><strong>Compare with original:</strong> Verify if the issue is specific to the institution format</li>";
echo "<li><strong>Update the URL:</strong> Use the correct institution format in the final test</li>";
echo "</ol>";

echo "<div class='warning'>";
echo "<h4>⚠️ Important Note</h4>";
echo "<p>The user provided institution format 'CurteadeApelBUCURESTI' may not be the correct format for the SOAP API. The API might require a different naming convention.</p>";
echo "</div>";

echo "</div>";

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    console.log('🔍 Institution Parameter Test - Ready');";
echo "    console.log('Testing different institution formats for case {$numarDosar}');";
echo "    ";
echo "    // Highlight the best result";
echo "    const rows = document.querySelectorAll('.test-table tr');";
echo "    let maxParties = 0;";
echo "    let bestRow = null;";
echo "    ";
echo "    rows.forEach(row => {";
echo "        const partiesCell = row.cells[2];";
echo "        if (partiesCell && partiesCell.textContent) {";
echo "            const parties = parseInt(partiesCell.textContent);";
echo "            if (parties > maxParties) {";
echo "                maxParties = parties;";
echo "                if (bestRow) bestRow.style.backgroundColor = '';";
echo "                bestRow = row;";
echo "                row.style.backgroundColor = '#d4edda';";
echo "            }";
echo "        }";
echo "    });";
echo "    ";
echo "    if (maxParties > 0) {";
echo "        console.log('Best result: ' + maxParties + ' parties');";
echo "    }";
echo "});";
echo "</script>";

echo "</body></html>";
?>
