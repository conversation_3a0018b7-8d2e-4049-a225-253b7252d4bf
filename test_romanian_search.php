<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Romanian Diacritics Search</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .highlight { background: yellow; font-weight: bold; }
        .test-section { border: 2px solid #007bff; margin: 20px 0; padding: 15px; border-radius: 8px; }
        .success { background: #d4edda; border-color: #28a745; }
        .warning { background: #fff3cd; border-color: #ffc107; }
        .danger { background: #f8d7da; border-color: #dc3545; }
        .party-row { cursor: pointer; }
        .party-row:hover { background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-search"></i> Romanian Diacritics Search Test</h1>
        <p class="lead">Testing search functionality with Romanian diacritical characters</p>
        
        <div class="test-section">
            <h3>Search Test</h3>
            <div class="mb-3">
                <label for="testSearch" class="form-label">Search Term:</label>
                <input type="text" class="form-control" id="testSearch" placeholder="Try: TUDORIŢA, tudorita, ŞERBĂNESCU, serbanescu, etc.">
            </div>
            <div id="searchResults" class="alert alert-info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Test Data (Case 130/98/2022 Sample)</h3>
            <div class="table-responsive">
                <table class="table table-striped" id="testTable">
                    <thead>
                        <tr>
                            <th>Index</th>
                            <th>Nume</th>
                            <th>Calitate</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // Sample data from the actual case with Romanian diacritics
                        $testParties = [
                            ['nume' => 'ŞERBĂNESCU ELENA', 'calitate' => 'Creditor'],
                            ['nume' => 'PASCU NICOLETA', 'calitate' => 'Creditor'],
                            ['nume' => 'ASOCIAŢIA CAR ÎNVĂŢĂMÂNT SLOBOZIA', 'calitate' => 'Debitor'],
                            ['nume' => 'COPILĂU GHEORGHEş.a-la av.Băbeanu Iulia And', 'calitate' => 'Creditor'],
                            ['nume' => 'ALECU NICULAE', 'calitate' => 'Creditor'],
                            ['nume' => 'ACHIM MARILENA', 'calitate' => 'Creditor'],
                            ['nume' => 'ALEXANDRESCU BOGDAN IONUŢ', 'calitate' => 'Creditor'],
                            ['nume' => 'ALEXE LIXANDRA', 'calitate' => 'Creditor'],
                            ['nume' => 'ALEXE MARIA', 'calitate' => 'Creditor'],
                            ['nume' => 'ANAGNOSTE GHEORGHE', 'calitate' => 'Creditor'],
                            ['nume' => 'BURDUŞELU TUDORIŢA', 'calitate' => 'Creditor'],
                            ['nume' => 'BALA ZOIŢA', 'calitate' => 'Creditor'],
                            ['nume' => 'BĂLAN ALEXANDRA', 'calitate' => 'Creditor'],
                            ['nume' => 'BĂNICĂ GABRIELA', 'calitate' => 'Creditor'],
                            ['nume' => 'BĂTRÎNU CONSTANTIN', 'calitate' => 'Creditor'],
                            ['nume' => 'CORNĂŢEANU ŞTEFANIA', 'calitate' => 'Creditor'],
                            ['nume' => 'CHIRIŢĂ VIORICA', 'calitate' => 'Creditor'],
                            ['nume' => 'CHIŢU EUGENIA', 'calitate' => 'Creditor'],
                            ['nume' => 'CÎRCIUMARU DANA ANDREEA', 'calitate' => 'Creditor'],
                            ['nume' => 'CREŢU AURICA-LILI', 'calitate' => 'Creditor']
                        ];
                        
                        foreach ($testParties as $index => $parte):
                        ?>
                            <tr class="parte-row" 
                                data-nume="<?php echo htmlspecialchars($parte['nume']); ?>"
                                data-calitate="<?php echo htmlspecialchars($parte['calitate']); ?>"
                                data-index="<?php echo $index + 1; ?>">
                                <td><span class="badge bg-secondary"><?php echo $index + 1; ?></span></td>
                                <td class="nume-parte"><?php echo htmlspecialchars($parte['nume']); ?></td>
                                <td class="calitate-parte"><?php echo htmlspecialchars($parte['calitate']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="test-section">
            <h3>Test Cases</h3>
            <p>Try these search terms to test Romanian diacritics normalization:</p>
            <div class="row">
                <div class="col-md-6">
                    <h5>With Diacritics:</h5>
                    <ul>
                        <li><code>TUDORIŢA</code> → should find "BURDUŞELU TUDORIŢA"</li>
                        <li><code>ŞERBĂNESCU</code> → should find "ŞERBĂNESCU ELENA"</li>
                        <li><code>ÎNVĂŢĂMÂNT</code> → should find "ASOCIAŢIA CAR ÎNVĂŢĂMÂNT SLOBOZIA"</li>
                        <li><code>BĂLAN</code> → should find "BĂLAN ALEXANDRA"</li>
                        <li><code>IONUŢ</code> → should find "ALEXANDRESCU BOGDAN IONUŢ"</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>Without Diacritics:</h5>
                    <ul>
                        <li><code>tudorita</code> → should find "BURDUŞELU TUDORIŢA"</li>
                        <li><code>serbanescu</code> → should find "ŞERBĂNESCU ELENA"</li>
                        <li><code>invatamant</code> → should find "ASOCIAŢIA CAR ÎNVĂŢĂMÂNT SLOBOZIA"</li>
                        <li><code>balan</code> → should find "BĂLAN ALEXANDRA"</li>
                        <li><code>ionut</code> → should find "ALEXANDRESCU BOGDAN IONUŢ"</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Copy the Romanian normalization functions from detalii_dosar.php
        function normalizeRomanianText(text) {
            if (!text) return '';
            
            return text.toLowerCase()
                .replace(/[ăâ]/g, 'a')
                .replace(/[îi]/g, 'i')
                .replace(/[șş]/g, 's')
                .replace(/[țţ]/g, 't')
                .replace(/\s+/g, ' ')
                .trim();
        }

        function romanianTextContains(text, searchTerm) {
            const normalizedText = normalizeRomanianText(text);
            const normalizedSearch = normalizeRomanianText(searchTerm);
            return normalizedText.includes(normalizedSearch);
        }

        function escapeRegExp(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }

        function highlightText(text, term) {
            if (!term || !text) return text;

            try {
                const normalizedTerm = normalizeRomanianText(term);
                const normalizedText = normalizeRomanianText(text);
                
                if (!normalizedText.includes(normalizedTerm)) {
                    return text;
                }
                
                let regexPattern = escapeRegExp(term)
                    .replace(/[aăâ]/gi, '[aăâ]')
                    .replace(/[iî]/gi, '[iî]')
                    .replace(/[sș]/gi, '[sș]')
                    .replace(/[tț]/gi, '[tț]');
                    
                const regex = new RegExp(`(${regexPattern})`, 'gi');
                return text.replace(regex, '<span class="highlight">$1</span>');
            } catch (error) {
                console.error('Eroare la evidențierea textului:', error);
                return text;
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('testSearch');
            const searchResults = document.getElementById('searchResults');
            const table = document.getElementById('testTable');
            const rows = table.querySelectorAll('tbody tr.parte-row');
            
            let searchTimeout;
            
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    const searchTerm = this.value.trim();
                    let visibleCount = 0;
                    
                    if (searchTerm === '') {
                        // Show all rows
                        rows.forEach(row => {
                            row.style.display = '';
                            // Restore original text
                            const numeCell = row.querySelector('.nume-parte');
                            const calitateCell = row.querySelector('.calitate-parte');
                            if (row.hasAttribute('data-original-nume')) {
                                numeCell.innerHTML = row.getAttribute('data-original-nume');
                            }
                            if (row.hasAttribute('data-original-calitate')) {
                                calitateCell.innerHTML = row.getAttribute('data-original-calitate');
                            }
                            visibleCount++;
                        });
                        searchResults.style.display = 'none';
                    } else {
                        // Filter and highlight
                        rows.forEach(row => {
                            const numeCell = row.querySelector('.nume-parte');
                            const calitateCell = row.querySelector('.calitate-parte');
                            
                            const numeText = numeCell.textContent || '';
                            const calitateText = calitateCell.textContent || '';
                            
                            const matchNume = romanianTextContains(numeText, searchTerm);
                            const matchCalitate = romanianTextContains(calitateText, searchTerm);
                            
                            if (matchNume || matchCalitate) {
                                row.style.display = '';
                                visibleCount++;
                                
                                // Save original text
                                if (!row.hasAttribute('data-original-nume')) {
                                    row.setAttribute('data-original-nume', numeCell.innerHTML);
                                }
                                if (!row.hasAttribute('data-original-calitate')) {
                                    row.setAttribute('data-original-calitate', calitateCell.innerHTML);
                                }
                                
                                // Highlight matches
                                if (matchNume) {
                                    numeCell.innerHTML = highlightText(numeText, searchTerm);
                                }
                                if (matchCalitate) {
                                    calitateCell.innerHTML = highlightText(calitateText, searchTerm);
                                }
                            } else {
                                row.style.display = 'none';
                            }
                        });
                        
                        // Show results
                        searchResults.style.display = 'block';
                        if (visibleCount === 0) {
                            searchResults.textContent = `Nu s-au găsit rezultate pentru "${searchTerm}".`;
                            searchResults.className = 'alert alert-warning';
                        } else {
                            searchResults.textContent = `S-au găsit ${visibleCount} rezultate pentru "${searchTerm}".`;
                            searchResults.className = 'alert alert-success';
                        }
                    }
                }, 300);
            });
        });
    </script>
</body>
</html>
