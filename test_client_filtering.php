<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

// Test pentru a verifica dacă filtrarea client-side afectează afișarea
$dosarService = new DosarService();

echo "<!DOCTYPE html>
<html lang='ro'>
<head>
    <meta charset='UTF-8'>
    <title>Test Filtrare Client-Side - Portal Judiciar România</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 2px solid #007bff; border-radius: 8px; padding: 20px; margin: 15px 0; background: #f8f9fa; }
        .result-box { background: #fff; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin: 10px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .code { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 10px; font-family: 'Courier New', monospace; font-size: 12px; margin: 10px 0; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class=\"container mt-4\">
        <h1>Test Filtrare Client-Side</h1>";

// Test 1: Căutare simplă fără filtre
echo "<div class='test-section'>";
echo "<h3>Test 1: Căutare simplă pentru 'POPESCU' fără filtre</h3>";

try {
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'numeParte' => 'POPESCU',
        'obiectDosar' => '',
        'dataStart' => '',
        'dataStop' => '',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => ''
    ];
    
    $results = $dosarService->cautareAvansata($searchParams);
    $count = count($results);
    
    echo "<div class='success'>";
    echo "<strong>Rezultate găsite:</strong> $count<br>";
    echo "</div>";
    
    if ($count > 0) {
        echo "<div class='result-box'>";
        echo "<h5>Primul rezultat:</h5>";
        $first = $results[0];
        echo "<div class='code'>";
        echo "Număr: " . htmlspecialchars($first->numar ?? 'N/A') . "\n";
        echo "Instanță: " . htmlspecialchars($first->institutie ?? 'N/A') . "\n";
        echo "Părți: " . count($first->parti ?? []) . " părți\n";
        echo "Categoria caz: " . htmlspecialchars($first->categorieCaz ?? 'N/A') . "\n";
        echo "</div>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<strong>Eroare:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</div>";

// Test 2: Căutare cu filtru de categorie caz
echo "<div class='test-section'>";
echo "<h3>Test 2: Căutare pentru 'POPESCU' cu filtru categorie caz 'civil'</h3>";

try {
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'numeParte' => 'POPESCU',
        'obiectDosar' => '',
        'dataStart' => '',
        'dataStop' => '',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
        'categorieCaz' => 'civil'
    ];
    
    $results = $dosarService->cautareAvansata($searchParams);
    $count = count($results);
    
    echo "<div class='success'>";
    echo "<strong>Rezultate găsite cu filtru SOAP:</strong> $count<br>";
    echo "</div>";
    
    // Simulez filtrarea client-side
    if ($count > 0) {
        $filteredResults = [];
        foreach ($results as $dosar) {
            $dosarCategory = strtolower($dosar->categorieCaz ?? '');
            if (stripos($dosarCategory, 'civil') !== false) {
                $filteredResults[] = $dosar;
            }
        }
        
        echo "<div class='warning'>";
        echo "<strong>Rezultate după filtrare client-side:</strong> " . count($filteredResults) . "<br>";
        echo "</div>";
        
        if (count($filteredResults) > 0) {
            echo "<div class='result-box'>";
            echo "<h5>Primul rezultat filtrat:</h5>";
            $first = $filteredResults[0];
            echo "<div class='code'>";
            echo "Număr: " . htmlspecialchars($first->numar ?? 'N/A') . "\n";
            echo "Instanță: " . htmlspecialchars($first->institutie ?? 'N/A') . "\n";
            echo "Categoria caz: " . htmlspecialchars($first->categorieCaz ?? 'N/A') . "\n";
            echo "</div>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<strong>Eroare:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</div>";

// Test 3: Simulare performBulkSearchWithFilters
echo "<div class='test-section'>";
echo "<h3>Test 3: Simulare performBulkSearchWithFilters</h3>";

// Include funcțiile necesare din index.php
function validateAndMapInstitutionCode($institutionCode) {
    if (empty($institutionCode)) {
        return null;
    }
    return $institutionCode;
}

function filterResultsByCaseCategory($termResults, $caseCategory) {
    if (empty($caseCategory) || empty($termResults)) {
        return $termResults;
    }
    
    $filteredResults = [];
    $filterCategory = strtolower($caseCategory);
    
    foreach ($termResults as $dosar) {
        $dosarCategory = strtolower($dosar->categorieCaz ?? '');
        
        if (stripos($dosarCategory, $filterCategory) !== false) {
            $filteredResults[] = $dosar;
        }
    }
    
    return $filteredResults;
}

try {
    $searchTermsData = [
        ['term' => 'POPESCU', 'type' => 'numeParte'],
        ['term' => 'IONESCU', 'type' => 'numeParte']
    ];
    
    $advancedFilters = [
        'categorieCaz' => 'civil'
    ];
    
    $results = [];
    $mappedInstitutionCode = validateAndMapInstitutionCode($advancedFilters['institutie'] ?? null);
    
    foreach ($searchTermsData as $termData) {
        $term = $termData['term'];
        $searchType = $termData['type'];
        
        $searchParams = [
            'numarDosar' => '',
            'institutie' => $mappedInstitutionCode,
            'numeParte' => $term,
            'obiectDosar' => '',
            'dataStart' => '',
            'dataStop' => '',
            'dataUltimaModificareStart' => '',
            'dataUltimaModificareStop' => '',
            'categorieCaz' => $advancedFilters['categorieCaz'] ?? ''
        ];
        
        $termResults = $dosarService->cautareAvansata($searchParams);
        
        // Aplicăm filtrarea client-side pentru categoria cazului
        if (!empty($termResults) && !empty($advancedFilters['categorieCaz'])) {
            $originalCount = count($termResults);
            $termResults = filterResultsByCaseCategory($termResults, $advancedFilters['categorieCaz']);
            $filteredCount = count($termResults);
            
            echo "<div class='result-box'>";
            echo "<h5>Termen: " . htmlspecialchars($term) . "</h5>";
            echo "<div class='code'>";
            echo "Rezultate originale: $originalCount\n";
            echo "Rezultate după filtrare client-side: $filteredCount\n";
            echo "</div>";
            echo "</div>";
        }
        
        $results[] = [
            'term' => $term,
            'type' => $searchType,
            'results' => $termResults ?: [],
            'count' => count($termResults ?: []),
            'error' => null,
            'filters' => $advancedFilters
        ];
    }
    
    echo "<div class='success'>";
    echo "<strong>Rezultate finale bulk search:</strong><br>";
    foreach ($results as $index => $result) {
        echo "Termen " . ($index + 1) . ": " . htmlspecialchars($result['term']) . " - " . $result['count'] . " rezultate<br>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<strong>Eroare:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</div>";

// Test 4: Verificare dacă există probleme cu sessionStorage
echo "<div class='test-section'>";
echo "<h3>Test 4: Verificare JavaScript și sessionStorage</h3>";

echo "<div class='result-box'>";
echo "<h5>Test JavaScript pentru filtrare:</h5>";
echo "<button onclick='testExactMatchFilter()' class='btn btn-primary'>Test Exact Match Filter</button>";
echo "<div id='filterTestResult' style='margin-top: 10px;'></div>";
echo "</div>";

echo "</div>";

echo "
    <br>
    <a href='index.php'>Înapoi la Portal</a>
    
    <script>
        function testExactMatchFilter() {
            const resultDiv = document.getElementById('filterTestResult');
            
            // Verificăm dacă există sessionStorage
            try {
                const testKey = 'test_key';
                const testValue = 'test_value';
                sessionStorage.setItem(testKey, testValue);
                const retrievedValue = sessionStorage.getItem(testKey);
                sessionStorage.removeItem(testKey);
                
                if (retrievedValue === testValue) {
                    resultDiv.innerHTML = '<div style=\"background: #d4edda; padding: 10px; border-radius: 4px;\">✓ SessionStorage funcționează corect</div>';
                } else {
                    resultDiv.innerHTML = '<div style=\"background: #f8d7da; padding: 10px; border-radius: 4px;\">✗ SessionStorage nu funcționează corect</div>';
                }
                
                // Verificăm dacă există filtrul exact match în sessionStorage
                const exactMatchFilter = sessionStorage.getItem('exactMatchFilter');
                if (exactMatchFilter) {
                    resultDiv.innerHTML += '<div style=\"background: #fff3cd; padding: 10px; border-radius: 4px; margin-top: 5px;\">⚠️ Filtrul exact match este activ în sessionStorage: ' + exactMatchFilter + '</div>';
                } else {
                    resultDiv.innerHTML += '<div style=\"background: #d1ecf1; padding: 10px; border-radius: 4px; margin-top: 5px;\">ℹ️ Nu există filtru exact match în sessionStorage</div>';
                }
                
            } catch (e) {
                resultDiv.innerHTML = '<div style=\"background: #f8d7da; padding: 10px; border-radius: 4px;\">✗ Eroare sessionStorage: ' + e.message + '</div>';
            }
        }
        
        // Verificăm automat la încărcare
        window.addEventListener('load', function() {
            testExactMatchFilter();
        });
    </script>
</body>
</html>";
?>
