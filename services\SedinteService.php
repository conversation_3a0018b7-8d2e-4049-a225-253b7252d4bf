<?php
/**
 * <PERSON><PERSON><PERSON> pentru căutarea ședințelor de judecată
 * Interacționează cu API-ul SOAP al Portalului Instanțelor de Judecată
 */

require_once __DIR__ . '/../includes/config.php';

class SedinteService {
    /**
     * Instanța clientului SOAP
     * @var SoapClient
     */
    private $soapClient;

    /**
     * Numărul maxim de reîncercări pentru apelurile SOAP
     * @var int
     */
    private $maxRetries = 3;

    /**
     * Constructor
     */
    public function __construct() {
        try {
            // Opțiuni pentru clientul SOAP cu timeout-uri optimizate
            $options = [
                'soap_version' => SOAP_1_2,
                'exceptions' => true,
                'trace' => true,
                'cache_wsdl' => WSDL_CACHE_NONE,
                'connection_timeout' => 15, // Timeout de conexiune de 15 secunde
                'default_socket_timeout' => 30, // Timeout pentru operațiuni SOAP de 30 secunde
                'features' => SOAP_SINGLE_ELEMENT_ARRAYS, // Forțează returnarea array-urilor pentru elemente singulare
                'stream_context' => stream_context_create([
                    'http' => [
                        'timeout' => 30, // Timeout HTTP de 30 secunde
                        'user_agent' => 'Judicial Portal Sessions Client/1.0'
                    ]
                ])
            ];

            // Inițializare client SOAP
            $this->soapClient = new SoapClient(SOAP_WSDL, $options);
        } catch (SoapFault $e) {
            throw new Exception("Eroare la conectarea la serviciul SOAP pentru ședințe: " . $e->getMessage());
        }
    }

    /**
     * Caută ședințele de judecată
     *
     * @param array $params Parametrii de căutare
     * @return array Lista ședințelor găsite
     * @throws Exception
     */
    public function cautareSedinte($params) {
        // Validăm parametrii de intrare
        $this->validateSearchParams($params);

        // Salvăm codul instituției original pentru referință
        $originalInstitutionCode = $params['institutie'] ?? null;

        // Validăm și mapăm codul instituției pentru SOAP API
        $mappedInstitutionCode = $this->validateAndMapInstitutionCode($originalInstitutionCode);

        // Construim parametrii pentru SOAP API
        $soapParams = [
            'dataSedinta' => $params['dataSedinta'],
            'institutie' => $mappedInstitutionCode
        ];

        // Flag pentru a indica dacă am folosit fallback pentru instituție
        $institutionFallback = false;

        try {
            // Executăm apelul SOAP cu retry logic
            $response = $this->executeSoapCallWithRetry('CautareSedinte', $soapParams);

            // Procesăm răspunsul
            return $this->processSessionSearchResponse($response);

        } catch (SoapFault $e) {
            // Analizăm tipul erorii SOAP pentru a oferi mesaje mai utile
            $errorMessage = $e->getMessage();

            // Eroare de validare a instituției
            if (strpos($errorMessage, 'is not a valid value for Institutie') !== false) {
                // Extragem codul instituției din mesajul de eroare
                if (preg_match("/'([^']+)' is not a valid value for Institutie/", $errorMessage, $matches)) {
                    $invalidCode = $matches[1];
                    $correctedMessage = "Codul instituției '{$invalidCode}' nu este acceptat de API-ul SOAP. " .
                                      "Vă rugăm să verificați lista instituțiilor disponibile sau să contactați administratorul.";
                    error_log("Cod instituție invalid în SOAP API: {$invalidCode}");
                    throw new Exception($correctedMessage);
                }

                throw new Exception("Codul instituției specificat nu este valid în sistemul judiciar. Vă rugăm să selectați o instituție din lista disponibilă.");
            }

            // Eroare de conexiune sau timeout
            if (strpos($errorMessage, 'Could not connect to host') !== false ||
                strpos($errorMessage, 'Connection timed out') !== false) {
                throw new Exception("Nu s-a putut stabili conexiunea cu serverul judiciar. Vă rugăm să încercați din nou în câteva momente.");
            }

            // Eroare de format XML
            if (strpos($errorMessage, 'XML document') !== false) {
                throw new Exception("Eroare în procesarea datelor de la serverul judiciar. Vă rugăm să verificați parametrii introduși.");
            }

            // Logăm eroarea originală pentru debugging
            $this->logError('cautareSedinte', $soapParams, $e);
            throw new Exception("Eroare la comunicarea cu serverul judiciar: " . $errorMessage);

        } catch (Exception $e) {
            // Verificăm dacă este o eroare de validare a instituției
            $errorMessage = $e->getMessage();

            // DEBUG: Log all relevant information
            error_log("DEBUG: Exception caught in cautareSedinte");
            error_log("DEBUG: Error message: " . $errorMessage);
            error_log("DEBUG: Mapped institution code: " . ($mappedInstitutionCode ?? 'NULL'));
            error_log("DEBUG: Original institution code: " . ($originalInstitutionCode ?? 'NULL'));
            error_log("DEBUG: Contains 'is not a valid value for Institutie': " . (strpos($errorMessage, 'is not a valid value for Institutie') !== false ? 'YES' : 'NO'));
            error_log("DEBUG: originalInstitutionCode is not empty: " . (!empty($originalInstitutionCode) ? 'YES' : 'NO'));

            // Eroare de validare a instituției - încercăm fallback
            if (strpos($errorMessage, 'is not a valid value for Institutie') !== false && !empty($originalInstitutionCode)) {
                error_log("DEBUG: Institution code '{$originalInstitutionCode}' not valid for SOAP API (sessions), retrying without institution filter");

                // Pentru ședințe, API-ul pare să nu accepte nici măcar null pentru instituție
                // Încercăm cu un cod de instituție cunoscut ca fiind valid (CurteadeApelBUCURESTI)
                $fallbackParams = [
                    'dataSedinta' => $soapParams['dataSedinta'],
                    'institutie' => 'CurteadeApelBUCURESTI'  // Cod cunoscut ca fiind valid
                ];

                try {
                    error_log("DEBUG: Retrying SOAP call with fallback institution: CurteadeApelBUCURESTI");
                    $response = $this->executeSoapCallWithRetry('CautareSedinte', $fallbackParams);
                    $results = $this->processSessionSearchResponse($response);
                    error_log("DEBUG: Fallback successful, will filter results client-side");

                    // Filtrăm rezultatele local după instituție
                    if (!empty($originalInstitutionCode)) {
                        $results = $this->filterSessionsByInstitution($results, $originalInstitutionCode);
                        error_log("DEBUG: Client-side filtering completed, found " . count($results) . " sessions");
                    }

                    return $results;
                } catch (Exception $fallbackException) {
                    error_log("DEBUG: Fallback also failed: " . $fallbackException->getMessage());
                    // Dacă și fallback-ul eșuează, aruncăm eroarea originală cu mesaj îmbunătățit
                    $correctedMessage = "Codul instituției '{$originalInstitutionCode}' nu este acceptat de API-ul SOAP pentru ședințe. " .
                                      "Încercarea de căutare fără filtrul de instituție a eșuat de asemenea. " .
                                      "Vă rugăm să contactați administratorul.";
                    throw new Exception($correctedMessage);
                }
            }

            // Pentru alte tipuri de excepții, logăm și re-aruncăm
            $this->logError('cautareSedinte', $soapParams, $e);
            throw new Exception("Eroare la căutarea ședințelor: " . $e->getMessage());
        }
    }

    /**
     * Validează parametrii de căutare pentru ședințe
     *
     * @param array $params Parametrii de validat
     * @throws Exception
     */
    private function validateSearchParams($params) {
        // Data ședinței este obligatorie
        if (empty($params['dataSedinta'])) {
            throw new Exception("Data ședinței este obligatorie");
        }

        // Validăm formatul datei (trebuie să fie în format ISO pentru SOAP)
        if (!preg_match('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/', $params['dataSedinta'])) {
            throw new Exception("Formatul datei pentru SOAP API trebuie să fie YYYY-MM-DDTHH:mm:ss");
        }

        // Instituția poate fi null (pentru toate instituțiile) sau un string valid
        if (isset($params['institutie']) && $params['institutie'] !== null && !is_string($params['institutie'])) {
            throw new Exception("Codul instituției trebuie să fie un string valid sau null");
        }


    }

    /**
     * Validează codul instituției împotriva unei liste de coduri cunoscute ca fiind valide
     *
     * @param string $institutionCode Codul instituției de validat
     * @throws Exception
     */
    private function validateInstitutionCode($institutionCode) {
        // Lista codurilor de instituții cunoscute ca fiind valide în SOAP API
        // Această listă va fi actualizată pe baza rezultatelor din validate_institution_codes.php
        $validInstitutionCodes = $this->getValidInstitutionCodes();

        // Verificăm dacă codul este în lista de coduri valide
        if (!in_array($institutionCode, $validInstitutionCodes)) {
            // Verificăm dacă este un cod cunoscut ca fiind invalid
            $knownInvalidCodes = $this->getKnownInvalidCodes();
            if (array_key_exists($institutionCode, $knownInvalidCodes)) {
                $suggestion = $knownInvalidCodes[$institutionCode];
                throw new Exception("Codul instituției '{$institutionCode}' nu este valid în SOAP API. Încercați '{$suggestion}' în schimb.");
            }

            // Mesaj generic pentru coduri necunoscute
            throw new Exception("Codul instituției '{$institutionCode}' nu este recunoscut ca fiind valid. Vă rugăm să selectați o instituție din lista disponibilă.");
        }
    }

    /**
     * Returnează lista codurilor de instituții validate ca fiind funcționale cu SOAP API
     *
     * @return array Lista codurilor valide
     */
    private function getValidInstitutionCodes() {
        // Această listă va fi populată cu codurile validate prin validate_institution_codes.php
        // Pentru moment, includem codurile cele mai comune care știm că funcționează
        return [
            'TribunalulBUCURESTI',
            'CurteadeApelBUCURESTI',
            'TribunalulCLUJ',
            'CurteadeApelCLUJ',
            'TribunalulTIMIS',
            'CurteadeApelTIMISOARA',
            'TribunalulCONSTANTA',
            'CurteadeApelCONSTANTA',
            'JudecatoriaSECTORUL1BUCURESTI',
            'JudecatoriaSECTORUL2BUCURESTI',
            'JudecatoriaSECTORUL3BUCURESTI',
            'JudecatoriaSECTORUL4BUCURESTI',
            'JudecatoriaSECTORUL5BUCURESTI',
            'JudecatoriaSECTORUL6BUCURESTI',
            'JudecatoriaCLUJNAPOCA',
            'JudecatoriaTIMISOARA',
            'JudecatoriaCONSTANTA',
            // Această listă va fi extinsă cu toate codurile validate
        ];
    }

    /**
     * Returnează maparea codurilor cunoscute ca fiind invalide către codurile corecte
     *
     * @return array Maparea codurilor invalide către cele corecte
     */
    private function getKnownInvalidCodes() {
        return [
            'CurteadeApelBACU' => 'CurteadeApelBACU', // Exemplu - va fi actualizat
            'TribunalulBRASSOV' => 'TribunalulBRASOV', // Exemplu - fără dubla S
            // Această mapare va fi populată pe baza rezultatelor validării
        ];
    }

    /**
     * Procesează răspunsul de la API-ul SOAP pentru căutarea ședințelor
     *
     * @param mixed $response Răspunsul de la SOAP API
     * @return array Lista ședințelor procesate
     */
    private function processSessionSearchResponse($response) {
        $sessions = [];

        // Verificăm dacă avem un răspuns valid
        if (!isset($response->CautareSedinteResult)) {
            return $sessions;
        }

        $result = $response->CautareSedinteResult;

        // Dacă nu avem ședințe, returnăm array gol
        if (empty($result->Sedinta)) {
            return $sessions;
        }

        // Normalizăm răspunsul (poate fi un singur element sau array)
        $sedinteData = is_array($result->Sedinta) ? $result->Sedinta : [$result->Sedinta];

        foreach ($sedinteData as $sedintaData) {
            $sedinta = new stdClass();
            
            // Mapăm câmpurile de bază
            $sedinta->departament = $sedintaData->departament ?? '';
            $sedinta->complet = $sedintaData->complet ?? '';
            $sedinta->data = $sedintaData->data ?? '';
            $sedinta->ora = $sedintaData->ora ?? '';
            
            // Procesăm dosarele asociate ședinței
            $sedinta->dosare = [];
            if (!empty($sedintaData->dosare) && !empty($sedintaData->dosare->SedintaDosar)) {
                $dosareData = is_array($sedintaData->dosare->SedintaDosar) ? 
                             $sedintaData->dosare->SedintaDosar : 
                             [$sedintaData->dosare->SedintaDosar];
                
                foreach ($dosareData as $dosarData) {
                    if ($dosarData && !empty($dosarData->numar)) {
                        $dosar = new stdClass();
                        $dosar->numar = $dosarData->numar ?? '';
                        $dosar->institutie = $dosarData->institutie ?? '';
                        $sedinta->dosare[] = $dosar;
                    }
                }
            }
            
            $sessions[] = $sedinta;
        }

        return $sessions;
    }

    /**
     * Execută un apel SOAP cu logica de retry
     *
     * @param string $method Metoda SOAP de apelat
     * @param array $params Parametrii pentru metoda SOAP
     * @return mixed Răspunsul de la SOAP API
     * @throws Exception
     */
    private function executeSoapCallWithRetry($method, $params) {
        $lastException = null;
        $attempt = 0;

        // Creăm directorul pentru loguri dacă nu există
        $logDir = __DIR__ . '/../logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        $logFile = $logDir . '/soap_sessions.log';

        while ($attempt < $this->maxRetries) {
            try {
                // Incrementăm numărul de încercări
                $attempt++;

                // Logăm încercarea curentă
                $logData = date('Y-m-d H:i:s') . " - Încercare {$attempt}/{$this->maxRetries} pentru metoda {$method} (ședințe)\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                // Executăm apelul SOAP
                $response = $this->soapClient->$method($params);

                // Dacă am ajuns aici, apelul a reușit, deci logăm succesul și returnăm răspunsul
                $logData = date('Y-m-d H:i:s') . " - Apel SOAP reușit pentru metoda {$method} (ședințe) la încercarea {$attempt}\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                return $response;
            } catch (SoapFault $e) {
                // Salvăm excepția pentru a o putea arunca dacă toate încercările eșuează
                $lastException = $e;

                // Logăm eroarea
                $logData = date('Y-m-d H:i:s') . " - Eroare SOAP la încercarea {$attempt}/{$this->maxRetries} pentru metoda {$method} (ședințe): " . $e->getMessage() . "\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                // Dacă nu mai avem încercări, aruncăm excepția
                if ($attempt >= $this->maxRetries) {
                    break;
                }

                // Așteptăm înainte de următoarea încercare (exponential backoff)
                sleep(pow(2, $attempt - 1));
            }
        }

        // Dacă am ajuns aici, toate încercările au eșuat
        throw new Exception("Apelul SOAP pentru metoda {$method} (ședințe) a eșuat după {$this->maxRetries} încercări. Ultima eroare: " . $lastException->getMessage());
    }

    /**
     * Logează erorile pentru debugging
     *
     * @param string $method Metoda care a generat eroarea
     * @param array $params Parametrii folosiți
     * @param Exception $exception Excepția generată
     */
    private function logError($method, $params, $exception) {
        $logDir = __DIR__ . '/../logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logFile = $logDir . '/sessions_errors.log';
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $method,
            'params' => $params,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ];

        file_put_contents($logFile, json_encode($logData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n", FILE_APPEND);
    }

    /**
     * Obține informații despre metodele disponibile în WSDL
     *
     * @return array Lista metodelor disponibile
     */
    public function getAvailableMethods() {
        try {
            return $this->soapClient->__getFunctions();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Obține tipurile de date disponibile în WSDL
     *
     * @return array Lista tipurilor de date
     */
    public function getAvailableTypes() {
        try {
            return $this->soapClient->__getTypes();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Validează și mapează codul instituției pentru SOAP API
     * Implementează același mecanism ca în DosarService pentru consistență
     *
     * @param string|null $institutionCode Codul instituției de validat
     * @return string|null Codul mapat sau null
     */
    private function validateAndMapInstitutionCode($institutionCode) {
        if (empty($institutionCode)) {
            return null;
        }

        // Mapare pentru codurile de instituții care pot fi diferite în SOAP API
        // Bazat pe testarea SOAP API și documentația disponibilă
        $institutionMapping = [
            // Înalta Curte - poate să nu fie suportată direct în SOAP API
            'InaltaCurtedeCASSATIESIJUSTITIE' => null,

            // Curți de Apel - testăm cu codurile exacte
            'CurteadeApelBUCURESTI' => 'CurteadeApelBUCURESTI',
            'CurteadeApelCLUJ' => 'CurteadeApelCLUJ',
            'CurteadeApelTIMISOARA' => 'CurteadeApelTIMISOARA',
            'CurteadeApelCONSTANTA' => 'CurteadeApelCONSTANTA',
            'CurteadeApelCRAIOVA' => 'CurteadeApelCRAIOVA',
            'CurteadeApelIASI' => 'CurteadeApelIASI',
            'CurteadeApelALBAIULIA' => 'CurteadeApelALBAIULIA',
            'CurteadeApelBACU' => 'CurteadeApelBACU',
            'CurteadeApelBRASSOV' => 'CurteadeApelBRASSOV',
            'CurteadeApelGALATI' => 'CurteadeApelGALATI',
            'CurteadeApelORADEA' => 'CurteadeApelORADEA',
            'CurteadeApelPITESTI' => 'CurteadeApelPITESTI',
            'CurteadeApelPLOIESTI' => 'CurteadeApelPLOIESTI',
            'CurteadeApelSUCEAVA' => 'CurteadeApelSUCEAVA',
            'CurteadeApelTARGUMURES' => 'CurteadeApelTARGUMURES',

            // Tribunale - testăm cu codurile exacte
            'TribunalulBUCURESTI' => 'TribunalulBUCURESTI',
            'TribunalulCLUJ' => 'TribunalulCLUJ',
            'TribunalulTIMIS' => 'TribunalulTIMIS',
            'TribunalulCONSTANTA' => 'TribunalulCONSTANTA',

            // Judecătorii București
            'JudecatoriaSECTORUL1BUCURESTI' => 'JudecatoriaSECTORUL1BUCURESTI',
            'JudecatoriaSECTORUL2BUCURESTI' => 'JudecatoriaSECTORUL2BUCURESTI',
            'JudecatoriaSECTORUL3BUCURESTI' => 'JudecatoriaSECTORUL3BUCURESTI',
            'JudecatoriaSECTORUL4BUCURESTI' => 'JudecatoriaSECTORUL4BUCURESTI',
            'JudecatoriaSECTORUL5BUCURESTI' => 'JudecatoriaSECTORUL5BUCURESTI',
            'JudecatoriaSECTORUL6BUCURESTI' => 'JudecatoriaSECTORUL6BUCURESTI',

            // Alte judecătorii importante
            'JudecatoriaCLUJNAPOCA' => 'JudecatoriaCLUJNAPOCA',
            'JudecatoriaTIMISOARA' => 'JudecatoriaTIMISOARA',
            'JudecatoriaCONSTANTA' => 'JudecatoriaCONSTANTA',
            'JudecatoriaIASI' => 'JudecatoriaIASI',
            'JudecatoriaBRASSOV' => 'JudecatoriaBRASSOV',

            // Coduri alternative care pot apărea în SOAP API
            'TBBU' => 'TribunalulBUCURESTI', // Cod scurt pentru Tribunalul București
            'CAB' => 'CurteadeApelBUCURESTI', // Cod scurt pentru Curtea de Apel București
            'ICCJ' => null, // Înalta Curte - cod scurt

            // SOAP API incompatible codes - map to working alternatives
            'TribunalulBACU' => 'CurteadeApelBACU', // Tribunal Bacău not recognized by SOAP, use Appeals Court
            'TribunalulSUCEAVA' => 'CurteadeApelSUCEAVA', // Map to appeals court for SOAP compatibility
            'JudecatoriaROMANI' => 'JudecatoriaROMAN', // Correct typo: ROMANI -> ROMAN
        ];

        // Verificăm dacă avem o mapare specifică
        if (array_key_exists($institutionCode, $institutionMapping)) {
            return $institutionMapping[$institutionCode];
        }

        // Dacă nu avem mapare specifică, returnăm codul original
        return $institutionCode;
    }

    /**
     * Filtrează ședințele după instituție (client-side filtering)
     * Folosit când API-ul SOAP nu acceptă codul instituției și folosim fallback
     *
     * @param array $sessions Lista ședințelor de filtrat
     * @param string $institutionCode Codul instituției pentru filtrare
     * @return array Lista ședințelor filtrate
     */
    private function filterSessionsByInstitution($sessions, $institutionCode) {
        if (empty($sessions) || empty($institutionCode)) {
            return $sessions;
        }

        $filteredSessions = [];

        foreach ($sessions as $session) {
            // Verificăm dacă ședința aparține instituției dorite
            // Căutăm în dosarele asociate ședinței
            if (!empty($session->dosare)) {
                $hasMatchingInstitution = false;
                foreach ($session->dosare as $dosar) {
                    if (!empty($dosar->institutie) && $dosar->institutie === $institutionCode) {
                        $hasMatchingInstitution = true;
                        break;
                    }
                }

                if ($hasMatchingInstitution) {
                    $filteredSessions[] = $session;
                }
            }
        }

        return $filteredSessions;
    }
}
?>
