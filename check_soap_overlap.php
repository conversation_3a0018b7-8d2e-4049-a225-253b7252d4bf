<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 CHECKING SOAP API OVERLAP\n";
echo "============================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    echo "Current total parties: " . count($dosar->parti) . "\n\n";
    
    // Get SOAP API parties
    $soapParties = [];
    foreach ($dosar->parti as $party) {
        $partyArray = (array) $party;
        if (isset($partyArray['source']) && $partyArray['source'] === 'soap_api') {
            $soapParties[] = $partyArray['nume'];
        }
    }
    
    echo "SOAP API parties: " . count($soapParties) . "\n\n";
    
    // Check specific missing names against SOAP API
    $testNames = [
        '<PERSON>u <PERSON>e',
        '<PERSON>e Lixandra', 
        '<PERSON>e <PERSON>',
        'Badea Emil',
        'Badea Mirela',
        'Anagnoste Gheorghe',
        'Constantin Vasilica',
        'Filipache Alexandru',
        'Filipache Elena',
        'Paraschiv Elena',
        'Popescu Constantin',
        'Popescu Stana',
        'Radu Sorica'
    ];
    
    echo "🔍 CHECKING SPECIFIC MISSING NAMES:\n";
    echo "===================================\n\n";
    
    foreach ($testNames as $testName) {
        echo "Testing: \"{$testName}\"\n";
        
        // Check if it exists in SOAP API (exact match)
        $exactMatch = in_array($testName, $soapParties);
        echo "  Exact match in SOAP: " . ($exactMatch ? "YES" : "NO") . "\n";
        
        // Check if it exists in SOAP API (case insensitive)
        $caseInsensitiveMatch = false;
        foreach ($soapParties as $soapName) {
            if (strtolower($testName) === strtolower($soapName)) {
                $caseInsensitiveMatch = true;
                echo "  Case insensitive match: \"{$soapName}\"\n";
                break;
            }
        }
        
        if (!$caseInsensitiveMatch) {
            echo "  Case insensitive match: NO\n";
        }
        
        // Check normalized match
        $normalizedTest = strtolower(str_replace(['ă', 'â', 'î', 'ș', 'ț'], ['a', 'a', 'i', 's', 't'], $testName));
        $normalizedMatch = false;
        foreach ($soapParties as $soapName) {
            $normalizedSoap = strtolower(str_replace(['ă', 'â', 'î', 'ș', 'ț'], ['a', 'a', 'i', 's', 't'], $soapName));
            if ($normalizedTest === $normalizedSoap) {
                $normalizedMatch = true;
                echo "  Normalized match: \"{$soapName}\"\n";
                break;
            }
        }
        
        if (!$normalizedMatch) {
            echo "  Normalized match: NO\n";
        }
        
        echo "\n";
    }
    
    // Show first 20 SOAP API names for reference
    echo "📋 FIRST 20 SOAP API NAMES:\n";
    echo "===========================\n\n";
    for ($i = 0; $i < min(20, count($soapParties)); $i++) {
        echo ($i + 1) . ". " . $soapParties[$i] . "\n";
    }
    
    echo "\n✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
