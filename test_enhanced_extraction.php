<?php
/**
 * Test the enhanced party extraction patterns
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<h1>🚀 Enhanced Party Extraction Test</h1>\n";
echo "<p><strong>Testing enhanced patterns on:</strong> 130/98/2022 from Curtea de Apel BUCURESTI</p>\n";

try {
    $dosarService = new DosarService();
    $numarDosar = '130/98/2022';
    $institutie = 'CurteadeApelBUCURESTI';
    
    echo "<h2>Before and After Comparison</h2>\n";
    
    // Test the case with enhanced extraction
    error_log("=== ENHANCED EXTRACTION TEST START ===");
    
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    error_log("=== ENHANCED EXTRACTION TEST END ===");
    
    if ($dosar && !empty($dosar->numar)) {
        $totalParties = count($dosar->parti ?? []);
        echo "<p><strong>Total Parties Found:</strong> <span style='font-size: 1.5em; color: " . ($totalParties >= 500 ? 'green' : ($totalParties >= 200 ? 'orange' : 'red')) . ";'>{$totalParties}</span></p>\n";
        
        // Count by source
        $soapCount = 0;
        $decisionCount = 0;
        $unknownCount = 0;
        
        foreach ($dosar->parti as $parte) {
            switch ($parte->source ?? 'unknown') {
                case 'soap_api': $soapCount++; break;
                case 'decision_text': $decisionCount++; break;
                default: $unknownCount++; break;
            }
        }
        
        echo "<h3>📊 Source Breakdown</h3>\n";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Source</th><th>Count</th><th>Percentage</th><th>Status</th></tr>\n";
        echo "<tr><td>SOAP API</td><td>{$soapCount}</td><td>" . round(($soapCount / $totalParties) * 100, 1) . "%</td><td>" . ($soapCount >= 100 ? "⚠️ Limit reached" : "✅ Normal") . "</td></tr>\n";
        echo "<tr><td>Decision Text</td><td style='color: " . ($decisionCount > 0 ? 'green' : 'red') . ";'><strong>{$decisionCount}</strong></td><td>" . round(($decisionCount / $totalParties) * 100, 1) . "%</td><td>" . ($decisionCount > 0 ? "✅ Working" : "❌ Not working") . "</td></tr>\n";
        echo "<tr><td>Unknown</td><td>{$unknownCount}</td><td>" . round(($unknownCount / $totalParties) * 100, 1) . "%</td><td>-</td></tr>\n";
        echo "<tr><td><strong>Total</strong></td><td><strong>{$totalParties}</strong></td><td><strong>100%</strong></td><td>" . ($totalParties >= 500 ? "🎯 Target met" : "❌ Below target") . "</td></tr>\n";
        echo "</table>\n";
        
        // Assessment
        echo "<h3>🎯 Assessment</h3>\n";
        
        if ($totalParties >= 500) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ SUCCESS: Target Achieved!</h4>\n";
            echo "<p style='color: #155724; margin: 0;'>The case now displays {$totalParties} parties, meeting the 500+ requirement.</p>\n";
            echo "</div>\n";
        } elseif ($totalParties >= 200) {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4 style='color: #856404; margin: 0 0 10px 0;'>⚠️ PROGRESS: Significant Improvement</h4>\n";
            echo "<p style='color: #856404; margin: 0;'>Found {$totalParties} parties (up from ~100). Enhanced extraction is working but may need further optimization.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ ISSUE: Limited Improvement</h4>\n";
            echo "<p style='color: #721c24; margin: 0;'>Only {$totalParties} parties found. The case may not contain 500+ parties in the decision text, or additional patterns are needed.</p>\n";
            echo "</div>\n";
        }
        
        // Hybrid extraction status
        if ($soapCount >= 100 && $decisionCount > 0) {
            echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4 style='color: #0c5460; margin: 0 0 10px 0;'>✅ Hybrid Extraction Working</h4>\n";
            echo "<p style='color: #0c5460; margin: 0;'>SOAP API limit reached ({$soapCount} parties) and decision text extraction found {$decisionCount} additional parties.</p>\n";
            echo "</div>\n";
        } elseif ($soapCount >= 100 && $decisionCount == 0) {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4 style='color: #856404; margin: 0 0 10px 0;'>⚠️ Hybrid Extraction Issue</h4>\n";
            echo "<p style='color: #856404; margin: 0;'>SOAP API limit reached ({$soapCount} parties) but no decision text parties found. The decision text may not contain additional party names.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
            echo "<h4 style='color: #383d41; margin: 0 0 10px 0;'>ℹ️ Normal Case</h4>\n";
            echo "<p style='color: #383d41; margin: 0;'>SOAP API returned {$soapCount} parties (under 100 limit). Hybrid extraction not triggered.</p>\n";
            echo "</div>\n";
        }
        
        // Show sample decision text parties
        if ($decisionCount > 0) {
            echo "<h3>📝 Sample Decision Text Parties</h3>\n";
            echo "<p>Showing first 10 parties extracted from decision text:</p>\n";
            echo "<ul>\n";
            $count = 0;
            foreach ($dosar->parti as $parte) {
                if ($parte->source === 'decision_text' && $count < 10) {
                    echo "<li><strong>" . htmlspecialchars($parte->nume) . "</strong> (" . $parte->calitate . ")</li>\n";
                    $count++;
                }
            }
            echo "</ul>\n";
        }
        
        // Check for specific names
        echo "<h3>🔍 Specific Name Search</h3>\n";
        $searchNames = ['SARAGEA', 'TUDORITA', 'Saragea', 'Tudorita'];
        $foundNames = [];
        
        foreach ($dosar->parti as $parte) {
            foreach ($searchNames as $searchName) {
                if (stripos($parte->nume, $searchName) !== false) {
                    $foundNames[] = [
                        'search' => $searchName,
                        'found' => $parte->nume,
                        'quality' => $parte->calitate,
                        'source' => $parte->source ?? 'unknown'
                    ];
                }
            }
        }
        
        if (count($foundNames) > 0) {
            echo "<p style='color: green;'><strong>✅ Found matching names:</strong></p>\n";
            echo "<ul>\n";
            foreach ($foundNames as $found) {
                echo "<li>Searched for '<strong>{$found['search']}</strong>' → Found '<strong>" . htmlspecialchars($found['found']) . "</strong>' ({$found['quality']}) [{$found['source']}]</li>\n";
            }
            echo "</ul>\n";
        } else {
            echo "<p style='color: red;'><strong>❌ No matching names found for: " . implode(', ', $searchNames) . "</strong></p>\n";
        }
        
    } else {
        echo "<p style='color: red;'><strong>❌ Case not found</strong></p>\n";
    }
    
    echo "<h2>🔧 Next Steps</h2>\n";
    echo "<p>Based on these results:</p>\n";
    echo "<ul>\n";
    echo "<li>If 500+ parties found: <strong>✅ Task completed successfully</strong></li>\n";
    echo "<li>If 200-499 parties: Enhanced extraction working, may need case-specific patterns</li>\n";
    echo "<li>If under 200 parties: The case may not actually contain 500+ parties in the decision text</li>\n";
    echo "<li>Verify the case number and institution are correct for the expected large case</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
