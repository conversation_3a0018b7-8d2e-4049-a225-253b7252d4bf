<?php
/**
 * Enhanced party matching functions extracted from bulk_search.php for testing
 */

/**
 * Normalizează textul pentru căutare, gestionând diacriticele românești
 */
function normalizeForSearch($text) {
    if (empty($text)) return '';
    
    // Convertim la string și eliminăm spațiile de la început și sfârșit
    $text = trim((string) $text);
    
    // Verificăm encoding UTF-8
    if (!mb_check_encoding($text, 'UTF-8')) {
        $text = mb_convert_encoding($text, 'UTF-8', 'auto');
    }
    
    // Mapare diacritice românești
    $diacritics = [
        'ă' => 'a', 'Ă' => 'A',
        'â' => 'a', 'Â' => 'A',
        'î' => 'i', 'Î' => 'I',
        'ș' => 's', 'Ș' => 'S',
        'ț' => 't', 'Ț' => 'T',
        'ş' => 's', 'Ş' => 'S',  // s-cedilla (varianta veche)
        'ţ' => 't', 'Ţ' => 'T'   // t-cedilla (varianta veche)
    ];
    
    // Înlocuim diacriticele
    $normalized = str_replace(array_keys($diacritics), array_values($diacritics), $text);
    
    // Eliminăm spațiile multiple și convertim la lowercase pentru comparație
    $normalized = preg_replace('/\s+/u', ' ', $normalized);
    $normalized = mb_strtolower($normalized, 'UTF-8');
    
    return $normalized;
}

/**
 * Extrage numele părții din structura de date (array sau object)
 */
function extractPartyName($party) {
    if (is_array($party) && isset($party['nume'])) {
        return $party['nume'];
    } elseif (is_object($party) && isset($party->nume)) {
        return $party->nume;
    }
    return '';
}

/**
 * Găsește partea care se potrivește cu termenul de căutare
 */
function findMatchingParty($parti, $searchTerm) {
    if (empty($parti) || empty($searchTerm)) {
        return null;
    }
    
    // Normalizăm termenul de căutare pentru comparații
    $normalizedSearchTerm = normalizeForSearch($searchTerm);
    $lowerSearchTerm = mb_strtolower(trim($searchTerm), 'UTF-8');
    
    // PHASE 1: Căutăm potrivire exactă (prioritate maximă)
    foreach ($parti as $party) {
        $partyName = extractPartyName($party);
        if (empty($partyName)) {
            continue;
        }
        
        // Verificare exactă cu diacritice normalizate
        $normalizedPartyName = normalizeForSearch($partyName);
        if (strcasecmp($normalizedPartyName, $normalizedSearchTerm) === 0) {
            return $party;
        }
        
        // Verificare exactă fără normalizare (pentru cazuri perfecte)
        if (strcasecmp(trim($partyName), trim($searchTerm)) === 0) {
            return $party;
        }
    }
    
    // PHASE 2: Căutăm potrivire parțială (fallback)
    foreach ($parti as $party) {
        $partyName = extractPartyName($party);
        if (empty($partyName)) {
            continue;
        }
        
        // Verificare parțială cu diacritice normalizate
        $normalizedPartyName = normalizeForSearch($partyName);
        if (mb_strpos($normalizedPartyName, $normalizedSearchTerm, 0, 'UTF-8') !== false) {
            return $party;
        }
        
        // Verificare parțială fără normalizare (pentru cazuri cu diacritice exacte)
        $lowerPartyName = mb_strtolower(trim($partyName), 'UTF-8');
        if (mb_strpos($lowerPartyName, $lowerSearchTerm, 0, 'UTF-8') !== false) {
            return $party;
        }
    }
    
    return null;
}

/**
 * Determină tipul de potrivire între numele părții și termenul de căutare
 */
function getMatchType($partyName, $searchTerm) {
    $normalizedPartyName = normalizeForSearch($partyName);
    $normalizedSearchTerm = normalizeForSearch($searchTerm);
    
    // Verificare exactă
    if (strcasecmp($normalizedPartyName, $normalizedSearchTerm) === 0) {
        return 'exact';
    }
    
    if (strcasecmp(trim($partyName), trim($searchTerm)) === 0) {
        return 'exact';
    }
    
    // Verificare parțială
    $lowerPartyName = mb_strtolower(trim($partyName), 'UTF-8');
    $lowerSearchTerm = mb_strtolower(trim($searchTerm), 'UTF-8');
    
    if (mb_strpos($normalizedPartyName, $normalizedSearchTerm, 0, 'UTF-8') !== false ||
        mb_strpos($lowerPartyName, $lowerSearchTerm, 0, 'UTF-8') !== false) {
        return 'partial';
    }
    
    return 'none';
}

/**
 * Evidențiază termenul de căutare în numele părții
 */
function highlightSearchTerm($partyName, $searchTerm) {
    $escapedPartyName = htmlspecialchars($partyName);
    
    // Încercăm evidențierea directă (case-insensitive)
    $highlighted = preg_replace(
        '/(' . preg_quote($searchTerm, '/') . ')/iu',
        '<strong class="text-primary">$1</strong>',
        $escapedPartyName
    );
    
    return $highlighted;
}

/**
 * Verifică dacă un nume de parte se potrivește cu termenul de căutare
 * și returnează numele cu evidențierea potrivirii
 */
function highlightMatchingPartyName($partyName, $searchTerm, $searchType) {
    if (empty($partyName) || empty($searchTerm) || $searchType !== 'numeParte') {
        return htmlspecialchars($partyName);
    }
    
    // Verificăm tipul de potrivire pentru styling diferențiat
    $matchType = getMatchType($partyName, $searchTerm);
    
    if ($matchType === 'none') {
        return htmlspecialchars($partyName);
    }
    
    // Încercăm să evidențiem termenul exact mai întâi
    $highlightedName = highlightSearchTerm($partyName, $searchTerm);
    
    // Aplicăm styling în funcție de tipul de potrivire
    $containerClass = 'matching-party-name party-match-indicator';
    if ($matchType === 'exact') {
        $containerClass .= ' exact-match';
    } elseif ($matchType === 'partial') {
        $containerClass .= ' partial-match';
    }
    
    return '<span class="' . $containerClass . '">' . $highlightedName . '</span>';
}

/**
 * Obține numele părții relevante din lista de părți
 */
function getRelevantPartyName($parti, $searchTerm = '', $searchType = '') {
    if (empty($parti) || !is_array($parti)) {
        return '';
    }
    
    // Pentru căutarea după nume parte, încercăm să găsim potrivirea
    if (!empty($searchTerm) && $searchType === 'numeParte') {
        $matchingParty = findMatchingParty($parti, $searchTerm);
        if ($matchingParty) {
            return $matchingParty['nume'] ?? '';
        }
    }
    
    // Fallback: returnăm prima parte
    $firstParty = reset($parti);
    if ($firstParty) {
        return $firstParty['nume'] ?? '';
    }
    
    return '';
}

/**
 * Obține calitatea părții relevante din lista de părți
 */
function getRelevantPartyQuality($parti, $searchTerm = '', $searchType = '') {
    if (empty($parti) || !is_array($parti)) {
        return '';
    }
    
    // Pentru căutarea după nume parte, încercăm să găsim potrivirea
    if (!empty($searchTerm) && $searchType === 'numeParte') {
        $matchingParty = findMatchingParty($parti, $searchTerm);
        if ($matchingParty) {
            return $matchingParty['calitate'] ?? '';
        }
    }
    
    // Fallback: returnăm prima parte
    $firstParty = reset($parti);
    if ($firstParty) {
        return $firstParty['calitate'] ?? '';
    }
    
    return '';
}
?>
