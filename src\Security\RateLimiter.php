<?php

namespace App\Security;

use App\Config\Database;
use Exception;

/**
 * Rate Limiting System for Case Monitoring
 * 
 * Implements flexible rate limiting with multiple strategies:
 * - IP-based limiting
 * - User-based limiting
 * - Action-specific limits
 * - Time window controls
 * 
 * Portal Judiciar România - Case Monitoring System
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */
class RateLimiter
{
    // Default rate limits
    private const DEFAULT_LIMITS = [
        'add_case' => ['limit' => 50, 'window' => 3600], // 50 cases per hour
        'remove_case' => ['limit' => 100, 'window' => 3600], // 100 removals per hour
        'update_frequency' => ['limit' => 200, 'window' => 3600], // 200 updates per hour
        'check_case' => ['limit' => 500, 'window' => 3600], // 500 checks per hour
        'contact_form' => ['limit' => 5, 'window' => 3600], // 5 contact forms per hour
        'email_share' => ['limit' => 10, 'window' => 3600], // 10 email shares per hour
        'api_request' => ['limit' => 1000, 'window' => 3600], // 1000 API requests per hour
        'login_attempt' => ['limit' => 10, 'window' => 900], // 10 login attempts per 15 minutes
    ];
    
    /**
     * Check if action is allowed under rate limit
     * 
     * @param string $action Action identifier
     * @param string|null $identifier User ID or IP address
     * @param array|null $customLimits Custom limits for this check
     * @return array Rate limit status
     */
    public static function checkLimit(string $action, ?string $identifier = null, ?array $customLimits = null): array
    {
        $identifier = $identifier ?? self::getIdentifier();
        $limits = $customLimits ?? self::DEFAULT_LIMITS[$action] ?? ['limit' => 100, 'window' => 3600];
        
        // Get current usage
        $usage = self::getCurrentUsage($action, $identifier, $limits['window']);
        
        $allowed = $usage['count'] < $limits['limit'];
        $remaining = max(0, $limits['limit'] - $usage['count']);
        $resetTime = $usage['window_start'] + $limits['window'];
        
        return [
            'allowed' => $allowed,
            'limit' => $limits['limit'],
            'remaining' => $remaining,
            'reset_time' => $resetTime,
            'current_count' => $usage['count'],
            'window_seconds' => $limits['window'],
            'identifier' => $identifier,
            'action' => $action
        ];
    }
    
    /**
     * Record an action attempt
     * 
     * @param string $action Action identifier
     * @param string|null $identifier User ID or IP address
     * @param bool $success Whether the action was successful
     * @param array|null $metadata Additional metadata
     * @return bool True if recorded successfully
     */
    public static function recordAttempt(string $action, ?string $identifier = null, bool $success = true, ?array $metadata = null): bool
    {
        $identifier = $identifier ?? self::getIdentifier();
        
        try {
            Database::insert('rate_limit_attempts', [
                'identifier' => $identifier,
                'action' => $action,
                'success' => $success ? 1 : 0,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'metadata' => $metadata ? json_encode($metadata, JSON_UNESCAPED_UNICODE) : null,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("Failed to record rate limit attempt: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Enforce rate limit with automatic recording
     * 
     * @param string $action Action identifier
     * @param callable $callback Function to execute if allowed
     * @param string|null $identifier User ID or IP address
     * @param array|null $customLimits Custom limits
     * @return array Result with rate limit info
     */
    public static function enforce(string $action, callable $callback, ?string $identifier = null, ?array $customLimits = null): array
    {
        $limitCheck = self::checkLimit($action, $identifier, $customLimits);
        
        if (!$limitCheck['allowed']) {
            // Record failed attempt
            self::recordAttempt($action, $identifier, false, [
                'reason' => 'rate_limit_exceeded',
                'limit' => $limitCheck['limit'],
                'current_count' => $limitCheck['current_count']
            ]);
            
            return [
                'success' => false,
                'error' => 'Rate limit exceeded. Please try again later.',
                'error_code' => 'RATE_LIMIT_EXCEEDED',
                'rate_limit' => $limitCheck
            ];
        }
        
        // Execute callback
        try {
            $result = $callback();
            
            // Record successful attempt
            self::recordAttempt($action, $identifier, true, [
                'result' => is_array($result) ? $result : ['executed' => true]
            ]);
            
            // Add rate limit info to result
            if (is_array($result)) {
                $result['rate_limit'] = $limitCheck;
            }
            
            return $result;
            
        } catch (Exception $e) {
            // Record failed attempt
            self::recordAttempt($action, $identifier, false, [
                'reason' => 'execution_error',
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Get current usage for an action
     * 
     * @param string $action Action identifier
     * @param string $identifier User ID or IP address
     * @param int $windowSeconds Time window in seconds
     * @return array Usage statistics
     */
    private static function getCurrentUsage(string $action, string $identifier, int $windowSeconds): array
    {
        $windowStart = time() - $windowSeconds;
        $windowStartDate = date('Y-m-d H:i:s', $windowStart);
        
        try {
            $result = Database::fetchOne("
                SELECT 
                    COUNT(*) as count,
                    MIN(UNIX_TIMESTAMP(created_at)) as window_start
                FROM rate_limit_attempts 
                WHERE identifier = ? 
                AND action = ? 
                AND created_at >= ?
            ", [$identifier, $action, $windowStartDate]);
            
            return [
                'count' => (int)($result['count'] ?? 0),
                'window_start' => (int)($result['window_start'] ?? $windowStart)
            ];
            
        } catch (Exception $e) {
            error_log("Failed to get rate limit usage: " . $e->getMessage());
            return ['count' => 0, 'window_start' => $windowStart];
        }
    }
    
    /**
     * Get identifier for rate limiting (IP or user ID)
     * 
     * @return string Identifier
     */
    private static function getIdentifier(): string
    {
        // Try to get user ID from session first
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (isset($_SESSION['user_id'])) {
            return 'user_' . $_SESSION['user_id'];
        }
        
        // Fall back to IP address
        return 'ip_' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown');
    }
    
    /**
     * Clean up old rate limit records
     * 
     * @param int $daysToKeep Number of days to keep records
     * @return int Number of records deleted
     */
    public static function cleanup(int $daysToKeep = 7): int
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
        
        try {
            $stmt = Database::execute(
                "DELETE FROM rate_limit_attempts WHERE created_at < ?",
                [$cutoffDate]
            );
            return $stmt->rowCount();
        } catch (Exception $e) {
            error_log("Failed to cleanup rate limit records: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get rate limit statistics
     * 
     * @param string|null $identifier Specific identifier to check
     * @param int $hours Number of hours to analyze
     * @return array Statistics
     */
    public static function getStatistics(?string $identifier = null, int $hours = 24): array
    {
        $since = date('Y-m-d H:i:s', strtotime("-{$hours} hours"));
        $identifier = $identifier ?? self::getIdentifier();
        
        try {
            $stats = Database::fetchOne("
                SELECT 
                    COUNT(*) as total_attempts,
                    SUM(success) as successful_attempts,
                    COUNT(DISTINCT action) as unique_actions,
                    MIN(created_at) as first_attempt,
                    MAX(created_at) as last_attempt
                FROM rate_limit_attempts 
                WHERE identifier = ? AND created_at >= ?
            ", [$identifier, $since]);
            
            $actionStats = Database::fetchAll("
                SELECT 
                    action,
                    COUNT(*) as attempts,
                    SUM(success) as successful,
                    MAX(created_at) as last_attempt
                FROM rate_limit_attempts 
                WHERE identifier = ? AND created_at >= ?
                GROUP BY action
                ORDER BY attempts DESC
            ", [$identifier, $since]);
            
            return [
                'identifier' => $identifier,
                'period_hours' => $hours,
                'total_attempts' => (int)($stats['total_attempts'] ?? 0),
                'successful_attempts' => (int)($stats['successful_attempts'] ?? 0),
                'failed_attempts' => (int)($stats['total_attempts'] ?? 0) - (int)($stats['successful_attempts'] ?? 0),
                'unique_actions' => (int)($stats['unique_actions'] ?? 0),
                'first_attempt' => $stats['first_attempt'],
                'last_attempt' => $stats['last_attempt'],
                'actions' => $actionStats
            ];
            
        } catch (Exception $e) {
            error_log("Failed to get rate limit statistics: " . $e->getMessage());
            return [
                'identifier' => $identifier,
                'period_hours' => $hours,
                'total_attempts' => 0,
                'successful_attempts' => 0,
                'failed_attempts' => 0,
                'unique_actions' => 0,
                'actions' => []
            ];
        }
    }
    
    /**
     * Check if identifier is currently blocked
     * 
     * @param string|null $identifier Identifier to check
     * @return array Block status
     */
    public static function isBlocked(?string $identifier = null): array
    {
        $identifier = $identifier ?? self::getIdentifier();
        
        // Check for temporary blocks (too many failed attempts)
        $recentFailures = Database::fetchOne("
            SELECT COUNT(*) as failures
            FROM rate_limit_attempts 
            WHERE identifier = ? 
            AND success = 0 
            AND created_at >= DATE_SUB(NOW(), INTERVAL 15 MINUTE)
        ", [$identifier]);
        
        $failureCount = (int)($recentFailures['failures'] ?? 0);
        $isBlocked = $failureCount >= 20; // Block after 20 failures in 15 minutes
        
        return [
            'blocked' => $isBlocked,
            'reason' => $isBlocked ? 'too_many_failures' : null,
            'failure_count' => $failureCount,
            'block_expires' => $isBlocked ? time() + 900 : null // 15 minutes
        ];
    }
    
    /**
     * Get remaining attempts for a specific action
     *
     * @param string $action Action identifier
     * @param string|null $identifier User ID or IP address
     * @param array|null $customLimits Custom limits for this check
     * @return int Number of remaining attempts
     */
    public static function getRemainingAttempts(string $action, ?string $identifier = null, ?array $customLimits = null): int
    {
        $limitCheck = self::checkLimit($action, $identifier, $customLimits);
        return $limitCheck['remaining'];
    }

    /**
     * Get rate limit headers for HTTP responses
     *
     * @param array $limitCheck Result from checkLimit()
     * @return array HTTP headers
     */
    public static function getHeaders(array $limitCheck): array
    {
        return [
            'X-RateLimit-Limit' => $limitCheck['limit'],
            'X-RateLimit-Remaining' => $limitCheck['remaining'],
            'X-RateLimit-Reset' => $limitCheck['reset_time'],
            'X-RateLimit-Window' => $limitCheck['window_seconds']
        ];
    }
}
