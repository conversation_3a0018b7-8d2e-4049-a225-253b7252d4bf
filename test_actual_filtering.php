<?php
// Test the actual filtering with real data
require_once 'bootstrap.php';
require_once 'includes/functions.php';

use App\Services\DosarService;

echo "<h1>Testing Actual Filtering Issue</h1>";

$searchTerm = "14096/3/2024*";

try {
    $dosarService = new DosarService();
    
    // Get the actual backend results
    echo "<h2>1. Backend Results</h2>";
    $backendResults = $dosarService->cautareAvansata(['numarDosar' => $searchTerm]);
    echo "<p>Backend count: " . count($backendResults) . "</p>";
    
    foreach ($backendResults as $index => $dosar) {
        echo "<div style='background: #e7f3ff; padding: 5px; margin: 2px 0;'>";
        echo "Backend #" . ($index + 1) . ": " . ($dosar->numar ?? 'N/A');
        echo "</div>";
    }
    
    // Test the normalization
    echo "<h2>2. Case Number Normalization</h2>";
    $reflection = new ReflectionClass($dosarService);
    $normalizeMethod = $reflection->getMethod('normalizeCaseNumber');
    $normalizeMethod->setAccessible(true);
    
    $caseNumberInfo = $normalizeMethod->invoke($dosarService, $searchTerm);
    echo "<pre>" . print_r($caseNumberInfo, true) . "</pre>";
    
    // Test the filtering function from index.php
    echo "<h2>3. Testing index.php filterResultsByCaseNumberPattern</h2>";
    
    // Include the function from index.php
    include_once 'index.php';
    
    $filteredResults = filterResultsByCaseNumberPattern($backendResults, $caseNumberInfo);
    echo "<p>Filtered count: " . count($filteredResults) . "</p>";
    
    foreach ($filteredResults as $index => $dosar) {
        echo "<div style='background: #f0fff0; padding: 5px; margin: 2px 0;'>";
        echo "Filtered #" . ($index + 1) . ": " . ($dosar->numar ?? 'N/A');
        echo "</div>";
    }
    
    // Compare results
    echo "<h2>4. Comparison</h2>";
    $backendCount = count($backendResults);
    $filteredCount = count($filteredResults);
    
    echo "<p>Backend: $backendCount results</p>";
    echo "<p>After filtering: $filteredCount results</p>";
    
    if ($backendCount != $filteredCount) {
        echo "<p style='color: red;'><strong>DISCREPANCY FOUND!</strong></p>";
        
        // Find missing cases
        $backendNumbers = array_map(function($dosar) { return $dosar->numar; }, $backendResults);
        $filteredNumbers = array_map(function($dosar) { return $dosar->numar; }, $filteredResults);
        
        $missing = array_diff($backendNumbers, $filteredNumbers);
        
        if (!empty($missing)) {
            echo "<p style='color: red;'>Missing cases after filtering:</p>";
            foreach ($missing as $missingCase) {
                echo "<div style='background: #f8d7da; padding: 5px; margin: 2px 0;'>";
                echo "MISSING: $missingCase";
                echo "</div>";
            }
        }
    } else {
        echo "<p style='color: green;'>✓ No discrepancy - filtering works correctly</p>";
    }
    
    // Test the DosarService filtering function
    echo "<h2>5. Testing DosarService filterResultsByCaseNumberPattern</h2>";
    
    $filterMethod = $reflection->getMethod('filterResultsByCaseNumberPattern');
    $filterMethod->setAccessible(true);
    
    $dosarServiceFiltered = $filterMethod->invoke($dosarService, $backendResults, $caseNumberInfo);
    echo "<p>DosarService filtered count: " . count($dosarServiceFiltered) . "</p>";
    
    foreach ($dosarServiceFiltered as $index => $dosar) {
        echo "<div style='background: #fff3cd; padding: 5px; margin: 2px 0;'>";
        echo "DosarService #" . ($index + 1) . ": " . ($dosar->numar ?? 'N/A');
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
