<?php
/**
 * Test the intervener pattern detection specifically
 */

require_once 'bootstrap.php';

use App\Services\DosarService;

echo "=== TESTING INTERVENER PATTERN DETECTION ===" . PHP_EOL;
echo "Case: 130/98/2022 from TribunalulIALOMITA" . PHP_EOL;
echo "Target: SARAGEA TUDORIŢA" . PHP_EOL;
echo PHP_EOL;

$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';
$targetParty = 'SARAGEA TUDORIŢA';

try {
    $dosarService = new DosarService();
    
    // Get the case details using the SOAP API
    $searchParams = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    // Use reflection to access the private executeSoapCallWithRetry method
    $reflection = new ReflectionClass($dosarService);
    $method = $reflection->getMethod('executeSoapCallWithRetry');
    $method->setAccessible(true);
    
    $response = $method->invoke($dosarService, 'CautareDosare2', $searchParams, "Test error");
    
    if (isset($response->CautareDosare2Result->Dosar)) {
        $dosare = $response->CautareDosare2Result->Dosar;
        if (!is_array($dosare)) {
            $dosare = [$dosare];
        }
        
        $dosar = $dosare[0];
        
        echo "=== TESTING INTERVENER PATTERN MATCHING ===" . PHP_EOL;
        
        if (isset($dosar->sedinte) && isset($dosar->sedinte->DosarSedinta)) {
            $sedinte = $dosar->sedinte->DosarSedinta;
            if (!is_array($sedinte)) {
                $sedinte = [$sedinte];
            }
            
            foreach ($sedinte as $sedintaIndex => $sedinta) {
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $solutieText = $sedinta->solutieSumar;
                    
                    echo "--- Session " . ($sedintaIndex + 1) . " ---" . PHP_EOL;
                    
                    // Test the intervener pattern
                    $intervenerPattern = '/introducerea acestora în cauză[^.]*în calitate de intervenienţi[^.]*\./i';
                    
                    if (preg_match($intervenerPattern, $solutieText, $matches)) {
                        echo "✅ INTERVENER PATTERN MATCHED!" . PHP_EOL;
                        echo "Matched text: " . $matches[0] . PHP_EOL;
                        
                        // Find the party list that precedes this statement
                        $beforeText = substr($solutieText, 0, strpos($solutieText, $matches[0]));
                        
                        echo "Text before intervener statement (last 500 chars):" . PHP_EOL;
                        echo "..." . substr($beforeText, -500) . PHP_EOL;
                        echo PHP_EOL;
                        
                        // Look for the last party list before the intervener statement
                        $partyPattern = '/([A-ZĂÂÎȘȚŢ][^.]*(?:,\s*[A-ZĂÂÎȘȚŢ][^.]*)*)\s*\.\s*Dispune/u';
                        if (preg_match($partyPattern, $beforeText, $partyMatches)) {
                            echo "✅ PARTY LIST PATTERN MATCHED!" . PHP_EOL;
                            echo "Party list text: " . $partyMatches[1] . PHP_EOL;
                            
                            $partiesText = $partyMatches[1];
                            $partyNames = explode(',', $partiesText); // Use comma as separator
                            
                            echo "Extracted party names:" . PHP_EOL;
                            foreach ($partyNames as $index => $partyName) {
                                $partyName = trim($partyName);
                                echo "  " . ($index + 1) . ". '{$partyName}'" . PHP_EOL;
                                
                                if (stripos($partyName, $targetParty) !== false) {
                                    echo "     🎯 TARGET PARTY FOUND!" . PHP_EOL;
                                }
                            }
                        } else {
                            echo "❌ PARTY LIST PATTERN NOT MATCHED" . PHP_EOL;
                            echo "Trying alternative patterns..." . PHP_EOL;
                            
                            // Try a simpler pattern
                            $simplePattern = '/([A-ZĂÂÎȘȚŢ][^.]*)\s*\.\s*Dispune introducerea/u';
                            if (preg_match($simplePattern, $beforeText, $simpleMatches)) {
                                echo "✅ SIMPLE PATTERN MATCHED!" . PHP_EOL;
                                echo "Simple match: " . $simpleMatches[1] . PHP_EOL;
                            }
                        }
                    } else {
                        echo "❌ Intervener pattern not found in this session" . PHP_EOL;
                        
                        // Check if the text contains intervener-related keywords
                        $keywords = ['intervenient', 'introducerea', 'acestora', 'cauză'];
                        $foundKeywords = [];
                        
                        foreach ($keywords as $keyword) {
                            if (stripos($solutieText, $keyword) !== false) {
                                $foundKeywords[] = $keyword;
                            }
                        }
                        
                        if (!empty($foundKeywords)) {
                            echo "Found keywords: " . implode(', ', $foundKeywords) . PHP_EOL;
                        }
                    }
                    
                    echo PHP_EOL;
                }
            }
        }
        
    } else {
        echo "❌ No SOAP response data" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
}
