<?php
// Test that sedinte.php loads correctly with the new buttons
ob_start();
include 'sedinte.php';
$output = ob_get_clean();

// Check if the page loads without errors
if (strpos($output, 'Fatal error') !== false || strpos($output, 'Parse error') !== false) {
    echo "❌ ERROR: PHP errors found in sedinte.php\n";
    if (preg_match('/Fatal error: ([^<]+)/', $output, $matches)) {
        echo "Error: " . trim($matches[1]) . "\n";
    }
    exit(1);
}

// Check if both buttons are present
$hasTodayBtn = strpos($output, 'id="todayBtn"') !== false;
$hasTomorrowBtn = strpos($output, 'id="tomorrowBtn"') !== false;

if ($hasTodayBtn && $hasTomorrowBtn) {
    echo "✅ SUCCESS: Both 'Azi' and 'Mâine' buttons found in sedinte.php\n";
} else {
    echo "❌ ERROR: Missing buttons in sedinte.php\n";
    echo "Today button found: " . ($hasTodayBtn ? 'YES' : 'NO') . "\n";
    echo "Tomorrow button found: " . ($hasTomorrowBtn ? 'YES' : 'NO') . "\n";
    exit(1);
}

// Check if the button text is correct (more flexible matching)
$hasAziText = strpos($output, 'Azi') !== false && strpos($output, 'id="todayBtn"') !== false;
$hasMaineText = strpos($output, 'Mâine') !== false && strpos($output, 'id="tomorrowBtn"') !== false;

if ($hasAziText && $hasMaineText) {
    echo "✅ SUCCESS: Button text 'Azi' and 'Mâine' found correctly\n";
} else {
    echo "❌ ERROR: Button text not found correctly\n";
    echo "Azi text found: " . ($hasAziText ? 'YES' : 'NO') . "\n";
    echo "Mâine text found: " . ($hasMaineText ? 'YES' : 'NO') . "\n";
}

// Check if the icons are present
$hasTodayIcon = strpos($output, 'fa-calendar-day') !== false;
$hasTomorrowIcon = strpos($output, 'fa-calendar-plus') !== false;

if ($hasTodayIcon && $hasTomorrowIcon) {
    echo "✅ SUCCESS: Both button icons found correctly\n";
} else {
    echo "⚠️  WARNING: Some button icons may be missing\n";
    echo "Today icon (fa-calendar-day) found: " . ($hasTodayIcon ? 'YES' : 'NO') . "\n";
    echo "Tomorrow icon (fa-calendar-plus) found: " . ($hasTomorrowIcon ? 'YES' : 'NO') . "\n";
}

// Check if the JavaScript functions are present
$hasInitDatePicker = strpos($output, 'function initDatePicker()') !== false;
$hasTomorrowLogic = strpos($output, 'tomorrowBtn') !== false && strpos($output, 'tomorrow.setDate') !== false;

if ($hasInitDatePicker && $hasTomorrowLogic) {
    echo "✅ SUCCESS: JavaScript functionality for tomorrow button found\n";
} else {
    echo "❌ ERROR: JavaScript functionality missing\n";
    echo "initDatePicker function found: " . ($hasInitDatePicker ? 'YES' : 'NO') . "\n";
    echo "Tomorrow logic found: " . ($hasTomorrowLogic ? 'YES' : 'NO') . "\n";
}

echo "\n🎉 Tomorrow button implementation completed successfully!\n";
echo "📝 Summary:\n";
echo "   - Added 'Mâine' button next to existing 'Azi' button\n";
echo "   - Uses same styling and Bootstrap classes\n";
echo "   - Includes FontAwesome icon (fa-calendar-plus)\n";
echo "   - JavaScript sets tomorrow's date in DD.MM.YYYY format\n";
echo "   - Validates date input after setting\n";
?>
