<?php
/**
 * Portal Judiciar - SMTP Test
 * 
 * Test rapid pentru verificarea configurației SMTP
 */

// Încărcăm bootstrap-ul aplicației
require_once 'bootstrap.php';

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\SMTP;

// Inițializăm sesiunea
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$testResult = null;
$debugOutput = [];

// Verificăm dacă formularul a fost trimis
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $testEmail = $_POST['test_email'] ?? CONTACT_EMAIL;
    
    try {
        $mail = new PHPMailer(true);
        
        // Capturăm debug output
        $mail->SMTPDebug = SMTP::DEBUG_CONNECTION;
        $mail->Debugoutput = function($str, $level) use (&$debugOutput) {
            $debugOutput[] = "Level $level: " . trim($str);
        };
        
        // Configurăm serverul SMTP
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = SMTP_PORT;
        
        // Configurăm email-ul de test
        $mail->setFrom(SMTP_USERNAME, 'Portal Judiciar Test');
        $mail->addAddress($testEmail, 'Test Recipient');
        
        // Setăm encoding-ul
        $mail->CharSet = 'UTF-8';
        $mail->Encoding = 'base64';
        
        // Conținutul email-ului
        $mail->isHTML(true);
        $mail->Subject = 'Test SMTP - Portal Judiciar';
        $mail->Body = '
        <h2>Test SMTP Reușit!</h2>
        <p>Acest email confirmă că configurația SMTP funcționează corect.</p>
        <p><strong>Timestamp:</strong> ' . date('d.m.Y H:i:s') . '</p>
        <p><strong>Server:</strong> ' . SMTP_HOST . ':' . SMTP_PORT . '</p>
        <p><strong>Username:</strong> ' . SMTP_USERNAME . '</p>
        ';
        
        $mail->AltBody = 'Test SMTP Reușit! Configurația SMTP funcționează corect. Timestamp: ' . date('d.m.Y H:i:s');
        
        // Trimitem email-ul
        $mail->send();
        
        $testResult = [
            'success' => true,
            'message' => 'Email-ul de test a fost trimis cu succes!'
        ];
        
    } catch (Exception $e) {
        $testResult = [
            'success' => false,
            'message' => 'Eroare la trimiterea email-ului: ' . $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }
}

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test SMTP - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            padding: 1.5rem;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        .config-item:last-child {
            border-bottom: none;
        }
        .config-value {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.875rem;
        }
        .debug-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .credentials-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="text-center mb-4">
            <h1 class="display-5">
                <i class="fas fa-envelope-open-text me-2"></i>
                Test SMTP Configuration
            </h1>
            <p class="lead">Verificare rapidă a configurației email</p>
        </div>

        <!-- Credentials Warning -->
        <?php if (SMTP_USERNAME === '<EMAIL>' || SMTP_PASSWORD === 'your-app-password'): ?>
            <div class="credentials-warning">
                <h5><i class="fas fa-exclamation-triangle me-2"></i>Credentiale neconfigurate!</h5>
                <p class="mb-0">
                    Trebuie să configurați credentialele SMTP reale în <code>src/Config/constants.php</code> 
                    înainte de a putea testa trimiterea email-urilor.
                </p>
            </div>
        <?php endif; ?>

        <!-- Current Configuration -->
        <div class="test-section">
            <h3><i class="fas fa-cog me-2"></i>Configurație Curentă</h3>
            <div class="config-item">
                <span>SMTP Host</span>
                <span class="config-value"><?php echo htmlspecialchars(SMTP_HOST); ?></span>
            </div>
            <div class="config-item">
                <span>SMTP Port</span>
                <span class="config-value"><?php echo htmlspecialchars(SMTP_PORT); ?></span>
            </div>
            <div class="config-item">
                <span>Username</span>
                <span class="config-value"><?php echo htmlspecialchars(SMTP_USERNAME); ?></span>
            </div>
            <div class="config-item">
                <span>Password</span>
                <span class="config-value">
                    <?php echo SMTP_PASSWORD === 'your-app-password' ? 'your-app-password' : substr(SMTP_PASSWORD, 0, 3) . '***'; ?>
                </span>
            </div>
            <div class="config-item">
                <span>Contact Email</span>
                <span class="config-value"><?php echo htmlspecialchars(CONTACT_EMAIL); ?></span>
            </div>
        </div>

        <!-- Test Form -->
        <div class="test-section">
            <h3><i class="fas fa-paper-plane me-2"></i>Test Trimitere Email</h3>
            
            <?php if ($testResult): ?>
                <div class="alert alert-<?php echo $testResult['success'] ? 'success' : 'danger'; ?> mb-3">
                    <h5>
                        <i class="fas fa-<?php echo $testResult['success'] ? 'check' : 'times'; ?> me-2"></i>
                        <?php echo $testResult['success'] ? 'Succes!' : 'Eroare!'; ?>
                    </h5>
                    <p class="mb-0"><?php echo htmlspecialchars($testResult['message']); ?></p>
                    <?php if (!$testResult['success'] && isset($testResult['file'])): ?>
                        <small class="d-block mt-2">
                            <strong>Fișier:</strong> <?php echo htmlspecialchars($testResult['file']); ?><br>
                            <strong>Linia:</strong> <?php echo htmlspecialchars($testResult['line']); ?>
                        </small>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <form method="POST" class="mb-3">
                <div class="mb-3">
                    <label for="test_email" class="form-label">Email destinatar pentru test:</label>
                    <input type="email" 
                           class="form-control" 
                           id="test_email" 
                           name="test_email" 
                           value="<?php echo htmlspecialchars(CONTACT_EMAIL); ?>" 
                           required>
                    <div class="form-text">Email-ul unde va fi trimis mesajul de test</div>
                </div>
                
                <button type="submit" 
                        class="btn btn-primary"
                        <?php echo (SMTP_USERNAME === '<EMAIL>' || SMTP_PASSWORD === 'your-app-password') ? 'disabled' : ''; ?>>
                    <i class="fas fa-paper-plane me-2"></i>
                    Trimite Email de Test
                </button>
            </form>

            <?php if (!empty($debugOutput)): ?>
                <div class="mt-3">
                    <h5>Debug Output SMTP:</h5>
                    <div class="debug-output"><?php echo htmlspecialchars(implode("\n", $debugOutput)); ?></div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Instructions -->
        <div class="test-section">
            <h3><i class="fas fa-info-circle me-2"></i>Instrucțiuni</h3>
            
            <div class="alert alert-info">
                <h6><i class="fas fa-lightbulb me-2"></i>Pentru configurarea Gmail:</h6>
                <ol class="mb-0">
                    <li>Activați autentificarea în 2 pași în contul Google</li>
                    <li>Generați o parolă de aplicație pentru "Mail"</li>
                    <li>Actualizați <code>SMTP_USERNAME</code> cu adresa Gmail</li>
                    <li>Actualizați <code>SMTP_PASSWORD</code> cu parola de aplicație (16 caractere)</li>
                </ol>
            </div>

            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>Probleme comune:</h6>
                <ul class="mb-0">
                    <li><strong>"Could not authenticate"</strong> - Credentiale greșite sau lipsă parolă de aplicație</li>
                    <li><strong>"Connection timed out"</strong> - Firewall blochează portul SMTP</li>
                    <li><strong>"SSL connection failed"</strong> - Extensia OpenSSL lipsește</li>
                </ul>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="test-section text-center">
            <div class="d-grid gap-2 d-md-block">
                <a href="debug_contact.php" class="btn btn-info">
                    <i class="fas fa-bug me-2"></i>
                    Debug Complet
                </a>
                <a href="contact.php" class="btn btn-primary">
                    <i class="fas fa-envelope me-2"></i>
                    Pagina Contact
                </a>
                <a href="SMTP_SETUP_GUIDE.md" class="btn btn-secondary" target="_blank">
                    <i class="fas fa-book me-2"></i>
                    Ghid Configurare
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
