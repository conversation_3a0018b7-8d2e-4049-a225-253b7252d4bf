<?php
/**
 * Test Enhanced Filtering
 * Verifică că sistemul filtrează cuvintele aleatorii dar păstrează părțile reale
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Enhanced Filtering</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .highlight { background: yellow; font-weight: bold; }
    .soap-party { background-color: #e8f5e8; }
    .decision-party { background-color: #fff3e0; }
</style></head><body>";

echo "<h1>🔍 Test Enhanced Filtering</h1>";
echo "<p>Verifică că sistemul filtrează cuvintele aleatorii dar păstrează părțile reale</p>";
echo "<hr>";

try {
    $dosarService = new \App\Services\DosarService();
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');
    
    if (!$dosar || !isset($dosar->parti)) {
        echo "<p class='error'>❌ Nu s-au putut obține datele dosarului</p>";
        exit;
    }
    
    $totalParties = count($dosar->parti);
    
    echo "<div class='section'>";
    echo "<h2>📊 Analiza Părților cu Filtrare Îmbunătățită</h2>";
    
    echo "<table>";
    echo "<tr><th>Metric</th><th>Valoare</th><th>Status</th></tr>";
    echo "<tr><td>Total părți afișate</td><td>{$totalParties}</td><td class='" . ($totalParties > 100 && $totalParties < 300 ? 'success' : 'warning') . "'>" . ($totalParties > 100 && $totalParties < 300 ? '✅ Număr rezonabil' : '⚠️ Verifică filtrarea') . "</td></tr>";
    
    // Analizăm sursele părților
    $soapCount = 0;
    $decisionCount = 0;
    $unknownCount = 0;
    
    foreach ($dosar->parti as $party) {
        $source = $party->source ?? 'unknown';
        switch ($source) {
            case 'soap_api': $soapCount++; break;
            case 'decision_text': $decisionCount++; break;
            default: $unknownCount++; break;
        }
    }
    
    echo "<tr><td>Părți din SOAP API</td><td>{$soapCount}</td><td class='success'>✅ Părți oficiale</td></tr>";
    echo "<tr><td>Părți din text decizie (filtrate)</td><td>{$decisionCount}</td><td class='" . ($decisionCount > 0 && $decisionCount < 200 ? 'success' : 'warning') . "'>" . ($decisionCount > 0 && $decisionCount < 200 ? '✅ Filtrate corect' : '⚠️ Verifică filtrarea') . "</td></tr>";
    echo "<tr><td>Părți sursă necunoscută</td><td>{$unknownCount}</td><td class='info'>ℹ️ Info</td></tr>";
    
    echo "</table>";
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>📋 Lista Părților (Primele 30)</h2>";
    
    echo "<table>";
    echo "<tr><th>#</th><th>Nume</th><th>Calitate</th><th>Sursă</th><th>Tip</th></tr>";
    
    $displayLimit = min(30, $totalParties);
    for ($i = 0; $i < $displayLimit; $i++) {
        $party = $dosar->parti[$i];
        $source = $party->source ?? 'unknown';
        $rowClass = '';
        $tip = '';
        
        // Determinăm tipul părții
        if ($source === 'soap_api') {
            $rowClass = 'soap-party';
            $tip = 'Parte oficială';
        } elseif ($source === 'decision_text') {
            $rowClass = 'decision-party';
            $tip = 'Extras din text (filtrat)';
        } else {
            $rowClass = '';
            $tip = 'Necunoscut';
        }
        
        echo "<tr class='{$rowClass}'>";
        echo "<td>" . ($i + 1) . "</td>";
        echo "<td>" . htmlspecialchars($party->nume ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($party->calitate ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($source) . "</td>";
        echo "<td>{$tip}</td>";
        echo "</tr>";
    }
    
    if ($totalParties > 30) {
        echo "<tr><td colspan='5' class='info'>... și încă " . ($totalParties - 30) . " părți</td></tr>";
    }
    
    echo "</table>";
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🔍 Verificare Părți Specifice</h2>";
    
    // Verificăm SARAGEA TUDORIŢA
    $saragea_found = false;
    $saragea_position = -1;
    $saragea_source = '';
    
    foreach ($dosar->parti as $index => $party) {
        if (isset($party->nume) && 
            stripos($party->nume, 'SARAGEA') !== false && 
            stripos($party->nume, 'TUDORI') !== false) {
            $saragea_found = true;
            $saragea_position = $index + 1;
            $saragea_source = $party->source ?? 'unknown';
            break;
        }
    }
    
    echo "<table>";
    echo "<tr><th>Test</th><th>Rezultat</th><th>Status</th></tr>";
    echo "<tr><td>SARAGEA TUDORIŢA găsită</td><td>" . ($saragea_found ? "Da" : "Nu") . "</td><td class='" . ($saragea_found ? 'success' : 'error') . "'>" . ($saragea_found ? '✅ Găsită' : '❌ Lipsă') . "</td></tr>";
    
    if ($saragea_found) {
        echo "<tr><td>Poziția în listă</td><td>{$saragea_position}</td><td class='info'>ℹ️ Info</td></tr>";
        echo "<tr><td>Sursă date</td><td>{$saragea_source}</td><td class='info'>ℹ️ Info</td></tr>";
        echo "<tr><td>Tip parte</td><td>" . ($saragea_source === 'soap_api' ? 'Oficială' : 'Din text') . "</td><td class='info'>ℹ️ Info</td></tr>";
    }
    
    // Verificăm dacă există cuvinte suspecte (care ar trebui filtrate)
    $suspiciousWords = ['PENTRU', 'CONTRA', 'PRIN', 'CĂTRE', 'LEI', 'RON', 'HOTĂRÂREA', 'SENTINȚA'];
    $suspiciousFound = [];
    
    foreach ($dosar->parti as $party) {
        $nume = mb_strtoupper($party->nume ?? '', 'UTF-8');
        foreach ($suspiciousWords as $word) {
            if (stripos($nume, $word) !== false) {
                $suspiciousFound[] = $party->nume;
                break;
            }
        }
    }
    
    echo "<tr><td>Cuvinte suspecte găsite</td><td>" . count($suspiciousFound) . "</td><td class='" . (count($suspiciousFound) === 0 ? 'success' : 'warning') . "'>" . (count($suspiciousFound) === 0 ? '✅ Filtrate corect' : '⚠️ Verifică filtrarea') . "</td></tr>";
    
    if (!empty($suspiciousFound)) {
        echo "<tr><td>Exemple cuvinte suspecte</td><td>" . htmlspecialchars(implode(', ', array_slice($suspiciousFound, 0, 3))) . "</td><td class='warning'>⚠️ Ar trebui filtrate</td></tr>";
    }
    
    echo "</table>";
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🎯 Evaluare Calitate Filtrare</h2>";
    
    $reasonable_count = ($totalParties >= 100 && $totalParties <= 250);
    $saragea_present = $saragea_found;
    $good_filtering = (count($suspiciousFound) <= 5); // Maxim 5 cuvinte suspecte
    $has_decision_parties = ($decisionCount > 0);
    
    echo "<table>";
    echo "<tr><th>Criteriu</th><th>Status</th></tr>";
    echo "<tr><td>Număr rezonabil de părți (100-250)</td><td class='" . ($reasonable_count ? 'success' : 'warning') . "'>" . ($reasonable_count ? '✅ Rezonabil' : '⚠️ Verifică') . "</td></tr>";
    echo "<tr><td>SARAGEA TUDORIŢA prezentă</td><td class='" . ($saragea_present ? 'success' : 'error') . "'>" . ($saragea_present ? '✅ Prezentă' : '❌ Lipsă') . "</td></tr>";
    echo "<tr><td>Filtrare eficientă (< 5 cuvinte suspecte)</td><td class='" . ($good_filtering ? 'success' : 'warning') . "'>" . ($good_filtering ? '✅ Bună' : '⚠️ Îmbunătățește') . "</td></tr>";
    echo "<tr><td>Extragere din text activă</td><td class='" . ($has_decision_parties ? 'success' : 'warning') . "'>" . ($has_decision_parties ? '✅ Activă' : '⚠️ Dezactivată') . "</td></tr>";
    echo "</table>";
    
    if ($reasonable_count && $saragea_present && $good_filtering && $has_decision_parties) {
        echo "<p class='success'>🎉 PERFECT! Filtrarea îmbunătățită funcționează excelent!</p>";
        echo "<p class='success'>✅ Păstrează părțile reale importante</p>";
        echo "<p class='success'>✅ Filtrează cuvintele aleatorii din text</p>";
        echo "<p class='success'>✅ Numărul de părți este rezonabil</p>";
    } elseif ($saragea_present && $has_decision_parties) {
        echo "<p class='warning'>⚠️ PARȚIAL: Funcționează dar poate fi îmbunătățit</p>";
    } else {
        echo "<p class='error'>❌ PROBLEME: Necesită ajustări suplimentare</p>";
    }
    
    echo "<h3>🔗 Test Links</h3>";
    echo "<ul>";
    echo "<li><a href='detalii_dosar.php?numar=130/98/2022&institutie=TribunalulIALOMITA' target='_blank'>Pagina principală (cu filtrare îmbunătățită)</a></li>";
    echo "<li><a href='detalii_dosar.php?numar=130/98/2022&institutie=TribunalulIALOMITA&numeParte=Saragea%20Tudorita' target='_blank'>Test căutare SARAGEA TUDORIŢA</a></li>";
    echo "<li><a href='detalii_dosar.php?numar=130/98/2022&institutie=TribunalulIALOMITA&debug=1' target='_blank'>Pagina cu debug</a></li>";
    echo "</ul>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2 class='error'>❌ Error Occurred</h2>";
    echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p class='info'>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Test completat la " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
