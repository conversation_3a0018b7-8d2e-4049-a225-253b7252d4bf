<?php
/**
 * Test SOAP Only Real Parties
 * Verifică că sistemul afișează doar părțile reale din SOAP API, fără cuvinte din sentință
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Test SOAP Only Real Parties</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .highlight { background: yellow; font-weight: bold; }
    .soap-party { background-color: #e8f5e8; }
</style></head><body>";

echo "<h1>🔍 Test SOAP Only Real Parties</h1>";
echo "<p>Verifică că sistemul afișează doar părțile reale din SOAP API, fără cuvinte din sentință</p>";
echo "<hr>";

try {
    $dosarService = new \App\Services\DosarService();
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');
    
    if (!$dosar || !isset($dosar->parti)) {
        echo "<p class='error'>❌ Nu s-au putut obține datele dosarului</p>";
        exit;
    }
    
    $totalParties = count($dosar->parti);
    
    echo "<div class='section'>";
    echo "<h2>📊 Analiza Părților (DOAR SOAP API)</h2>";
    
    echo "<table>";
    echo "<tr><th>Metric</th><th>Valoare</th><th>Status</th></tr>";
    echo "<tr><td>Total părți afișate</td><td>{$totalParties}</td><td class='" . ($totalParties <= 150 ? 'success' : 'warning') . "'>" . ($totalParties <= 150 ? '✅ Doar părți reale' : '⚠️ Posibil încă extrage din text') . "</td></tr>";
    
    // Analizăm sursele părților
    $soapCount = 0;
    $decisionCount = 0;
    $unknownCount = 0;
    
    foreach ($dosar->parti as $party) {
        $source = $party->source ?? 'unknown';
        switch ($source) {
            case 'soap_api': $soapCount++; break;
            case 'decision_text': $decisionCount++; break;
            default: $unknownCount++; break;
        }
    }
    
    echo "<tr><td>Părți din SOAP API</td><td>{$soapCount}</td><td class='success'>✅ Părți oficiale reale</td></tr>";
    echo "<tr><td>Părți din text decizie</td><td>{$decisionCount}</td><td class='" . ($decisionCount === 0 ? 'success' : 'error') . "'>" . ($decisionCount === 0 ? '✅ Niciuna (PERFECT!)' : '❌ Încă extrage din text!') . "</td></tr>";
    echo "<tr><td>Părți sursă necunoscută</td><td>{$unknownCount}</td><td class='info'>ℹ️ Info</td></tr>";
    
    echo "</table>";
    
    if ($decisionCount === 0) {
        echo "<p class='success'>🎉 PERFECT! Nu se mai extrag cuvinte din sentință!</p>";
    } else {
        echo "<p class='error'>❌ PROBLEMĂ: Încă se extrag {$decisionCount} părți din text!</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>📋 Lista Completă a Părților Reale</h2>";
    
    echo "<table>";
    echo "<tr><th>#</th><th>Nume</th><th>Calitate</th><th>Sursă</th><th>Validare</th></tr>";
    
    for ($i = 0; $i < $totalParties; $i++) {
        $party = $dosar->parti[$i];
        $source = $party->source ?? 'unknown';
        $nume = $party->nume ?? 'N/A';
        
        // Verificăm dacă pare a fi o parte reală
        $isRealParty = true;
        $validationNote = 'Parte reală';
        
        // Verificăm pentru cuvinte suspecte
        $suspiciousWords = ['PENTRU', 'CONTRA', 'PRIN', 'CĂTRE', 'LEI', 'RON', 'HOTĂRÂREA', 'SENTINȚA', 'DECIZIA', 'CONSIDERÂND'];
        foreach ($suspiciousWords as $word) {
            if (stripos($nume, $word) !== false) {
                $isRealParty = false;
                $validationNote = 'Cuvânt suspect din text';
                break;
            }
        }
        
        // Verificăm dacă este doar un număr
        if (preg_match('/^\d+$/', $nume)) {
            $isRealParty = false;
            $validationNote = 'Doar număr';
        }
        
        // Verificăm dacă este prea scurt
        if (strlen($nume) < 3) {
            $isRealParty = false;
            $validationNote = 'Prea scurt';
        }
        
        $rowClass = $isRealParty ? 'soap-party' : 'style="background-color: #ffebee;"';
        $validationClass = $isRealParty ? 'success' : 'error';
        
        echo "<tr class='{$rowClass}'>";
        echo "<td>" . ($i + 1) . "</td>";
        echo "<td>" . htmlspecialchars($nume) . "</td>";
        echo "<td>" . htmlspecialchars($party->calitate ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($source) . "</td>";
        echo "<td class='{$validationClass}'>{$validationNote}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🔍 Verificare Părți Specifice</h2>";
    
    // Verificăm SARAGEA TUDORIŢA
    $saragea_found = false;
    $saragea_position = -1;
    $saragea_source = '';
    
    foreach ($dosar->parti as $index => $party) {
        if (isset($party->nume) && 
            stripos($party->nume, 'SARAGEA') !== false && 
            stripos($party->nume, 'TUDORI') !== false) {
            $saragea_found = true;
            $saragea_position = $index + 1;
            $saragea_source = $party->source ?? 'unknown';
            break;
        }
    }
    
    echo "<table>";
    echo "<tr><th>Test</th><th>Rezultat</th><th>Status</th></tr>";
    echo "<tr><td>SARAGEA TUDORIŢA găsită</td><td>" . ($saragea_found ? "Da" : "Nu") . "</td><td class='" . ($saragea_found ? 'success' : 'error') . "'>" . ($saragea_found ? '✅ Găsită' : '❌ Lipsă') . "</td></tr>";
    
    if ($saragea_found) {
        echo "<tr><td>Poziția în listă</td><td>{$saragea_position}</td><td class='info'>ℹ️ Info</td></tr>";
        echo "<tr><td>Sursă date</td><td>{$saragea_source}</td><td class='info'>ℹ️ Info</td></tr>";
        echo "<tr><td>Este din SOAP API</td><td>" . ($saragea_source === 'soap_api' ? 'Da' : 'Nu') . "</td><td class='" . ($saragea_source === 'soap_api' ? 'success' : 'warning') . "'>" . ($saragea_source === 'soap_api' ? '✅ Parte oficială' : '⚠️ Din text') . "</td></tr>";
    }
    
    // Verificăm dacă există cuvinte suspecte
    $suspiciousCount = 0;
    $suspiciousExamples = [];
    
    foreach ($dosar->parti as $party) {
        $nume = mb_strtoupper($party->nume ?? '', 'UTF-8');
        $suspiciousWords = ['PENTRU', 'CONTRA', 'PRIN', 'CĂTRE', 'LEI', 'RON', 'HOTĂRÂREA', 'SENTINȚA'];
        foreach ($suspiciousWords as $word) {
            if (stripos($nume, $word) !== false) {
                $suspiciousCount++;
                if (count($suspiciousExamples) < 3) {
                    $suspiciousExamples[] = $party->nume;
                }
                break;
            }
        }
    }
    
    echo "<tr><td>Cuvinte suspecte găsite</td><td>{$suspiciousCount}</td><td class='" . ($suspiciousCount === 0 ? 'success' : 'error') . "'>" . ($suspiciousCount === 0 ? '✅ Niciuna (PERFECT!)' : '❌ Încă există!') . "</td></tr>";
    
    if (!empty($suspiciousExamples)) {
        echo "<tr><td>Exemple cuvinte suspecte</td><td>" . htmlspecialchars(implode(', ', $suspiciousExamples)) . "</td><td class='error'>❌ Trebuie eliminate</td></tr>";
    }
    
    echo "</table>";
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🎯 Evaluare Finală</h2>";
    
    $only_soap = ($decisionCount === 0);
    $no_suspicious = ($suspiciousCount === 0);
    $reasonable_count = ($totalParties >= 10 && $totalParties <= 150);
    $saragea_in_soap = ($saragea_found && $saragea_source === 'soap_api');
    
    echo "<table>";
    echo "<tr><th>Criteriu</th><th>Status</th></tr>";
    echo "<tr><td>Doar părți din SOAP API</td><td class='" . ($only_soap ? 'success' : 'error') . "'>" . ($only_soap ? '✅ Perfect' : '❌ Încă extrage din text') . "</td></tr>";
    echo "<tr><td>Fără cuvinte suspecte</td><td class='" . ($no_suspicious ? 'success' : 'error') . "'>" . ($no_suspicious ? '✅ Perfect' : '❌ Încă există') . "</td></tr>";
    echo "<tr><td>Număr rezonabil de părți</td><td class='" . ($reasonable_count ? 'success' : 'warning') . "'>" . ($reasonable_count ? '✅ Rezonabil' : '⚠️ Verifică') . "</td></tr>";
    echo "<tr><td>SARAGEA în SOAP API</td><td class='" . ($saragea_in_soap ? 'success' : 'warning') . "'>" . ($saragea_in_soap ? '✅ Parte oficială' : '⚠️ Nu în SOAP') . "</td></tr>";
    echo "</table>";
    
    if ($only_soap && $no_suspicious && $reasonable_count) {
        echo "<p class='success'>🎉 PERFECT! Sistemul afișează doar părți reale din SOAP API!</p>";
        echo "<p class='success'>✅ Nu mai extrage cuvinte din sentință</p>";
        echo "<p class='success'>✅ Toate părțile sunt legitime</p>";
        echo "<p class='success'>✅ Numărul de părți este rezonabil</p>";
    } elseif ($only_soap && $no_suspicious) {
        echo "<p class='success'>🎉 EXCELENT! Nu mai extrage cuvinte din text!</p>";
        echo "<p class='info'>ℹ️ Verifică numărul de părți dacă pare neobișnuit</p>";
    } else {
        echo "<p class='error'>❌ PROBLEMĂ: Încă există probleme cu extragerea</p>";
        if (!$only_soap) {
            echo "<p class='error'>• Încă se extrag părți din textul deciziei</p>";
        }
        if (!$no_suspicious) {
            echo "<p class='error'>• Încă există cuvinte suspecte în lista de părți</p>";
        }
    }
    
    echo "<h3>🔗 Test Links</h3>";
    echo "<ul>";
    echo "<li><a href='detalii_dosar.php?numar=130/98/2022&institutie=TribunalulIALOMITA' target='_blank'>Pagina principală (doar părți SOAP)</a></li>";
    echo "<li><a href='detalii_dosar.php?numar=130/98/2022&institutie=TribunalulIALOMITA&debug=1' target='_blank'>Pagina cu debug</a></li>";
    echo "</ul>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2 class='error'>❌ Error Occurred</h2>";
    echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p class='info'>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Test completat la " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
