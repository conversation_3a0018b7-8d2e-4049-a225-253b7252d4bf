# 🌐 Portal Judiciar România - Online Hosting Setup Guide

## 📦 Database Export Completed Successfully!

**Export File:** `portal_judiciar_export_2025-07-28_18-15-39.sql`  
**Size:** ~65 KB  
**Contains:** Complete database structure, data, and views

---

## 🚀 Step-by-Step Online Deployment

### 1. **Upload Files to Online Hosting**

Upload these files to your online hosting:
```
portal_judiciar_export_2025-07-28_18-15-39.sql  (database export)
src/                                             (all PHP source files)
public/                                          (web-accessible files)
includes/                                        (configuration files)
cron/                                           (cron job scripts)
logs/                                           (create empty directory)
```

### 2. **Create Database on Online Hosting**

1. **Access cPanel/Hosting Control Panel**
2. **Open phpMyAdmin**
3. **Create new database:**
   - Database name: `portal_judiciar` (or your preferred name)
   - Collation: `utf8mb4_unicode_ci`
4. **Create database user:**
   - Username: `portal_user` (or your preferred name)
   - Password: Generate strong password
   - Grant ALL privileges to the database

### 3. **Import Database**

1. **Select your database** in phpMyAdmin
2. **Click "Import" tab**
3. **Choose file:** `portal_judiciar_export_2025-07-28_18-15-39.sql`
4. **Click "Go"**
5. **Verify import success** - you should see all tables and data

### 4. **Update Configuration Files**

#### **A. Database Configuration (`src/Config/Database.php`)**
```php
<?php
namespace App\Config;

use PDO;
use PDOException;

class Database 
{
    // UPDATE THESE WITH YOUR ONLINE HOSTING DETAILS
    private static $host = 'localhost';           // Usually 'localhost'
    private static $dbname = 'your_db_name';      // Your online database name
    private static $username = 'your_db_user';    // Your online database user
    private static $password = 'your_db_pass';    // Your online database password
    private static $charset = 'utf8mb4';
    
    // Rest of the class remains the same...
}
```

#### **B. Constants Configuration (`src/Config/constants.php`)**
```php
<?php
// UPDATE THESE FOR ONLINE HOSTING

// Database settings (if used elsewhere)
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_online_db_name');
define('DB_USER', 'your_online_db_user');
define('DB_PASS', 'your_online_db_password');

// Email settings - UPDATE WITH YOUR DETAILS
define('CONTACT_EMAIL', '<EMAIL>');
define('CONTACT_NAME', 'Portal Judiciar România');

// Paths - UPDATE FOR ONLINE HOSTING
define('BASE_URL', 'https://yourdomain.com');
define('LOG_DIR', __DIR__ . '/../../logs');

// Notification settings (keep these)
define('MAX_NOTIFICATION_ATTEMPTS', 3);
define('NOTIFICATION_RETRY_DELAY', 3600);
define('NOTIFICATION_BATCH_SIZE', 20);
```

#### **C. Legacy Config (`includes/config.php` - if exists)**
```php
<?php
// UPDATE THESE FOR ONLINE HOSTING
$host = 'localhost';
$dbname = 'your_online_db_name';
$username = 'your_online_db_user';
$password = 'your_online_db_password';

// Rest of configuration...
```

### 5. **Set Up Directory Permissions**

Create and set permissions for these directories:
```bash
mkdir logs
mkdir logs/notifications
chmod 755 logs
chmod 755 logs/notifications
```

### 6. **Test the Installation**

1. **Access your website:** `https://yourdomain.com`
2. **Test database connection:** Check if pages load without errors
3. **Test user login:** Try logging in with existing users
4. **Test search functionality:** Verify SOAP API integration works
5. **Test notifications:** Check if notification system is operational

### 7. **Set Up Cron Jobs**

Add these cron jobs in your hosting control panel:

```bash
# Process notifications every 5 minutes
*/5 * * * * /usr/bin/php /path/to/your/site/cron/send_scheduled_notifications.php

# Monitor cases every 30 minutes  
*/30 * * * * /usr/bin/php /path/to/your/site/cron/monitor_cases.php

# Daily cleanup at 2 AM
0 2 * * * /usr/bin/php /path/to/your/site/cron/cleanup_notifications.php
```

---

## 🔧 Configuration Checklist

### **Database Setup:**
- [ ] Database created on online hosting
- [ ] SQL file imported successfully
- [ ] All tables present (users, monitored_cases, notification_queue, etc.)
- [ ] Views created (active_users)
- [ ] Database user has proper privileges

### **File Configuration:**
- [ ] `src/Config/Database.php` updated with online credentials
- [ ] `src/Config/constants.php` updated with online settings
- [ ] `includes/config.php` updated (if exists)
- [ ] Email settings configured
- [ ] Base URL updated

### **Directory Setup:**
- [ ] `logs/` directory created and writable
- [ ] `logs/notifications/` directory created
- [ ] Proper file permissions set
- [ ] All source files uploaded

### **Functionality Testing:**
- [ ] Website loads without errors
- [ ] Database connection working
- [ ] User authentication functional
- [ ] Search functionality operational
- [ ] Notification system active
- [ ] Cron jobs scheduled

---

## 🚨 Common Issues & Solutions

### **Database Connection Errors:**
- Verify database credentials in config files
- Check if database user has proper privileges
- Ensure database name matches exactly

### **File Permission Errors:**
- Set logs directory to 755 or 777
- Ensure web server can write to logs directory
- Check file ownership (www-data or similar)

### **Email Not Working:**
- Update CONTACT_EMAIL in constants.php
- Configure SMTP settings if required by hosting
- Test with a simple email script first

### **SOAP API Issues:**
- Verify hosting allows external SOAP connections
- Check if curl/soap extensions are enabled
- Test API connectivity from hosting environment

### **Cron Jobs Not Running:**
- Verify correct PHP path (/usr/bin/php or /usr/local/bin/php)
- Check cron job syntax in hosting control panel
- Ensure scripts have proper file permissions

---

## 📞 Support Information

### **What's Included in Export:**
- ✅ Complete database structure
- ✅ All user accounts and data
- ✅ Notification preferences
- ✅ System logs and monitoring data
- ✅ Views without privilege issues
- ✅ Indexes for optimal performance

### **System Requirements:**
- PHP 7.4+ (PHP 8.0+ recommended)
- MySQL 5.7+ or MariaDB 10.2+
- Extensions: PDO, curl, soap, json, mbstring
- Minimum 256MB RAM
- Cron job support

### **Post-Deployment Testing:**
1. Run: `php test_notification_system.php` (if uploaded)
2. Check error logs for any issues
3. Test user registration and login
4. Verify search functionality
5. Test notification system

---

## 🎉 Deployment Complete!

Your Portal Judiciar România database is now ready for online hosting. Follow the steps above to complete your deployment and enjoy your fully functional judicial portal with notification system!

**Need help?** Check the error logs and verify each configuration step carefully.
