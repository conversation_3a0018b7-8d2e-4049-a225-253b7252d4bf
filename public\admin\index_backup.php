<?php

/**
 * Portal Judiciar România - Administrative Dashboard (Simplified Version)
 * 
 * Versiune simplificată care funcționează fără dependențe de tabele de monitorizare
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */

// Load bootstrap
require_once dirname(__DIR__, 2) . '/bootstrap.php';

use App\Helpers\TemplateEngine;
use App\Services\AdminAuthService;
use App\Config\Database;
use App\Security\CSRFProtection;
use App\Security\RateLimiter;
use App\Security\GDPRCompliance;

// Session is already started in bootstrap.php

// Initialize services
$templateEngine = new TemplateEngine();

// Require admin access
AdminAuthService::requireAdmin();

$userId = $_SESSION['user_id'];
$userRole = AdminAuthService::getAdminRole($userId);

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'];
        
        switch ($action) {
            case 'get_user_details':
                // Validate CSRF token
                if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '', 'get_user_details')) {
                    throw new Exception('Invalid CSRF token');
                }
                
                $userId = intval($_POST['user_id'] ?? 0);
                if ($userId <= 0) {
                    throw new Exception('Invalid user ID');
                }
                
                // Get user details (simplified)
                $user = Database::fetchOne("SELECT * FROM users WHERE id = ? AND deleted_at IS NULL", [$userId]);
                if (!$user) {
                    throw new Exception('User not found');
                }
                
                echo json_encode([
                    'success' => true,
                    'data' => $user
                ]);
                break;
                
            default:
                throw new Exception('Unknown action');
        }
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    
    exit;
}

// Get dashboard statistics (simplified)
$stats = AdminAuthService::getDashboardStats();
$recentActivity = AdminAuthService::getRecentActivity(15);

// Get basic monitoring statistics (with fallbacks)
$monitoringStats = [
    'total_users' => 0,
    'active_users' => 0,
    'total_monitored_cases' => 0,
    'notifications_today' => 0,
    'notifications_pending' => 0,
    'cases_checked_today' => 0,
    'changes_this_week' => 0
];

// Try to get real stats if tables exist
try {
    $monitoringStats['total_users'] = Database::fetchOne("SELECT COUNT(*) as count FROM users WHERE deleted_at IS NULL")['count'] ?? 0;
} catch (Exception $e) {
    // Table doesn't exist, use fallback
}

$allUsers = [];
$allMonitoredCases = [];

// Try to get users if table exists
try {
    $allUsers = Database::fetchAll("
        SELECT 
            id, email, first_name, last_name, created_at, 
            email_verified, is_active
        FROM users 
        WHERE deleted_at IS NULL 
        ORDER BY created_at DESC 
        LIMIT 50
    ");
} catch (Exception $e) {
    // Table doesn't exist or has different structure
}

$systemHealth = [
    'database_status' => 'healthy',
    'recent_errors' => 0,
    'failed_notifications' => 0,
    'stale_cases' => 0,
    'overall_score' => 100
];

// Get CSRF tokens for various actions
$csrfTokens = [
    'get_user_details' => CSRFProtection::generateToken('get_user_details'),
    'update_user_status' => CSRFProtection::generateToken('update_user_status'),
    'export_gdpr_data' => CSRFProtection::generateToken('export_gdpr_data'),
    'resolve_security_incident' => CSRFProtection::generateToken('resolve_security_incident')
];

// Check rate limits for UI display
$rateLimitKey = "admin_{$userId}";
$rateLimits = [
    'get_user_details' => RateLimiter::getRemainingAttempts('get_user_details', $rateLimitKey),
    'update_user_status' => RateLimiter::getRemainingAttempts('update_user_status', $rateLimitKey),
    'export_gdpr_data' => RateLimiter::getRemainingAttempts('export_gdpr_data', $rateLimitKey)
];

// Prepare template data
$templateData = [
    'page_title' => 'Panou Administrativ - Portal Judiciar România',
    'user_name' => $_SESSION['user_name'] ?? 'Administrator',
    'user_role' => $userRole,
    'user_permissions' => AdminAuthService::PERMISSIONS[$userRole] ?? [],
    'stats' => $stats,
    'recent_activity' => $recentActivity,
    'monitoring_stats' => $monitoringStats,
    'all_users' => $allUsers,
    'all_monitored_cases' => $allMonitoredCases,
    'system_health' => $systemHealth,
    'csrf_tokens' => $csrfTokens,
    'rate_limits' => $rateLimits,
    'current_time' => date('Y-m-d H:i:s')
];

// Get CSRF tokens for various actions
$csrfTokens = [
    'get_user_details' => CSRFProtection::generateToken('get_user_details'),
    'update_user_status' => CSRFProtection::generateToken('update_user_status'),
    'export_gdpr_data' => CSRFProtection::generateToken('export_gdpr_data'),
    'resolve_security_incident' => CSRFProtection::generateToken('resolve_security_incident')
];

// Check rate limits for UI display
$rateLimitKey = "admin_{$userId}";
$rateLimits = [
    'get_user_details' => RateLimiter::getRemainingAttempts('get_user_details', $rateLimitKey),
    'update_user_status' => RateLimiter::getRemainingAttempts('update_user_status', $rateLimitKey),
    'export_gdpr_data' => RateLimiter::getRemainingAttempts('export_gdpr_data', $rateLimitKey)
];

// Render template
echo $templateEngine->render('admin/dashboard.twig', $templateData);
