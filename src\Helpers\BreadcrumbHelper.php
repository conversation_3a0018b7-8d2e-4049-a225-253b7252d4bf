<?php

namespace App\Helpers;

/**
 * Breadcrumb Helper - Gestionează breadcrumb-urile pentru navigație și SEO
 */
class BreadcrumbHelper
{
    /**
     * Generează breadcrumb-urile pentru o pagină specifică
     */
    public static function generateBreadcrumbs($page, $params = [])
    {
        $baseUrl = 'http://localhost/just';
        
        // Breadcrumb-ul de bază (Acasă)
        $breadcrumbs = [
            [
                'name' => 'Acasă',
                'url' => $baseUrl . '/',
                'active' => false
            ]
        ];
        
        switch ($page) {
            case 'index':
                $breadcrumbs[0]['active'] = true;
                break;
                
            case 'contact':
                $breadcrumbs[] = [
                    'name' => 'Contact',
                    'url' => $baseUrl . '/contact.php',
                    'active' => true
                ];
                break;
                
            case 'sedinte':
                $breadcrumbs[] = [
                    'name' => 'Ședințe Judecătorești',
                    'url' => $baseUrl . '/sedinte.php',
                    'active' => true
                ];
                break;
                
            case 'search':
                $breadcrumbs[] = [
                    'name' => 'Căutare Dosare',
                    'url' => $baseUrl . '/search.php',
                    'active' => false
                ];
                
                if (!empty($params['search_term'])) {
                    $breadcrumbs[] = [
                        'name' => 'Rezultate pentru "' . htmlspecialchars($params['search_term']) . '"',
                        'url' => $baseUrl . '/search.php?search_term=' . urlencode($params['search_term']),
                        'active' => true
                    ];
                } else {
                    $breadcrumbs[count($breadcrumbs) - 1]['active'] = true;
                }
                break;
                
            case 'detalii_dosar':
                $breadcrumbs[] = [
                    'name' => 'Căutare Dosare',
                    'url' => $baseUrl . '/search.php',
                    'active' => false
                ];
                
                if (!empty($params['numar_dosar'])) {
                    $breadcrumbs[] = [
                        'name' => 'Dosar ' . htmlspecialchars($params['numar_dosar']),
                        'url' => $baseUrl . '/detalii_dosar.php?numar=' . urlencode($params['numar_dosar']),
                        'active' => true
                    ];
                } else {
                    $breadcrumbs[] = [
                        'name' => 'Detalii Dosar',
                        'url' => $baseUrl . '/detalii_dosar.php',
                        'active' => true
                    ];
                }
                break;
                
            case 'avans':
                $breadcrumbs[] = [
                    'name' => 'Căutare Avansată',
                    'url' => $baseUrl . '/avans.php',
                    'active' => true
                ];
                break;
                
            case 'avansat':
                $breadcrumbs[] = [
                    'name' => 'Căutare Complexă',
                    'url' => $baseUrl . '/avansat.php',
                    'active' => true
                ];
                break;
                
            default:
                $breadcrumbs[0]['active'] = true;
                break;
        }
        
        return $breadcrumbs;
    }
    
    /**
     * Renderează breadcrumb-urile ca HTML
     */
    public static function renderBreadcrumbs($page, $params = [])
    {
        $breadcrumbs = self::generateBreadcrumbs($page, $params);
        
        if (count($breadcrumbs) <= 1) {
            return ''; // Nu afișăm breadcrumb-uri pentru pagina principală
        }
        
        $html = '<nav aria-label="breadcrumb" class="breadcrumb-nav">' . "\n";
        $html .= '  <ol class="breadcrumb">' . "\n";
        
        foreach ($breadcrumbs as $breadcrumb) {
            if ($breadcrumb['active']) {
                $html .= '    <li class="breadcrumb-item active" aria-current="page">';
                $html .= htmlspecialchars($breadcrumb['name']);
                $html .= '</li>' . "\n";
            } else {
                $html .= '    <li class="breadcrumb-item">';
                $html .= '<a href="' . htmlspecialchars($breadcrumb['url']) . '">';
                $html .= htmlspecialchars($breadcrumb['name']);
                $html .= '</a>';
                $html .= '</li>' . "\n";
            }
        }
        
        $html .= '  </ol>' . "\n";
        $html .= '</nav>' . "\n";
        
        return $html;
    }
    
    /**
     * Generează breadcrumb-urile pentru structured data (JSON-LD)
     */
    public static function generateStructuredDataBreadcrumbs($page, $params = [])
    {
        $breadcrumbs = self::generateBreadcrumbs($page, $params);
        
        // Convertim la formatul necesar pentru structured data
        $structuredBreadcrumbs = [];
        foreach ($breadcrumbs as $breadcrumb) {
            $structuredBreadcrumbs[] = [
                'name' => $breadcrumb['name'],
                'url' => $breadcrumb['url']
            ];
        }
        
        return $structuredBreadcrumbs;
    }
    
    /**
     * Renderează CSS-ul pentru breadcrumb-uri
     */
    public static function renderBreadcrumbCSS()
    {
        return '
<style>
.breadcrumb-nav {
    margin-bottom: 1.5rem;
    padding: 0;
}

.breadcrumb {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0;
    font-size: 0.875rem;
}

.breadcrumb-item {
    color: #6c757d;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
    font-weight: bold;
    padding: 0 0.5rem;
}

.breadcrumb-item a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumb-item a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #2c3e50;
    font-weight: 500;
}

/* Responsive design */
@media (max-width: 767.98px) {
    .breadcrumb {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        padding: 0 0.25rem;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .breadcrumb {
        border: 2px solid #000;
        background-color: #fff;
    }
    
    .breadcrumb-item a {
        color: #000;
        font-weight: bold;
    }
    
    .breadcrumb-item.active {
        color: #000;
        font-weight: bold;
    }
}

/* Print styles */
@media print {
    .breadcrumb-nav {
        display: none;
    }
}
</style>';
    }
}
