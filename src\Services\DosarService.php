<?php

namespace App\Services;

use Exception;
use SoapClient;
use SoapFault;

/**
 * Serviciu pentru interacțiunea cu API-ul SOAP al Portalului Instanțelor de Judecată
 */
class DosarService
{
    /**
     * Instanța clientului SOAP
     * @var SoapClient
     */
    private $soapClient;

    /**
     * Numărul maxim de încercări pentru apelurile SOAP
     * @var int
     */
    private $maxRetries = 3;

    /**
     * Timpul de așteptare între reîncercări (în microsecunde)
     * @var int
     */
    private $retryDelay = 500000; // 0.5 secunde

    /**
     * Constructor
     */
    public function __construct()
    {
        try {
            // Opțiuni pentru clientul SOAP cu timeout-uri optimizate
            $options = [
                'soap_version' => SOAP_1_2,
                'exceptions' => true,
                'trace' => true,
                'cache_wsdl' => WSDL_CACHE_NONE,
                'connection_timeout' => 15, // Timeout de conexiune de 15 secunde
                'default_socket_timeout' => 30, // Timeout pentru operațiuni SOAP de 30 secunde
                'features' => SOAP_SINGLE_ELEMENT_ARRAYS, // Forțează returnarea array-urilor pentru elemente singulare
                'stream_context' => stream_context_create([
                    'http' => [
                        'timeout' => 30, // Timeout HTTP de 30 secunde
                        'user_agent' => 'Judicial Portal Client/1.0'
                    ]
                ])
            ];

            // Inițializare client SOAP
            $this->soapClient = new SoapClient(SOAP_WSDL, $options);
        } catch (SoapFault $e) {
            throw new Exception("Eroare la conectarea la serviciul SOAP: " . $e->getMessage());
        }
    }

    /**
     * Caută dosare după număr
     *
     * @param string $numarDosar Numărul dosarului
     * @param string $instanta Instanța judecătorească
     * @param string $obiectDosar Obiectul dosarului
     * @param string $dataInceput Data de început (format: d.m.Y)
     * @param string $dataSfarsit Data de sfârșit (format: d.m.Y)
     * @return array Rezultatele căutării
     */
    public function cautareDupaNumarDosar($numarDosar, $instanta = '', $obiectDosar = '', $dataInceput = '', $dataSfarsit = '')
    {
        try {
            // Handle empty institutie parameter
            $institutie = !empty($instanta) ? $instanta : null;

            $params = [
                'numarDosar' => $numarDosar,
                'obiectDosar' => $obiectDosar,
                'numeParte' => '',
                'institutie' => $institutie,
                'dataStart' => $this->formatDateForSoap($dataInceput),
                'dataStop' => $this->formatDateForSoap($dataSfarsit),
                'dataUltimaModificareStart' => null,
                'dataUltimaModificareStop' => null
            ];

            // Utilizăm mecanismul de reîncercare pentru apelul SOAP
            $response = $this->executeSoapCallWithRetry('CautareDosare2', $params, "Eroare la căutarea după număr dosar");
            return $this->processResponse($response);
        } catch (Exception $e) {
            throw new Exception("Eroare la căutarea după număr dosar: " . $e->getMessage());
        }
    }

    /**
     * Caută dosare după nume parte
     *
     * @param string $numeParte Numele părții
     * @param string $instanta Instanța judecătorească
     * @param string $obiectDosar Obiectul dosarului
     * @param string $dataInceput Data de început (format: d.m.Y)
     * @param string $dataSfarsit Data de sfârșit (format: d.m.Y)
     * @return array Rezultatele căutării
     */
    public function cautareDupaNumeParte($numeParte, $instanta = '', $obiectDosar = '', $dataInceput = '', $dataSfarsit = '')
    {
        try {
            // Handle empty institutie parameter
            $institutie = !empty($instanta) ? $instanta : null;

            // Normalizăm numele părții pentru a gestiona diacriticele
            $normalizedNumeParte = $this->normalizeDiacritics($numeParte);

            $params = [
                'numarDosar' => '',
                'obiectDosar' => $obiectDosar,
                'numeParte' => $numeParte,
                'institutie' => $institutie,
                'dataStart' => $this->formatDateForSoap($dataInceput),
                'dataStop' => $this->formatDateForSoap($dataSfarsit),
                'dataUltimaModificareStart' => null,
                'dataUltimaModificareStop' => null
            ];

            // Utilizăm mecanismul de reîncercare pentru apelul SOAP
            $response = $this->executeSoapCallWithRetry('CautareDosare2', $params, "Eroare la căutarea după nume parte");
            $results = $this->processResponse($response);

            // Dacă nu am găsit rezultate și numele părții conține diacritice, încercăm cu versiunea normalizată
            if (empty($results) && $numeParte !== $normalizedNumeParte) {
                $normalizedParams = $params;
                $normalizedParams['numeParte'] = $normalizedNumeParte;

                try {
                    $normalizedResponse = $this->executeSoapCallWithRetry('CautareDosare2', $normalizedParams, "Eroare la căutarea după nume parte normalizat");
                    $normalizedResults = $this->processResponse($normalizedResponse);

                    if (!empty($normalizedResults)) {
                        return $normalizedResults;
                    }
                } catch (Exception $e) {
                    // Ignorăm erorile la căutarea cu nume normalizat și returnăm rezultatele originale (goale)
                }
            }

            return $results;
        } catch (Exception $e) {
            throw new Exception("Eroare la căutarea după nume parte: " . $e->getMessage());
        }
    }

    /**
     * Caută dosare după obiect
     *
     * @param string $obiectDosar Obiectul dosarului
     * @param string $instanta Instanța judecătorească
     * @param string $dataInceput Data de început (format: d.m.Y)
     * @param string $dataSfarsit Data de sfârșit (format: d.m.Y)
     * @return array Rezultatele căutării
     */
    public function cautareDupaObiect($obiectDosar, $instanta = '', $dataInceput = '', $dataSfarsit = '')
    {
        try {
            // Handle empty institutie parameter
            $institutie = !empty($instanta) ? $instanta : null;

            $params = [
                'numarDosar' => '',
                'obiectDosar' => $obiectDosar,
                'numeParte' => '',
                'institutie' => $institutie,
                'dataStart' => $this->formatDateForSoap($dataInceput),
                'dataStop' => $this->formatDateForSoap($dataSfarsit),
                'dataUltimaModificareStart' => null,
                'dataUltimaModificareStop' => null
            ];

            // Utilizăm mecanismul de reîncercare pentru apelul SOAP
            $response = $this->executeSoapCallWithRetry('CautareDosare2', $params, "Eroare la căutarea după obiect dosar");
            return $this->processResponse($response);
        } catch (Exception $e) {
            throw new Exception("Eroare la căutarea după obiect dosar: " . $e->getMessage());
        }
    }

    /**
     * Execută un apel SOAP cu mecanism de reîncercare
     *
     * @param string $method Metoda SOAP care va fi apelată
     * @param array $params Parametrii pentru apelul SOAP
     * @param string $errorPrefix Prefixul pentru mesajul de eroare
     * @return mixed Răspunsul de la apelul SOAP
     * @throws Exception Dacă apelul eșuează după toate reîncercările
     */
    private function executeSoapCallWithRetry($method, $params, $errorPrefix = "Eroare SOAP")
    {
        $attempt = 0;
        $lastException = null;
        $logDir = dirname(__DIR__, 2) . '/logs';

        // Asigurăm-ne că directorul de loguri există
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logFile = "{$logDir}/soap_calls.log";

        while ($attempt < $this->maxRetries) {
            try {
                // Incrementăm numărul de încercări
                $attempt++;

                // Logăm încercarea curentă
                $logData = date('Y-m-d H:i:s') . " - Încercare {$attempt}/{$this->maxRetries} pentru metoda {$method}\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                // Executăm apelul SOAP
                $response = $this->soapClient->$method($params);

                // Dacă am ajuns aici, apelul a reușit, deci logăm succesul și returnăm răspunsul
                $logData = date('Y-m-d H:i:s') . " - Apel SOAP reușit pentru metoda {$method} la încercarea {$attempt}\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                return $response;
            } catch (SoapFault $e) {
                // Salvăm excepția pentru a o putea arunca dacă toate încercările eșuează
                $lastException = $e;

                // Logăm eroarea
                $logData = date('Y-m-d H:i:s') . " - Eroare la încercarea {$attempt}/{$this->maxRetries} pentru metoda {$method}: " . $e->getMessage() . "\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                // Verificăm dacă eroarea este una care poate fi rezolvată prin reîncercare
                $retryableError = (
                    strpos($e->getMessage(), 'looks like we got no XML document') !== false ||
                    strpos($e->getMessage(), 'Connection reset by peer') !== false ||
                    strpos($e->getMessage(), 'Error Fetching http headers') !== false ||
                    strpos($e->getMessage(), 'Could not connect to host') !== false ||
                    strpos($e->getMessage(), 'Operation timed out') !== false
                );

                if (!$retryableError) {
                    // Dacă eroarea nu este una care poate fi rezolvată prin reîncercare, o aruncăm imediat
                    $logData = date('Y-m-d H:i:s') . " - Eroare nerecuperabilă, nu mai reîncercăm: " . $e->getMessage() . "\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);
                    throw new Exception("{$errorPrefix}: " . $e->getMessage());
                }

                // Dacă nu am epuizat toate încercările, așteptăm înainte de a reîncerca
                if ($attempt < $this->maxRetries) {
                    // Calculăm timpul de așteptare cu backoff exponențial
                    $waitTime = $this->retryDelay * pow(2, $attempt - 1);

                    $logData = date('Y-m-d H:i:s') . " - Așteptăm {$waitTime} microsecunde înainte de reîncercare\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);

                    // Așteptăm înainte de a reîncerca
                    usleep($waitTime);
                }
            }
        }

        // Dacă am ajuns aici, toate încercările au eșuat
        $logData = date('Y-m-d H:i:s') . " - Toate încercările au eșuat pentru metoda {$method}\n";
        file_put_contents($logFile, $logData, FILE_APPEND);

        // Logăm eroarea și în fișierul de erori SOAP
        $errorLogFile = "{$logDir}/search_errors.log";
        $errorLogData = date('Y-m-d H:i:s') . " - Eroare SOAP după {$this->maxRetries} încercări: " . $lastException->getMessage() . "\n";
        file_put_contents($errorLogFile, $errorLogData, FILE_APPEND);

        throw new Exception("{$errorPrefix} (după {$this->maxRetries} încercări): " . $lastException->getMessage());
    }

    /**
     * Caută dosare cu parametri multipli
     *
     * @param array $params Parametrii de căutare
     * @return array Rezultatele căutării
     */
    public function cautareAvansata($params)
    {
        try {
            // Extragem parametrul maxResults dacă există
            $maxResults = isset($params['_maxResults']) ? (int)$params['_maxResults'] : 1000;
            unset($params['_maxResults']); // Eliminăm parametrul din array pentru a nu-l trimite la API

            // Handle empty institutie parameter - convert empty string to null
            $institutie = isset($params['institutie']) && $params['institutie'] !== '' ? $params['institutie'] : null;

            // Validate and map institution code if provided
            if ($institutie) {
                require_once dirname(__DIR__, 2) . '/includes/InstitutionCodeValidator.php';
                $validation = InstitutionCodeValidator::validateAndMap($institutie);
                $institutie = $validation['code'];

                // Store validation info for potential user feedback
                $params['_institutionValidation'] = $validation;
            }

            // Asigurăm-ne că numeParte este corect codificat pentru SOAP
            $numeParte = $params['numeParte'] ?? '';

            // Încercăm atât cu textul original, cât și cu textul normalizat
            $normalizedNumeParte = $this->normalizeDiacritics($numeParte);

            // Creăm un director pentru loguri dacă nu există
            $logDir = dirname(__DIR__, 2) . '/logs';
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }

            // Logăm parametrii de căutare pentru depanare
            $logFile = "{$logDir}/search_params.log";
            $logData = date('Y-m-d H:i:s') . " - Parametri căutare: " . json_encode($params, JSON_UNESCAPED_UNICODE) . "\n";
            file_put_contents($logFile, $logData, FILE_APPEND);

            // Validăm datele pentru a ne asigura că datele de sfârșit nu sunt înainte de datele de început
            $dataStart = $params['dataStart'] ?? '';
            $dataStop = $params['dataStop'] ?? '';
            $dataUltimaModificareStart = $params['dataUltimaModificareStart'] ?? '';
            $dataUltimaModificareStop = $params['dataUltimaModificareStop'] ?? '';

            // Verificăm și corectăm datele dacă este necesar
            if (!empty($dataStart) && !empty($dataStop)) {
                $startTimestamp = strtotime($dataStart);
                $stopTimestamp = strtotime($dataStop);

                if ($startTimestamp && $stopTimestamp && $startTimestamp > $stopTimestamp) {
                    // Dacă data de început este după data de sfârșit, le inversăm
                    $logData = date('Y-m-d H:i:s') . " - Avertisment: Data de început ({$dataStart}) este după data de sfârșit ({$dataStop}). Inversăm datele.\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);

                    $temp = $dataStart;
                    $dataStart = $dataStop;
                    $dataStop = $temp;
                }
            }

            if (!empty($dataUltimaModificareStart) && !empty($dataUltimaModificareStop)) {
                $startTimestamp = strtotime($dataUltimaModificareStart);
                $stopTimestamp = strtotime($dataUltimaModificareStop);

                if ($startTimestamp && $stopTimestamp && $startTimestamp > $stopTimestamp) {
                    // Dacă data de început este după data de sfârșit, le inversăm
                    $logData = date('Y-m-d H:i:s') . " - Avertisment: Data ultimei modificări de început ({$dataUltimaModificareStart}) este după data ultimei modificări de sfârșit ({$dataUltimaModificareStop}). Inversăm datele.\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);

                    $temp = $dataUltimaModificareStart;
                    $dataUltimaModificareStart = $dataUltimaModificareStop;
                    $dataUltimaModificareStop = $temp;
                }
            }

            // Pregătim parametrii pentru căutare - DOAR parametrii suportați de SOAP API
            $searchParams = [
                'numarDosar' => $params['numarDosar'] ?? '',
                'obiectDosar' => $params['obiectDosar'] ?? '',
                'numeParte' => $numeParte, // Folosim versiunea originală
                'institutie' => $institutie,
                'dataStart' => $this->formatDateForSoap($dataStart),
                'dataStop' => $this->formatDateForSoap($dataStop),
                'dataUltimaModificareStart' => $this->formatDateForSoap($dataUltimaModificareStart),
                'dataUltimaModificareStop' => $this->formatDateForSoap($dataUltimaModificareStop)
            ];

            // IMPORTANT: categorieInstanta și categorieCaz nu sunt suportate direct de SOAP API
            // Acestea sunt folosite pentru filtrarea client-side după obținerea rezultatelor
            $categorieInstanta = $params['categorieInstanta'] ?? '';
            $categorieCaz = $params['categorieCaz'] ?? '';

            // Logăm parametrii de filtrare client-side
            if (!empty($categorieInstanta) || !empty($categorieCaz)) {
                $logData = date('Y-m-d H:i:s') . " - Filtre client-side: categorieInstanta={$categorieInstanta}, categorieCaz={$categorieCaz}\n";
                file_put_contents($logFile, $logData, FILE_APPEND);
            }

            // Logăm parametrii SOAP pentru depanare - ENHANCED FOR FUTURE DATES
            $logData = date('Y-m-d H:i:s') . " - Parametri SOAP: " . json_encode($searchParams, JSON_UNESCAPED_UNICODE) . "\n";
            file_put_contents($logFile, $logData, FILE_APPEND);

            // Special logging for future date ranges
            if (!empty($searchParams['dataStart']) || !empty($searchParams['dataStop'])) {
                $startYear = !empty($searchParams['dataStart']) ? date('Y', strtotime($searchParams['dataStart'])) : 'N/A';
                $stopYear = !empty($searchParams['dataStop']) ? date('Y', strtotime($searchParams['dataStop'])) : 'N/A';
                $logData = date('Y-m-d H:i:s') . " - Date range SOAP format: {$startYear} - {$stopYear} (SOAP: {$searchParams['dataStart']} - {$searchParams['dataStop']})\n";
                file_put_contents($logFile, $logData, FILE_APPEND);
            }

            // Facem prima căutare cu parametrii originali folosind mecanismul de reîncercare
            $response = $this->executeSoapCallWithRetry('CautareDosare2', $searchParams, "Eroare la căutarea avansată");

            // Procesăm rezultatele cu limita specificată
            $results = $this->processResponse($response, $maxResults);

            // ENHANCED LOGGING: Analyze date distribution in raw results
            if (!empty($results) && (!empty($searchParams['dataStart']) || !empty($searchParams['dataStop']))) {
                $dateDistribution = [];
                foreach ($results as $result) {
                    $date = $result->data ?? '';
                    if (!empty($date)) {
                        $year = date('Y', strtotime($date));
                        $dateDistribution[$year] = ($dateDistribution[$year] ?? 0) + 1;
                    }
                }
                if (!empty($dateDistribution)) {
                    ksort($dateDistribution);
                    $distributionStr = '';
                    foreach ($dateDistribution as $year => $count) {
                        $distributionStr .= "{$year}: {$count} cases; ";
                    }
                    $logData = date('Y-m-d H:i:s') . " - Raw SOAP results date distribution: {$distributionStr}\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);
                }
            }

            // Aplicăm filtrarea client-side pentru parametrii care nu sunt suportați de SOAP API
            $filteredResults = $this->applyClientSideFiltering($results, $categorieInstanta, $categorieCaz, $logFile);

            // ENHANCED LOGGING: Compare before/after client-side filtering
            if (count($results) !== count($filteredResults) && (!empty($searchParams['dataStart']) || !empty($searchParams['dataStop']))) {
                $filteredDateDistribution = [];
                foreach ($filteredResults as $result) {
                    $date = $result->data ?? '';
                    if (!empty($date)) {
                        $year = date('Y', strtotime($date));
                        $filteredDateDistribution[$year] = ($filteredDateDistribution[$year] ?? 0) + 1;
                    }
                }
                if (!empty($filteredDateDistribution)) {
                    ksort($filteredDateDistribution);
                    $filteredDistributionStr = '';
                    foreach ($filteredDateDistribution as $year => $count) {
                        $filteredDistributionStr .= "{$year}: {$count} cases; ";
                    }
                    $logData = date('Y-m-d H:i:s') . " - After client-side filtering date distribution: {$filteredDistributionStr}\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);
                }
            }

            $results = $filteredResults;

            // Logăm răspunsul pentru depanare (doar informații de bază)
            $responseInfo = "Răspuns primit (versiune originală): ";
            if (isset($response->CautareDosare2Result->Dosar)) {
                $dosare = $response->CautareDosare2Result->Dosar;
                if (is_array($dosare)) {
                    $responseInfo .= "Număr dosare găsite: " . count($dosare);
                } else {
                    $responseInfo .= "Un singur dosar găsit";
                }
            } else {
                $responseInfo .= "Niciun dosar găsit";
            }
            $logData = date('Y-m-d H:i:s') . " - " . $responseInfo . "\n";
            file_put_contents($logFile, $logData, FILE_APPEND);

            // Dacă nu am găsit rezultate și avem un nume de parte cu diacritice, încercăm cu versiunea normalizată
            if (empty($results) && !empty($numeParte) && $numeParte !== $normalizedNumeParte) {
                $logData = date('Y-m-d H:i:s') . " - Încercare căutare cu nume normalizat: " . $normalizedNumeParte . "\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                // Creăm parametrii pentru căutarea cu nume normalizat
                $normalizedParams = $searchParams;
                $normalizedParams['numeParte'] = $normalizedNumeParte;

                // Logăm parametrii normalizați
                $logData = date('Y-m-d H:i:s') . " - Parametri SOAP normalizați: " . json_encode($normalizedParams, JSON_UNESCAPED_UNICODE) . "\n";
                file_put_contents($logFile, $logData, FILE_APPEND);

                try {
                    // Facem căutarea cu parametrii normalizați folosind mecanismul de reîncercare
                    $normalizedResponse = $this->executeSoapCallWithRetry('CautareDosare2', $normalizedParams, "Eroare la căutarea cu nume normalizat");
                    $normalizedResults = $this->processResponse($normalizedResponse, $maxResults);

                    // Aplicăm filtrarea client-side și pentru rezultatele normalizate
                    $normalizedResults = $this->applyClientSideFiltering($normalizedResults, $categorieInstanta, $categorieCaz, $logFile);

                    // Logăm rezultatele căutării normalizate
                    $normalizedResponseInfo = "Răspuns primit (versiune normalizată): ";
                    if (isset($normalizedResponse->CautareDosare2Result->Dosar)) {
                        $dosare = $normalizedResponse->CautareDosare2Result->Dosar;
                        if (is_array($dosare)) {
                            $normalizedResponseInfo .= "Număr dosare găsite: " . count($dosare);
                        } else {
                            $normalizedResponseInfo .= "Un singur dosar găsit";
                        }
                    } else {
                        $normalizedResponseInfo .= "Niciun dosar găsit";
                    }
                    $logData = date('Y-m-d H:i:s') . " - " . $normalizedResponseInfo . "\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);

                    // Dacă am găsit rezultate cu versiunea normalizată, le returnăm pe acestea
                    if (!empty($normalizedResults)) {
                        $logData = date('Y-m-d H:i:s') . " - Returnăm rezultatele găsite cu versiunea normalizată\n";
                        file_put_contents($logFile, $logData, FILE_APPEND);
                        return $normalizedResults;
                    }
                } catch (Exception $e) {
                    $logData = date('Y-m-d H:i:s') . " - Eroare la căutarea cu nume normalizat: " . $e->getMessage() . "\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);
                }
            }

            // Returnăm rezultatele originale (pot fi goale)
            return $results;
        } catch (Exception $e) {
            // Logăm eroarea pentru depanare
            $logDir = dirname(__DIR__, 2) . '/logs';
            $logFile = "{$logDir}/search_errors.log";
            $logData = date('Y-m-d H:i:s') . " - Eroare: " . $e->getMessage() . "\n";
            file_put_contents($logFile, $logData, FILE_APPEND);

            throw new Exception("Eroare la căutarea avansată: " . $e->getMessage());
        }
    }

    /**
     * Obține detalii pentru un dosar specific
     *
     * @param string $numarDosar Numărul dosarului
     * @param string $institutie Instituția
     * @return object Detaliile dosarului
     */
    public function getDetaliiDosar($numarDosar, $institutie)
    {
        try {
            // Ensure institutie is not empty
            if (empty($institutie)) {
                throw new Exception("Instituția este obligatorie pentru obținerea detaliilor dosarului.");
            }

            // Parametrii pentru căutare
            $searchParams = [
                'numarDosar' => $numarDosar,
                'institutie' => $institutie,
                'obiectDosar' => '',
                'numeParte' => '',
                'dataStart' => null,
                'dataStop' => null,
                'dataUltimaModificareStart' => null,
                'dataUltimaModificareStop' => null
            ];

            // Apelare metodă SOAP cu mecanism de reîncercare
            $response = $this->executeSoapCallWithRetry('CautareDosare2', $searchParams, "Eroare la obținerea detaliilor dosarului");

            // Procesare rezultat
            if (isset($response->CautareDosare2Result->Dosar)) {
                $dosare = $response->CautareDosare2Result->Dosar;

                // Verifică dacă rezultatul este un singur dosar sau un array de dosare
                if (!is_array($dosare)) {
                    return $this->mapDosarToObject($dosare);
                } else {
                    // Caută dosarul specific în rezultate
                    foreach ($dosare as $dosar) {
                        if ($dosar->numar === $numarDosar && $dosar->institutie === $institutie) {
                            return $this->mapDosarToObject($dosar);
                        }
                    }
                }
            }

            // Returnăm un obiect gol în loc de null pentru a respecta tipul de returnare
            return (object)[];
        } catch (Exception $e) {
            throw new Exception("Eroare la obținerea detaliilor dosarului: " . $e->getMessage());
        }
    }

    /**
     * Procesează răspunsul de la API pentru căutare
     *
     * @param object $response Răspunsul de la API
     * @param int $maxResults Numărul maxim de rezultate de procesat
     * @return array Rezultatele procesate
     */
    private function processResponse($response, $maxResults = 1000)
    {
        $results = [];
        $count = 0;

        if (isset($response->CautareDosare2Result->Dosar)) {
            $dosare = $response->CautareDosare2Result->Dosar;

            // Verificăm dacă avem un singur dosar sau mai multe
            if (is_array($dosare)) {
                foreach ($dosare as $dosar) {
                    // Verificăm dacă am atins limita maximă de rezultate
                    if ($count >= $maxResults) {
                        break;
                    }

                    $results[] = $this->mapDosarToObject($dosar);
                    $count++;
                }
            } else {
                // Pentru un singur dosar, îl adăugăm direct
                $results[] = $this->mapDosarToObject($dosare);
            }
        }

        return $results;
    }

    /**
     * Mapează un dosar din răspunsul API la un obiect
     *
     * @param object $dosar Dosarul din răspunsul API
     * @return object Obiectul mapat
     */
    private function mapDosarToObject($dosar)
    {
        $obj = new \stdClass();

        // Informații de bază despre dosar
        $obj->numar = $dosar->numar ?? '';
        $obj->numarVechi = $dosar->numarVechi ?? '';
        $obj->data = isset($dosar->data) ? $this->formatDateFromSoap($dosar->data) : '';
        $obj->institutie = $dosar->institutie ?? '';
        $obj->departament = $dosar->departament ?? '';
        $obj->categorieCaz = $dosar->categorieCaz ?? '';
        $obj->categorieCazNume = $dosar->categorieCazNume ?? '';
        $obj->stadiuProcesual = $dosar->stadiuProcesual ?? '';
        $obj->stadiuProcesualNume = $dosar->stadiuProcesualNume ?? '';
        $obj->obiect = $dosar->obiect ?? '';
        $obj->dataModificare = isset($dosar->dataModificare) ? $this->formatDateFromSoap($dosar->dataModificare) : '';

        // Părțile implicate
        $obj->parti = [];
        if (isset($dosar->parti) && isset($dosar->parti->DosarParte)) {
            $parti = $dosar->parti->DosarParte;
            if (is_array($parti)) {
                foreach ($parti as $parte) {
                    if (isset($parte->nume)) {
                        $obj->parti[] = [
                            'nume' => $parte->nume ?? '',
                            'calitate' => $parte->calitateParte ?? ''
                        ];
                    }
                }
            } elseif (isset($parti->nume)) {
                $obj->parti[] = [
                    'nume' => $parti->nume ?? '',
                    'calitate' => $parti->calitateParte ?? ''
                ];
            }
        }

        // Ședințele de judecată
        $obj->sedinte = [];
        if (isset($dosar->sedinte) && isset($dosar->sedinte->DosarSedinta)) {
            $sedinte = $dosar->sedinte->DosarSedinta;
            if (is_array($sedinte)) {
                foreach ($sedinte as $sedinta) {
                    if (isset($sedinta->data)) {
                        $obj->sedinte[] = [
                            'data' => isset($sedinta->data) ? $this->formatDateFromSoap($sedinta->data) : '',
                            'ora' => $sedinta->ora ?? '',
                            'complet' => $sedinta->complet ?? '',
                            'solutie' => $sedinta->solutie ?? '',
                            'solutieSumar' => $sedinta->solutieSumar ?? '',
                            'dataPronuntare' => isset($sedinta->dataPronuntare) ? $this->formatDateFromSoap($sedinta->dataPronuntare) : '',
                            'documentSedinta' => $sedinta->documentSedinta ?? '',
                            'numarDocument' => $sedinta->numarDocument ?? '',
                            'dataDocument' => isset($sedinta->dataDocument) ? $this->formatDateFromSoap($sedinta->dataDocument) : ''
                        ];
                    }
                }
            } elseif (isset($dosar->sedinte->DosarSedinta->data)) {
                $sedinta = $dosar->sedinte->DosarSedinta;
                $obj->sedinte[] = [
                    'data' => isset($sedinta->data) ? $this->formatDateFromSoap($sedinta->data) : '',
                    'ora' => $sedinta->ora ?? '',
                    'complet' => $sedinta->complet ?? '',
                    'solutie' => $sedinta->solutie ?? '',
                    'solutieSumar' => $sedinta->solutieSumar ?? '',
                    'dataPronuntare' => isset($sedinta->dataPronuntare) ? $this->formatDateFromSoap($sedinta->dataPronuntare) : '',
                    'documentSedinta' => $sedinta->documentSedinta ?? '',
                    'numarDocument' => $sedinta->numarDocument ?? '',
                    'dataDocument' => isset($sedinta->dataDocument) ? $this->formatDateFromSoap($sedinta->dataDocument) : ''
                ];
            }
        }

        // Căile de atac
        $obj->caiAtac = [];
        if (isset($dosar->caiAtac) && isset($dosar->caiAtac->DosarCaleAtac)) {
            $caiAtac = $dosar->caiAtac->DosarCaleAtac;
            if (is_array($caiAtac)) {
                foreach ($caiAtac as $caleAtac) {
                    if (isset($caleAtac->dataDeclarare)) {
                        $obj->caiAtac[] = [
                            'dataDeclarare' => isset($caleAtac->dataDeclarare) ? $this->formatDateFromSoap($caleAtac->dataDeclarare) : '',
                            'tipCaleAtac' => $caleAtac->tipCaleAtac ?? '',
                            'parteDeclaratoare' => $caleAtac->parteDeclaratoare ?? '',
                            'numarDosarInstantaSuperior' => $caleAtac->numarDosarInstantaSuperior ?? '',
                            'instantaSuperior' => $caleAtac->instantaSuperior ?? ''
                        ];
                    }
                }
            } elseif (isset($dosar->caiAtac->DosarCaleAtac->dataDeclarare)) {
                $caleAtac = $dosar->caiAtac->DosarCaleAtac;
                $obj->caiAtac[] = [
                    'dataDeclarare' => isset($caleAtac->dataDeclarare) ? $this->formatDateFromSoap($caleAtac->dataDeclarare) : '',
                    'tipCaleAtac' => $caleAtac->tipCaleAtac ?? '',
                    'parteDeclaratoare' => $caleAtac->parteDeclaratoare ?? '',
                    'numarDosarInstantaSuperior' => $caleAtac->numarDosarInstantaSuperior ?? '',
                    'instantaSuperior' => $caleAtac->instantaSuperior ?? ''
                ];
            }
        }

        return $obj;
    }

    /**
     * Formatează o dată pentru a fi utilizată în cererea SOAP
     * Funcție îmbunătățită pentru a gestiona mai multe formate de dată
     *
     * @param string $date Data în format string (d.m.Y sau alte formate)
     * @return string Data formatată pentru SOAP sau null dacă data este invalidă
     */
    private function formatDateForSoap($date)
    {
        if (empty($date)) {
            return null;
        }

        // Creăm un director pentru loguri dacă nu există
        $logDir = dirname(__DIR__, 2) . '/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        // Logăm data originală pentru depanare
        $logFile = "{$logDir}/date_format_debug.log";
        $logData = date('Y-m-d H:i:s') . " - Data originală: {$date}\n";
        file_put_contents($logFile, $logData, FILE_APPEND);

        // Încercăm mai multe formate posibile
        $formats = [
            'd.m.Y',    // 31.12.2023
            'Y-m-d',    // 2023-12-31
            'd/m/Y',    // 31/12/2023
            'Y/m/d',    // 2023/12/31
            'd-m-Y',    // 31-12-2023
            'j.n.Y',    // 1.1.2023 (fără zero-uri)
            'j/n/Y',    // 1/1/2023 (fără zero-uri)
            'j-n-Y'     // 1-1-2023 (fără zero-uri)
        ];

        foreach ($formats as $format) {
            $dateObj = \DateTime::createFromFormat($format, $date);
            if ($dateObj && $dateObj->format($format) == $date) {
                $formattedDate = $dateObj->format('Y-m-d\TH:i:s');
                $logData = date('Y-m-d H:i:s') . " - Data formatată pentru SOAP: {$formattedDate} (format detectat: {$format})\n";
                file_put_contents($logFile, $logData, FILE_APPEND);
                return $formattedDate;
            }
        }

        // Încercăm să parsăm data cu strtotime ca ultimă soluție
        $timestamp = strtotime($date);
        if ($timestamp !== false) {
            $formattedDate = date('Y-m-d\TH:i:s', $timestamp);
            $logData = date('Y-m-d H:i:s') . " - Data formatată pentru SOAP: {$formattedDate} (folosind strtotime)\n";
            file_put_contents($logFile, $logData, FILE_APPEND);
            return $formattedDate;
        }

        // Dacă nu am reușit să parsăm data, logăm eroarea
        $logData = date('Y-m-d H:i:s') . " - Eroare: Nu s-a putut formata data '{$date}' pentru SOAP\n";
        file_put_contents($logFile, $logData, FILE_APPEND);

        return null;
    }

    /**
     * Formatează o dată primită de la SOAP
     *
     * @param string $date Data în format SOAP
     * @return string Data formatată (d.m.Y)
     */
    private function formatDateFromSoap($date)
    {
        if (empty($date)) {
            return '';
        }

        try {
            $dateObj = new \DateTime($date);
            return $dateObj->format('d.m.Y');
        } catch (Exception $e) {
            return '';
        }
    }

    /**
     * Normalizează caracterele diacritice pentru a asigura compatibilitatea cu API-ul SOAP
     * Funcție îmbunătățită pentru a gestiona toate variațiile posibile de diacritice românești
     *
     * @param string $text Textul care trebuie normalizat
     * @return string Textul normalizat
     */
    private function normalizeDiacritics($text)
    {
        if (empty($text)) {
            return '';
        }

        // Creăm un director pentru loguri dacă nu există
        $logDir = dirname(__DIR__, 2) . '/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        // Logăm textul original pentru depanare
        $logFile = $logDir . '/diacritics_debug.log';
        $logData = date('Y-m-d H:i:s') . " - Text original: " . $text . "\n";
        file_put_contents($logFile, $logData, FILE_APPEND);

        // Mapare extinsă a caracterelor diacritice la forma lor normalizată
        // Include toate variațiile posibile de codificare pentru diacriticele românești
        $diacritics = [
            // Diacritice românești standard
            'ă' => 'a', 'Ă' => 'A',
            'â' => 'a', 'Â' => 'A',
            'î' => 'i', 'Î' => 'I',
            'ș' => 's', 'Ș' => 'S',
            'ț' => 't', 'Ț' => 'T',

            // Variante alternative de codificare
            'ş' => 's', 'Ş' => 'S',
            'ţ' => 't', 'Ţ' => 'T',

            // Variante cu accente
            'á' => 'a', 'Á' => 'A',
            'à' => 'a', 'À' => 'A',
            'ä' => 'a', 'Ä' => 'A',
            'é' => 'e', 'É' => 'E',
            'è' => 'e', 'È' => 'E',
            'ë' => 'e', 'Ë' => 'E',
            'í' => 'i', 'Í' => 'I',
            'ì' => 'i', 'Ì' => 'I',
            'ï' => 'i', 'Ï' => 'I',
            'ó' => 'o', 'Ó' => 'O',
            'ò' => 'o', 'Ò' => 'O',
            'ö' => 'o', 'Ö' => 'O',
            'ú' => 'u', 'Ú' => 'U',
            'ù' => 'u', 'Ù' => 'U',
            'ü' => 'u', 'Ü' => 'U'
        ];

        // Metoda 1: Utilizăm strtr pentru înlocuire directă
        $normalizedText = strtr($text, $diacritics);

        // Metoda 2: Utilizăm transliterarea iconv ca backup
        // Această metodă poate gestiona și alte caractere Unicode care nu sunt în maparea noastră
        if (function_exists('iconv')) {
            $translit = @iconv('UTF-8', 'ASCII//TRANSLIT', $text);
            if ($translit !== false) {
                // Dacă transliterarea a reușit, comparăm rezultatele și alegem cel mai bun
                // Preferăm rezultatul strtr dacă diferă doar prin diacritice
                if (strlen($normalizedText) !== strlen($text) || $normalizedText === $text) {
                    $normalizedText = $translit;
                }
            }
        }

        // Metoda 3: Utilizăm Normalizer din intl dacă este disponibil
        if (class_exists('Normalizer')) {
            // Descompunem caracterele în forma lor de bază + accente
            $decomposed = \Normalizer::normalize($text, \Normalizer::FORM_D);
            if ($decomposed !== false) {
                // Eliminăm toate semnele diacritice (categoria Mn - Mark, nonspacing)
                $withoutDiacritics = preg_replace('/\p{Mn}/u', '', $decomposed);
                if ($withoutDiacritics !== null) {
                    // Dacă rezultatul este mai bun decât cel obținut anterior, îl folosim
                    if (strlen($normalizedText) !== strlen($text) || $normalizedText === $text) {
                        $normalizedText = $withoutDiacritics;
                    }
                }
            }
        }

        // Logăm textul normalizat pentru depanare
        $logData = date('Y-m-d H:i:s') . " - Text normalizat: " . $normalizedText . "\n";
        file_put_contents($logFile, $logData, FILE_APPEND);

        return $normalizedText;
    }

    /**
     * Aplică filtrarea client-side pentru parametrii care nu sunt suportați direct de SOAP API
     *
     * @param array $results Rezultatele de la SOAP API
     * @param string $categorieInstanta Categoria instanței pentru filtrare
     * @param string $categorieCaz Categoria cazului pentru filtrare
     * @param string $logFile Fișierul de log pentru depanare
     * @return array Rezultatele filtrate
     */
    private function applyClientSideFiltering($results, $categorieInstanta = '', $categorieCaz = '', $logFile = '')
    {
        if (empty($results)) {
            return $results;
        }

        $originalCount = count($results);
        $filteredResults = $results;

        // Filtrare după categoria instanței - ENHANCED PATTERNS
        if (!empty($categorieInstanta)) {
            $filteredResults = array_filter($filteredResults, function($dosar) use ($categorieInstanta, $logFile) {
                $institutie = $dosar->institutie ?? '';
                $institutieNormalized = strtolower($institutie);

                $matches = false;

                switch ($categorieInstanta) {
                    case 'curtea_suprema':
                        $patterns = ['inaltacurte', 'înalta curte', 'iccj', 'curtea suprema'];
                        foreach ($patterns as $pattern) {
                            if (stripos($institutieNormalized, $pattern) !== false) {
                                $matches = true;
                                break;
                            }
                        }
                        break;

                    case 'curte_apel':
                        $patterns = ['curteadeapel', 'curtea de apel', 'c.a.', 'ca '];
                        foreach ($patterns as $pattern) {
                            if (stripos($institutieNormalized, $pattern) !== false) {
                                $matches = true;
                                break;
                            }
                        }
                        break;

                    case 'tribunal':
                        // Enhanced patterns for tribunal matching - including variations without spaces
                        $patterns = ['tribunalul', 'tribunal ', 'trib.', 'tribunal'];
                        foreach ($patterns as $pattern) {
                            if (stripos($institutieNormalized, $pattern) !== false) {
                                $matches = true;
                                break;
                            }
                        }
                        // Additional check: ensure it's not a Court of Appeal or Supreme Court
                        if ($matches) {
                            $excludePatterns = ['curteadeapel', 'curtea de apel', 'inaltacurte', 'înalta curte'];
                            foreach ($excludePatterns as $excludePattern) {
                                if (stripos($institutieNormalized, $excludePattern) !== false) {
                                    $matches = false;
                                    break;
                                }
                            }
                        }
                        break;

                    case 'judecatorie':
                        $patterns = ['judecatoria', 'judecătoria', 'jud.'];
                        foreach ($patterns as $pattern) {
                            if (stripos($institutieNormalized, $pattern) !== false) {
                                $matches = true;
                                break;
                            }
                        }
                        break;

                    default:
                        $matches = true;
                }

                // Logăm match-ul pentru debugging
                if ($matches && !empty($logFile)) {
                    $logData = date('Y-m-d H:i:s') . " - Institution category match: '{$categorieInstanta}' pentru {$institutie}\n";
                    file_put_contents($logFile, $logData, FILE_APPEND);
                }

                return $matches;
            });

            // Re-indexăm array-ul după filtrare
            $filteredResults = array_values($filteredResults);
        }

        // Filtrare după categoria cazului - ENHANCED pentru "munca" (labor law)
        if (!empty($categorieCaz)) {
            $filteredResults = array_filter($filteredResults, function($dosar) use ($categorieCaz, $logFile) {
                $categorieCazDosar = strtolower($dosar->categorieCaz ?? '');
                $categorieCazNume = strtolower($dosar->categorieCazNume ?? '');
                $obiect = strtolower($dosar->obiect ?? '');

                $categorieCazLower = strtolower($categorieCaz);

                // Pentru "munca" (labor law), căutăm în mai multe câmpuri cu termeni relevanți
                if ($categorieCazLower === 'munca') {
                    $laborTerms = ['munca', 'muncă', 'salarial', 'salariat', 'angajat', 'angajator',
                                  'contract de munca', 'contract de muncă', 'individual de munca',
                                  'individual de muncă', 'concediere', 'licențiere', 'despăgubiri',
                                  'daune morale', 'discriminare', 'hărțuire', 'overtime', 'ore suplimentare'];

                    foreach ($laborTerms as $term) {
                        if (stripos($categorieCazDosar, $term) !== false ||
                            stripos($categorieCazNume, $term) !== false ||
                            stripos($obiect, $term) !== false) {

                            // Logăm match-ul pentru debugging
                            if (!empty($logFile)) {
                                $logData = date('Y-m-d H:i:s') . " - Labor law match: '{$term}' în dosarul {$dosar->numar}\n";
                                file_put_contents($logFile, $logData, FILE_APPEND);
                            }
                            return true;
                        }
                    }
                    return false;
                }
                // Pentru "contencios_administrativ" (administrative litigation), căutăm termeni specifici
                elseif ($categorieCazLower === 'contencios_administrativ' || $categorieCazLower === 'contencios administrativ') {
                    $adminTerms = ['contencios administrativ', 'contencios', 'administrativ', 'administrative',
                                  'litigiu administrativ', 'drept administrativ', 'admin', 'contenciosul administrativ',
                                  'act administrativ', 'decizie administrativa', 'decizie administrativă',
                                  'autoritate publica', 'autoritate publică', 'instituție publică', 'institutie publica',
                                  'anulare act', 'anularea actului', 'obligarea la', 'constatarea nulității',
                                  'repararea prejudiciului', 'daune-interese', 'contencios fiscal',
                                  'contencios urbanistic', 'contencios în materie', 'recurs administrativ'];

                    foreach ($adminTerms as $term) {
                        if (stripos($categorieCazDosar, $term) !== false ||
                            stripos($categorieCazNume, $term) !== false ||
                            stripos($obiect, $term) !== false) {

                            // Logăm match-ul pentru debugging
                            if (!empty($logFile)) {
                                $logData = date('Y-m-d H:i:s') . " - Administrative litigation match: '{$term}' în dosarul {$dosar->numar}\n";
                                file_put_contents($logFile, $logData, FILE_APPEND);
                            }
                            return true;
                        }
                    }
                    return false;
                }
                // Pentru "civil" (civil law), căutăm termeni specifici
                elseif ($categorieCazLower === 'civil') {
                    $civilTerms = ['civil', 'civilă', 'civile', 'drept civil', 'proces civil', 'litigiu civil',
                                  'contracte civile', 'răspundere civilă', 'daune civile', 'obligații civile',
                                  'drepturi civile', 'acțiune civilă', 'cerere civilă', 'contencios civil'];

                    foreach ($civilTerms as $term) {
                        if (stripos($categorieCazDosar, $term) !== false ||
                            stripos($categorieCazNume, $term) !== false ||
                            stripos($obiect, $term) !== false) {

                            if (!empty($logFile)) {
                                $logData = date('Y-m-d H:i:s') . " - Civil law match: '{$term}' în dosarul {$dosar->numar}\n";
                                file_put_contents($logFile, $logData, FILE_APPEND);
                            }
                            return true;
                        }
                    }
                    return false;
                }
                // Pentru "penal" (criminal law), căutăm termeni specifici
                elseif ($categorieCazLower === 'penal') {
                    $penalTerms = ['penal', 'penală', 'penale', 'drept penal', 'infracțiune', 'infracțiuni',
                                  'crimă', 'crime', 'delict', 'delicte', 'proces penal', 'urmărire penală',
                                  'acțiune penală', 'plângere penală', 'dosar penal', 'cauză penală'];

                    foreach ($penalTerms as $term) {
                        if (stripos($categorieCazDosar, $term) !== false ||
                            stripos($categorieCazNume, $term) !== false ||
                            stripos($obiect, $term) !== false) {

                            if (!empty($logFile)) {
                                $logData = date('Y-m-d H:i:s') . " - Criminal law match: '{$term}' în dosarul {$dosar->numar}\n";
                                file_put_contents($logFile, $logData, FILE_APPEND);
                            }
                            return true;
                        }
                    }
                    return false;
                }
                // Pentru "comercial" (commercial law), căutăm termeni specifici
                elseif ($categorieCazLower === 'comercial') {
                    $comercialTerms = ['comercial', 'comercială', 'comerciale', 'drept comercial', 'societate comercială',
                                      'societăți comerciale', 'afaceri', 'comerț', 'întreprindere', 'întreprinderi',
                                      'contract comercial', 'tranzacție comercială', 'activitate comercială'];

                    foreach ($comercialTerms as $term) {
                        if (stripos($categorieCazDosar, $term) !== false ||
                            stripos($categorieCazNume, $term) !== false ||
                            stripos($obiect, $term) !== false) {

                            if (!empty($logFile)) {
                                $logData = date('Y-m-d H:i:s') . " - Commercial law match: '{$term}' în dosarul {$dosar->numar}\n";
                                file_put_contents($logFile, $logData, FILE_APPEND);
                            }
                            return true;
                        }
                    }
                    return false;
                }
                // Pentru "fiscal" (tax law), căutăm termeni specifici
                elseif ($categorieCazLower === 'fiscal') {
                    $fiscalTerms = ['fiscal', 'fiscală', 'fiscale', 'drept fiscal', 'impozit', 'impozite',
                                   'taxe', 'taxă', 'contribuții', 'contribuție', 'ANAF', 'fisc', 'fiscalitate',
                                   'obligații fiscale', 'declarație fiscală', 'control fiscal', 'verificare fiscală'];

                    foreach ($fiscalTerms as $term) {
                        if (stripos($categorieCazDosar, $term) !== false ||
                            stripos($categorieCazNume, $term) !== false ||
                            stripos($obiect, $term) !== false) {

                            if (!empty($logFile)) {
                                $logData = date('Y-m-d H:i:s') . " - Tax law match: '{$term}' în dosarul {$dosar->numar}\n";
                                file_put_contents($logFile, $logData, FILE_APPEND);
                            }
                            return true;
                        }
                    }
                    return false;
                }
                // Pentru "familie" (family law), căutăm termeni specifici
                elseif ($categorieCazLower === 'familie') {
                    $familieTerms = ['familie', 'familii', 'familial', 'familială', 'drept de familie',
                                    'divorț', 'căsătorie', 'căsătorii', 'custodie', 'întreținere', 'adopție',
                                    'adopții', 'tutela', 'curatela', 'autoritate părintească', 'pensie alimentară'];

                    foreach ($familieTerms as $term) {
                        if (stripos($categorieCazDosar, $term) !== false ||
                            stripos($categorieCazNume, $term) !== false ||
                            stripos($obiect, $term) !== false) {

                            if (!empty($logFile)) {
                                $logData = date('Y-m-d H:i:s') . " - Family law match: '{$term}' în dosarul {$dosar->numar}\n";
                                file_put_contents($logFile, $logData, FILE_APPEND);
                            }
                            return true;
                        }
                    }
                    return false;
                }
                // Pentru "administrativ" (administrative law), căutăm termeni specifici
                elseif ($categorieCazLower === 'administrativ') {
                    $administrativTerms = ['administrativ', 'administrativă', 'administrative', 'drept administrativ',
                                          'autoritate publică', 'autoritate publica', 'instituție publică', 'institutie publica',
                                          'act administrativ', 'decizie administrativă', 'decizie administrativa',
                                          'procedură administrativă', 'procedura administrativa'];

                    foreach ($administrativTerms as $term) {
                        if (stripos($categorieCazDosar, $term) !== false ||
                            stripos($categorieCazNume, $term) !== false ||
                            stripos($obiect, $term) !== false) {

                            if (!empty($logFile)) {
                                $logData = date('Y-m-d H:i:s') . " - Administrative law match: '{$term}' în dosarul {$dosar->numar}\n";
                                file_put_contents($logFile, $logData, FILE_APPEND);
                            }
                            return true;
                        }
                    }
                    return false;
                } else {
                    // Pentru alte categorii, folosim căutarea standard
                    $matches = stripos($categorieCazDosar, $categorieCazLower) !== false ||
                              stripos($categorieCazNume, $categorieCazLower) !== false ||
                              stripos($obiect, $categorieCazLower) !== false;

                    // Logăm match-ul pentru debugging
                    if ($matches && !empty($logFile)) {
                        $logData = date('Y-m-d H:i:s') . " - Standard category match: '{$categorieCazLower}' în dosarul {$dosar->numar}\n";
                        file_put_contents($logFile, $logData, FILE_APPEND);
                    }

                    return $matches;
                }
            });

            // Re-indexăm array-ul după filtrare
            $filteredResults = array_values($filteredResults);
        }

        $filteredCount = count($filteredResults);

        // Logăm rezultatele filtrării
        if (!empty($logFile) && ($originalCount !== $filteredCount)) {
            $logData = date('Y-m-d H:i:s') . " - Filtrare client-side: {$originalCount} -> {$filteredCount} rezultate\n";
            file_put_contents($logFile, $logData, FILE_APPEND);
        }

        return $filteredResults;
    }
}