<?php
echo "🔍 DEBUGGING REGEX VALIDATION FOR: Burduşelu Tudoriţa\n";
echo "=====================================================\n\n";

$testName = "Burduşelu Tudoriţa";
echo "Testing name: '{$testName}'\n";
echo "Length: " . strlen($testName) . "\n\n";

// Test the current regex pattern
$currentPattern = '/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+$/u';
echo "Current pattern: {$currentPattern}\n";
echo "Match result: " . (preg_match($currentPattern, $testName) ? 'PASS' : 'FAIL') . "\n\n";

// Let's analyze each character
echo "🔍 CHARACTER ANALYSIS:\n";
echo "======================\n";
for ($i = 0; $i < strlen($testName); $i++) {
    $char = mb_substr($testName, $i, 1, 'UTF-8');
    $ord = ord($char);
    echo "Position {$i}: '{$char}' (ord: {$ord})\n";
}

echo "\n🔍 TESTING DIFFERENT PATTERNS:\n";
echo "===============================\n";

// Test patterns
$patterns = [
    '/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+$/u' => 'Current pattern',
    '/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.ş]+$/u' => 'Added ş',
    '/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.şŞ]+$/u' => 'Added ş and Ş',
    '/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.şŞţŢ]+$/u' => 'Added ş, Ş, ţ, Ţ',
    '/^[A-ZĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.şŞţŢ]+$/u' => 'Added all Romanian chars',
    '/^[A-ZĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+$/u' => 'Complete Romanian charset',
    '/^[A-Za-zĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+$/u' => 'Allow lowercase start',
    '/^[A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+$/u' => 'Very permissive'
];

foreach ($patterns as $pattern => $description) {
    $result = preg_match($pattern, $testName);
    echo "{$description}: " . ($result ? '✅ PASS' : '❌ FAIL') . "\n";
    
    if (!$result) {
        // Try to find which character is causing the issue
        for ($i = 0; $i < mb_strlen($testName, 'UTF-8'); $i++) {
            $char = mb_substr($testName, $i, 1, 'UTF-8');
            $charPattern = str_replace('^', '^.*', str_replace('$', '.*$', $pattern));
            if (!preg_match($charPattern, $char)) {
                echo "  Problem character at position {$i}: '{$char}'\n";
                break;
            }
        }
    }
}

echo "\n🔍 TESTING INDIVIDUAL CHARACTERS:\n";
echo "==================================\n";

$chars = ['B', 'u', 'r', 'd', 'u', 'ş', 'e', 'l', 'u', ' ', 'T', 'u', 'd', 'o', 'r', 'i', 'ţ', 'a'];
$charPattern = '/[A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]/u';

foreach ($chars as $i => $char) {
    $result = preg_match($charPattern, $char);
    echo "'{$char}': " . ($result ? '✅ PASS' : '❌ FAIL') . "\n";
}

echo "\n🔍 TESTING SPECIFIC ROMANIAN CHARACTERS:\n";
echo "=========================================\n";

$romanianChars = ['ă', 'â', 'î', 'ș', 'ț', 'ş', 'ţ', 'Ă', 'Â', 'Î', 'Ș', 'Ț', 'Ş', 'Ţ'];
foreach ($romanianChars as $char) {
    $result = preg_match('/[A-Za-zĂÂÎȘȚăâîșțţ]/u', $char);
    echo "'{$char}': " . ($result ? '✅ PASS' : '❌ FAIL') . "\n";
}

echo "\n✅ Analysis complete\n";
?>
