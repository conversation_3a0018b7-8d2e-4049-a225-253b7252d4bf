<?php

/**
 * Portal Judiciar România - Admin Login
 * 
 * Administrative login interface with enhanced security
 */

require_once dirname(__DIR__, 2) . '/bootstrap.php';
require_once dirname(__DIR__, 2) . '/includes/config.php';

use App\Config\Database;
use App\Security\CSRFProtection;
use App\Security\RateLimiter;

// Session is already started in bootstrap.php

// If already logged in as admin, redirect to dashboard
if (isset($_SESSION['user_id'])) {
    $userId = $_SESSION['user_id'];
    $adminRole = Database::fetchOne("SELECT admin_role FROM users WHERE id = ?", [$userId])['admin_role'] ?? null;
    
    if ($adminRole) {
        header('Location: index.php');
        exit;
    }
}

$error = '';
$success = '';

// Check for logout success message
if (isset($_GET['message']) && $_GET['message'] === 'logout_success') {
    $success = 'Ați fost deconectat cu succes.';
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate CSRF token
        if (!CSRFProtection::validateRequest($_POST, 'admin_login')) {
            throw new Exception('Invalid CSRF token');
        }
        
        $email = trim($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if (empty($email) || empty($password)) {
            throw new Exception('Email și parola sunt obligatorii');
        }
        
        // Check rate limiting
        $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $rateLimitKey = "admin_login_{$clientIp}";

        $rateLimitCheck = RateLimiter::checkLimit('admin_login', $rateLimitKey, [
            'limit' => 5,
            'window' => 900 // 15 minutes
        ]);

        if (!$rateLimitCheck['allowed']) {
            throw new Exception('Prea multe încercări de autentificare. Încercați din nou în 15 minute.');
        }
        
        // Find user
        $user = Database::fetchOne("
            SELECT id, email, password_hash, first_name, last_name, admin_role, 
                   email_verified, locked_until, deleted_at
            FROM users 
            WHERE email = ? AND admin_role IS NOT NULL
        ", [$email]);
        
        if (!$user) {
            RateLimiter::recordAttempt('admin_login', $rateLimitKey);
            throw new Exception('Email sau parolă incorectă');
        }
        
        // Check if account is active
        if ($user['deleted_at']) {
            throw new Exception('Contul este dezactivat');
        }
        
        if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
            throw new Exception('Contul este suspendat temporar');
        }
        
        if (!$user['email_verified']) {
            throw new Exception('Email-ul nu este verificat');
        }
        
        // Verify password
        if (!password_verify($password, $user['password_hash'])) {
            RateLimiter::recordAttempt('admin_login', $rateLimitKey);
            throw new Exception('Email sau parolă incorectă');
        }
        
        // Successful login
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
        $_SESSION['admin_role'] = $user['admin_role'];
        $_SESSION['login_time'] = time();
        
        // Update last login
        Database::update('users', [
            'last_login_at' => date('Y-m-d H:i:s'),
            'last_login_ip' => $clientIp
        ], ['id' => $user['id']]);
        
        // Log admin login
        Database::insert('data_processing_logs', [
            'user_id' => $user['id'],
            'action' => 'admin_login',
            'ip_address' => $clientIp,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        // Redirect to dashboard
        header('Location: index.php');
        exit;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Generate CSRF token
$csrfToken = CSRFProtection::generateToken('admin_login');

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autentificare Administrativ - Portal Judiciar România</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #2c3e50;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .btn-admin {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 10px;
            padding: 0.75rem 2rem;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s;
        }
        
        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .input-group-text {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 10px 0 0 10px;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .back-link {
            color: white;
            text-decoration: none;
            opacity: 0.8;
            transition: opacity 0.3s;
        }
        
        .back-link:hover {
            color: white;
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="login-container">
                    <div class="login-header">
                        <h2 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            Panou Administrativ
                        </h2>
                        <p class="mb-0 opacity-75">Portal Judiciar România</p>
                        <div class="mt-3">
                            <a href="../monitor.php" class="back-link">
                                <i class="fas fa-arrow-left me-1"></i>
                                Înapoi la Portal
                            </a>
                        </div>
                    </div>
                    
                    <div class="login-body">
                        <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?= htmlspecialchars($error) ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?= htmlspecialchars($success) ?>
                        </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrfToken) ?>">
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    Email Administrator
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" 
                                           placeholder="<EMAIL>" required>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    Parolă
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-key"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="Introduceți parola" required>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-admin text-white">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Autentificare
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Acces restricționat doar pentru administratori
                            </small>
                        </div>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <strong>Demo:</strong> <EMAIL> / admin123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
