-- Security and GDPR Compliance Tables
-- Portal Judiciar <PERSON><PERSON> - Case Monitoring System
-- Migration 003: Security Features

-- Rate limiting attempts table
CREATE TABLE IF NOT EXISTS rate_limit_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    identifier VARCHAR(255) NOT NULL COMMENT 'User ID or IP address',
    action VARCHAR(100) NOT NULL COMMENT 'Action being rate limited',
    success TINYINT(1) NOT NULL DEFAULT 1 COMMENT 'Whether the action was successful',
    ip_address VARCHAR(45) NULL COMMENT 'IP address of the request',
    user_agent TEXT NULL COMMENT 'User agent string',
    metadata JSON NULL COMMENT 'Additional metadata about the attempt',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_identifier_action (identifier, action),
    INDEX idx_created_at (created_at),
    INDEX idx_action_success (action, success)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Rate limiting attempts tracking';

-- User consents table for GDPR compliance
CREATE TABLE IF NOT EXISTS user_consents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    consent_type VARCHAR(100) NOT NULL COMMENT 'Type of consent (monitoring, email_notifications, etc.)',
    granted TINYINT(1) NOT NULL COMMENT 'Whether consent was granted',
    ip_address VARCHAR(45) NULL COMMENT 'IP address when consent was given',
    user_agent TEXT NULL COMMENT 'User agent when consent was given',
    metadata JSON NULL COMMENT 'Additional consent metadata',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_consent (user_id, consent_type),
    INDEX idx_consent_type (consent_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User consent records for GDPR compliance';

-- Data processing logs for GDPR audit trail
CREATE TABLE IF NOT EXISTS data_processing_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NULL COMMENT 'User ID (null for system actions)',
    action VARCHAR(100) NOT NULL COMMENT 'Action performed',
    context JSON NULL COMMENT 'Context and details of the action',
    ip_address VARCHAR(45) NULL COMMENT 'IP address of the request',
    user_agent TEXT NULL COMMENT 'User agent string',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Data processing audit trail for GDPR';

-- Security incidents table
CREATE TABLE IF NOT EXISTS security_incidents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    incident_type VARCHAR(100) NOT NULL COMMENT 'Type of security incident',
    severity ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
    description TEXT NOT NULL COMMENT 'Description of the incident',
    ip_address VARCHAR(45) NULL COMMENT 'IP address involved',
    user_id INT UNSIGNED NULL COMMENT 'User ID if applicable',
    user_agent TEXT NULL COMMENT 'User agent string',
    request_data JSON NULL COMMENT 'Request data that triggered the incident',
    resolved TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'Whether the incident has been resolved',
    resolved_at TIMESTAMP NULL COMMENT 'When the incident was resolved',
    resolved_by INT UNSIGNED NULL COMMENT 'Admin user who resolved the incident',
    notes TEXT NULL COMMENT 'Additional notes about the incident',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_incident_type (incident_type),
    INDEX idx_severity (severity),
    INDEX idx_resolved (resolved),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Security incidents tracking';

-- User sessions table for session management
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(128) PRIMARY KEY COMMENT 'Session ID',
    user_id INT UNSIGNED NULL COMMENT 'User ID if logged in',
    ip_address VARCHAR(45) NOT NULL COMMENT 'IP address',
    user_agent TEXT NULL COMMENT 'User agent string',
    session_data TEXT NULL COMMENT 'Serialized session data',
    csrf_tokens JSON NULL COMMENT 'CSRF tokens for this session',
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL COMMENT 'Session expiration time',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity),
    INDEX idx_expires_at (expires_at),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User session management';

-- Privacy settings table
CREATE TABLE IF NOT EXISTS user_privacy_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    setting_name VARCHAR(100) NOT NULL COMMENT 'Privacy setting name',
    setting_value TEXT NOT NULL COMMENT 'Privacy setting value',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_setting (user_id, setting_name),
    INDEX idx_setting_name (setting_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User privacy settings';

-- Data export requests table
CREATE TABLE IF NOT EXISTS data_export_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    request_type ENUM('export', 'deletion') NOT NULL COMMENT 'Type of data request',
    status ENUM('pending', 'processing', 'completed', 'failed') NOT NULL DEFAULT 'pending',
    requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL COMMENT 'When the request was processed',
    export_file_path VARCHAR(500) NULL COMMENT 'Path to exported data file',
    export_expires_at TIMESTAMP NULL COMMENT 'When the export file expires',
    deletion_summary JSON NULL COMMENT 'Summary of deleted data',
    notes TEXT NULL COMMENT 'Additional notes',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_request_type (request_type),
    INDEX idx_requested_at (requested_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='GDPR data export and deletion requests';

-- Add deleted_at column to users table for soft deletes
ALTER TABLE users 
ADD COLUMN deleted_at TIMESTAMP NULL COMMENT 'Soft delete timestamp for GDPR compliance' AFTER updated_at;

-- Add GDPR compliance fields to users table
ALTER TABLE users 
ADD COLUMN gdpr_consent_date TIMESTAMP NULL COMMENT 'Date when GDPR consent was given' AFTER deleted_at,
ADD COLUMN data_processing_consent TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'Consent for data processing' AFTER gdpr_consent_date,
ADD COLUMN marketing_consent TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'Consent for marketing communications' AFTER data_processing_consent;

-- Add security fields to users table
ALTER TABLE users 
ADD COLUMN failed_login_attempts INT NOT NULL DEFAULT 0 COMMENT 'Number of failed login attempts' AFTER marketing_consent,
ADD COLUMN locked_until TIMESTAMP NULL COMMENT 'Account locked until this time' AFTER failed_login_attempts,
ADD COLUMN last_login_at TIMESTAMP NULL COMMENT 'Last successful login' AFTER locked_until,
ADD COLUMN last_login_ip VARCHAR(45) NULL COMMENT 'IP address of last login' AFTER last_login_at;

-- Create indexes for new user fields
CREATE INDEX idx_users_deleted_at ON users(deleted_at);
CREATE INDEX idx_users_gdpr_consent ON users(gdpr_consent_date);
CREATE INDEX idx_users_locked_until ON users(locked_until);
CREATE INDEX idx_users_last_login ON users(last_login_at);

-- Insert default privacy settings for existing users
INSERT IGNORE INTO user_privacy_settings (user_id, setting_name, setting_value)
SELECT id, 'data_retention_period', '365' FROM users WHERE deleted_at IS NULL;

INSERT IGNORE INTO user_privacy_settings (user_id, setting_name, setting_value)
SELECT id, 'notification_frequency', 'daily' FROM users WHERE deleted_at IS NULL;

INSERT IGNORE INTO user_privacy_settings (user_id, setting_name, setting_value)
SELECT id, 'data_sharing', 'none' FROM users WHERE deleted_at IS NULL;

-- Create a view for active users (not soft deleted)
CREATE OR REPLACE VIEW active_users AS
SELECT * FROM users WHERE deleted_at IS NULL;

-- Create a view for GDPR compliant users
CREATE OR REPLACE VIEW gdpr_compliant_users AS
SELECT u.*, 
       CASE WHEN u.gdpr_consent_date IS NOT NULL AND u.data_processing_consent = 1 THEN 1 ELSE 0 END as is_gdpr_compliant
FROM users u 
WHERE u.deleted_at IS NULL;

-- Insert sample consent records for existing users
INSERT IGNORE INTO user_consents (user_id, consent_type, granted, ip_address, created_at)
SELECT id, 'data_processing', 1, '127.0.0.1', NOW() 
FROM users 
WHERE deleted_at IS NULL AND data_processing_consent = 1;

INSERT IGNORE INTO user_consents (user_id, consent_type, granted, ip_address, created_at)
SELECT id, 'monitoring', 1, '127.0.0.1', NOW() 
FROM users 
WHERE deleted_at IS NULL;

-- Log the migration
INSERT INTO data_processing_logs (user_id, action, context, created_at)
VALUES (NULL, 'security_migration_applied', 
        JSON_OBJECT('migration', '003_create_security_tables', 'tables_created', 7, 'views_created', 2),
        NOW());

-- Update migration tracking
INSERT INTO schema_migrations (version, applied_at) 
VALUES ('003', NOW()) 
ON DUPLICATE KEY UPDATE applied_at = NOW();
