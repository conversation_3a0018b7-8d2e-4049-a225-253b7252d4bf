<?php

namespace App\Services;

use App\Config\Database;
use Exception;

/**
 * Notification Queue Manager
 * 
 * Handles efficient queue management with batch processing,
 * priority handling, and resource optimization.
 */
class NotificationQueueManager
{
    private $logFile;
    private $maxBatchSize;
    private $maxExecutionTime;

    public function __construct()
    {
        $this->logFile = LOG_DIR . '/queue_manager.log';
        $this->maxBatchSize = NOTIFICATION_BATCH_SIZE ?? 50;
        $this->maxExecutionTime = 300; // 5 minutes max execution
        
        // Ensure log directory exists
        if (!is_dir(LOG_DIR)) {
            mkdir(LOG_DIR, 0755, true);
        }
    }

    /**
     * Process notifications in optimized batches
     * 
     * @param int $maxNotifications Maximum notifications to process
     * @return array Processing summary
     */
    public function processBatch(int $maxNotifications = null): array
    {
        $startTime = time();
        $maxNotifications = $maxNotifications ?? $this->maxBatchSize;
        
        $this->logInfo("Starting batch processing (max: {$maxNotifications})");
        
        $summary = [
            'batches_processed' => 0,
            'total_processed' => 0,
            'total_sent' => 0,
            'total_failed' => 0,
            'total_retried' => 0,
            'execution_time' => 0,
            'errors' => []
        ];

        try {
            // Process in smaller batches to avoid memory issues
            $batchSize = min($this->maxBatchSize, $maxNotifications);
            $processed = 0;
            
            while ($processed < $maxNotifications && (time() - $startTime) < $this->maxExecutionTime) {
                $remaining = $maxNotifications - $processed;
                $currentBatchSize = min($batchSize, $remaining);
                
                $batchResult = $this->processSingleBatch($currentBatchSize);
                
                // Update summary
                $summary['batches_processed']++;
                $summary['total_processed'] += $batchResult['processed'];
                $summary['total_sent'] += $batchResult['sent'];
                $summary['total_failed'] += $batchResult['failed'];
                $summary['total_retried'] += $batchResult['retried'];
                $summary['errors'] = array_merge($summary['errors'], $batchResult['errors']);
                
                $processed += $batchResult['processed'];
                
                // If no notifications were processed, break to avoid infinite loop
                if ($batchResult['processed'] === 0) {
                    break;
                }
                
                // Small delay between batches to prevent overwhelming the system
                if ($processed < $maxNotifications) {
                    usleep(500000); // 0.5 second delay
                }
            }
            
        } catch (Exception $e) {
            $this->logError("Batch processing error: " . $e->getMessage());
            $summary['errors'][] = "Batch processing error: " . $e->getMessage();
        }

        $summary['execution_time'] = time() - $startTime;
        
        $this->logInfo("Batch processing completed. Batches: {$summary['batches_processed']}, " .
                      "Processed: {$summary['total_processed']}, Sent: {$summary['total_sent']}, " .
                      "Failed: {$summary['total_failed']}, Time: {$summary['execution_time']}s");

        return $summary;
    }

    /**
     * Process a single batch of notifications
     * 
     * @param int $batchSize Size of the batch
     * @return array Batch processing result
     */
    private function processSingleBatch(int $batchSize): array
    {
        $notificationManager = new NotificationManager();
        return $notificationManager->processPendingNotifications($batchSize);
    }

    /**
     * Get queue statistics
     * 
     * @return array Queue statistics
     */
    public function getQueueStats(): array
    {
        try {
            $stats = Database::fetchOne(
                "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                    SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                    SUM(CASE WHEN priority = 1 THEN 1 ELSE 0 END) as high_priority,
                    SUM(CASE WHEN priority = 2 THEN 1 ELSE 0 END) as normal_priority,
                    SUM(CASE WHEN priority = 3 THEN 1 ELSE 0 END) as low_priority,
                    MIN(scheduled_for) as oldest_pending,
                    MAX(created_at) as newest_created
                 FROM notification_queue"
            );
            
            // Add additional metrics
            $stats['pending_overdue'] = Database::fetchOne(
                "SELECT COUNT(*) as count 
                 FROM notification_queue 
                 WHERE status = 'pending' 
                 AND scheduled_for < DATE_SUB(NOW(), INTERVAL 1 HOUR)"
            )['count'] ?? 0;
            
            $stats['failed_today'] = Database::fetchOne(
                "SELECT COUNT(*) as count 
                 FROM notification_queue 
                 WHERE status = 'failed' 
                 AND DATE(created_at) = CURDATE()"
            )['count'] ?? 0;
            
            return $stats ?: [];
            
        } catch (Exception $e) {
            $this->logError("Failed to get queue stats: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Clean up stuck notifications
     * 
     * @return int Number of notifications cleaned up
     */
    public function cleanupStuckNotifications(): int
    {
        try {
            // Reset notifications that have been "processing" for too long
            $updated = Database::execute(
                "UPDATE notification_queue 
                 SET status = 'pending', 
                     scheduled_for = NOW() + INTERVAL 5 MINUTE
                 WHERE status = 'processing' 
                 AND updated_at < DATE_SUB(NOW(), INTERVAL 30 MINUTE)"
            )->rowCount();
            
            if ($updated > 0) {
                $this->logInfo("Reset {$updated} stuck notifications from processing to pending");
            }
            
            return $updated;
            
        } catch (Exception $e) {
            $this->logError("Failed to cleanup stuck notifications: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Optimize queue performance
     * 
     * @return array Optimization results
     */
    public function optimizeQueue(): array
    {
        $results = [
            'stuck_cleaned' => 0,
            'old_cleaned' => 0,
            'indexes_optimized' => false
        ];

        try {
            // Clean up stuck notifications
            $results['stuck_cleaned'] = $this->cleanupStuckNotifications();
            
            // Clean up old processed notifications (older than 30 days)
            $deleted = Database::execute(
                "DELETE FROM notification_queue 
                 WHERE status IN ('sent', 'failed') 
                 AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
                 LIMIT 1000"
            )->rowCount();
            
            $results['old_cleaned'] = $deleted;
            
            if ($deleted > 0) {
                $this->logInfo("Cleaned up {$deleted} old notifications");
            }
            
            // Optimize table (MySQL specific)
            try {
                Database::execute("OPTIMIZE TABLE notification_queue");
                $results['indexes_optimized'] = true;
                $this->logInfo("Optimized notification_queue table");
            } catch (Exception $e) {
                $this->logError("Failed to optimize table: " . $e->getMessage());
            }
            
        } catch (Exception $e) {
            $this->logError("Queue optimization error: " . $e->getMessage());
        }

        return $results;
    }

    /**
     * Get queue health status
     * 
     * @return array Health status information
     */
    public function getHealthStatus(): array
    {
        $stats = $this->getQueueStats();
        
        $health = [
            'status' => 'healthy',
            'issues' => [],
            'recommendations' => []
        ];

        // Check for issues
        if (($stats['pending'] ?? 0) > 1000) {
            $health['status'] = 'warning';
            $health['issues'][] = 'High number of pending notifications';
            $health['recommendations'][] = 'Consider increasing batch processing frequency';
        }

        if (($stats['pending_overdue'] ?? 0) > 100) {
            $health['status'] = 'critical';
            $health['issues'][] = 'Many overdue notifications';
            $health['recommendations'][] = 'Check system resources and email configuration';
        }

        if (($stats['failed_today'] ?? 0) > 50) {
            $health['status'] = 'warning';
            $health['issues'][] = 'High failure rate today';
            $health['recommendations'][] = 'Review error logs and email settings';
        }

        if (($stats['processing'] ?? 0) > 10) {
            $health['status'] = 'warning';
            $health['issues'][] = 'Many notifications stuck in processing';
            $health['recommendations'][] = 'Run cleanup to reset stuck notifications';
        }

        return $health;
    }

    /**
     * Log info message
     */
    private function logInfo(string $message): void
    {
        $this->writeLog('INFO', $message);
    }

    /**
     * Log error message
     */
    private function logError(string $message): void
    {
        $this->writeLog('ERROR', $message);
        error_log("NotificationQueueManager Error: {$message}");
    }

    /**
     * Write log message to file
     */
    private function writeLog(string $level, string $message): void
    {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}
