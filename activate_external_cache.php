<?php
require_once 'src/Helpers/PerformanceHelper.php';
use App\Helpers\PerformanceHelper;

echo "=== External Resource Caching Activation ===\n";

// Activează cache headers pentru resurse externe
echo "\n--- Setting Cache Headers ---\n";
$resourceHints = PerformanceHelper::setExternalResourceCacheHeaders();
echo "✓ Cache headers set for external resources (24 hours)\n";
echo "✓ CORS headers configured\n";
echo "✓ Resource hints generated:\n";
echo $resourceHints . "\n";

// Testează cache pentru CDN-uri
echo "\n--- CDN Cache Test ---\n";
$cdnResources = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
    'https://code.jquery.com/jquery-3.6.0.min.js'
];

foreach ($cdnResources as $resource) {
    echo "Testing: " . basename($resource) . "\n";
    
    // Simulează verificarea cache
    $headers = get_headers($resource, 1);
    if ($headers) {
        echo "  ✓ Resource accessible\n";
        if (isset($headers['Cache-Control'])) {
            echo "  ✓ Cache-Control: " . $headers['Cache-Control'] . "\n";
        }
        if (isset($headers['ETag'])) {
            echo "  ✓ ETag present\n";
        }
    } else {
        echo "  ✗ Resource not accessible\n";
    }
    echo "\n";
}

// Generează .htaccess pentru cache local
echo "\n--- Generating .htaccess Cache Rules ---\n";
$htaccessRules = PerformanceHelper::generateCacheRules();
echo "Cache rules generated:\n";
echo $htaccessRules . "\n";

// Verifică dacă .htaccess există și actualizează-l
$htaccessPath = '.htaccess';
if (file_exists($htaccessPath)) {
    $currentContent = file_get_contents($htaccessPath);
    
    // Verifică dacă regulile de cache sunt deja prezente
    if (strpos($currentContent, '# Performance Optimization') === false) {
        echo "Adding cache rules to .htaccess...\n";
        $newContent = $currentContent . "\n\n" . $htaccessRules;
        file_put_contents($htaccessPath, $newContent);
        echo "✓ .htaccess updated with cache rules\n";
    } else {
        echo "✓ Cache rules already present in .htaccess\n";
    }
} else {
    echo "Creating .htaccess with cache rules...\n";
    file_put_contents($htaccessPath, $htaccessRules);
    echo "✓ .htaccess created with cache rules\n";
}

// Testează performanța
echo "\n--- Performance Test ---\n";
$startTime = microtime(true);

// Simulează încărcarea resurselor
$resources = [
    'assets/css/style.min.css',
    'assets/css/responsive.min.css',
    'assets/css/footer.min.css',
    'assets/js/script.min.js',
    'images/logo.webp'
];

$totalSize = 0;
foreach ($resources as $resource) {
    if (file_exists($resource)) {
        $size = filesize($resource);
        $totalSize += $size;
        echo "✓ $resource (" . PerformanceHelper::formatFileSize($size) . ")\n";
    }
}

$endTime = microtime(true);
$loadTime = round(($endTime - $startTime) * 1000, 2);

echo "\nPerformance Summary:\n";
echo "Total local resources size: " . PerformanceHelper::formatFileSize($totalSize) . "\n";
echo "Load time simulation: {$loadTime}ms\n";

// Calculează economiile de bandwidth
$originalSizes = [
    'style.css' => file_exists('assets/css/style.css') ? filesize('assets/css/style.css') : 0,
    'responsive.css' => file_exists('assets/css/responsive.css') ? filesize('assets/css/responsive.css') : 0,
    'footer.css' => file_exists('assets/css/footer.css') ? filesize('assets/css/footer.css') : 0,
    'script.js' => file_exists('assets/js/script.js') ? filesize('assets/js/script.js') : 0,
    'logo.jpg' => file_exists('images/logo.jpg') ? filesize('images/logo.jpg') : 0
];

$totalOriginal = array_sum($originalSizes);
$savings = $totalOriginal - $totalSize;
$savingsPercent = $totalOriginal > 0 ? round(($savings / $totalOriginal) * 100, 2) : 0;

echo "Original size: " . PerformanceHelper::formatFileSize($totalOriginal) . "\n";
echo "Optimized size: " . PerformanceHelper::formatFileSize($totalSize) . "\n";
echo "Total savings: " . PerformanceHelper::formatFileSize($savings) . " ({$savingsPercent}%)\n";

echo "\n=== External Resource Caching Activated Successfully! ===\n";
?>
