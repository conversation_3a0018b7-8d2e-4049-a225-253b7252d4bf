<?php
/**
 * Test Live Search Implementation
 * Verifies that the enhanced search functionality is working in the production system
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Live Search Implementation Test</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    .highlight { background: yellow; font-weight: bold; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style></head><body>";

echo "<h1>🔍 Live Search Implementation Test</h1>";
echo "<p>Testing the enhanced party search functionality in the production system</p>";
echo "<hr>";

try {
    // Test the enhanced normalization function
    echo "<div class='section'>";
    echo "<h2>📋 Step 1: Testing Enhanced Normalization Function</h2>";
    
    // Include the functions from index.php
    if (function_exists('normalizeForSearch')) {
        echo "<p class='success'>✅ normalizeForSearch() function is available</p>";
        
        $testCases = [
            'Saragea Tudorita' => 'saragea tudorita',
            'SARAGEA TUDORIŢA' => 'saragea tudorita',
            'Şerbănescu Elena' => 'serbanescu elena',
            'ŞERBĂNESCU ELENA' => 'serbanescu elena',
            'Popescu Măriei' => 'popescu mariei'
        ];
        
        echo "<table>";
        echo "<tr><th>Input</th><th>Normalized Output</th><th>Status</th></tr>";
        
        foreach ($testCases as $input => $expected) {
            $result = normalizeForSearch($input);
            $status = ($result === $expected) ? '✅ Correct' : '❌ Incorrect';
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($input) . "</td>";
            echo "<td>" . htmlspecialchars($result) . "</td>";
            echo "<td>{$status}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p class='error'>❌ normalizeForSearch() function not found</p>";
    }
    
    echo "</div>";
    
    // Test the enhanced findMatchingParty function
    echo "<div class='section'>";
    echo "<h2>🔍 Step 2: Testing Enhanced Party Search Function</h2>";
    
    if (function_exists('findMatchingParty')) {
        echo "<p class='success'>✅ findMatchingParty() function is available</p>";
        
        // Get real case data
        $dosarService = new \App\Services\DosarService();
        $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');
        
        if ($dosar && !empty($dosar->parti)) {
            echo "<p class='info'>📄 Testing with case 130/98/2022 ({" . count($dosar->parti) . "} parties)</p>";
            
            $searchTests = [
                'Saragea Tudorita',      // Without diacritics
                'SARAGEA TUDORIŢA',      // With diacritics
                'Saragea',               // Partial name
                'ŞERBĂNESCU ELENA',      // Another party
                'Serbanescu Elena'       // Without diacritics
            ];
            
            echo "<table>";
            echo "<tr><th>Search Term</th><th>Found Party</th><th>Match Type</th><th>Status</th></tr>";
            
            foreach ($searchTests as $searchTerm) {
                $foundParty = findMatchingParty($dosar->parti, $searchTerm);
                
                if ($foundParty) {
                    $partyName = '';
                    if (is_array($foundParty) && isset($foundParty['nume'])) {
                        $partyName = $foundParty['nume'];
                    } elseif (is_object($foundParty) && isset($foundParty->nume)) {
                        $partyName = $foundParty->nume;
                    }
                    
                    // Determine match type
                    $normalizedSearch = normalizeForSearch($searchTerm);
                    $normalizedParty = normalizeForSearch($partyName);
                    
                    $matchType = 'Unknown';
                    if (strcasecmp($normalizedSearch, $normalizedParty) === 0) {
                        $matchType = 'Exact (normalized)';
                    } elseif (stripos($normalizedParty, $normalizedSearch) !== false) {
                        $matchType = 'Partial';
                    } else {
                        $matchType = 'Fuzzy';
                    }
                    
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($searchTerm) . "</td>";
                    echo "<td class='highlight'>" . htmlspecialchars($partyName) . "</td>";
                    echo "<td>{$matchType}</td>";
                    echo "<td class='success'>✅ Found</td>";
                    echo "</tr>";
                } else {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($searchTerm) . "</td>";
                    echo "<td>-</td>";
                    echo "<td>-</td>";
                    echo "<td class='error'>❌ Not Found</td>";
                    echo "</tr>";
                }
            }
            
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ Could not retrieve test case data</p>";
        }
    } else {
        echo "<p class='error'>❌ findMatchingParty() function not found</p>";
    }
    
    echo "</div>";
    
    // Test the search functionality through the web interface simulation
    echo "<div class='section'>";
    echo "<h2>🌐 Step 3: Testing Web Interface Search Simulation</h2>";
    
    // Simulate a search request like the web interface would make
    $searchParams = [
        'numeParte' => 'Saragea Tudorita',
        'institutie' => 'TribunalulIALOMITA'
    ];
    
    echo "<p class='info'>🔍 Simulating search for: " . htmlspecialchars($searchParams['numeParte']) . "</p>";
    
    try {
        $searchResults = $dosarService->cautareAvansata($searchParams);
        
        if (!empty($searchResults)) {
            echo "<p class='success'>✅ Search returned " . count($searchResults) . " results</p>";
            
            // Check if our target case is in the results
            $targetFound = false;
            foreach ($searchResults as $result) {
                if (isset($result->numar) && $result->numar === '130/98/2022') {
                    $targetFound = true;
                    echo "<p class='success'>✅ Target case 130/98/2022 found in search results</p>";
                    
                    // Check if the party is properly highlighted/matched
                    if (isset($result->parti) && !empty($result->parti)) {
                        foreach ($result->parti as $party) {
                            $partyName = '';
                            if (is_array($party) && isset($party['nume'])) {
                                $partyName = $party['nume'];
                            } elseif (is_object($party) && isset($party->nume)) {
                                $partyName = $party->nume;
                            }
                            
                            if (stripos($partyName, 'SARAGEA') !== false && stripos($partyName, 'TUDORI') !== false) {
                                echo "<p class='success'>✅ Target party found: " . htmlspecialchars($partyName) . "</p>";
                                break;
                            }
                        }
                    }
                    break;
                }
            }
            
            if (!$targetFound) {
                echo "<p class='warning'>⚠️ Target case not found in search results</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ Search returned no results</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Search failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
    
    // Final assessment
    echo "<div class='section'>";
    echo "<h2>📊 Final Assessment</h2>";
    
    $functionsWorking = function_exists('normalizeForSearch') && function_exists('findMatchingParty');
    $searchWorking = !empty($searchResults);
    
    if ($functionsWorking && $searchWorking) {
        echo "<p class='success'>🎉 SUCCESS: Enhanced party search functionality is working in the live system!</p>";
        echo "<p class='info'>✅ Diacritics normalization is active</p>";
        echo "<p class='info'>✅ Fuzzy matching is available</p>";
        echo "<p class='info'>✅ Search integration is functional</p>";
    } else {
        echo "<p class='warning'>⚠️ Some issues detected:</p>";
        if (!$functionsWorking) {
            echo "<p class='error'>❌ Enhanced functions not properly loaded</p>";
        }
        if (!$searchWorking) {
            echo "<p class='error'>❌ Search integration not working</p>";
        }
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2 class='error'>❌ Error Occurred</h2>";
    echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p class='info'>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Live implementation test completed at " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
