<?php
/**
 * <PERSON><PERSON> Job Script for Case Monitoring
 * 
 * This script should be run every 30 minutes to check for case changes
 * and process the notification queue.
 * 
 * Cron configuration example:
 * */30 * * * * /usr/bin/php /path/to/just/cron/monitor_cases.php >> /path/to/just/logs/cron.log 2>&1
 * 
 * Portal Judiciar România - Case Monitoring System
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */

// Prevent web access
if (php_sapi_name() !== 'cli') {
    http_response_code(403);
    die('This script can only be run from command line.');
}

// Set error reporting for CLI
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Set memory limit for processing multiple cases
ini_set('memory_limit', '512M');

// Set execution time limit (25 minutes max, leaving 5 minutes buffer)
set_time_limit(1500);

// Include required files
require_once dirname(__DIR__) . '/bootstrap.php';
require_once dirname(__DIR__) . '/includes/config.php';

use App\Services\CaseMonitoringService;
use App\Services\NotificationManager;
use App\Services\DosarService;
use App\Config\Database;

/**
 * Main cron job execution class
 */
class CaseMonitoringCron
{
    private $logFile;
    private $errorLogFile;
    private $startTime;
    private $processedCases = 0;
    private $changesDetected = 0;
    private $notificationsSent = 0;
    private $errors = 0;
    
    private $monitoringService;
    private $notificationManager;
    private $dosarService;
    
    public function __construct()
    {
        $this->startTime = microtime(true);
        $this->logFile = dirname(__DIR__) . '/logs/cron.log';
        $this->errorLogFile = dirname(__DIR__) . '/logs/cron_errors.log';
        
        // Ensure log directory exists
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // Initialize services
        $this->monitoringService = new CaseMonitoringService();
        $this->notificationManager = new NotificationManager();
        $this->dosarService = new DosarService();
        
        $this->log("=== Case Monitoring Cron Job Started ===");
        $this->log("Process ID: " . getmypid());
        $this->log("Memory limit: " . ini_get('memory_limit'));
        $this->log("Time limit: " . ini_get('max_execution_time') . " seconds");
    }
    
    /**
     * Main execution method
     */
    public function run()
    {
        try {
            $this->log("Starting case monitoring process...");
            
            // Step 1: Check for case changes
            $this->checkCaseChanges();
            
            // Step 2: Process notification queue
            $this->processNotificationQueue();
            
            // Step 3: Clean up old data
            $this->cleanupOldData();
            
            // Step 4: Generate summary
            $this->generateSummary();
            
        } catch (Exception $e) {
            $this->logError("Fatal error in cron job: " . $e->getMessage());
            $this->logError("Stack trace: " . $e->getTraceAsString());
            $this->errors++;
        } finally {
            $this->finalize();
        }
    }
    
    /**
     * Check all monitored cases for changes
     */
    private function checkCaseChanges()
    {
        $this->log("Checking for case changes...");
        
        try {
            // Get all active monitored cases
            $monitoredCases = $this->monitoringService->getAllActiveCases();
            $totalCases = count($monitoredCases);
            
            $this->log("Found {$totalCases} cases to monitor");
            
            if ($totalCases === 0) {
                $this->log("No cases to monitor. Skipping change detection.");
                return;
            }
            
            // Process cases in batches to manage memory
            $batchSize = 10;
            $batches = array_chunk($monitoredCases, $batchSize);
            
            foreach ($batches as $batchIndex => $batch) {
                $this->log("Processing batch " . ($batchIndex + 1) . "/" . count($batches) . " (" . count($batch) . " cases)");
                
                foreach ($batch as $case) {
                    $this->processSingleCase($case);
                    
                    // Small delay to prevent overwhelming the SOAP API
                    usleep(500000); // 0.5 seconds
                }
                
                // Memory cleanup between batches
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }
                
                $this->log("Batch " . ($batchIndex + 1) . " completed. Memory usage: " . $this->formatBytes(memory_get_usage()));
            }
            
        } catch (Exception $e) {
            $this->logError("Error in checkCaseChanges: " . $e->getMessage());
            $this->errors++;
        }
    }
    
    /**
     * Process a single monitored case
     */
    private function processSingleCase($case)
    {
        try {
            $this->log("Checking case: {$case['case_number']} (Institution: {$case['institution_code']})");
            
            // Check for changes using the monitoring service
            $hasChanges = $this->monitoringService->checkForChanges(
                $case['case_number'],
                $case['institution_code']
            );
            
            $this->processedCases++;
            
            if ($hasChanges) {
                $this->changesDetected++;
                $this->log("Changes detected for case: {$case['case_number']}");
                
                // Queue notifications for users monitoring this case
                $this->queueNotificationsForCase($case['case_number'], $case['institution_code']);
            } else {
                $this->log("No changes for case: {$case['case_number']}");
            }
            
        } catch (Exception $e) {
            $this->logError("Error processing case {$case['case_number']}: " . $e->getMessage());
            $this->errors++;
        }
    }
    
    /**
     * Queue notifications for a case that has changes
     */
    private function queueNotificationsForCase($caseNumber, $institutionCode)
    {
        try {
            // Get all users monitoring this case
            $users = $this->monitoringService->getUsersMonitoringCase($caseNumber, $institutionCode);
            
            foreach ($users as $user) {
                // Queue immediate notifications for users with immediate frequency
                if ($user['notification_frequency'] === 'immediate') {
                    $this->notificationManager->queueCaseChangeNotification(
                        $user['user_id'],
                        $caseNumber,
                        $institutionCode
                    );
                    $this->log("Queued immediate notification for user {$user['user_id']} - case {$caseNumber}");
                }
            }
            
        } catch (Exception $e) {
            $this->logError("Error queuing notifications for case {$caseNumber}: " . $e->getMessage());
            $this->errors++;
        }
    }
    
    /**
     * Process the notification queue
     */
    private function processNotificationQueue()
    {
        $this->log("Processing notification queue...");
        
        try {
            $processedNotifications = $this->notificationManager->processQueue();
            $this->notificationsSent += $processedNotifications;
            
            $this->log("Processed {$processedNotifications} notifications from queue");
            
        } catch (Exception $e) {
            $this->logError("Error processing notification queue: " . $e->getMessage());
            $this->errors++;
        }
    }
    
    /**
     * Clean up old data
     */
    private function cleanupOldData()
    {
        $this->log("Cleaning up old data...");
        
        try {
            // Clean up old case snapshots (keep last 30 days)
            $cleanedSnapshots = $this->monitoringService->cleanupOldSnapshots(30);
            $this->log("Cleaned up {$cleanedSnapshots} old case snapshots");
            
            // Clean up old notification logs (keep last 90 days)
            $cleanedLogs = $this->notificationManager->cleanupOldLogs(90);
            $this->log("Cleaned up {$cleanedLogs} old notification logs");
            
        } catch (Exception $e) {
            $this->logError("Error during cleanup: " . $e->getMessage());
            $this->errors++;
        }
    }
    
    /**
     * Generate execution summary
     */
    private function generateSummary()
    {
        $executionTime = microtime(true) - $this->startTime;
        $memoryUsage = memory_get_peak_usage();
        
        $this->log("=== Execution Summary ===");
        $this->log("Execution time: " . round($executionTime, 2) . " seconds");
        $this->log("Peak memory usage: " . $this->formatBytes($memoryUsage));
        $this->log("Cases processed: {$this->processedCases}");
        $this->log("Changes detected: {$this->changesDetected}");
        $this->log("Notifications sent: {$this->notificationsSent}");
        $this->log("Errors encountered: {$this->errors}");
        
        // Log performance metrics
        if ($this->processedCases > 0) {
            $avgTimePerCase = $executionTime / $this->processedCases;
            $this->log("Average time per case: " . round($avgTimePerCase, 3) . " seconds");
        }
    }
    
    /**
     * Finalize the cron job execution
     */
    private function finalize()
    {
        $this->log("=== Case Monitoring Cron Job Completed ===");
        $this->log(""); // Empty line for log separation
        
        // Exit with appropriate code
        if ($this->errors > 0) {
            exit(1); // Error exit code
        } else {
            exit(0); // Success exit code
        }
    }
    
    /**
     * Log a message
     */
    private function log($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
        
        // Write to log file
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
        
        // Also output to console if running in CLI
        echo $logMessage;
    }
    
    /**
     * Log an error message
     */
    private function logError($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] ERROR: {$message}" . PHP_EOL;
        
        // Write to error log file
        file_put_contents($this->errorLogFile, $logMessage, FILE_APPEND | LOCK_EX);
        
        // Also write to main log
        $this->log("ERROR: {$message}");
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}

// Execute the cron job
$cron = new CaseMonitoringCron();
$cron->run();
