<?php
/**
 * Compare the Bucharest case with the working Ialomita case
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<h1>🔄 Case Comparison Test</h1>\n";

try {
    $dosarService = new DosarService();
    
    $cases = [
        'Bucharest (Problem Case)' => ['130/98/2022', 'CurteadeApelBUCURESTI'],
        'Ialomita (Working Case)' => ['130/98/2022', 'TribunalulIALOMITA']
    ];
    
    foreach ($cases as $caseName => $caseData) {
        list($numarDosar, $institutie) = $caseData;
        
        echo "<h2>📊 {$caseName}</h2>\n";
        echo "<p><strong>Case:</strong> {$numarDosar} from {$institutie}</p>\n";
        
        error_log("=== TESTING {$caseName} ===");
        
        $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
        
        if ($dosar && !empty($dosar->numar)) {
            $totalParties = count($dosar->parti ?? []);
            echo "<p><strong>Total Parties:</strong> <span style='font-size: 1.2em; color: " . ($totalParties >= 500 ? 'green' : 'red') . ";'>{$totalParties}</span></p>\n";
            
            // Count by source
            $soapCount = 0;
            $decisionCount = 0;
            $unknownCount = 0;
            
            foreach ($dosar->parti as $parte) {
                switch ($parte->source ?? 'unknown') {
                    case 'soap_api': $soapCount++; break;
                    case 'decision_text': $decisionCount++; break;
                    default: $unknownCount++; break;
                }
            }
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
            echo "<tr><th>Source</th><th>Count</th><th>Percentage</th></tr>\n";
            echo "<tr><td>SOAP API</td><td>{$soapCount}</td><td>" . round(($soapCount / $totalParties) * 100, 1) . "%</td></tr>\n";
            echo "<tr><td>Decision Text</td><td>{$decisionCount}</td><td>" . round(($decisionCount / $totalParties) * 100, 1) . "%</td></tr>\n";
            echo "<tr><td>Unknown</td><td>{$unknownCount}</td><td>" . round(($unknownCount / $totalParties) * 100, 1) . "%</td></tr>\n";
            echo "<tr><td><strong>Total</strong></td><td><strong>{$totalParties}</strong></td><td><strong>100%</strong></td></tr>\n";
            echo "</table>\n";
            
            // Status assessment
            if ($totalParties >= 500) {
                echo "<p style='color: green;'><strong>✅ SUCCESS: Case meets 500+ party requirement</strong></p>\n";
            } else {
                echo "<p style='color: red;'><strong>❌ ISSUE: Case has only {$totalParties} parties (need 500+)</strong></p>\n";
            }
            
            if ($soapCount >= 100 && $decisionCount > 0) {
                echo "<p style='color: green;'><strong>✅ Hybrid extraction working: SOAP limit reached, decision text parties found</strong></p>\n";
            } elseif ($soapCount >= 100 && $decisionCount == 0) {
                echo "<p style='color: orange;'><strong>⚠️ Potential issue: SOAP limit reached but no decision text parties</strong></p>\n";
            } else {
                echo "<p style='color: blue;'><strong>ℹ️ Normal case: SOAP under limit ({$soapCount} parties)</strong></p>\n";
            }
            
            // Show sample parties
            echo "<h4>Sample Parties (first 5):</h4>\n";
            echo "<ul>\n";
            for ($i = 0; $i < min(5, count($dosar->parti)); $i++) {
                $parte = $dosar->parti[$i];
                echo "<li>" . htmlspecialchars($parte->nume) . " (" . $parte->calitate . ") [" . ($parte->source ?? 'unknown') . "]</li>\n";
            }
            echo "</ul>\n";
            
            // Check for specific names
            $saragea_found = false;
            foreach ($dosar->parti as $parte) {
                if (stripos($parte->nume, 'SARAGEA') !== false && stripos($parte->nume, 'TUDORITA') !== false) {
                    $saragea_found = true;
                    echo "<p style='color: green;'><strong>🎯 FOUND: 'Saragea Tudorita' - " . htmlspecialchars($parte->nume) . " [" . ($parte->source ?? 'unknown') . "]</strong></p>\n";
                    break;
                }
            }
            
            if (!$saragea_found) {
                echo "<p style='color: red;'><strong>❌ 'Saragea Tudorita' not found in this case</strong></p>\n";
            }
            
        } else {
            echo "<p style='color: red;'><strong>❌ Case not found</strong></p>\n";
        }
        
        echo "<hr>\n";
    }
    
    echo "<h2>📋 Comparison Summary</h2>\n";
    echo "<p>This comparison helps identify:</p>\n";
    echo "<ul>\n";
    echo "<li>Whether the issue is specific to the Bucharest case or systemic</li>\n";
    echo "<li>If the Ialomita case actually has 500+ parties as expected</li>\n";
    echo "<li>Whether hybrid extraction is working differently between institutions</li>\n";
    echo "<li>If 'Saragea Tudorita' exists in either case</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>
