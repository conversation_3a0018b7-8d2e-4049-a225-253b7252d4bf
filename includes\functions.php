<?php
/**
 * Funcții utilitare pentru aplicație
 */

/**
 * Formatează o dată în formatul specificat
 *
 * @param string $date Data în format string
 * @param string $format Formatul dorit (implicit DATE_FORMAT)
 * @return string Data formatată
 */
function formatDate($date, $format = DATE_FORMAT) {
    if (empty($date)) {
        return '';
    }

    $dateObj = new DateTime($date);
    return $dateObj->format($format);
}

/**
 * Curăță și validează datele de intrare
 *
 * @param string $data Datele de intrare
 * @return string Datele curățate
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Generează un URL pentru paginare
 *
 * @param array $params Parametrii pentru URL
 * @param int $page Numărul paginii
 * @return string URL-ul generat
 */
function getPaginationUrl($params, $page) {
    $params['page'] = $page;
    return '?' . http_build_query($params);
}

/**
 * Generează HTML pentru paginare
 *
 * @param int $currentPage Pagina curentă
 * @param int $totalPages Numărul total de pagini
 * @param array $params Parametrii pentru URL
 * @return string HTML-ul pentru paginare
 */
function generatePagination($currentPage, $totalPages, $params) {
    if ($totalPages <= 1) {
        return '';
    }

    $html = '<div class="pagination">';

    // Buton pentru pagina anterioară
    if ($currentPage > 1) {
        $html .= '<a href="' . getPaginationUrl($params, $currentPage - 1) . '" class="page-link">&laquo; Anterior</a>';
    } else {
        $html .= '<span class="page-link disabled">&laquo; Anterior</span>';
    }

    // Paginile - afișăm maxim 9 pagini în jurul paginii curente pentru navigare îmbunătățită
    $startPage = max(1, $currentPage - 4);
    $endPage = min($totalPages, $currentPage + 4);

    if ($startPage > 1) {
        $html .= '<a href="' . getPaginationUrl($params, 1) . '" class="page-link">1</a>';
        if ($startPage > 2) {
            $html .= '<span class="page-link dots">...</span>';
        }
    }

    for ($i = $startPage; $i <= $endPage; $i++) {
        if ($i == $currentPage) {
            $html .= '<span class="page-link active">' . $i . '</span>';
        } else {
            $html .= '<a href="' . getPaginationUrl($params, $i) . '" class="page-link">' . $i . '</a>';
        }
    }

    if ($endPage < $totalPages) {
        if ($endPage < $totalPages - 1) {
            $html .= '<span class="page-link dots">...</span>';
        }
        $html .= '<a href="' . getPaginationUrl($params, $totalPages) . '" class="page-link">' . $totalPages . '</a>';
    }

    // Buton pentru pagina următoare
    if ($currentPage < $totalPages) {
        $html .= '<a href="' . getPaginationUrl($params, $currentPage + 1) . '" class="page-link">Următor &raquo;</a>';
    } else {
        $html .= '<span class="page-link disabled">Următor &raquo;</span>';
    }

    $html .= '</div>';

    return $html;
}

/**
 * Obține lista completă a instanțelor judecătorești din România
 * Lista este organizată alfabetic și include toate instanțele majore
 *
 * @return array Lista instanțelor
 */
function getInstanteList() {
    return [
        // Înalta Curte de Casație și Justiție
        'InaltaCurtedeCASSATIESIJUSTITIE' => 'Înalta Curte de Casație și Justiție',

        // Curți de Apel - ordine alfabetică
        'CurteadeApelALBAIULIA' => 'Curtea de Apel ALBA IULIA',
        'CurteadeApelBACU' => 'Curtea de Apel BACĂU',
        'CurteadeApelBRASSOV' => 'Curtea de Apel BRAŞOV',
        'CurteadeApelBUCURESTI' => 'Curtea de Apel BUCUREŞTI',
        'CurteadeApelCLUJ' => 'Curtea de Apel CLUJ',
        'CurteadeApelCONSTANTA' => 'Curtea de Apel CONSTANŢA',
        'CurteadeApelCRAIOVA' => 'Curtea de Apel CRAIOVA',
        'CurteadeApelGALATI' => 'Curtea de Apel GALAŢI',
        'CurteadeApelIASI' => 'Curtea de Apel IAŞI',
        'CurteadeApelORADEA' => 'Curtea de Apel ORADEA',
        'CurteadeApelPITESTI' => 'Curtea de Apel PITEŞTI',
        'CurteadeApelPLOIESTI' => 'Curtea de Apel PLOIEŞTI',
        'CurteadeApelSUCEAVA' => 'Curtea de Apel SUCEAVA',
        'CurteadeApelTARGUMURES' => 'Curtea de Apel TÂRGU MUREŞ',
        'CurteadeApelTIMISOARA' => 'Curtea de Apel TIMIŞOARA',
        'CurteaMilitaradeApelBUCURESTI' => 'Curtea Militara de Apel BUCUREŞTI',

        // Judecătorii - ordine alfabetică
        'JudecatoriaADJUD' => 'Judecătoria ADJUD',
        'JudecatoriaAGNITA' => 'Judecătoria AGNITA',
        'JudecatoriaAIUD' => 'Judecătoria AIUD',
        'JudecatoriaALBAIULIA' => 'Judecătoria ALBA IULIA',
        'JudecatoriaALESD' => 'Judecătoria ALESD',
        'JudecatoriaALEXANDRIA' => 'Judecătoria ALEXANDRIA',
        'JudecatoriaARAD' => 'Judecătoria ARAD',
        'JudecatoriaAVRIG' => 'Judecătoria AVRIG',
        'JudecatoriaBABADAG' => 'Judecătoria BABADAG',
        'JudecatoriaBACU' => 'Judecătoria BACĂU',
        'JudecatoriaBAIADEARAMA' => 'Judecătoria BAIA DE ARAMĂ',
        'JudecatoriaBAIAMARE' => 'Judecătoria BAIA MARE',
        'JudecatoriaBAILESTI' => 'Judecătoria BĂILEŞTI',
        'JudecatoriaBALCESTI' => 'Judecătoria BĂLCEŞTI',
        'JudecatoriaBALS' => 'Judecătoria BALŞ',
        'JudecatoriaBARLAD' => 'Judecătoria BÂRLAD',
        'JudecatoriaBECLEAN' => 'Judecătoria BECLEAN',
        'JudecatoriaBEIUS' => 'Judecătoria BEIUŞ',
        'JudecatoriaBICAZ' => 'Judecătoria BICAZ',
        'JudecatoriaBISTRITA' => 'Judecătoria BISTRIŢA',
        'JudecatoriaBLAJ' => 'Judecătoria BLAJ',
        'JudecatoriaBOLINTINVALE' => 'Judecătoria BOLINTIN VALE',
        'JudecatoriaBOTOSANI' => 'Judecătoria BOTOŞANI',
        'JudecatoriaBRAD' => 'Judecătoria BRAD',
        'JudecatoriaBRAILA' => 'Judecătoria BRĂILA',
        'JudecatoriaBRASSOV' => 'Judecătoria BRAŞOV',
        'JudecatoriaBREZOI' => 'Judecătoria BREZOI',
        'JudecatoriaBUFTEA' => 'Judecătoria BUFTEA',
        'JudecatoriaBUHUSI' => 'Judecătoria BUHUŞI',
        'JudecatoriaBUZAU' => 'Judecătoria BUZĂU',
        'JudecatoriaCALAFAT' => 'Judecătoria CALAFAT',
        'JudecatoriaCALARASI' => 'Judecătoria CĂLĂRAŞI',
        'JudecatoriaCAMPENI' => 'Judecătoria CÂMPENI',
        'JudecatoriaCAMPINA' => 'Judecătoria CÂMPINA',
        'JudecatoriaCAMPULUNG' => 'Judecătoria CĂMPULUNG',
        'JudecatoriaCAMPULUNGMOLDOVENESC' => 'Judecătoria CÂMPULUNG MOLDOVENESC',
        'JudecatoriaCARACAL' => 'Judecătoria CARACAL',
        'JudecatoriaCARANSEBES' => 'Judecătoria CARANSEBEŞ',
        'JudecatoriaCAREI' => 'Judecătoria CAREI',
        'JudecatoriaCHISINEUCRIS' => 'Judecătoria CHIŞINEU CRIŞ',
        'JudecatoriaCLUJNAPOCA' => 'Judecătoria CLUJ-NAPOCA',
        'JudecatoriaCONSTANTA' => 'Judecătoria CONSTANŢA',
        'JudecatoriaCORABIA' => 'Judecătoria CORABIA',
        'JudecatoriaCORNETU' => 'Judecătoria CORNETU',
        'JudecatoriaCOSTESTI' => 'Judecătoria COSTEŞTI',
        'JudecatoriaCRAIOVA' => 'Judecătoria CRAIOVA',
        'JudecatoriaCURTEADEARGES' => 'Judecătoria CURTEA DE ARGEŞ',
        'JudecatoriaDARABANI' => 'Judecătoria Darabani',
        'JudecatoriaDEJ' => 'Judecătoria DEJ',
        'JudecatoriaDETA' => 'Judecătoria DETA',
        'JudecatoriaDEVA' => 'Judecătoria DEVA',
        'JudecatoriaDOROHOI' => 'Judecătoria DOROHOI',
        'JudecatoriaDRAGASANI' => 'Judecătoria DRĂGĂŞANI',
        'JudecatoriaDRAGOMIRESTI' => 'Judecătoria DRAGOMIREŞTI',
        'JudecatoriaDROBETATURNUSEVERIN' => 'Judecătoria DROBETA-TURNU SEVERIN',
        'JudecatoriaFAGARAS' => 'Judecătoria FĂGĂRAŞ',
        'JudecatoriaFAGET' => 'Judecătoria FĂGET',
        'JudecatoriaFALTICENI' => 'Judecătoria FĂLTICENI',
        'JudecatoriaFAUREI' => 'Judecătoria FĂUREI',
        'JudecatoriaFETESTI' => 'Judecătoria FETEŞTI',
        'JudecatoriaFILIASI' => 'Judecătoria FILIAŞI',
        'JudecatoriaFOCSANI' => 'Judecătoria FOCŞANI',
        'JudecatoriaGAESTI' => 'Judecătoria GĂEŞTI',
        'JudecatoriaGALATI' => 'Judecătoria GALAŢI',
        'JudecatoriaGHEORGHENI' => 'Judecătoria GHEORGHENI',
        'JudecatoriaGHERLA' => 'Judecătoria GHERLA',
        'JudecatoriaGIURGIU' => 'Judecătoria GIURGIU',
        'JudecatoriaGURAHONT' => 'Judecătoria GURA HONŢ',
        'JudecatoriaGURAHUMORULUI' => 'Judecătoria GURA HUMORULUI',
        'JudecatoriaHARLAU' => 'Judecătoria HÂRLĂU',
        'JudecatoriaHARSOVA' => 'Judecătoria HÂRŞOVA',
        'JudecatoriaHATEG' => 'Judecătoria HAŢEG',
        'JudecatoriaHOREZU' => 'Judecătoria HOREZU',
        'JudecatoriaHUEDIN' => 'Judecătoria HUEDIN',
        'JudecatoriaHUNEDOARA' => 'Judecătoria HUNEDOARA',
        'JudecatoriaHUSI' => 'Judecătoria HUŞI',
        'JudecatoriaIASI' => 'Judecătoria IAŞI',
        'JudecatoriaINEU' => 'Judecătoria INEU',
        'JudecatoriaINSURATEI' => 'Judecătoria ÎNSURĂŢEI',
        'JudecatoriaINTORSURABUZAULUI' => 'Judecătoria ÎNTORSURA BUZĂULUI',
        'JudecatoriaJIBOU' => 'Judecătoria JIBOU',
        'JudecatoriaLEHLIUGARA' => 'Judecătoria LEHLIU-GARA',
        'JudecatoriaLIESTI' => 'Judecătoria LIEŞTI',
        'JudecatoriaLIPOVA' => 'Judecătoria LIPOVA',
        'JudecatoriaLUDUS' => 'Judecătoria LUDUŞ',
        'JudecatoriaLUGOJ' => 'Judecătoria LUGOJ',
        'JudecatoriaMACIN' => 'Judecătoria MACIN',
        'JudecatoriaMANGALIA' => 'Judecătoria MANGALIA',
        'JudecatoriaMARGHITA' => 'Judecătoria MARGHITA',
        'JudecatoriaMEDGIDIA' => 'Judecătoria MEDGIDIA',
        'JudecatoriaMEDIAS' => 'Judecătoria MEDIAŞ',
        'JudecatoriaMIERCUREACIUC' => 'Judecătoria MIERCUREA CIUC',
        'JudecatoriaMIZIL' => 'Judecătoria MIZIL',
        'JudecatoriaMOINESTI' => 'Judecătoria MOINEŞTI',
        'JudecatoriaMOLDOVANOUA' => 'Judecătoria MOLDOVA-NOUĂ',
        'JudecatoriaMORENI' => 'Judecătoria MORENI',
        'JudecatoriaMOTRU' => 'Judecătoria MOTRU',
        'JudecatoriaNASAUD' => 'Judecătoria NĂSĂUD',
        'JudecatoriaNEGRESTIOAS' => 'Judecătoria NEGREŞTI-OAŞ',
        'JudecatoriaNOVACI' => 'Judecătoria NOVACI',
        'JudecatoriaODORHEIULSECUIESC' => 'Judecătoria ODORHEIUL SECUIESC',
        'JudecatoriaOLTENITA' => 'Judecătoria OLTENIŢA',
        'JudecatoriaONESTI' => 'Judecătoria ONEŞTI',
        'JudecatoriaORADEA' => 'Judecătoria ORADEA',
        'JudecatoriaORASTIE' => 'Judecătoria ORAŞTIE',
        'JudecatoriaORAVITA' => 'Judecătoria ORAVIŢA',
        'JudecatoriaORSOVA' => 'Judecătoria ORŞOVA',
        'JudecatoriaPANCIU' => 'Judecătoria PANCIU',
        'JudecatoriaPASCANI' => 'Judecătoria PAŞCANI',
        'JudecatoriaPATARLAGELE' => 'Judecătoria PĂTÂRLAGELE',
        'JudecatoriaPETROSANI' => 'Judecătoria PETROŞANI',
        'JudecatoriaPIATRANEAMT' => 'Judecătoria PIATRA-NEAMT',
        'JudecatoriaPITESTI' => 'Judecătoria PITEŞTI',
        'JudecatoriaPLOIESTI' => 'Judecătoria PLOIEŞTI',
        'JudecatoriaPODUTURCULUI' => 'Judecătoria PODU TURCULUI',
        'JudecatoriaPOGOANELE' => 'Judecătoria POGOANELE',
        'JudecatoriaPUCIOASA' => 'Judecătoria PUCIOASA',
        'JudecatoriaRACari' => 'Judecătoria RĂCARI',
        'JudecatoriaRADAUTI' => 'Judecătoria RĂDĂUŢI',
        'JudecatoriaRADUCANENI' => 'Judecătoria RĂDUCĂNENI',
        'JudecatoriaRAMNICUSARAT' => 'Judecătoria RÂMNICU SARAT',
        'JudecatoriaRAMNICUVALCEA' => 'Judecătoria RÂMNICU VALCEA',
        'JudecatoriaREGHIN' => 'Judecătoria REGHIN',
        'JudecatoriaRESITA' => 'Judecătoria REŞITA',
        'JudecatoriaROMAN' => 'Judecătoria ROMAN',
        'JudecatoriaROSIORIDEVEDE' => 'Judecătoria ROŞIORI DE VEDE',
        'JudecatoriaRUPEA' => 'Judecătoria RUPEA',
        'JudecatoriaSALISTE' => 'Judecătoria SALIŞTE',
        'JudecatoriaSALONTA' => 'Judecătoria SALONTA',
        'JudecatoriaSANNICOLAULMARE' => 'Judecătoria SÂNNICOLAUL MARE',
        'JudecatoriaSATUMARE' => 'Judecătoria SATU MARE',
        'JudecatoriaSAVENI' => 'Judecătoria SĂVENI',
        'JudecatoriaSEBES' => 'Judecătoria SEBEŞ',
        'JudecatoriaSECTORUL1BUCURESTI' => 'Judecătoria SECTORUL 1 BUCUREŞTI',
        'JudecatoriaSECTORUL2BUCURESTI' => 'Judecătoria SECTORUL 2 BUCUREŞTI',
        'JudecatoriaSECTORUL3BUCURESTI' => 'Judecătoria SECTORUL 3 BUCUREŞTI',
        'JudecatoriaSECTORUL4BUCURESTI' => 'Judecătoria SECTORUL 4 BUCUREŞTI',
        'JudecatoriaSECTORUL5BUCURESTI' => 'Judecătoria SECTORUL 5 BUCUREŞTI',
        'JudecatoriaSECTORUL6BUCURESTI' => 'Judecătoria SECTORUL 6 BUCUREŞTI',
        'JudecatoriaSEGARCEA' => 'Judecătoria SEGARCEA',
        'JudecatoriaSFANTUGHEORGHE' => 'Judecătoria SFÂNTU GHEORGHE',
        'JudecatoriaSIBIU' => 'Judecătoria SIBIU',
        'JudecatoriaSIGHETUMARMATIEI' => 'Judecătoria SIGHETU MARMAŢIEI',
        'JudecatoriaSIGHISOARA' => 'Judecătoria SIGHIŞOARA',
        'JudecatoriaSIMLEULSILVANIEI' => 'Judecătoria ŞIMLEUL SILVANIEI',
        'JudecatoriaSINAIA' => 'Judecătoria SINAIA',
        'JudecatoriaSLATINA' => 'Judecătoria SLATINA',
        'JudecatoriaSLOBOZIA' => 'Judecătoria SLOBOZIA',
        'JudecatoriaSTREHAIA' => 'Judecătoria STREHAIA',
        'JudecatoriaSUCEAVA' => 'Judecătoria SUCEAVA',
        'JudecatoriaTARGOVISTE' => 'Judecătoria TÂRGOVIŞTE',
        'JudecatoriaTARGUBUJOR' => 'Judecătoria TÂRGU BUJOR',
        'JudecatoriaTARGUJIU' => 'Judecătoria TÂRGU JIU',
        'JudecatoriaTARGULAPUS' => 'Judecătoria TÂRGU LAPUŞ',
        'JudecatoriaTARGUMURES' => 'Judecătoria TÂRGU MUREŞ',
        'JudecatoriaTARGUNEAMT' => 'Judecătoria TÂRGU NEAMŢ',
        'JudecatoriaTARGUSECUIESC' => 'Judecătoria TÂRGU SECUIESC',
        'JudecatoriaTARGUCARBUNESTI' => 'Judecătoria TÂRGU-CĂRBUNEŞTI',
        'JudecatoriaTARNAVENI' => 'Judecătoria TÂRNAVENI',
        'JudecatoriaTECUCI' => 'Judecătoria TECUCI',
        'JudecatoriaTIMISOARA' => 'Judecătoria TIMIŞOARA',
        'JudecatoriaTOPLITA' => 'Judecătoria TOPLIŢA',
        'JudecatoriaTOPOLOVENI' => 'Judecătoria TOPOLOVENI',
        'JudecatoriaTULCEA' => 'Judecătoria TULCEA',
        'JudecatoriaTURDA' => 'Judecătoria TURDA',
        'JudecatoriaTURNUMAGURELE' => 'Judecătoria TURNU MĂGURELE',
        'JudecatoriaURZICENI' => 'Judecătoria URZICENI',
        'JudecatoriaVALENIIDEMUNTE' => 'Judecătoria VĂLENII DE MUNTE',
        'JudecatoriaVANJUMARE' => 'Judecătoria VÂNJU MARE',
        'JudecatoriaVASLUI' => 'Judecătoria VASLUI',
        'JudecatoriaVATRADORNEI' => 'Judecătoria VATRA DORNEI',
        'JudecatoriaVIDELE' => 'Judecătoria VIDELE',
        'JudecatoriaVISEUDESUS' => 'Judecătoria VIŞEU DE SUS',
        'JudecatoriaZALAU' => 'Judecătoria ZALĂU',
        'JudecatoriaZARNESTI' => 'Judecătoria ZĂRNEŞTI',
        'JudecatoriaZIMNICEA' => 'Judecătoria ZIMNICEA',

        // Tribunale - ordine alfabetică
        'TribunalulALBA' => 'Tribunalul ALBA',
        'TribunalulARAD' => 'Tribunalul ARAD',
        'TribunalulARGES' => 'Tribunalul ARGEŞ',
        'TribunalulBACU' => 'Tribunalul BACĂU',
        'TribunalulBIHOR' => 'Tribunalul BIHOR',
        'TribunalulBISTRITANASAUD' => 'Tribunalul BISTRIŢA NĂSĂUD',
        'TribunalulBOTOSANI' => 'Tribunalul BOTOŞANI',
        'TribunalulBRAILA' => 'Tribunalul BRĂILA',
        'TribunalulBRASSOV' => 'Tribunalul BRAŞOV',
        'TribunalulBUCURESTI' => 'Tribunalul BUCUREŞTI',
        'TribunalulBUZAU' => 'Tribunalul BUZĂU',
        'TribunalulCALARASI' => 'Tribunalul CĂLĂRAŞI',
        'TribunalulCARASSSEVERIN' => 'Tribunalul CARAŞ SEVERIN',
        'TribunalulCLUJ' => 'Tribunalul CLUJ',
        'TribunalulCONSTANTA' => 'Tribunalul CONSTANŢA',
        'TribunalulCOVASNA' => 'Tribunalul COVASNA',
        'TribunalulDAMBOVITA' => 'Tribunalul DÂMBOVIŢA',
        'TribunalulDOLJ' => 'Tribunalul DOLJ',
        'TribunalulGALATI' => 'Tribunalul GALAŢI',
        'TribunalulGIURGIU' => 'Tribunalul GIURGIU',
        'TribunalulGORJ' => 'Tribunalul GORJ',
        'TribunalulHARGHITA' => 'Tribunalul HARGHITA',
        'TribunalulHUNEDOARA' => 'Tribunalul HUNEDOARA',
        'TribunalulIALOMITA' => 'Tribunalul IALOMIŢA',
        'TribunalulIASI' => 'Tribunalul IAŞI',
        'TribunalulILFOV' => 'Tribunalul ILFOV',
        'TribunalulMARAMURES' => 'Tribunalul MARAMUREŞ',
        'TribunalulMEHEDINTI' => 'Tribunalul MEHEDINŢI',
        'TribunalulMilitarBUCURESTI' => 'Tribunalul Militar BUCUREŞTI',
        'TribunalulMilitarCLUJNAPOCA' => 'Tribunalul Militar CLUJ-NAPOCA',
        'TribunalulMilitarIASI' => 'Tribunalul Militar IAŞI',
        'TribunalulMilitarTeritoralBUCURESTI' => 'Tribunalul Militar Teritorial BUCUREŞTI',
        'TribunalulMilitarTIMISOARA' => 'Tribunalul Militar TIMIŞOARA',
        'TribunalulMURES' => 'Tribunalul MUREŞ',
        'TribunalulNEAMT' => 'Tribunalul NEAMŢ',
        'TribunalulOLT' => 'Tribunalul OLT',
        'TribunalulpentruminorisifamilieBRASSOV' => 'Tribunalul pentru minori şi familie BRAŞOV',
        'TribunalulPRAHOVA' => 'Tribunalul PRAHOVA',
        'TribunalulSALAJ' => 'Tribunalul SĂLAJ',
        'TribunalulSATUMARE' => 'Tribunalul SATU MARE',
        'TribunalulSIBIU' => 'Tribunalul SIBIU',
        'TribunalulSpecializatARGES' => 'Tribunalul Specializat ARGEŞ',
        'TribunalulSpecializatCLUJ' => 'Tribunalul Specializat CLUJ',
        'TribunalulSpecializatMURES' => 'Tribunalul Specializat MUREŞ',
        'TribunalulSUCEAVA' => 'Tribunalul SUCEAVA',
        'TribunalulTELEORMAN' => 'Tribunalul TELEORMAN',
        'TribunalulTIMIS' => 'Tribunalul TIMIŞ',
        'TribunalulTULCEA' => 'Tribunalul TULCEA',
        'TribunalulVALCEA' => 'Tribunalul VÂLCEA',
        'TribunalulVASLUI' => 'Tribunalul VASLUI',
        'TribunalulVRANCEA' => 'Tribunalul VRANCEA',
    ];
}

/**
 * Alias pentru getInstanteList() pentru compatibilitate cu codul existent
 *
 * @return array Lista instanțelor
 */
function getInstituteList() {
    return getInstanteList();
}

/**
 * Parsează informațiile despre judecători din câmpul complet
 *
 * @param string $complet Câmpul complet din răspunsul SOAP
 * @return array Informații structurate despre judecători
 */
function parseJudgeInformation($complet) {
    $result = [
        'type' => 'unknown',
        'judges' => [],
        'raw' => $complet,
        'parsed' => false
    ];

    if (empty($complet)) {
        return $result;
    }

    // Normalizăm textul pentru procesare
    $normalizedComplet = trim($complet);

    // Pattern pentru judecător unic
    if (preg_match('/^judec[ăa]tor\s+unic[:\s]*(.+)$/i', $normalizedComplet, $matches)) {
        $result['type'] = 'judecator_unic';
        $judgeName = trim($matches[1]);
        if (!empty($judgeName)) {
            $result['judges'][] = [
                'name' => $judgeName,
                'role' => 'Judecător unic'
            ];
            $result['parsed'] = true;
        }
        return $result;
    }

    // Pattern pentru complet cu număr
    if (preg_match('/^complet\s+(\d+)[:\s]*(.*)$/i', $normalizedComplet, $matches)) {
        $result['type'] = 'complet';
        $result['complet_number'] = $matches[1];
        $judgesText = trim($matches[2]);

        if (!empty($judgesText)) {
            $result['judges'] = parseJudgesList($judgesText);
            $result['parsed'] = !empty($result['judges']);
        }
        return $result;
    }

    // Pattern pentru complet fără număr explicit
    if (preg_match('/^complet[:\s]*(.+)$/i', $normalizedComplet, $matches)) {
        $result['type'] = 'complet';
        $judgesText = trim($matches[1]);

        if (!empty($judgesText)) {
            $result['judges'] = parseJudgesList($judgesText);
            $result['parsed'] = !empty($result['judges']);
        }
        return $result;
    }

    // Încercăm să parsăm direct ca listă de judecători
    $judges = parseJudgesList($normalizedComplet);
    if (!empty($judges)) {
        $result['type'] = count($judges) > 1 ? 'complet' : 'judecator_unic';
        $result['judges'] = $judges;
        $result['parsed'] = true;
    }

    return $result;
}

/**
 * Parsează o listă de judecători din text
 *
 * @param string $judgesText Textul cu lista judecătorilor
 * @return array Lista judecătorilor parsați
 */
function parseJudgesList($judgesText) {
    $judges = [];

    if (empty($judgesText)) {
        return $judges;
    }

    // Împărțim după virgulă, punct și virgulă sau "și"
    $parts = preg_split('/[,;]|\s+și\s+/i', $judgesText);

    foreach ($parts as $part) {
        $part = trim($part);
        if (empty($part)) {
            continue;
        }

        // Încercăm să identificăm rolul și numele
        $judge = parseIndividualJudge($part);
        if ($judge) {
            $judges[] = $judge;
        }
    }

    return $judges;
}

/**
 * Parsează informațiile unui judecător individual
 *
 * @param string $judgeText Textul cu informațiile judecătorului
 * @return array|null Informațiile judecătorului sau null dacă nu poate fi parsat
 */
function parseIndividualJudge($judgeText) {
    $judgeText = trim($judgeText);

    if (empty($judgeText)) {
        return null;
    }

    // Pattern pentru "Judecător principal: Nume"
    if (preg_match('/^judec[ăa]tor\s+principal[:\s]*(.+)$/i', $judgeText, $matches)) {
        return [
            'name' => trim($matches[1]),
            'role' => 'Judecător principal'
        ];
    }

    // Pattern pentru "Judecător: Nume"
    if (preg_match('/^judec[ăa]tor[:\s]*(.+)$/i', $judgeText, $matches)) {
        return [
            'name' => trim($matches[1]),
            'role' => 'Judecător'
        ];
    }

    // Pattern pentru "Președinte: Nume"
    if (preg_match('/^pre[șs]edinte[:\s]*(.+)$/i', $judgeText, $matches)) {
        return [
            'name' => trim($matches[1]),
            'role' => 'Președinte'
        ];
    }

    // Pattern pentru "Raportor: Nume"
    if (preg_match('/^raportor[:\s]*(.+)$/i', $judgeText, $matches)) {
        return [
            'name' => trim($matches[1]),
            'role' => 'Judecător raportor'
        ];
    }

    // Dacă nu găsim un pattern specific, considerăm că este un nume de judecător
    // și încercăm să determinăm dacă pare a fi un nume valid
    if (preg_match('/^[A-ZĂÂÎȘȚ][a-zăâîșț]+(\s+[A-ZĂÂÎȘȚ][a-zăâîșț]+)+$/u', $judgeText)) {
        return [
            'name' => $judgeText,
            'role' => 'Judecător'
        ];
    }

    return null;
}

/**
 * Formatează informațiile despre judecători pentru afișare
 *
 * @param array $judgeInfo Informațiile parsate despre judecători
 * @return string HTML formatat pentru afișare
 */
function formatJudgeInformation($judgeInfo) {
    if (!$judgeInfo['parsed'] || empty($judgeInfo['judges'])) {
        // Fallback la afișarea brută
        return '<span class="text-muted">' . htmlspecialchars($judgeInfo['raw']) . '</span>';
    }

    $html = '';

    // Afișăm tipul de complet
    if ($judgeInfo['type'] === 'judecator_unic') {
        $html .= '<div class="judge-composition"><strong>Judecător unic</strong></div>';
    } elseif ($judgeInfo['type'] === 'complet') {
        $completText = 'Complet';
        if (isset($judgeInfo['complet_number'])) {
            $completText .= ' ' . $judgeInfo['complet_number'];
        }
        $html .= '<div class="judge-composition"><strong>' . $completText . '</strong></div>';
    }

    // Afișăm lista judecătorilor
    if (!empty($judgeInfo['judges'])) {
        $html .= '<ul class="judge-list mb-0">';
        foreach ($judgeInfo['judges'] as $judge) {
            $html .= '<li>';
            $html .= '<span class="judge-role text-primary">' . htmlspecialchars($judge['role']) . ':</span> ';
            $html .= '<span class="judge-name">' . htmlspecialchars($judge['name']) . '</span>';
            $html .= '</li>';
        }
        $html .= '</ul>';
    }

    return $html;
}

/**
 * Formatează informațiile despre judecători pentru export (text simplu)
 *
 * @param array $judgeInfo Informațiile parsate despre judecători
 * @return string Text formatat pentru export
 */
function formatJudgeInformationForExport($judgeInfo) {
    if (!$judgeInfo['parsed'] || empty($judgeInfo['judges'])) {
        return $judgeInfo['raw'];
    }

    $text = '';

    // Tipul de complet
    if ($judgeInfo['type'] === 'judecator_unic') {
        $text .= 'Judecător unic: ';
    } elseif ($judgeInfo['type'] === 'complet') {
        $completText = 'Complet';
        if (isset($judgeInfo['complet_number'])) {
            $completText .= ' ' . $judgeInfo['complet_number'];
        }
        $text .= $completText . ': ';
    }

    // Lista judecătorilor
    $judgeTexts = [];
    foreach ($judgeInfo['judges'] as $judge) {
        $judgeTexts[] = $judge['role'] . ' ' . $judge['name'];
    }

    $text .= implode(', ', $judgeTexts);

    return $text;
}
