<?php
/**
 * Test Enhanced Party Extraction for Case 130/98/2022
 * Verifies that the enhanced system can retrieve all 450+ parties
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

// Test parameters for the case with 450+ parties
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "<!DOCTYPE html>";
echo "<html><head><title>Enhanced Party Extraction Test</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    .stats { background: #f8f9fa; padding: 10px; margin: 10px 0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .party-list { max-height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; }
</style></head><body>";

echo "<h1>🔍 Enhanced Party Extraction Test</h1>";
echo "<p><strong>Case:</strong> {$numarDosar} from {$institutie}</p>";
echo "<p><strong>Expected:</strong> 450+ parties (previously limited to 100)</p>";
echo "<hr>";

try {
    // Initialize the enhanced service
    $dosarService = new \App\Services\DosarService();
    
    echo "<div class='section'>";
    echo "<h2>📡 Step 1: Testing Enhanced Party Retrieval</h2>";
    
    $startTime = microtime(true);
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    $endTime = microtime(true);
    
    $processingTime = round(($endTime - $startTime) * 1000, 2);
    
    if (!$dosar || empty((array)$dosar)) {
        echo "<p class='error'>❌ ERROR: No case data returned</p>";
        exit;
    }
    
    echo "<p class='success'>✅ Case data retrieved successfully</p>";
    echo "<p class='info'>⏱️ Processing time: {$processingTime}ms</p>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>📊 Step 2: Party Count Analysis</h2>";
    
    $totalParties = count($dosar->parti ?? []);
    $soapParties = 0;
    $decisionParties = 0;
    
    foreach ($dosar->parti as $party) {
        if (isset($party->source)) {
            if ($party->source === 'soap_api') {
                $soapParties++;
            } elseif ($party->source === 'decision_text') {
                $decisionParties++;
            }
        }
    }
    
    echo "<div class='stats'>";
    echo "<table>";
    echo "<tr><th>Source</th><th>Count</th><th>Percentage</th></tr>";
    echo "<tr><td>SOAP API</td><td>{$soapParties}</td><td>" . round(($soapParties / max($totalParties, 1)) * 100, 1) . "%</td></tr>";
    echo "<tr><td>Decision Text</td><td>{$decisionParties}</td><td>" . round(($decisionParties / max($totalParties, 1)) * 100, 1) . "%</td></tr>";
    echo "<tr><td><strong>Total</strong></td><td><strong>{$totalParties}</strong></td><td><strong>100%</strong></td></tr>";
    echo "</table>";
    echo "</div>";
    
    // Analyze the results
    if ($totalParties >= 450) {
        echo "<p class='success'>🎉 SUCCESS: Retrieved {$totalParties} parties (≥450 expected)</p>";
        echo "<p class='success'>✅ Enhanced extraction successfully overcame the 100-party SOAP API limitation!</p>";
    } elseif ($totalParties > 100) {
        echo "<p class='warning'>⚠️ PARTIAL SUCCESS: Retrieved {$totalParties} parties (>100 but <450)</p>";
        echo "<p class='info'>Enhanced extraction is working but may need further optimization</p>";
    } else {
        echo "<p class='error'>❌ LIMITATION STILL EXISTS: Only {$totalParties} parties retrieved</p>";
        echo "<p class='error'>Enhanced extraction may not be working properly</p>";
    }
    
    if ($soapParties >= 100) {
        echo "<p class='info'>📋 SOAP API hit the 100-party limit as expected</p>";
    }
    
    if ($decisionParties > 0) {
        echo "<p class='success'>📄 Decision text extraction contributed {$decisionParties} additional parties</p>";
    } else {
        echo "<p class='warning'>⚠️ No parties extracted from decision text - this may indicate an issue</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🔍 Step 3: Quality Analysis</h2>";
    
    $qualityStats = [];
    foreach ($dosar->parti as $party) {
        $quality = $party->calitate ?? 'Unknown';
        if (!isset($qualityStats[$quality])) {
            $qualityStats[$quality] = 0;
        }
        $qualityStats[$quality]++;
    }
    
    arsort($qualityStats);
    
    echo "<div class='stats'>";
    echo "<table>";
    echo "<tr><th>Party Quality</th><th>Count</th></tr>";
    foreach ($qualityStats as $quality => $count) {
        echo "<tr><td>" . htmlspecialchars($quality) . "</td><td>{$count}</td></tr>";
    }
    echo "</table>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>👥 Step 4: Sample Parties</h2>";
    echo "<p>Showing first 20 parties for verification:</p>";
    
    echo "<div class='party-list'>";
    echo "<table>";
    echo "<tr><th>#</th><th>Name</th><th>Quality</th><th>Source</th></tr>";
    
    $displayCount = min(20, $totalParties);
    for ($i = 0; $i < $displayCount; $i++) {
        $party = $dosar->parti[$i];
        $rowClass = ($party->source ?? '') === 'soap_api' ? 'style="background-color: #e8f5e8;"' : 'style="background-color: #fff3cd;"';
        echo "<tr {$rowClass}>";
        echo "<td>" . ($i + 1) . "</td>";
        echo "<td>" . htmlspecialchars($party->nume ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($party->calitate ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($party->source ?? 'unknown') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "</div>";
    
    if ($totalParties > 20) {
        echo "<p class='info'>... and " . ($totalParties - 20) . " more parties</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2 class='error'>❌ Error Occurred</h2>";
    echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p class='info'>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
