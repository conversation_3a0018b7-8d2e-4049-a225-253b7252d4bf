<?php
/**
 * Test Party Limit Comparison
 * Compară afișarea cu și fără limitare pentru a identifica problema
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 0; // 0 = no limit

// Obținem datele
$dosarService = new \App\Services\DosarService();
$dosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');

if (!$dosar || empty($dosar->parti)) {
    echo "ERROR: Nu s-au putut obține datele dosarului";
    exit;
}

$totalParties = count($dosar->parti);
$displayParties = ($limit > 0) ? array_slice($dosar->parti, 0, $limit) : $dosar->parti;
$displayCount = count($displayParties);

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Party Limit Comparison</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .controls { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
    .controls a { margin-right: 10px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
    .controls a:hover { background: #0056b3; }
    .controls a.active { background: #28a745; }
</style></head><body>";

echo "<h1>🔍 Test Party Limit Comparison</h1>";
echo "<p>Compară afișarea cu și fără limitare pentru dosarul 130/98/2022</p>";

echo "<div class='controls'>";
echo "<strong>Teste disponibile:</strong><br>";
echo "<a href='?limit=0'" . ($limit === 0 ? " class='active'" : "") . ">Toate părțile ({$totalParties})</a>";
echo "<a href='?limit=100'" . ($limit === 100 ? " class='active'" : "") . ">Primele 100 părți</a>";
echo "<a href='?limit=200'" . ($limit === 200 ? " class='active'" : "") . ">Primele 200 părți</a>";
echo "<a href='?limit=500'" . ($limit === 500 ? " class='active'" : "") . ">Primele 500 părți</a>";
echo "</div>";

echo "<hr>";

echo "<div class='section'>";
echo "<h2>📊 Informații Test</h2>";
echo "<table>";
echo "<tr><th>Metric</th><th>Valoare</th></tr>";
echo "<tr><td>Total părți în backend</td><td>{$totalParties}</td></tr>";
echo "<tr><td>Limitare aplicată</td><td>" . ($limit > 0 ? $limit : 'Fără limitare') . "</td></tr>";
echo "<tr><td>Părți de afișat</td><td>{$displayCount}</td></tr>";
echo "</table>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>📋 Tabel Părți</h2>";

$start_time = microtime(true);

echo "<div class='table-responsive'>";
echo "<table class='table table-striped' id='tabelParti'>";
echo "<thead>";
echo "<tr>";
echo "<th>#</th>";
echo "<th>Nume</th>";
echo "<th>Calitate</th>";
echo "<th>Sursă</th>";
echo "</tr>";
echo "</thead>";
echo "<tbody>";

$rendered_count = 0;
foreach ($displayParties as $index => $parte) {
    $rendered_count++;
    
    echo "<tr class='parte-row' data-nume='" . htmlspecialchars($parte->nume) . "'>";
    echo "<td>{$rendered_count}</td>";
    echo "<td>" . htmlspecialchars($parte->nume) . "</td>";
    echo "<td>" . htmlspecialchars($parte->calitate ?? '-') . "</td>";
    echo "<td>" . htmlspecialchars($parte->source ?? 'unknown') . "</td>";
    echo "</tr>";
    
    // Flush periodic pentru teste mari
    if ($rendered_count % 100 === 0) {
        if (ob_get_level()) ob_flush();
        flush();
    }
}

echo "</tbody>";
echo "</table>";
echo "</div>";

$end_time = microtime(true);
$execution_time = round(($end_time - $start_time) * 1000, 2);

echo "</div>";

echo "<div class='section'>";
echo "<h2>📊 Rezultate Randare</h2>";

echo "<table>";
echo "<tr><th>Metric</th><th>Valoare</th></tr>";
echo "<tr><td>Părți randate de PHP</td><td>{$rendered_count}</td></tr>";
echo "<tr><td>Timp de execuție</td><td>{$execution_time} ms</td></tr>";
echo "<tr><td>Părți pe secundă</td><td>" . round($rendered_count / max($execution_time / 1000, 0.001), 0) . "</td></tr>";
echo "<tr><td>Memory usage</td><td>" . round(memory_get_usage() / 1024 / 1024, 2) . " MB</td></tr>";
echo "<tr><td>Peak memory</td><td>" . round(memory_get_peak_usage() / 1024 / 1024, 2) . " MB</td></tr>";
echo "</table>";

// Verificăm dacă SARAGEA TUDORIŢA este în lista afișată
$saragea_found = false;
$saragea_position = -1;

foreach ($displayParties as $index => $parte) {
    if (stripos($parte->nume, 'SARAGEA') !== false && stripos($parte->nume, 'TUDORI') !== false) {
        $saragea_found = true;
        $saragea_position = $index + 1;
        break;
    }
}

echo "<h3>🔍 Verificare SARAGEA TUDORIŢA</h3>";
if ($saragea_found) {
    echo "<p class='success'>✅ SARAGEA TUDORIŢA găsită la poziția {$saragea_position}</p>";
} else {
    echo "<p class='error'>❌ SARAGEA TUDORIŢA nu a fost găsită în părțile afișate</p>";
    if ($limit > 0 && $limit < 444) {
        echo "<p class='info'>ℹ️ SARAGEA TUDORIŢA este la poziția 444, dar limitarea este la {$limit}</p>";
    }
}

echo "</div>";

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    const rows = document.querySelectorAll('#tabelParti tbody tr.parte-row');";
echo "    console.log('JavaScript: Found', rows.length, 'rows in DOM');";
echo "    ";
echo "    // Verificăm dacă SARAGEA TUDORIŢA este în DOM";
echo "    let saragea_found_js = false;";
echo "    rows.forEach((row, index) => {";
echo "        const nume = row.getAttribute('data-nume') || '';";
echo "        if (nume.includes('SARAGEA') && nume.includes('TUDORI')) {";
echo "            saragea_found_js = true;";
echo "            console.log('JavaScript: SARAGEA TUDORIŢA found at position', index + 1);";
echo "        }";
echo "    });";
echo "    ";
echo "    if (!saragea_found_js) {";
echo "        console.error('JavaScript: SARAGEA TUDORIŢA NOT found in DOM');";
echo "    }";
echo "    ";
echo "    // Afișăm rezultatul în pagină";
echo "    const resultDiv = document.createElement('div');";
echo "    resultDiv.style.cssText = 'position: fixed; top: 10px; right: 10px; background: #28a745; color: white; padding: 10px; border-radius: 5px; z-index: 1000;';";
echo "    resultDiv.innerHTML = `DOM: \${rows.length} rânduri<br>PHP: {$rendered_count} rânduri`;";
echo "    document.body.appendChild(resultDiv);";
echo "    ";
echo "    setTimeout(() => resultDiv.remove(), 5000);";
echo "});";
echo "</script>";

echo "<hr>";
echo "<p><em>Test completat la " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
