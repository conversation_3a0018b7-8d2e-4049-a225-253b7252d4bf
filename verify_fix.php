<?php
/**
 * Script de verificare finală - Portal Judiciar România
 * Verifică că toate problemele au fost rezolvate
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔍 Portal Judiciar România - Verificare Finală\n";
echo "=" . str_repeat("=", 60) . "\n\n";

$allGood = true;

// 1. Verifică .htaccess
echo "1. 📄 Verificând .htaccess...\n";
$htaccessContent = file_get_contents('public/.htaccess');
if (strpos($htaccessContent, 'Order allow,deny') !== false) {
    echo "   ❌ .htaccess încă conține comenzi vechi Apache\n";
    $allGood = false;
} else {
    echo "   ✅ .htaccess este compatibil cu Apache 2.4+\n";
}

// 2. Verifică bootstrap.php
echo "\n2. 🚀 Verificând bootstrap.php...\n";
$bootstrapContent = file_get_contents('bootstrap.php');
if (strpos($bootstrapContent, 'session_status()') !== false) {
    echo "   ✅ Bootstrap.php are session_start() protejat\n";
} else {
    echo "   ❌ Bootstrap.php nu are session_start() protejat\n";
    $allGood = false;
}

// 3. Testează încărcarea bootstrap
echo "\n3. 🔧 Testând încărcarea bootstrap...\n";
try {
    require_once 'bootstrap.php';
    echo "   ✅ Bootstrap se încarcă fără erori\n";
} catch (Exception $e) {
    echo "   ❌ Eroare la încărcarea bootstrap: " . $e->getMessage() . "\n";
    $allGood = false;
}

// 4. Testează conexiunea la baza de date
echo "\n4. 🗄️ Testând conexiunea la baza de date...\n";
try {
    $db = App\Config\Database::getConnection();
    echo "   ✅ Conexiune la baza de date funcționează\n";
} catch (Exception $e) {
    echo "   ❌ Eroare conexiune baza de date: " . $e->getMessage() . "\n";
    $allGood = false;
}

// 5. Verifică fișierele admin
echo "\n5. 👨‍💼 Verificând fișierele admin...\n";
if (file_exists('public/admin/index.php')) {
    $output = [];
    $return_var = 0;
    exec('php -l "public/admin/index.php" 2>&1', $output, $return_var);
    
    if ($return_var === 0) {
        echo "   ✅ public/admin/index.php - Sintaxă OK\n";
    } else {
        echo "   ❌ public/admin/index.php - Eroare sintaxă\n";
        $allGood = false;
    }
} else {
    echo "   ❌ public/admin/index.php lipsește\n";
    $allGood = false;
}

// 6. Verifică fișierele monitor
echo "\n6. 👁️ Verificând fișierele monitor...\n";
if (file_exists('public/monitor.php')) {
    $output = [];
    $return_var = 0;
    exec('php -l "public/monitor.php" 2>&1', $output, $return_var);
    
    if ($return_var === 0) {
        echo "   ✅ public/monitor.php - Sintaxă OK\n";
    } else {
        echo "   ❌ public/monitor.php - Eroare sintaxă\n";
        $allGood = false;
    }
} else {
    echo "   ❌ public/monitor.php lipsește\n";
    $allGood = false;
}

// 7. Verifică template engine
echo "\n7. 🎨 Verificând template engine...\n";
try {
    $templateEngine = new App\Helpers\TemplateEngine();
    echo "   ✅ TemplateEngine se încarcă corect\n";
} catch (Exception $e) {
    echo "   ❌ Eroare TemplateEngine: " . $e->getMessage() . "\n";
    $allGood = false;
}

// 8. Verifică serviciile admin
echo "\n8. 🔐 Verificând serviciile admin...\n";
try {
    $adminService = new App\Services\AdminAuthService();
    echo "   ✅ AdminAuthService se încarcă corect\n";
} catch (Exception $e) {
    echo "   ❌ Eroare AdminAuthService: " . $e->getMessage() . "\n";
    $allGood = false;
}

// 9. Testează un request simplu
echo "\n9. 🌐 Testând request HTTP simplu...\n";
$testUrl = 'http://localhost/just/test_portal.php';
$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$response = @file_get_contents($testUrl, false, $context);
if ($response !== false && strpos($response, 'Test Portal Judiciar') !== false) {
    echo "   ✅ Request HTTP funcționează\n";
} else {
    echo "   ❌ Request HTTP eșuat\n";
    $allGood = false;
}

// Raport final
echo "\n" . str_repeat("=", 60) . "\n";
if ($allGood) {
    echo "🎉 TOATE VERIFICĂRILE AU TRECUT!\n\n";
    echo "✅ Sistemul Portal Judiciar România funcționează corect!\n\n";
    echo "🔗 LINK-URI DE TEST:\n";
    echo "- Test general: http://localhost/just/test_portal.php\n";
    echo "- Admin dashboard: http://localhost/just/public/admin/\n";
    echo "- Monitor dosare: http://localhost/just/public/monitor.php\n\n";
    echo "📋 PAȘI URMĂTORI:\n";
    echo "1. Rulează setup baza de date: php setup_monitoring_database.php\n";
    echo "2. Testează sistemul complet: php test_monitoring_system.php\n";
    echo "3. Configurează email-ul în src/Config/constants.php\n";
    echo "4. Configurează cron jobs pentru monitorizare\n";
} else {
    echo "❌ ÎNCĂ EXISTĂ PROBLEME!\n\n";
    echo "Verifică erorile de mai sus și încearcă din nou.\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
?>
