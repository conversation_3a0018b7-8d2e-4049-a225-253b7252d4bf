<?php
/**
 * Detailed analysis of decision text to improve party extraction
 */

require_once 'config/config.php';

echo "=== DETAILED DECISION TEXT ANALYSIS ===" . PHP_EOL;

try {
    // Read the SOAP response file
    $responseFile = 'soap_response_2025-07-04_17-14-14.xml';
    $xmlContent = file_get_contents($responseFile);
    
    // Extract solutieSumar content
    if (preg_match('/<solutieSumar>(.*?)<\/solutieSumar>/s', $xmlContent, $matches)) {
        $solutieSumar = $matches[1];
        
        echo "Decision text length: " . strlen($solutieSumar) . " characters" . PHP_EOL;
        echo PHP_EOL;
        
        // Look for different patterns that might contain party names
        echo "=== PATTERN ANALYSIS ===" . PHP_EOL;
        
        // Pattern 1: Original pattern
        $pattern1 = '/formulate de creditorii ([^;]+(?:;[^;]+)*)/';
        if (preg_match($pattern1, $solutieSumar, $matches1)) {
            echo "Pattern 1 (original): Found match" . PHP_EOL;
            echo "Length: " . strlen($matches1[1]) . " characters" . PHP_EOL;
            
            // Count semicolons to estimate number of parties
            $semicolonCount = substr_count($matches1[1], ';');
            echo "Semicolons found: $semicolonCount" . PHP_EOL;
            echo "Estimated parties: " . ($semicolonCount + 1) . PHP_EOL;
            
            // Show first and last parts
            $parts = explode(';', $matches1[1]);
            echo "First party: " . trim($parts[0]) . PHP_EOL;
            echo "Last party: " . trim($parts[count($parts) - 1]) . PHP_EOL;
            echo PHP_EOL;
        }
        
        // Pattern 2: Look for the specific parties we're missing
        $missingParties = ['SARAGEA TUDORIŢA', 'ZAMFIR NICOLETA'];
        foreach ($missingParties as $party) {
            if (strpos($solutieSumar, $party) !== false) {
                echo "✅ Found '$party' in decision text" . PHP_EOL;
                
                // Find context around the party
                $pos = strpos($solutieSumar, $party);
                $start = max(0, $pos - 100);
                $end = min(strlen($solutieSumar), $pos + strlen($party) + 100);
                $context = substr($solutieSumar, $start, $end - $start);
                echo "Context: ...{$context}..." . PHP_EOL;
            } else {
                echo "❌ '$party' NOT found in decision text" . PHP_EOL;
            }
        }
        echo PHP_EOL;
        
        // Pattern 3: Look for all uppercase names followed by semicolon
        echo "=== ALL UPPERCASE NAMES ANALYSIS ===" . PHP_EOL;
        $pattern3 = '/([A-ZĂÂÎȘȚ][A-ZĂÂÎȘȚ\s\-\.]+)\s*;/u';
        if (preg_match_all($pattern3, $solutieSumar, $matches3)) {
            echo "Found " . count($matches3[1]) . " uppercase names with semicolons" . PHP_EOL;
            
            // Check if our missing parties are in this list
            foreach ($missingParties as $party) {
                $found = false;
                foreach ($matches3[1] as $match) {
                    if (trim($match) === $party) {
                        echo "✅ Found '$party' in uppercase pattern" . PHP_EOL;
                        $found = true;
                        break;
                    }
                }
                if (!$found) {
                    echo "❌ '$party' NOT found in uppercase pattern" . PHP_EOL;
                }
            }
        }
        echo PHP_EOL;
        
        // Pattern 4: Look for the end of the creditors list
        echo "=== CREDITORS LIST END ANALYSIS ===" . PHP_EOL;
        
        // Find where the creditors list ends
        $endPatterns = [
            'în contradictoriu cu debitoarea',
            'Admite contestaţiile',
            'Suma de 200 de lei',
            'Cu drept de apel'
        ];
        
        foreach ($endPatterns as $endPattern) {
            $pos = strpos($solutieSumar, $endPattern);
            if ($pos !== false) {
                echo "Found end pattern '$endPattern' at position $pos" . PHP_EOL;
                
                // Show text before this pattern (last 200 chars)
                $start = max(0, $pos - 200);
                $beforeEnd = substr($solutieSumar, $start, $pos - $start);
                echo "Text before end: ...{$beforeEnd}" . PHP_EOL;
                echo PHP_EOL;
                break;
            }
        }
        
        // Pattern 5: Extract everything between "creditorii" and "în contradictoriu"
        echo "=== FULL CREDITORS EXTRACTION ===" . PHP_EOL;
        $fullPattern = '/creditorii\s+([^.]+)\s+în contradictoriu/s';
        if (preg_match($fullPattern, $solutieSumar, $fullMatches)) {
            echo "Full creditors text length: " . strlen($fullMatches[1]) . " characters" . PHP_EOL;
            
            // Count all semicolons in the full text
            $fullSemicolons = substr_count($fullMatches[1], ';');
            echo "Total semicolons in full text: $fullSemicolons" . PHP_EOL;
            
            // Check if missing parties are in full text
            foreach ($missingParties as $party) {
                if (strpos($fullMatches[1], $party) !== false) {
                    echo "✅ Found '$party' in full creditors text" . PHP_EOL;
                } else {
                    echo "❌ '$party' NOT found in full creditors text" . PHP_EOL;
                }
            }
        }
        
    } else {
        echo "❌ Could not extract solutieSumar from XML" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
}

echo PHP_EOL . "=== ANALYSIS COMPLETE ===" . PHP_EOL;
?>
