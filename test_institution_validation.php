<?php
/**
 * Comprehensive test script for institution code validation and SOAP API compatibility
 * Tests the complete institution validation system including mapping and fallback mechanisms
 */

require_once 'includes/InstitutionCodeValidator.php';
require_once 'includes/functions.php';

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Institution Code Validation Test</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { color: green; }
    .warning { color: orange; }
    .error { color: red; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .code { font-family: monospace; background-color: #f5f5f5; padding: 2px 4px; }
</style>\n";

/**
 * Test 1: Validate InstitutionCodeValidator functionality
 */
echo "<div class='test-section'>\n";
echo "<h2>Test 1: InstitutionCodeValidator Functionality</h2>\n";

// Test known valid codes
echo "<h3>Testing Known Valid Codes</h3>\n";
$validCodes = ['TribunalulBUCURESTI', 'CurteadeApelBUCURESTI', 'JudecatoriaSECTORUL1BUCURESTI'];
foreach ($validCodes as $code) {
    $result = InstitutionCodeValidator::validateAndMap($code);
    $status = $result['mapped'] ? 'warning' : 'success';
    echo "<p class='{$status}'>Code: <span class='code'>{$code}</span> - ";
    echo "Valid: " . (InstitutionCodeValidator::isValidCode($code) ? 'YES' : 'NO');
    echo ", Mapped: " . ($result['mapped'] ? 'YES' : 'NO');
    if ($result['warning']) {
        echo ", Warning: {$result['warning']}";
    }
    echo "</p>\n";
}

// Test known invalid codes
echo "<h3>Testing Known Invalid Codes</h3>\n";
$invalidCodes = ['JudecatoriaROMANI', 'JudecatoriaVICTORIA', 'NonExistentCode'];
foreach ($invalidCodes as $code) {
    $result = InstitutionCodeValidator::validateAndMap($code);
    $status = $result['fallback_needed'] ? 'error' : ($result['mapped'] ? 'warning' : 'info');
    echo "<p class='{$status}'>Code: <span class='code'>{$code}</span> - ";
    echo "Valid: " . (InstitutionCodeValidator::isValidCode($code) ? 'YES' : 'NO');
    echo ", Mapped: " . ($result['mapped'] ? 'YES' : 'NO');
    echo ", Fallback needed: " . ($result['fallback_needed'] ? 'YES' : 'NO');
    if ($result['warning']) {
        echo ", Warning: {$result['warning']}";
    }
    echo "</p>\n";
}

echo "</div>\n";

/**
 * Test 2: Test all institution codes from getInstanteList()
 */
echo "<div class='test-section'>\n";
echo "<h2>Test 2: All Institution Codes Analysis</h2>\n";

$allInstitutions = getInstanteList();
$validCount = 0;
$mappedCount = 0;
$fallbackCount = 0;
$unknownCount = 0;

echo "<table>\n";
echo "<tr><th>Code</th><th>Name</th><th>Status</th><th>Action</th><th>Warning</th></tr>\n";

foreach ($allInstitutions as $code => $name) {
    $result = InstitutionCodeValidator::validateAndMap($code);
    
    if (InstitutionCodeValidator::isValidCode($code)) {
        $status = "<span class='success'>Valid</span>";
        $action = "Direct SOAP call";
        $validCount++;
    } elseif ($result['mapped']) {
        $status = "<span class='warning'>Invalid (Mapped)</span>";
        $action = "Mapped to: <span class='code'>{$result['code']}</span>";
        $mappedCount++;
    } elseif ($result['fallback_needed']) {
        $status = "<span class='error'>Invalid (Fallback)</span>";
        $action = "Local filtering only";
        $fallbackCount++;
    } else {
        $status = "<span class='info'>Unknown</span>";
        $action = "Needs testing";
        $unknownCount++;
    }
    
    $warning = $result['warning'] ? htmlspecialchars($result['warning']) : '-';
    
    echo "<tr>";
    echo "<td><span class='code'>{$code}</span></td>";
    echo "<td>" . htmlspecialchars($name) . "</td>";
    echo "<td>{$status}</td>";
    echo "<td>{$action}</td>";
    echo "<td>{$warning}</td>";
    echo "</tr>\n";
}

echo "</table>\n";

echo "<h3>Summary Statistics</h3>\n";
echo "<ul>\n";
echo "<li class='success'>Valid codes: {$validCount}</li>\n";
echo "<li class='warning'>Mapped codes: {$mappedCount}</li>\n";
echo "<li class='error'>Fallback needed: {$fallbackCount}</li>\n";
echo "<li class='info'>Unknown/Untested: {$unknownCount}</li>\n";
echo "<li><strong>Total institutions: " . count($allInstitutions) . "</strong></li>\n";
echo "</ul>\n";

echo "</div>\n";

/**
 * Test 3: Test SOAP API integration (if available)
 */
echo "<div class='test-section'>\n";
echo "<h2>Test 3: SOAP API Integration Test</h2>\n";

try {
    // Test with DosarService if available
    if (class_exists('App\Services\DosarService')) {
        echo "<h3>Testing DosarService Integration</h3>\n";
        
        // Test with a known problematic code
        $testParams = [
            'institutie' => 'JudecatoriaROMANI',
            'numarDosar' => '',
            'numeParte' => '',
            'obiectDosar' => '',
            'dataStart' => '',
            'dataStop' => '',
            '_maxResults' => 10
        ];
        
        echo "<p class='info'>Testing search with problematic institution code: JudecatoriaROMANI</p>\n";
        
        try {
            $dosarService = new \App\Services\DosarService();
            $results = $dosarService->cautareAvansata($testParams);
            
            // Check for institution metadata
            if (is_array($results) && isset($results['_institution_metadata'])) {
                $metadata = $results['_institution_metadata'];
                echo "<p class='success'>Institution validation metadata found:</p>\n";
                echo "<ul>\n";
                echo "<li>Original code: <span class='code'>{$metadata['original_code']}</span></li>\n";
                echo "<li>Final code: <span class='code'>{$metadata['final_code']}</span></li>\n";
                echo "<li>Was mapped: " . ($metadata['was_mapped'] ? 'YES' : 'NO') . "</li>\n";
                echo "<li>Fallback used: " . ($metadata['fallback_used'] ? 'YES' : 'NO') . "</li>\n";
                if ($metadata['warning']) {
                    echo "<li>Warning: {$metadata['warning']}</li>\n";
                }
                echo "</ul>\n";
                
                // Remove metadata and show results count
                unset($results['_institution_metadata']);
                echo "<p class='info'>Search results: " . count($results) . " cases found</p>\n";
            } else {
                echo "<p class='warning'>No institution metadata found in results</p>\n";
                echo "<p class='info'>Search results: " . (is_array($results) ? count($results) : 'Invalid response') . "</p>\n";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>DosarService test failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        }
        
    } else {
        echo "<p class='warning'>DosarService not available for testing</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>SOAP API test failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "</div>\n";

/**
 * Test 4: Performance and logging test
 */
echo "<div class='test-section'>\n";
echo "<h2>Test 4: Performance and Logging</h2>\n";

$startTime = microtime(true);

// Test validation performance
$testCodes = array_keys(array_slice($allInstitutions, 0, 50)); // Test first 50 codes
foreach ($testCodes as $code) {
    InstitutionCodeValidator::validateAndMap($code);
}

$endTime = microtime(true);
$executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

echo "<p class='info'>Validation performance: {$executionTime:.2f}ms for 50 codes</p>\n";

// Check log files
$logDir = __DIR__ . '/logs';
if (is_dir($logDir)) {
    echo "<h3>Log Files Status</h3>\n";
    $logFiles = [
        'institution_validation.log' => 'Institution validation logs',
        'sessions_institution_validation.log' => 'Sessions institution validation logs',
        'search_params.log' => 'Search parameters logs',
        'search_errors.log' => 'Search error logs'
    ];
    
    foreach ($logFiles as $file => $description) {
        $filePath = "{$logDir}/{$file}";
        if (file_exists($filePath)) {
            $size = filesize($filePath);
            $modified = date('Y-m-d H:i:s', filemtime($filePath));
            echo "<p class='success'>{$description}: {$size} bytes, last modified: {$modified}</p>\n";
        } else {
            echo "<p class='warning'>{$description}: File not found</p>\n";
        }
    }
} else {
    echo "<p class='warning'>Logs directory not found: {$logDir}</p>\n";
}

echo "</div>\n";

echo "<div class='test-section'>\n";
echo "<h2>Test Completion</h2>\n";
echo "<p class='success'>All tests completed successfully!</p>\n";
echo "<p class='info'>Next steps:</p>\n";
echo "<ul>\n";
echo "<li>Review the validation results above</li>\n";
echo "<li>Test actual searches with problematic institution codes</li>\n";
echo "<li>Monitor log files for validation behavior</li>\n";
echo "<li>Update institution mappings based on real SOAP API responses</li>\n";
echo "</ul>\n";
echo "</div>\n";
?>
