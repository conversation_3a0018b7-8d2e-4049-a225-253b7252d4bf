<?php

namespace App\Services;

use App\Config\Database;
use Exception;

/**
 * User Preferences Manager
 * 
 * Handles user notification preferences and settings
 * with validation and default value management.
 */
class UserPreferencesManager
{
    /**
     * Default notification preferences
     */
    private const DEFAULT_PREFERENCES = [
        'immediate_notifications' => true,
        'daily_digest' => true,
        'weekly_summary' => false,
        'email_format' => 'html',
        'max_notifications_per_day' => 50,
        'quiet_hours_start' => '22:00',
        'quiet_hours_end' => '08:00',
        'timezone' => 'Europe/Bucharest'
    ];

    /**
     * Get user notification preferences
     * 
     * @param int $userId User ID
     * @return array User preferences with defaults applied
     */
    public function getUserPreferences(int $userId): array
    {
        try {
            $user = Database::fetchOne(
                "SELECT notification_preferences FROM active_users
                 WHERE id = ? AND email_verified = 1",
                [$userId]
            );

            if (!$user) {
                throw new Exception("User not found or inactive");
            }

            // Parse JSON preferences
            $preferences = [];
            if (!empty($user['notification_preferences'])) {
                $preferences = json_decode($user['notification_preferences'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    error_log("Invalid JSON in user preferences for user {$userId}: " . json_last_error_msg());
                    $preferences = [];
                }
            }

            // Merge with defaults
            return array_merge(self::DEFAULT_PREFERENCES, $preferences);

        } catch (Exception $e) {
            error_log("Error getting user preferences for user {$userId}: " . $e->getMessage());
            return self::DEFAULT_PREFERENCES;
        }
    }

    /**
     * Update user notification preferences
     * 
     * @param int $userId User ID
     * @param array $preferences New preferences
     * @return bool Success status
     */
    public function updateUserPreferences(int $userId, array $preferences): bool
    {
        try {
            // Validate preferences
            $validatedPreferences = $this->validatePreferences($preferences);
            
            // Get current preferences
            $currentPreferences = $this->getUserPreferences($userId);
            
            // Merge with current preferences
            $updatedPreferences = array_merge($currentPreferences, $validatedPreferences);
            
            // Update database
            $success = Database::update(
                'users',
                ['notification_preferences' => json_encode($updatedPreferences)],
                ['id' => $userId]
            );

            if ($success) {
                error_log("Updated notification preferences for user {$userId}");
                return true;
            } else {
                throw new Exception("Database update failed");
            }

        } catch (Exception $e) {
            error_log("Error updating user preferences for user {$userId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if user wants specific notification type
     * 
     * @param int $userId User ID
     * @param string $notificationType Type of notification
     * @return bool Whether user wants this notification type
     */
    public function wantsNotification(int $userId, string $notificationType): bool
    {
        $preferences = $this->getUserPreferences($userId);
        
        switch ($notificationType) {
            case 'immediate':
                return $preferences['immediate_notifications'] ?? true;
            case 'daily':
                return $preferences['daily_digest'] ?? true;
            case 'weekly':
                return $preferences['weekly_summary'] ?? false;
            default:
                return false;
        }
    }

    /**
     * Check if current time is within user's quiet hours
     * 
     * @param int $userId User ID
     * @return bool Whether it's quiet hours for the user
     */
    public function isQuietHours(int $userId): bool
    {
        $preferences = $this->getUserPreferences($userId);
        
        $timezone = new \DateTimeZone($preferences['timezone']);
        $now = new \DateTime('now', $timezone);
        $currentTime = $now->format('H:i');
        
        $quietStart = $preferences['quiet_hours_start'];
        $quietEnd = $preferences['quiet_hours_end'];
        
        // Handle overnight quiet hours (e.g., 22:00 to 08:00)
        if ($quietStart > $quietEnd) {
            return $currentTime >= $quietStart || $currentTime <= $quietEnd;
        } else {
            return $currentTime >= $quietStart && $currentTime <= $quietEnd;
        }
    }

    /**
     * Get user's preferred email format
     * 
     * @param int $userId User ID
     * @return string Email format ('html' or 'text')
     */
    public function getEmailFormat(int $userId): string
    {
        $preferences = $this->getUserPreferences($userId);
        return $preferences['email_format'] ?? 'html';
    }

    /**
     * Check if user has reached daily notification limit
     * 
     * @param int $userId User ID
     * @return bool Whether user has reached limit
     */
    public function hasReachedDailyLimit(int $userId): bool
    {
        try {
            $preferences = $this->getUserPreferences($userId);
            $maxPerDay = $preferences['max_notifications_per_day'] ?? 50;
            
            $sentToday = Database::fetchOne(
                "SELECT COUNT(*) as count 
                 FROM notification_queue 
                 WHERE user_id = ? 
                 AND status = 'sent' 
                 AND DATE(sent_at) = CURDATE()",
                [$userId]
            );
            
            return ($sentToday['count'] ?? 0) >= $maxPerDay;
            
        } catch (Exception $e) {
            error_log("Error checking daily limit for user {$userId}: " . $e->getMessage());
            return false; // Allow notifications if we can't check
        }
    }

    /**
     * Validate user preferences
     * 
     * @param array $preferences Raw preferences
     * @return array Validated preferences
     */
    private function validatePreferences(array $preferences): array
    {
        $validated = [];
        
        // Boolean preferences
        $booleanFields = ['immediate_notifications', 'daily_digest', 'weekly_summary'];
        foreach ($booleanFields as $field) {
            if (isset($preferences[$field])) {
                $validated[$field] = (bool)$preferences[$field];
            }
        }
        
        // Email format
        if (isset($preferences['email_format'])) {
            $validated['email_format'] = in_array($preferences['email_format'], ['html', 'text']) 
                ? $preferences['email_format'] 
                : 'html';
        }
        
        // Max notifications per day
        if (isset($preferences['max_notifications_per_day'])) {
            $max = (int)$preferences['max_notifications_per_day'];
            $validated['max_notifications_per_day'] = max(1, min(100, $max)); // Between 1 and 100
        }
        
        // Quiet hours
        if (isset($preferences['quiet_hours_start'])) {
            $validated['quiet_hours_start'] = $this->validateTimeFormat($preferences['quiet_hours_start']);
        }
        if (isset($preferences['quiet_hours_end'])) {
            $validated['quiet_hours_end'] = $this->validateTimeFormat($preferences['quiet_hours_end']);
        }
        
        // Timezone
        if (isset($preferences['timezone'])) {
            try {
                new \DateTimeZone($preferences['timezone']);
                $validated['timezone'] = $preferences['timezone'];
            } catch (Exception $e) {
                // Invalid timezone, use default
                $validated['timezone'] = 'Europe/Bucharest';
            }
        }
        
        return $validated;
    }

    /**
     * Validate time format (HH:MM)
     * 
     * @param string $time Time string
     * @return string Validated time or default
     */
    private function validateTimeFormat(string $time): string
    {
        if (preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $time)) {
            return $time;
        }
        return '08:00'; // Default time
    }

    /**
     * Get users with specific notification preferences
     * 
     * @param string $notificationType Type of notification
     * @param bool $respectQuietHours Whether to respect quiet hours
     * @return array Users who want this notification type
     */
    public function getUsersForNotificationType(string $notificationType, bool $respectQuietHours = true): array
    {
        try {
            switch($notificationType) {
                case 'immediate':
                    $jsonPath = '$.immediate_notifications';
                    break;
                case 'daily':
                    $jsonPath = '$.daily_digest';
                    break;
                case 'weekly':
                    $jsonPath = '$.weekly_summary';
                    break;
                default:
                    $jsonPath = '$.immediate_notifications';
                    break;
            }
            
            $users = Database::fetchAll(
                "SELECT id, email, first_name, last_name, notification_preferences
                 FROM active_users
                 WHERE email_verified = 1
                 AND data_processing_consent = 1
                 AND (
                     JSON_EXTRACT(notification_preferences, '{$jsonPath}') = true
                     OR notification_preferences IS NULL
                 )"
            );
            
            // Filter by quiet hours if requested
            if ($respectQuietHours) {
                $users = array_filter($users, function($user) {
                    return !$this->isQuietHours($user['id']);
                });
            }
            
            return array_values($users); // Re-index array
            
        } catch (Exception $e) {
            error_log("Error getting users for notification type {$notificationType}: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Initialize default preferences for new user
     * 
     * @param int $userId User ID
     * @return bool Success status
     */
    public function initializeDefaultPreferences(int $userId): bool
    {
        return $this->updateUserPreferences($userId, self::DEFAULT_PREFERENCES);
    }
}
