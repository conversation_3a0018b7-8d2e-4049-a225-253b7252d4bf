<?php
// Test POST search functionality
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test POST Search</title>
</head>
<body>
    <h2>Testing POST Search for 130/98/2022</h2>
    
    <form method="POST" action="index.php" target="_blank">
        <input type="hidden" name="bulkSearchTerms" value="130/98/2022">
        <button type="submit">Submit POST Search</button>
    </form>
    
    <hr>
    
    <h3>Manual Test Instructions:</h3>
    <ol>
        <li>Go to <a href="index.php" target="_blank">index.php</a></li>
        <li>Enter "130/98/2022" in the search box</li>
        <li>Click the search button</li>
        <li>Check if "Expandează rezultatele" buttons appear</li>
        <li>Test clicking the expansion buttons</li>
        <li>Check browser console for JavaScript errors</li>
    </ol>
    
    <script>
        // Auto-submit the form after 2 seconds
        setTimeout(function() {
            document.querySelector('form').submit();
        }, 2000);
    </script>
</body>
</html>
