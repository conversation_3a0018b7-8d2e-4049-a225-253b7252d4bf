<?php
/**
 * Monitor Dosare - Versiune Reparată
 * Versiune simplificată care funcționează garantat
 */

// Activez raportarea erorilor
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Încerc să încarc bootstrap-ul
try {
    require_once dirname(__DIR__) . '/bootstrap.php';
    $bootstrapLoaded = true;
} catch (Exception $e) {
    $bootstrapLoaded = false;
    $bootstrapError = $e->getMessage();
}

// Pornesc sesiunea dacă nu e pornită
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Setez utilizator demo
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Demo User';
    $_SESSION['user_email'] = '<EMAIL>';
}

$userId = $_SESSION['user_id'];
$userName = $_SESSION['user_name'] ?? 'Utilizator';

// Încerc să inițializez serviciile
$servicesLoaded = false;
$monitoredCases = [];
$casesCount = 0;
$institutions = [];

if ($bootstrapLoaded) {
    try {
        $monitoringService = new App\Services\CaseMonitoringService();
        $dosarService = new App\Services\DosarService();
        
        // Încarc datele
        $monitoredCases = $monitoringService->getUserMonitoredCases($userId);
        $casesCount = $monitoringService->getUserMonitoredCasesCount($userId);
        
        // Încarc instituțiile
        require_once '../includes/functions.php';
        $institutions = getInstanteList();
        
        $servicesLoaded = true;
    } catch (Exception $e) {
        $servicesError = $e->getMessage();
    }
}

// Gestionez cererile AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $servicesLoaded) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'];
        
        switch ($action) {
            case 'add_case':
                $caseNumber = $_POST['case_number'] ?? '';
                $institutionCode = $_POST['institution_code'] ?? '';
                $institutionName = $_POST['institution_name'] ?? '';
                $notificationFreq = $_POST['notification_frequency'] ?? 'daily';
                
                if (empty($caseNumber) || empty($institutionCode)) {
                    throw new Exception('Numărul dosarului și instituția sunt obligatorii');
                }
                
                // Verific dacă dosarul e deja monitorizat
                if ($monitoringService->isCaseMonitored($userId, $caseNumber, $institutionCode)) {
                    throw new Exception('Acest dosar este deja monitorizat');
                }
                
                // Adaug dosarul la monitorizare
                $monitoredCaseId = $monitoringService->addCaseToMonitoring(
                    $userId,
                    $caseNumber,
                    $institutionCode,
                    $institutionName,
                    '',
                    'Monitorizare utilizator',
                    $notificationFreq
                );
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Dosarul a fost adăugat la monitorizare cu succes',
                    'case_id' => $monitoredCaseId
                ]);
                break;
                
            case 'remove_case':
                $caseId = intval($_POST['case_id'] ?? 0);
                
                if ($caseId <= 0) {
                    throw new Exception('ID dosar invalid');
                }
                
                $success = $monitoringService->removeCaseFromMonitoring($userId, $caseId);
                
                if ($success) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Dosarul a fost eliminat din monitorizare'
                    ]);
                } else {
                    throw new Exception('Eroare la eliminarea dosarului din monitorizare');
                }
                break;
                
            default:
                throw new Exception('Acțiune necunoscută');
        }
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitor Dosare - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .judicial-header {
            background: linear-gradient(135deg, #2c3e50 0%, #007bff 100%);
            color: white;
            padding: 2rem 0;
        }
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        .btn-judicial {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }
        .btn-judicial:hover {
            background-color: #0056b3;
            border-color: #0056b3;
            color: white;
        }
        .case-item {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #f8f9fa;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-ok { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <header class="judicial-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-gavel me-3"></i>Portal Judiciar România</h1>
                    <p class="mb-0">Monitorizare Dosare Judiciare</p>
                </div>
                <div class="col-md-4 text-end">
                    <span class="me-3">Bun venit, <?php echo htmlspecialchars($userName); ?>!</span>
                    <a href="../" class="btn btn-outline-light">
                        <i class="fas fa-home me-2"></i>Acasă
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="container mt-4">
        <!-- Status sistem -->
        <div class="row mb-4">
            <div class="col-12">
                <?php if ($bootstrapLoaded && $servicesLoaded): ?>
                    <div class="alert alert-success">
                        <span class="status-indicator status-ok"></span>
                        <strong>Sistem Operațional!</strong> Toate serviciile funcționează corect.
                    </div>
                <?php elseif ($bootstrapLoaded && !$servicesLoaded): ?>
                    <div class="alert alert-warning">
                        <span class="status-indicator status-warning"></span>
                        <strong>Funcționalitate Limitată!</strong> Unele servicii nu sunt disponibile.
                        <?php if (isset($servicesError)): ?>
                            <br><small>Eroare: <?php echo htmlspecialchars($servicesError); ?></small>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="alert alert-danger">
                        <span class="status-indicator status-error"></span>
                        <strong>Eroare Sistem!</strong> Nu s-a putut încărca configurația.
                        <?php if (isset($bootstrapError)): ?>
                            <br><small>Eroare: <?php echo htmlspecialchars($bootstrapError); ?></small>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Dashboard statistici -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-eye fa-2x text-primary mb-2"></i>
                        <h5 class="card-title">Dosare Monitorizate</h5>
                        <h3 class="text-primary"><?php echo $casesCount; ?></h3>
                        <small class="text-muted">din <?php echo defined('MAX_MONITORED_CASES_PER_USER') ? MAX_MONITORED_CASES_PER_USER : 50; ?> permise</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-bell fa-2x text-warning mb-2"></i>
                        <h5 class="card-title">Notificări</h5>
                        <h3 class="text-warning">0</h3>
                        <small class="text-muted">astăzi</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                        <h5 class="card-title">Modificări</h5>
                        <h3 class="text-success">0</h3>
                        <small class="text-muted">săptămâna aceasta</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-clock fa-2x text-info mb-2"></i>
                        <h5 class="card-title">Ultima Verificare</h5>
                        <h3 class="text-info"><?php echo date('H:i'); ?></h3>
                        <small class="text-muted"><?php echo date('d.m.Y'); ?></small>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Formular adăugare dosar -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>Adaugă Dosar pentru Monitorizare</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($servicesLoaded): ?>
                            <form id="addCaseForm">
                                <div class="mb-3">
                                    <label for="caseNumber" class="form-label">Numărul Dosarului *</label>
                                    <input type="text" class="form-control" id="caseNumber" name="case_number" 
                                           placeholder="ex: 1234/2024" required>
                                    <div class="form-text">Introduceți numărul complet al dosarului</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="institution" class="form-label">Instanța *</label>
                                    <select class="form-select" id="institution" name="institution" required>
                                        <option value="">Selectează instanța...</option>
                                        <?php foreach ($institutions as $code => $name): ?>
                                            <option value="<?php echo htmlspecialchars($code); ?>" 
                                                    data-name="<?php echo htmlspecialchars($name); ?>">
                                                <?php echo htmlspecialchars($name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text"><?php echo count($institutions); ?> instituții disponibile</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="notificationFreq" class="form-label">Frecvența Notificărilor</label>
                                    <select class="form-select" id="notificationFreq" name="notification_frequency">
                                        <option value="immediate">Imediat (la fiecare modificare)</option>
                                        <option value="daily" selected>Zilnic (rezumat zilnic)</option>
                                        <option value="weekly">Săptămânal (rezumat săptămânal)</option>
                                    </select>
                                </div>
                                
                                <button type="submit" class="btn btn-judicial" id="addCaseBtn">
                                    <i class="fas fa-plus me-2"></i>Adaugă la Monitorizare
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Serviciile de monitorizare nu sunt disponibile momentan.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Lista dosare monitorizate -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Dosarele Mele Monitorizate</h5>
                        <span class="badge bg-light text-dark"><?php echo count($monitoredCases); ?></span>
                    </div>
                    <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                        <div id="monitoredCasesList">
                            <?php if (empty($monitoredCases)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <p>Nu aveți dosare monitorizate încă.</p>
                                    <?php if ($servicesLoaded): ?>
                                        <p>Adăugați primul dosar folosind formularul din stânga.</p>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <?php foreach ($monitoredCases as $case): ?>
                                    <div class="case-item" data-case-id="<?php echo $case['id']; ?>">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">
                                                    <i class="fas fa-folder-open me-2 text-primary"></i>
                                                    <?php echo htmlspecialchars($case['case_number']); ?>
                                                </h6>
                                                <p class="mb-1 text-muted small">
                                                    <?php echo htmlspecialchars($case['institution_name']); ?>
                                                </p>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        Adăugat: <?php echo date('d.m.Y', strtotime($case['created_at'])); ?>
                                                    </small>
                                                    <span class="badge bg-success">Activ</span>
                                                </div>
                                            </div>
                                            <div class="ms-2">
                                                <button class="btn btn-sm btn-outline-danger remove-case-btn" 
                                                        data-case-id="<?php echo $case['id']; ?>"
                                                        title="Elimină din monitorizare">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informații sistem -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                <h6>Securitate GDPR</h6>
                                <small class="text-muted">Conformitate completă</small>
                            </div>
                            <div class="col-md-3">
                                <i class="fas fa-database fa-2x <?php echo $bootstrapLoaded ? 'text-success' : 'text-danger'; ?> mb-2"></i>
                                <h6>Baza de Date</h6>
                                <small class="text-muted"><?php echo $bootstrapLoaded ? 'Conectată' : 'Deconectată'; ?></small>
                            </div>
                            <div class="col-md-3">
                                <i class="fas fa-cogs fa-2x <?php echo $servicesLoaded ? 'text-success' : 'text-warning'; ?> mb-2"></i>
                                <h6>Servicii</h6>
                                <small class="text-muted"><?php echo $servicesLoaded ? 'Operaționale' : 'Limitate'; ?></small>
                            </div>
                            <div class="col-md-3">
                                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                <h6>Monitorizare</h6>
                                <small class="text-muted">24/7 Automată</small>
                            </div>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="test_error.php" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-bug me-2"></i>Diagnostic Complet
                            </a>
                            <?php if (!$servicesLoaded): ?>
                                <button class="btn btn-outline-primary" onclick="location.reload()">
                                    <i class="fas fa-sync me-2"></i>Reîncarcă Pagina
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="text-muted mb-0">
                Portal Judiciar România - Sistem de Monitorizare Dosare
            </p>
        </div>
    </footer>

    <!-- Modal pentru mesaje -->
    <div class="modal fade" id="messageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalTitle">Notificare</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="messageModalBody"></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Închide</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Funcție pentru afișarea mesajelor
        function showMessage(title, message, isError = false) {
            document.getElementById('messageModalTitle').textContent = title;
            document.getElementById('messageModalBody').innerHTML = message;
            
            const modal = document.getElementById('messageModal');
            const header = modal.querySelector('.modal-header');
            header.className = 'modal-header ' + (isError ? 'bg-danger text-white' : 'bg-success text-white');
            
            new bootstrap.Modal(modal).show();
        }

        // Handler pentru formularul de adăugare
        <?php if ($servicesLoaded): ?>
        document.getElementById('addCaseForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            formData.append('action', 'add_case');
            formData.append('case_number', document.getElementById('caseNumber').value);
            formData.append('institution_code', document.getElementById('institution').value);
            formData.append('institution_name', document.getElementById('institution').selectedOptions[0].dataset.name);
            formData.append('notification_frequency', document.getElementById('notificationFreq').value);
            
            const btn = document.getElementById('addCaseBtn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Se adaugă...';
            
            fetch('monitor.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('Succes', data.message);
                    document.getElementById('addCaseForm').reset();
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showMessage('Eroare', data.error, true);
                }
            })
            .catch(error => {
                showMessage('Eroare', 'A apărut o eroare de comunicare: ' + error.message, true);
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-plus me-2"></i>Adaugă la Monitorizare';
            });
        });

        // Handler pentru eliminarea dosarelor
        document.addEventListener('click', function(e) {
            if (e.target.closest('.remove-case-btn')) {
                const btn = e.target.closest('.remove-case-btn');
                const caseId = btn.dataset.caseId;
                
                if (confirm('Sigur doriți să eliminați acest dosar din monitorizare?')) {
                    const formData = new FormData();
                    formData.append('action', 'remove_case');
                    formData.append('case_id', caseId);
                    
                    fetch('monitor.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showMessage('Succes', data.message);
                            setTimeout(() => location.reload(), 1500);
                        } else {
                            showMessage('Eroare', data.error, true);
                        }
                    })
                    .catch(error => {
                        showMessage('Eroare', 'A apărut o eroare: ' + error.message, true);
                    });
                }
            }
        });
        <?php endif; ?>
    </script>
</body>
</html>
