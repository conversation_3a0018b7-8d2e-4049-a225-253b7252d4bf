<?php

namespace App\Security;

use App\Config\Database;
use Exception;

/**
 * GDPR Compliance System
 * 
 * Implements comprehensive GDPR compliance features:
 * - Consent management
 * - Data export (Right to portability)
 * - Data deletion (Right to erasure)
 * - Data processing logs
 * - Privacy controls
 * 
 * Portal Judiciar România - Case Monitoring System
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */
class GDPRCompliance
{
    // Consent types
    public const CONSENT_TYPES = [
        'monitoring' => 'Case monitoring and notifications',
        'email_notifications' => 'Email notifications',
        'data_processing' => 'Personal data processing',
        'analytics' => 'Usage analytics',
        'marketing' => 'Marketing communications'
    ];
    
    // Data retention periods (in days)
    private const RETENTION_PERIODS = [
        'monitored_cases' => 365, // 1 year
        'case_snapshots' => 90,   // 3 months
        'notification_logs' => 180, // 6 months
        'system_logs' => 90,      // 3 months
        'rate_limit_attempts' => 30, // 1 month
        'user_sessions' => 7      // 1 week
    ];
    
    /**
     * Record user consent
     * 
     * @param int $userId User ID
     * @param string $consentType Type of consent
     * @param bool $granted Whether consent was granted
     * @param string|null $ipAddress IP address
     * @param array|null $metadata Additional metadata
     * @return bool Success status
     */
    public static function recordConsent(int $userId, string $consentType, bool $granted, ?string $ipAddress = null, ?array $metadata = null): bool
    {
        if (!array_key_exists($consentType, self::CONSENT_TYPES)) {
            throw new \InvalidArgumentException("Invalid consent type: {$consentType}");
        }
        
        $ipAddress = $ipAddress ?? ($_SERVER['REMOTE_ADDR'] ?? null);
        
        try {
            // Insert new consent record
            Database::insert('user_consents', [
                'user_id' => $userId,
                'consent_type' => $consentType,
                'granted' => $granted ? 1 : 0,
                'ip_address' => $ipAddress,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'metadata' => $metadata ? json_encode($metadata, JSON_UNESCAPED_UNICODE) : null,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            // Log the consent action
            self::logDataProcessing($userId, 'consent_recorded', [
                'consent_type' => $consentType,
                'granted' => $granted,
                'ip_address' => $ipAddress
            ]);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Failed to record consent: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if user has given consent for a specific type
     * 
     * @param int $userId User ID
     * @param string $consentType Type of consent
     * @return bool True if consent is granted and valid
     */
    public static function hasConsent(int $userId, string $consentType): bool
    {
        try {
            $consent = Database::fetchOne("
                SELECT granted, created_at
                FROM user_consents 
                WHERE user_id = ? AND consent_type = ?
                ORDER BY created_at DESC 
                LIMIT 1
            ", [$userId, $consentType]);
            
            if (!$consent) {
                return false;
            }
            
            return (bool)$consent['granted'];
            
        } catch (Exception $e) {
            error_log("Failed to check consent: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all consents for a user
     * 
     * @param int $userId User ID
     * @return array User consents
     */
    public static function getUserConsents(int $userId): array
    {
        try {
            $consents = Database::fetchAll("
                SELECT consent_type, granted, created_at, ip_address
                FROM user_consents 
                WHERE user_id = ?
                ORDER BY consent_type, created_at DESC
            ", [$userId]);
            
            // Group by consent type, keeping only the latest
            $latestConsents = [];
            foreach ($consents as $consent) {
                if (!isset($latestConsents[$consent['consent_type']])) {
                    $latestConsents[$consent['consent_type']] = $consent;
                }
            }
            
            return $latestConsents;
            
        } catch (Exception $e) {
            error_log("Failed to get user consents: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Export all user data (Right to portability)
     * 
     * @param int $userId User ID
     * @return array Complete user data export
     */
    public static function exportUserData(int $userId): array
    {
        try {
            $export = [
                'export_date' => date('Y-m-d H:i:s'),
                'user_id' => $userId,
                'data' => []
            ];
            
            // User basic information
            $user = Database::fetchOne("SELECT * FROM users WHERE id = ?", [$userId]);
            if ($user) {
                unset($user['password']); // Never export passwords
                $export['data']['user_profile'] = $user;
            }
            
            // Monitored cases
            $monitoredCases = Database::fetchAll("
                SELECT case_number, institution_code, institution_name, 
                       notification_frequency, is_active, created_at
                FROM monitored_cases 
                WHERE user_id = ?
            ", [$userId]);
            $export['data']['monitored_cases'] = $monitoredCases;
            
            // Notification history
            $notifications = Database::fetchAll("
                SELECT notification_type, email_subject, status, 
                       created_at, sent_at
                FROM notification_queue 
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT 1000
            ", [$userId]);
            $export['data']['notification_history'] = $notifications;
            
            // Consent history
            $consents = Database::fetchAll("
                SELECT consent_type, granted, created_at, ip_address
                FROM user_consents 
                WHERE user_id = ?
                ORDER BY created_at DESC
            ", [$userId]);
            $export['data']['consent_history'] = $consents;
            
            // Data processing logs
            $processingLogs = Database::fetchAll("
                SELECT action, context, created_at
                FROM data_processing_logs 
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT 1000
            ", [$userId]);
            $export['data']['processing_logs'] = $processingLogs;
            
            // Rate limit attempts (if any)
            $rateLimitAttempts = Database::fetchAll("
                SELECT action, success, created_at
                FROM rate_limit_attempts 
                WHERE identifier = ?
                ORDER BY created_at DESC
                LIMIT 500
            ", ["user_{$userId}"]);
            $export['data']['rate_limit_history'] = $rateLimitAttempts;
            
            // Log the export action
            self::logDataProcessing($userId, 'data_exported', [
                'export_size' => strlen(json_encode($export)),
                'tables_included' => array_keys($export['data'])
            ]);
            
            return $export;
            
        } catch (Exception $e) {
            error_log("Failed to export user data: " . $e->getMessage());
            throw new \Exception("Data export failed. Please try again later.");
        }
    }
    
    /**
     * Delete all user data (Right to erasure)
     * 
     * @param int $userId User ID
     * @param bool $keepLegallyRequired Whether to keep legally required data
     * @return array Deletion summary
     */
    public static function deleteUserData(int $userId, bool $keepLegallyRequired = true): array
    {
        try {
            Database::beginTransaction();
            
            $deletionSummary = [
                'user_id' => $userId,
                'deletion_date' => date('Y-m-d H:i:s'),
                'tables_affected' => [],
                'records_deleted' => 0,
                'kept_for_legal_reasons' => []
            ];
            
            // Delete monitored cases
            $deletedCases = Database::execute("DELETE FROM monitored_cases WHERE user_id = ?", [$userId]);
            $deletionSummary['tables_affected']['monitored_cases'] = $deletedCases;
            $deletionSummary['records_deleted'] += $deletedCases;
            
            // Delete notification queue entries
            $deletedNotifications = Database::execute("DELETE FROM notification_queue WHERE user_id = ?", [$userId]);
            $deletionSummary['tables_affected']['notification_queue'] = $deletedNotifications;
            $deletionSummary['records_deleted'] += $deletedNotifications;
            
            // Delete or anonymize consents (keep for legal compliance)
            if ($keepLegallyRequired) {
                $anonymizedConsents = Database::execute("
                    UPDATE user_consents 
                    SET ip_address = 'ANONYMIZED', user_agent = 'ANONYMIZED', metadata = NULL
                    WHERE user_id = ?
                ", [$userId]);
                $deletionSummary['kept_for_legal_reasons']['user_consents'] = $anonymizedConsents;
            } else {
                $deletedConsents = Database::execute("DELETE FROM user_consents WHERE user_id = ?", [$userId]);
                $deletionSummary['tables_affected']['user_consents'] = $deletedConsents;
                $deletionSummary['records_deleted'] += $deletedConsents;
            }
            
            // Anonymize processing logs (keep for audit trail)
            $anonymizedLogs = Database::execute("
                UPDATE data_processing_logs 
                SET context = JSON_OBJECT('anonymized', true, 'original_user_id', user_id)
                WHERE user_id = ?
            ", [$userId]);
            $deletionSummary['kept_for_legal_reasons']['data_processing_logs'] = $anonymizedLogs;
            
            // Delete rate limit attempts
            $deletedRateLimit = Database::execute("DELETE FROM rate_limit_attempts WHERE identifier = ?", ["user_{$userId}"]);
            $deletionSummary['tables_affected']['rate_limit_attempts'] = $deletedRateLimit;
            $deletionSummary['records_deleted'] += $deletedRateLimit;
            
            // Anonymize or delete user profile
            if ($keepLegallyRequired) {
                Database::execute("
                    UPDATE users 
                    SET email = CONCAT('deleted_', id, '@anonymized.local'),
                        first_name = 'DELETED',
                        last_name = 'USER',
                        phone = NULL,
                        is_active = 0,
                        deleted_at = NOW()
                    WHERE id = ?
                ", [$userId]);
                $deletionSummary['kept_for_legal_reasons']['users'] = 1;
            } else {
                Database::execute("DELETE FROM users WHERE id = ?", [$userId]);
                $deletionSummary['tables_affected']['users'] = 1;
                $deletionSummary['records_deleted'] += 1;
            }
            
            // Log the deletion action (before committing)
            self::logDataProcessing($userId, 'data_deleted', $deletionSummary);
            
            Database::commit();
            
            return $deletionSummary;
            
        } catch (Exception $e) {
            Database::rollback();
            error_log("Failed to delete user data: " . $e->getMessage());
            throw new \Exception("Data deletion failed. Please try again later.");
        }
    }
    
    /**
     * Log data processing activities
     * 
     * @param int|null $userId User ID (null for system actions)
     * @param string $action Action performed
     * @param array $context Additional context
     * @return bool Success status
     */
    public static function logDataProcessing(?int $userId, string $action, array $context = []): bool
    {
        try {
            Database::insert('data_processing_logs', [
                'user_id' => $userId,
                'action' => $action,
                'context' => json_encode($context, JSON_UNESCAPED_UNICODE),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Failed to log data processing: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clean up expired data according to retention policies
     * 
     * @return array Cleanup summary
     */
    public static function cleanupExpiredData(): array
    {
        $summary = [
            'cleanup_date' => date('Y-m-d H:i:s'),
            'tables_cleaned' => [],
            'total_deleted' => 0
        ];
        
        foreach (self::RETENTION_PERIODS as $table => $days) {
            try {
                $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
                
                switch($table) {
                    case 'case_snapshots':
                        $deleted = Database::execute("
                            DELETE FROM case_snapshots
                            WHERE created_at < ?
                            AND id NOT IN (
                                SELECT * FROM (
                                    SELECT MAX(id)
                                    FROM case_snapshots cs2
                                    GROUP BY monitored_case_id
                                ) as latest_snapshots
                            )
                        ", [$cutoffDate]);
                        break;

                    case 'notification_logs':
                        $deleted = Database::execute("
                            DELETE FROM notification_queue
                            WHERE created_at < ? AND status IN ('sent', 'failed')
                        ", [$cutoffDate]);
                        break;

                    case 'system_logs':
                        $deleted = Database::execute("
                            DELETE FROM system_logs WHERE created_at < ?
                        ", [$cutoffDate]);
                        break;

                    case 'rate_limit_attempts':
                        $deleted = Database::execute("
                            DELETE FROM rate_limit_attempts WHERE created_at < ?
                        ", [$cutoffDate]);
                        break;

                    default:
                        $deleted = 0;
                        break;
                }
                
                $summary['tables_cleaned'][$table] = $deleted;
                $summary['total_deleted'] += $deleted;
                
            } catch (Exception $e) {
                error_log("Failed to cleanup {$table}: " . $e->getMessage());
                $summary['tables_cleaned'][$table] = 'ERROR: ' . $e->getMessage();
            }
        }
        
        // Log the cleanup action
        self::logDataProcessing(null, 'data_cleanup', $summary);
        
        return $summary;
    }
    
    /**
     * Get data retention information
     * 
     * @return array Retention policies
     */
    public static function getRetentionPolicies(): array
    {
        return self::RETENTION_PERIODS;
    }
    
    /**
     * Generate privacy report for user
     * 
     * @param int $userId User ID
     * @return array Privacy report
     */
    public static function generatePrivacyReport(int $userId): array
    {
        $consents = self::getUserConsents($userId);
        
        $dataTypes = Database::fetchAll("
            SELECT 
                'monitored_cases' as data_type,
                COUNT(*) as record_count,
                MIN(created_at) as oldest_record,
                MAX(created_at) as newest_record
            FROM monitored_cases WHERE user_id = ?
            UNION ALL
            SELECT 
                'notifications' as data_type,
                COUNT(*) as record_count,
                MIN(created_at) as oldest_record,
                MAX(created_at) as newest_record
            FROM notification_queue WHERE user_id = ?
        ", [$userId, $userId]);
        
        return [
            'user_id' => $userId,
            'report_date' => date('Y-m-d H:i:s'),
            'consents' => $consents,
            'data_types' => $dataTypes,
            'retention_policies' => self::RETENTION_PERIODS,
            'rights' => [
                'access' => 'You can request a copy of your data',
                'rectification' => 'You can request corrections to your data',
                'erasure' => 'You can request deletion of your data',
                'portability' => 'You can request your data in a portable format',
                'restriction' => 'You can request restriction of processing',
                'objection' => 'You can object to certain processing'
            ]
        ];
    }
}
