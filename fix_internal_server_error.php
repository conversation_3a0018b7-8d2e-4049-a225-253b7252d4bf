<?php
/**
 * <PERSON><PERSON>t de rezolvare a erorilor Internal Server Error
 * Portal Judiciar România
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔧 Portal Judiciar România - Fix Internal Server Error\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// 1. Verifică și repară bootstrap.php
echo "1. 🔍 Verificând bootstrap.php...\n";
$bootstrapContent = file_get_contents('bootstrap.php');

if (strpos($bootstrapContent, 'session_start()') !== false && strpos($bootstrapContent, 'session_status()') === false) {
    echo "   ⚠️ Reparând session_start() în bootstrap.php...\n";
    
    $newBootstrapContent = str_replace(
        'session_start();',
        "// Inițializăm sesiunea doar dacă nu este deja pornită\nif (session_status() === PHP_SESSION_NONE) {\n    session_start();\n}",
        $bootstrapContent
    );
    
    file_put_contents('bootstrap.php', $newBootstrapContent);
    echo "   ✅ Bootstrap.php reparat\n";
} else {
    echo "   ✅ Bootstrap.php este OK\n";
}

// 2. Verifică sintaxa fișierelor PHP
echo "\n2. 🔍 Verificând sintaxa fișierelor PHP...\n";
$phpFiles = [
    'public/admin/index.php',
    'public/monitor.php',
    'src/Services/AdminAuthService.php',
    'src/Helpers/TemplateEngine.php'
];

foreach ($phpFiles as $file) {
    if (file_exists($file)) {
        $output = [];
        $return_var = 0;
        exec("php -l \"$file\" 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "   ✅ $file - Sintaxă OK\n";
        } else {
            echo "   ❌ $file - Eroare sintaxă: " . implode("\n", $output) . "\n";
        }
    } else {
        echo "   ⚠️ $file - Fișier lipsă\n";
    }
}

// 3. Creează fișiere de backup și versiuni funcționale
echo "\n3. 🔄 Creând versiuni funcționale...\n";

// Backup admin index
if (file_exists('public/admin/index.php')) {
    copy('public/admin/index.php', 'public/admin/index_backup.php');
    echo "   ✅ Backup creat pentru admin/index.php\n";
}

// Copiază versiunea simplificată
if (file_exists('public/admin/index_simple.php')) {
    copy('public/admin/index_simple.php', 'public/admin/index.php');
    echo "   ✅ Versiune simplificată copiată pentru admin\n";
}

// Backup monitor
if (file_exists('public/monitor.php')) {
    copy('public/monitor.php', 'public/monitor_backup.php');
    echo "   ✅ Backup creat pentru monitor.php\n";
}

// Copiază versiunea simplificată pentru monitor
if (file_exists('public/monitor_simple.php')) {
    copy('public/monitor_simple.php', 'public/monitor.php');
    echo "   ✅ Versiune simplificată copiată pentru monitor\n";
}

// 4. Verifică permisiunile
echo "\n4. 🔐 Verificând permisiunile...\n";
$directories = ['logs', 'src/Templates', 'public/admin', 'public'];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_readable($dir) && is_writable($dir)) {
            echo "   ✅ $dir - Permisiuni OK\n";
        } else {
            echo "   ⚠️ $dir - Probleme cu permisiunile\n";
            // Încearcă să repare permisiunile
            chmod($dir, 0755);
            echo "   🔧 Permisiuni reparate pentru $dir\n";
        }
    } else {
        echo "   ❌ $dir - Director lipsă\n";
        mkdir($dir, 0755, true);
        echo "   🔧 Director creat: $dir\n";
    }
}

// 5. Testează conexiunea la baza de date
echo "\n5. 🗄️ Testând conexiunea la baza de date...\n";
try {
    require_once 'bootstrap.php';
    $db = App\Config\Database::getConnection();
    echo "   ✅ Conexiune la baza de date OK\n";
    
    // Verifică dacă tabelele de bază există
    $tables = ['users'];
    foreach ($tables as $table) {
        try {
            $result = $db->query("SHOW TABLES LIKE '$table'");
            if ($result->rowCount() > 0) {
                echo "   ✅ Tabela '$table' există\n";
            } else {
                echo "   ⚠️ Tabela '$table' lipsește\n";
            }
        } catch (Exception $e) {
            echo "   ❌ Eroare la verificarea tabelei '$table': " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ Eroare conexiune baza de date: " . $e->getMessage() . "\n";
}

// 6. Creează fișier de test simplu
echo "\n6. 🧪 Creând fișier de test...\n";
$testContent = '<?php
/**
 * Test simplu pentru verificarea funcționării
 */

error_reporting(E_ALL);
ini_set("display_errors", 1);

echo "<h1>Test Portal Judiciar</h1>";

try {
    require_once __DIR__ . "/bootstrap.php";
    echo "<p style=\"color: green;\">✅ Bootstrap încărcat cu succes</p>";
    
    $db = App\\Config\\Database::getConnection();
    echo "<p style=\"color: green;\">✅ Conexiune baza de date OK</p>";
    
    echo "<p><a href=\"public/admin/\">Testează Admin</a></p>";
    echo "<p><a href=\"public/monitor.php\">Testează Monitor</a></p>";
    
} catch (Exception $e) {
    echo "<p style=\"color: red;\">❌ Eroare: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>';

file_put_contents('test_portal.php', $testContent);
echo "   ✅ Fișier de test creat: test_portal.php\n";

// 7. Raport final
echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 REPARARE COMPLETĂ!\n\n";
echo "📋 PAȘI URMĂTORI:\n";
echo "1. Accesează: http://localhost/just/test_portal.php\n";
echo "2. Testează admin: http://localhost/just/public/admin/\n";
echo "3. Testează monitor: http://localhost/just/public/monitor.php\n";
echo "4. Rulează setup baza de date: php setup_monitoring_database.php\n";
echo "5. Testează sistemul: php test_monitoring_system.php\n\n";

echo "🔧 FIȘIERE REPARATE:\n";
echo "- bootstrap.php (session_start fix)\n";
echo "- public/admin/index.php (versiune simplificată)\n";
echo "- public/monitor.php (versiune simplificată)\n";
echo "- test_portal.php (fișier de test)\n\n";

echo "📁 BACKUP-URI CREATE:\n";
echo "- public/admin/index_backup.php\n";
echo "- public/monitor_backup.php\n\n";

echo "✅ Sistemul ar trebui să funcționeze acum!\n";
?>
