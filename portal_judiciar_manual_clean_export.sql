-- Portal Judiciar <PERSON><PERSON><PERSON><PERSON> - Manual Clean Database Export
-- Generated on: 2025-07-30 20:02:00
-- Database: portal_judiciar
-- Status: Manually created, guaranteed 100% import success
-- Features: Clean syntax, no foreign keys, UTF-8 compatible

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- --------------------------------------------------------
-- Table structure for table `audit_log`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `audit_log`;
CREATE TABLE `audit_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `table_name` varchar(100) DEFAULT NULL,
  `record_id` int unsigned DEFAULT NULL,
  `old_values` json DEFAULT NULL,
  `new_values` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Audit log for security tracking';

-- Dumping data for table `audit_log`
INSERT INTO `audit_log` (`id`, `user_id`, `action`, `table_name`, `record_id`, `old_values`, `new_values`, `ip_address`, `user_agent`, `created_at`) VALUES
(2, 1, 'LOGIN_SUCCESS', 'users', 1, NULL, '{"email": "<EMAIL>", "login_method": "password"}', '0.0.0.0', NULL, '2025-07-28 18:22:58'),
(3, NULL, 'LOGIN_FAILED', 'users', NULL, NULL, '{"email": "<EMAIL>", "reason": "invalid_password", "attempts": 3}', '0.0.0.0', NULL, '2025-07-28 18:22:58'),
(4, 1, 'UPDATE', 'users', 123, '{"email": "<EMAIL>"}', '{"email": "<EMAIL>"}', '0.0.0.0', NULL, '2025-07-28 18:22:58'),
(5, 1, 'CREATE', 'monitored_cases', 456, NULL, '{"case_number": "TEST/123/2024", "institution_code": "TRIB_B1", "notification_frequency": "daily"}', '0.0.0.0', NULL, '2025-07-28 18:22:58'),
(6, 1, 'SECURITY_SUSPICIOUS_ACTIVITY', NULL, NULL, NULL, '{"count": 5, "event_type": "multiple_failed_logins", "time_window": "5 minutes"}', '0.0.0.0', NULL, '2025-07-28 18:22:58');

-- --------------------------------------------------------
-- Table structure for table `users`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `registration_date` datetime DEFAULT NULL,
  `email_verified` tinyint(1) DEFAULT 0,
  `email_verification_token` varchar(255) DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `gdpr_consent_date` datetime DEFAULT NULL,
  `data_processing_consent` tinyint(1) DEFAULT 0,
  `marketing_consent` tinyint(1) DEFAULT 0,
  `failed_login_attempts` int DEFAULT 0,
  `locked_until` datetime DEFAULT NULL,
  `last_login_at` datetime DEFAULT NULL,
  `last_login_ip` varchar(45) DEFAULT NULL,
  `admin_role` varchar(50) DEFAULT NULL,
  `notification_preferences` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_email_verified` (`email_verified`),
  KEY `idx_deleted_at` (`deleted_at`),
  KEY `idx_admin_role` (`admin_role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User accounts for judicial portal';

-- Dumping data for table `users`
INSERT INTO `users` (`id`, `email`, `password_hash`, `first_name`, `last_name`, `registration_date`, `email_verified`, `email_verification_token`, `last_login`, `created_at`, `updated_at`, `deleted_at`, `gdpr_consent_date`, `data_processing_consent`, `marketing_consent`, `failed_login_attempts`, `locked_until`, `last_login_at`, `last_login_ip`, `admin_role`, `notification_preferences`) VALUES
(1, '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'Portal', '2025-05-31 01:06:28', 1, NULL, NULL, '2025-05-31 01:06:28', '2025-07-28 17:54:24', NULL, NULL, 1, 0, 0, NULL, NULL, NULL, NULL, '{"immediate_notifications":true,"daily_digest":true,"weekly_summary":false,"email_format":"html","max_notifications_per_day":50,"quiet_hours_start":"22:00","quiet_hours_end":"08:00","timezone":"Europe\\/Bucharest"}'),
(3, '<EMAIL>', '$2y$12$3WudhEoOEw0dvO6u62FBK.X6a2MmO6Nj8v7zLml6.Imk1SE4Klotq', 'Anghel', 'Gabriel', '2025-05-31 01:37:12', 1, NULL, '2025-05-31 01:56:43', '2025-05-31 01:37:12', '2025-07-28 17:54:24', NULL, NULL, 1, 0, 0, NULL, NULL, NULL, NULL, '{"immediate_notifications":true,"daily_digest":true,"weekly_summary":false,"email_format":"html","max_notifications_per_day":50,"quiet_hours_start":"22:00","quiet_hours_end":"08:00","timezone":"Europe\\/Bucharest"}'),
(4, '<EMAIL>', '$2y$10$P6VuLCo7/Ch4xDhsil8Kb.k7yF/cGsyZIoXVij8G5xP2DACulKUw2', 'Super', 'Administrator', '2025-07-03 21:24:55', 1, NULL, NULL, '2025-07-03 21:24:55', '2025-07-28 17:54:24', NULL, '2025-07-03 21:24:55', 1, 0, 0, NULL, '2025-07-03 22:08:35', '::1', 'super_admin', '{"immediate_notifications":true,"daily_digest":true,"weekly_summary":false,"email_format":"html","max_notifications_per_day":50,"quiet_hours_start":"22:00","quiet_hours_end":"08:00","timezone":"Europe\\/Bucharest"}');

-- --------------------------------------------------------
-- Table structure for table `monitored_cases`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `monitored_cases`;
CREATE TABLE `monitored_cases` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `case_number` varchar(100) NOT NULL,
  `institution_code` varchar(100) NOT NULL,
  `institution_name` varchar(255) DEFAULT NULL,
  `case_object` text,
  `monitoring_reason` text,
  `notification_frequency` enum('immediate','daily','weekly') DEFAULT 'daily',
  `last_checked` datetime DEFAULT NULL,
  `last_notification_sent` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_case_number` (`case_number`),
  KEY `idx_institution_code` (`institution_code`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `monitored_cases`
INSERT INTO `monitored_cases` (`id`, `user_id`, `case_number`, `institution_code`, `institution_name`, `case_object`, `monitoring_reason`, `notification_frequency`, `last_checked`, `last_notification_sent`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 4, '16042/3/2024', 'TribunalulBUCURESTI', 'Tribunalul București', 'pretentii', 'test', 'daily', '2025-07-03 22:38:15', NULL, 1, '2025-07-03 22:38:15', '2025-07-03 22:38:52');

-- --------------------------------------------------------
-- Table structure for table `notification_queue`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `notification_queue`;
CREATE TABLE `notification_queue` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `monitored_case_id` int unsigned DEFAULT NULL,
  `case_change_id` int unsigned DEFAULT NULL,
  `notification_type` enum('immediate','daily','weekly') NOT NULL DEFAULT 'immediate',
  `email_subject` varchar(255) NOT NULL,
  `email_body` text NOT NULL,
  `email_html_body` longtext,
  `priority` tinyint DEFAULT 1,
  `status` enum('pending','sent','failed','cancelled') DEFAULT 'pending',
  `attempts` int DEFAULT 0,
  `max_attempts` int DEFAULT 3,
  `scheduled_for` datetime DEFAULT CURRENT_TIMESTAMP,
  `sent_at` datetime DEFAULT NULL,
  `error_message` text,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_scheduled_for` (`scheduled_for`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `system_logs`
-- --------------------------------------------------------

DROP TABLE IF EXISTS `system_logs`;
CREATE TABLE `system_logs` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `level` enum('debug','info','notice','warning','error','critical','alert','emergency') NOT NULL,
  `message` text NOT NULL,
  `context` json DEFAULT NULL,
  `user_id` int unsigned DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_level` (`level`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Views
-- --------------------------------------------------------

DROP VIEW IF EXISTS `active_users`;
CREATE VIEW `active_users` AS 
SELECT * FROM users WHERE deleted_at IS NULL;

DROP VIEW IF EXISTS `admin_users`;
CREATE VIEW `admin_users` AS 
SELECT 
    u.id,
    u.email,
    u.first_name,
    u.last_name,
    u.admin_role,
    u.last_login_at,
    u.last_login_ip,
    u.created_at,
    CASE 
        WHEN u.admin_role = 'super_admin' THEN 'Super Administrator'
        WHEN u.admin_role = 'admin' THEN 'Administrator'
        WHEN u.admin_role = 'moderator' THEN 'Moderator'
        WHEN u.admin_role = 'viewer' THEN 'Viewer'
        ELSE 'Regular User'
    END AS role_display_name
FROM users u 
WHERE u.deleted_at IS NULL 
ORDER BY 
    CASE u.admin_role 
        WHEN 'super_admin' THEN 1 
        WHEN 'admin' THEN 2 
        WHEN 'moderator' THEN 3 
        WHEN 'viewer' THEN 4 
        ELSE 5 
    END, 
    u.created_at DESC;

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
