<?php
/**
 * Institution Code Validator and Mapper
 * Handles SOAP API validation issues for court instance codes
 */

class InstitutionCodeValidator {
    
    /**
     * Known valid institution codes that work with SOAP API
     * These codes have been tested and confirmed to work
     */
    private static $validCodes = [
        // Curtea Supremă
        'InaltaCurtedeCASSATIESIJUSTITIE',
        
        // <PERSON><PERSON><PERSON><PERSON> - confirmed valid
        'CurteadeApelBACU',  // Added for Bacău appeals court
        'CurteadeApelBUCURESTI',
        '<PERSON><PERSON><PERSON>eApelCLUJ',
        'CurteadeApelCONSTANTA',
        'CurteadeApelCRAIOVA',
        'CurteadeApelGALATI',
        'CurteadeApelIASI',
        'CurteadeApelORADEA',
        'C<PERSON>eadeApelPITESTI',
        'CurteadeApelPLOIESTI',
        'CurteadeApelSUCEAVA',
        'CurteadeApelTIMISOARA',
        
        // Tribunale - major ones confirmed valid
        'TribunalulBUCURESTI',
        'TribunalulCLUJ',
        'TribunalulCONSTANTA',
        'TribunalulIASI',
        'TribunalulTIMIS',
        'TribunalulARGES',
        // 'TribunalulBACU', // Removed - causes SOAP API errors, mapped to CurteadeApelBACU instead
        'TribunalulBIHOR',
        'TribunalulBRASSOV',
        'TribunalulGALATI',
        'TribunalulPRAHOVA',
        
        // Judecătorii - major ones confirmed valid
        'JudecatoriaSECTORUL1BUCURESTI',
        'JudecatoriaSECTORUL2BUCURESTI',
        'JudecatoriaSECTORUL3BUCURESTI',
        'JudecatoriaSECTORUL4BUCURESTI',
        'JudecatoriaSECTORUL5BUCURESTI',
        'JudecatoriaSECTORUL6BUCURESTI',
        'JudecatoriaCLUJNAPOCA',
        'JudecatoriaCONSTANTA',
        'JudecatoriaIASI',
        'JudecatoriaTIMISOARA',
        'JudecatoriaPLOIESTI',
        'JudecatoriaBRASSOV',
        'JudecatoriaGALATI',
        'JudecatoriaBACU',
        'JudecatoriaORADEA',
        'JudecatoriaPITESTI',
        'JudecatoriaROMAN'
    ];
    
    /**
     * Known invalid codes that cause SOAP API errors
     * Maps invalid codes to suggested valid alternatives
     */
    private static $invalidCodeMappings = [
        // Common problematic patterns - typos and incorrect codes
        'JudecatoriaROMANI' => 'JudecatoriaROMAN', // Correct typo: ROMANI -> ROMAN
        'JudecatoriaVICTORIA' => 'TribunalulBRASSOV',
        'JudecatoriaFAGARAS' => 'TribunalulBRASSOV',
        'JudecatoriaSACELE' => 'TribunalulBRASSOV',
        'JudecatoriaZARNESTI' => 'TribunalulBRASSOV',

        // SOAP API incompatible codes - map to working alternatives
        'TribunalulBACU' => 'CurteadeApelBACU', // Tribunal Bacău not recognized by SOAP, use Appeals Court
        'TribunalulSUCEAVA' => 'CurteadeApelSUCEAVA', // Map to appeals court for SOAP compatibility

        // Add more mappings as they are discovered
    ];
    
    /**
     * Codes that are known to be problematic but don't have clear mappings
     */
    private static $knownProblematicCodes = [
        'JudecatoriaROMANI', // Confirmed problematic from user report
        'JudecatoriaVICTORIA',
        'JudecatoriaFAGARAS',
        'JudecatoriaSACELE',
        'JudecatoriaZARNESTI',
        // Add more as discovered through validation
    ];
    
    /**
     * Check if an institution code is valid for SOAP API
     * 
     * @param string $code Institution code to validate
     * @return bool True if valid, false otherwise
     */
    public static function isValidCode($code) {
        return in_array($code, self::$validCodes);
    }
    
    /**
     * Get a valid alternative for an invalid institution code
     * 
     * @param string $code Invalid institution code
     * @return string|null Valid alternative code or null if no mapping exists
     */
    public static function getValidAlternative($code) {
        return self::$invalidCodeMappings[$code] ?? null;
    }
    
    /**
     * Check if a code is known to be problematic
     * 
     * @param string $code Institution code to check
     * @return bool True if known to be problematic
     */
    public static function isKnownProblematic($code) {
        return in_array($code, self::$knownProblematicCodes) || 
               array_key_exists($code, self::$invalidCodeMappings);
    }
    
    /**
     * Validate and potentially map an institution code for SOAP API use
     * 
     * @param string $code Original institution code
     * @return array Result with 'code', 'mapped', 'warning' keys
     */
    public static function validateAndMap($code) {
        $result = [
            'code' => $code,
            'mapped' => false,
            'warning' => null,
            'fallback_needed' => false
        ];
        
        // Empty code is always valid (means "all institutions")
        if (empty($code)) {
            return $result;
        }
        
        // Check if code is already valid
        if (self::isValidCode($code)) {
            return $result;
        }
        
        // Try to find a valid alternative
        $alternative = self::getValidAlternative($code);
        if ($alternative) {
            $result['code'] = $alternative;
            $result['mapped'] = true;
            $result['warning'] = "Codul instituției a fost înlocuit cu o alternativă validă pentru compatibilitate SOAP API.";
            return $result;
        }
        
        // Code is problematic and no mapping exists
        if (self::isKnownProblematic($code)) {
            $result['fallback_needed'] = true;
            $result['warning'] = "Această instituție nu este compatibilă cu API-ul SOAP. Căutarea va fi efectuată local.";
            return $result;
        }
        
        // Unknown code - might be problematic
        $result['warning'] = "Codul instituției nu a fost testat cu API-ul SOAP. Rezultatele pot fi limitate.";
        return $result;
    }
    
    /**
     * Get all valid institution codes
     * 
     * @return array List of valid codes
     */
    public static function getValidCodes() {
        return self::$validCodes;
    }
    
    /**
     * Get all known invalid codes with their mappings
     * 
     * @return array Invalid codes mapped to valid alternatives
     */
    public static function getInvalidCodeMappings() {
        return self::$invalidCodeMappings;
    }
    
    /**
     * Add a new valid code to the list
     * 
     * @param string $code Valid institution code
     */
    public static function addValidCode($code) {
        if (!in_array($code, self::$validCodes)) {
            self::$validCodes[] = $code;
        }
    }
    
    /**
     * Add a new invalid code mapping
     * 
     * @param string $invalidCode Invalid institution code
     * @param string $validAlternative Valid alternative code
     */
    public static function addInvalidCodeMapping($invalidCode, $validAlternative) {
        self::$invalidCodeMappings[$invalidCode] = $validAlternative;
    }
    
    /**
     * Get institution type based on code pattern
     * 
     * @param string $code Institution code
     * @return string Institution type
     */
    public static function getInstitutionType($code) {
        if (strpos($code, 'InaltaCurte') === 0) {
            return 'supreme_court';
        } elseif (strpos($code, 'CurteadeApel') === 0) {
            return 'appeal_court';
        } elseif (strpos($code, 'Tribunalul') === 0) {
            return 'tribunal';
        } elseif (strpos($code, 'Judecatoria') === 0) {
            return 'local_court';
        } elseif (strpos($code, 'CurteaMilitara') === 0) {
            return 'military_court';
        } else {
            return 'unknown';
        }
    }
    
    /**
     * Get user-friendly error message for invalid institution codes
     * 
     * @param string $code Invalid institution code
     * @param string $institutionName Human-readable institution name
     * @return string User-friendly error message
     */
    public static function getErrorMessage($code, $institutionName = null) {
        $name = $institutionName ?: $code;
        
        $alternative = self::getValidAlternative($code);
        if ($alternative) {
            return "Instituția '{$name}' nu este compatibilă cu API-ul SOAP, dar căutarea va continua cu o alternativă validă.";
        }
        
        if (self::isKnownProblematic($code)) {
            return "Instituția '{$name}' nu este compatibilă cu API-ul SOAP. Căutarea va fi efectuată doar local.";
        }
        
        return "Codul instituției '{$name}' nu a fost validat cu API-ul SOAP. Rezultatele pot fi limitate.";
    }
}
