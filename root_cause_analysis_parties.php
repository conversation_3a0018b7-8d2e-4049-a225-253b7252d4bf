<?php
/**
 * Root Cause Analysis: Party Display Issues
 * Comprehensive analysis of the party extraction and display pipeline
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

// Test cases
$testCases = [
    ['numar' => '130/98/2022', 'institutie' => 'TribunalulIALOMITA', 'searchParty' => 'Saragea Tudorita'],
    ['numar' => '130/98/2022', 'institutie' => 'TribunalulIALOMITA', 'searchParty' => 'SARAGEA TUDORIŢA'],
    ['numar' => '130/98/2022', 'institutie' => 'TribunalulIALOMITA', 'searchParty' => 'ŞERBĂNESCU ELENA']
];

echo "<!DOCTYPE html>";
echo "<html><head><title>Root Cause Analysis - Party Display Issues</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    .highlight { background: yellow; font-weight: bold; }
    .code { background: #f8f9fa; padding: 10px; font-family: monospace; white-space: pre-wrap; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .test-case { border: 2px solid #007bff; margin: 20px 0; padding: 15px; }
</style></head><body>";

echo "<h1>🔍 Root Cause Analysis: Party Display Issues</h1>";
echo "<hr>";

try {
    $dosarService = new \App\Services\DosarService();
    
    foreach ($testCases as $index => $testCase) {
        $numarDosar = $testCase['numar'];
        $institutie = $testCase['institutie'];
        $searchParty = $testCase['searchParty'];
        
        echo "<div class='test-case'>";
        echo "<h2>🧪 Test Case " . ($index + 1) . "</h2>";
        echo "<p><strong>Case:</strong> {$numarDosar}</p>";
        echo "<p><strong>Institution:</strong> {$institutie}</p>";
        echo "<p><strong>Search Party:</strong> <span class='highlight'>{$searchParty}</span></p>";
        
        // Step 1: Backend Processing Analysis
        echo "<div class='section'>";
        echo "<h3>🔧 Backend Processing Analysis</h3>";
        
        $processedDosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
        
        if (!$processedDosar) {
            echo "<p class='error'>❌ Failed to get case data</p>";
            continue;
        }
        
        $allParties = $processedDosar->parti ?? [];
        echo "<p><strong>Total Parties in Backend:</strong> " . count($allParties) . "</p>";
        
        // Search for the party in backend data
        $backendMatches = [];
        foreach ($allParties as $index => $party) {
            $partyName = $party->nume ?? '';
            
            // Exact match
            if (strcasecmp($partyName, $searchParty) === 0) {
                $backendMatches[] = [
                    'type' => 'exact',
                    'index' => $index + 1,
                    'name' => $partyName,
                    'quality' => $party->calitate ?? '',
                    'source' => $party->source ?? ''
                ];
            }
            // Partial match
            elseif (stripos($partyName, $searchParty) !== false || stripos($searchParty, $partyName) !== false) {
                $backendMatches[] = [
                    'type' => 'partial',
                    'index' => $index + 1,
                    'name' => $partyName,
                    'quality' => $party->calitate ?? '',
                    'source' => $party->source ?? ''
                ];
            }
            // Diacritics-normalized match
            else {
                $normalizedPartyName = normalizeDiacritics($partyName);
                $normalizedSearchParty = normalizeDiacritics($searchParty);
                if (strcasecmp($normalizedPartyName, $normalizedSearchParty) === 0) {
                    $backendMatches[] = [
                        'type' => 'diacritics',
                        'index' => $index + 1,
                        'name' => $partyName,
                        'quality' => $party->calitate ?? '',
                        'source' => $party->source ?? ''
                    ];
                }
            }
        }
        
        if (!empty($backendMatches)) {
            echo "<p class='success'>✅ Found " . count($backendMatches) . " matches in backend data:</p>";
            echo "<table>";
            echo "<tr><th>Type</th><th>Index</th><th>Name</th><th>Quality</th><th>Source</th></tr>";
            foreach ($backendMatches as $match) {
                echo "<tr>";
                echo "<td>{$match['type']}</td>";
                echo "<td>{$match['index']}</td>";
                echo "<td class='highlight'>" . htmlspecialchars($match['name']) . "</td>";
                echo "<td>" . htmlspecialchars($match['quality']) . "</td>";
                echo "<td>" . htmlspecialchars($match['source']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ Party NOT found in backend data</p>";
        }
        
        echo "</div>";
        
        // Step 2: Frontend Display Simulation
        echo "<div class='section'>";
        echo "<h3>🖥️ Frontend Display Simulation</h3>";
        
        // Simulate the frontend processing that happens in detalii_dosar.php
        $validParti = [];
        $filteredParti = [];
        
        foreach ($allParties as $parteIndex => $parte) {
            $nume = trim($parte->nume ?? '');
            $calitate = trim($parte->calitate ?? '');
            
            // Apply the same validation logic as frontend
            if (empty($nume) || strlen($nume) < 2) {
                $filteredParti[] = [
                    'reason' => 'empty_or_short_name',
                    'data' => $parte,
                    'index' => $parteIndex
                ];
                continue;
            }
            
            // Check for duplicates (simplified version)
            $isDuplicate = false;
            foreach ($validParti as $existingParte) {
                if (strcasecmp($existingParte->nume, $nume) === 0) {
                    $isDuplicate = true;
                    break;
                }
            }
            
            if ($isDuplicate) {
                $filteredParti[] = [
                    'reason' => 'duplicate',
                    'data' => $parte,
                    'index' => $parteIndex
                ];
                continue;
            }
            
            // Add to valid parties
            $parte->originalIndex = $parteIndex;
            $parte->displayIndex = count($validParti) + 1;
            $validParti[] = $parte;
        }
        
        echo "<p><strong>Valid Parties for Display:</strong> " . count($validParti) . "</p>";
        echo "<p><strong>Filtered Out Parties:</strong> " . count($filteredParti) . "</p>";
        
        // Check if our search party is in valid parties
        $foundInValid = false;
        foreach ($validParti as $party) {
            $partyName = $party->nume ?? '';
            if (stripos($partyName, $searchParty) !== false ||
                strcasecmp(normalizeDiacritics($partyName), normalizeDiacritics($searchParty)) === 0) {
                $foundInValid = true;
                echo "<p class='success'>✅ Party found in valid display list at position {$party->displayIndex}</p>";
                break;
            }
        }
        
        if (!$foundInValid) {
            echo "<p class='warning'>⚠️ Party NOT found in valid display list</p>";
            
            // Check if it was filtered out
            foreach ($filteredParti as $filtered) {
                $partyName = $filtered['data']->nume ?? '';
                if (stripos($partyName, $searchParty) !== false ||
                    strcasecmp(normalizeDiacritics($partyName), normalizeDiacritics($searchParty)) === 0) {
                    echo "<p class='error'>❌ Party was FILTERED OUT! Reason: {$filtered['reason']}</p>";
                    echo "<p class='info'>Filtered party data: " . htmlspecialchars($partyName) . "</p>";
                }
            }
        }
        
        echo "</div>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2 class='error'>❌ Error Occurred</h2>";
    echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p class='info'>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

// Helper function for diacritics normalization
function normalizeDiacritics($text) {
    $diacritics = [
        'ă' => 'a', 'â' => 'a', 'î' => 'i', 'ș' => 's', 'ț' => 't',
        'Ă' => 'A', 'Â' => 'A', 'Î' => 'I', 'Ș' => 'S', 'Ț' => 'T',
        'ţ' => 't', 'ş' => 's', 'Ţ' => 'T', 'Ş' => 'S'
    ];
    
    return strtr($text, $diacritics);
}

echo "<hr>";
echo "<p><em>Root cause analysis completed at " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
