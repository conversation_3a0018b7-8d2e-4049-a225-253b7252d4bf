<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 CHECKING SPECIFIC MISSING NAMES\n";
echo "===================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    echo "Current total parties: " . count($dosar->parti) . "\n\n";
    
    // Get current decision text parties
    $currentDecisionParties = [];
    foreach ($dosar->parti as $party) {
        $partyArray = (array) $party;
        if (isset($partyArray['source']) && $partyArray['source'] === 'decision_text') {
            $currentDecisionParties[] = $partyArray['nume'];
        }
    }
    
    echo "Current decision text parties: " . count($currentDecisionParties) . "\n\n";
    
    // Test specific names that should be unique
    $testNames = [
        'Aron Bogdan-Cristian',
        'Badic Angela', 
        'Barbu Adrian-Cristian',
        'Barbu Vasilica-Steluţa',
        'Boerescu Emilia-Adriana',
        'Câlţea Lică',
        'Chiţu Gheorghe',
        'Cioban Gabriel-Stelian',
        'Copilău Gheorghe',
        'Creţu Aurica-Lili'
    ];
    
    echo "🔍 CHECKING SPECIFIC NAMES:\n";
    echo "===========================\n\n";
    
    foreach ($testNames as $testName) {
        echo "Testing: \"{$testName}\"\n";
        
        // Check if it exists in current decision text parties
        $found = false;
        foreach ($currentDecisionParties as $currentName) {
            if (strtolower(trim($testName)) === strtolower(trim($currentName))) {
                $found = true;
                echo "  Found as: \"{$currentName}\"\n";
                break;
            }
        }
        
        if (!$found) {
            echo "  Status: NOT FOUND\n";
        }
        
        echo "\n";
    }
    
    // Show some current decision text parties for comparison
    echo "📋 SAMPLE CURRENT DECISION TEXT PARTIES:\n";
    echo "========================================\n\n";
    for ($i = 0; $i < min(20, count($currentDecisionParties)); $i++) {
        echo ($i + 1) . ". " . $currentDecisionParties[$i] . "\n";
    }
    
    echo "\n✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
