<?php
/**
 * Data Flow Verification Test
 * Verify complete data flow from DosarService to frontend display
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Test case with known high party count
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Data Flow Verification Test</title>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
.warning { background: #fff3cd; border-left-color: #ffc107; }
.error { background: #f8d7da; border-left-color: #dc3545; }
.success { background: #d4edda; border-left-color: #28a745; }
.code { background: #f1f3f4; padding: 10px; font-family: monospace; white-space: pre-wrap; }
.party-row { border-bottom: 1px solid #eee; padding: 5px; }
.party-row:nth-child(even) { background-color: #f9f9f9; }
.debug-info { background: #e3f2fd; border: 1px solid #2196f3; padding: 10px; margin: 10px 0; }
</style></head><body>";

echo "<h1>🔍 Data Flow Verification Test</h1>";
echo "<p><strong>Case:</strong> {$numarDosar} from {$institutie}</p>";
echo "<p><strong>Objective:</strong> Trace complete data flow from DosarService to frontend</p>";
echo "<hr>";

try {
    // Step 1: Backend Data Retrieval
    echo "<div class='section'>";
    echo "<h2>📊 Step 1: Backend Data Retrieval</h2>";
    
    $dosarService = new DosarService();
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if (!$dosar) {
        echo "<div class='error'>❌ Failed to retrieve case data</div>";
        exit;
    }
    
    $totalParties = count($dosar->parti ?? []);
    echo "<p><strong>✅ Case data retrieved successfully</strong></p>";
    echo "<p><strong>Total parties in \$dosar->parti:</strong> {$totalParties}</p>";
    
    if ($totalParties >= 340) {
        echo "<div class='success'>✅ Backend has expected high party count ({$totalParties})</div>";
    } elseif ($totalParties == 100) {
        echo "<div class='warning'>⚠️ Backend shows only 100 parties (SOAP API limit issue)</div>";
    } else {
        echo "<div class='warning'>⚠️ Unexpected party count: {$totalParties}</div>";
    }
    echo "</div>";
    
    // Step 2: Data Structure Analysis
    echo "<div class='section'>";
    echo "<h2>🔬 Step 2: Data Structure Analysis</h2>";
    
    if (!empty($dosar->parti)) {
        echo "<p><strong>First 5 parties structure:</strong></p>";
        echo "<div class='code'>";
        for ($i = 0; $i < min(5, count($dosar->parti)); $i++) {
            echo "Party " . ($i + 1) . ":\n";
            echo "  Name: '" . htmlspecialchars($dosar->parti[$i]['nume'] ?? 'N/A') . "'\n";
            echo "  Quality: '" . htmlspecialchars($dosar->parti[$i]['calitate'] ?? 'N/A') . "'\n";
            echo "  Source: '" . htmlspecialchars($dosar->parti[$i]['source'] ?? 'N/A') . "'\n";
            echo "\n";
        }
        echo "</div>";
        
        if (count($dosar->parti) > 5) {
            echo "<p><strong>Last 5 parties structure:</strong></p>";
            echo "<div class='code'>";
            $start = max(0, count($dosar->parti) - 5);
            for ($i = $start; $i < count($dosar->parti); $i++) {
                echo "Party " . ($i + 1) . ":\n";
                echo "  Name: '" . htmlspecialchars($dosar->parti[$i]['nume'] ?? 'N/A') . "'\n";
                echo "  Quality: '" . htmlspecialchars($dosar->parti[$i]['calitate'] ?? 'N/A') . "'\n";
                echo "  Source: '" . htmlspecialchars($dosar->parti[$i]['source'] ?? 'N/A') . "'\n";
                echo "\n";
            }
            echo "</div>";
        }
    }
    echo "</div>";
    
    // Step 3: Template Variable Preparation
    echo "<div class='section'>";
    echo "<h2>🎨 Step 3: Template Variable Preparation</h2>";
    echo "<p>Simulating the exact variable preparation as in detalii_dosar.php:</p>";
    
    // This is exactly what happens in detalii_dosar.php
    $loop_index = 0;
    $totalPartiCount = count($dosar->parti);
    
    echo "<div class='debug-info'>";
    echo "<strong>Template Variables:</strong><br>";
    echo "\$loop_index = {$loop_index}<br>";
    echo "\$totalPartiCount = {$totalPartiCount}<br>";
    echo "\$dosar->parti is " . (is_array($dosar->parti) ? "array" : gettype($dosar->parti)) . "<br>";
    echo "count(\$dosar->parti) = " . count($dosar->parti) . "<br>";
    echo "</div>";
    echo "</div>";
    
    // Step 4: Frontend Rendering Simulation
    echo "<div class='section'>";
    echo "<h2>🖥️ Step 4: Frontend Rendering Simulation</h2>";
    echo "<p>Simulating the exact foreach loop from detalii_dosar.php:</p>";
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped' id='tabelParti'>";
    echo "<thead>";
    echo "<tr>";
    echo "<th>Loop Index</th>";
    echo "<th>Array Index</th>";
    echo "<th>Nume</th>";
    echo "<th>Calitate</th>";
    echo "<th>Source</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    $renderedCount = 0;
    
    // Exact same loop as in detalii_dosar.php
    foreach ($dosar->parti as $parteIndex => $parte) {
        $loop_index++;
        $renderedCount++;
        
        echo "<tr class='parte-row' data-index='{$loop_index}' data-party-id='{$parteIndex}'>";
        echo "<td><span class='badge bg-primary'>{$loop_index}</span></td>";
        echo "<td><span class='badge bg-secondary'>{$parteIndex}</span></td>";
        echo "<td class='nume-parte'>" . htmlspecialchars($parte['nume'] ?? 'N/A') . "</td>";
        echo "<td class='calitate-parte'>" . htmlspecialchars($parte['calitate'] ?? '-') . "</td>";
        echo "<td><span class='badge bg-info'>" . htmlspecialchars($parte['source'] ?? 'unknown') . "</span></td>";
        echo "</tr>";
        
        // Progress indicator every 50 parties
        if ($renderedCount % 50 == 0) {
            echo "<tr><td colspan='5' class='text-center bg-light'>";
            echo "<em>--- Rendered {$renderedCount} of {$totalPartiCount} parties ---</em>";
            echo "</td></tr>";
        }
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    echo "<div class='debug-info'>";
    echo "<strong>Rendering Results:</strong><br>";
    echo "Total parties in backend: {$totalPartiCount}<br>";
    echo "Parties rendered in loop: {$renderedCount}<br>";
    echo "Final loop_index: {$loop_index}<br>";
    echo "Loop completed: " . ($renderedCount == $totalPartiCount ? "✅ YES" : "❌ NO") . "<br>";
    echo "</div>";
    echo "</div>";
    
    // Step 5: JavaScript DOM Verification
    echo "<div class='section'>";
    echo "<h2>🔧 Step 5: JavaScript DOM Verification</h2>";
    echo "<div id='jsResults'></div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Exception Occurred:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    console.log('🔍 Data Flow Verification - JavaScript Analysis');";
echo "    ";
echo "    // Count DOM elements";
echo "    const table = document.getElementById('tabelParti');";
echo "    const tbody = table ? table.querySelector('tbody') : null;";
echo "    const allRows = tbody ? tbody.querySelectorAll('tr') : [];";
echo "    const partyRows = tbody ? tbody.querySelectorAll('tr.parte-row') : [];";
echo "    const progressRows = tbody ? tbody.querySelectorAll('tr:not(.parte-row)') : [];";
echo "    ";
echo "    console.log('DOM Analysis:', {";
echo "        tableFound: !!table,";
echo "        tbodyFound: !!tbody,";
echo "        totalRows: allRows.length,";
echo "        partyRows: partyRows.length,";
echo "        progressRows: progressRows.length";
echo "    });";
echo "    ";
echo "    // Check for hidden rows";
echo "    const hiddenRows = Array.from(partyRows).filter(row => {";
echo "        const style = window.getComputedStyle(row);";
echo "        return style.display === 'none' || style.visibility === 'hidden';";
echo "    });";
echo "    ";
echo "    // Display results";
echo "    const resultsDiv = document.getElementById('jsResults');";
echo "    if (resultsDiv) {";
echo "        let html = '<div class=\"alert alert-info\">';";
echo "        html += '<h5>JavaScript DOM Analysis Results:</h5>';";
echo "        html += '<ul>';";
echo "        html += '<li><strong>Table found:</strong> ' + (table ? 'YES' : 'NO') + '</li>';";
echo "        html += '<li><strong>Total rows in DOM:</strong> ' + allRows.length + '</li>';";
echo "        html += '<li><strong>Party rows (.parte-row):</strong> ' + partyRows.length + '</li>';";
echo "        html += '<li><strong>Progress indicator rows:</strong> ' + progressRows.length + '</li>';";
echo "        html += '<li><strong>Hidden party rows:</strong> ' + hiddenRows.length + '</li>';";
echo "        html += '</ul>';";
echo "        ";
echo "        if (partyRows.length > 0) {";
echo "            const firstParty = partyRows[0].querySelector('.nume-parte')?.textContent || 'N/A';";
echo "            const lastParty = partyRows[partyRows.length-1].querySelector('.nume-parte')?.textContent || 'N/A';";
echo "            html += '<p><strong>First party in DOM:</strong> ' + firstParty + '</p>';";
echo "            html += '<p><strong>Last party in DOM:</strong> ' + lastParty + '</p>';";
echo "        }";
echo "        ";
echo "        if (hiddenRows.length > 0) {";
echo "            html += '<div class=\"alert alert-warning mt-2\">';";
echo "            html += '<strong>⚠️ Warning:</strong> Found ' + hiddenRows.length + ' hidden party rows';";
echo "            html += '</div>';";
echo "        }";
echo "        ";
echo "        html += '</div>';";
echo "        resultsDiv.innerHTML = html;";
echo "    }";
echo "    ";
echo "    // Final verification";
echo "    const expectedParties = {$totalParties};";
echo "    const actualParties = partyRows.length;";
echo "    ";
echo "    console.log('Final Verification:', {";
echo "        expectedParties: expectedParties,";
echo "        actualParties: actualParties,";
echo "        match: expectedParties === actualParties";
echo "    });";
echo "    ";
echo "    if (expectedParties !== actualParties) {";
echo "        console.error('❌ MISMATCH: Expected ' + expectedParties + ' parties, but found ' + actualParties + ' in DOM');";
echo "    } else {";
echo "        console.log('✅ SUCCESS: Party count matches between backend and frontend');";
echo "    }";
echo "});";
echo "</script>";

echo "</body></html>";
?>
