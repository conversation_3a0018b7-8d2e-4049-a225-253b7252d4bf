<?php
// Comprehensive party extraction analysis to achieve exactly 380 parties

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

function analyzeCompleteDecisionText($caseNumber, $institution, $expectedParties = 380) {
    echo "=== COMPREHENSIVE ANALYSIS: $caseNumber - $institution ===" . PHP_EOL;
    echo "Target: $expectedParties parties" . PHP_EOL . PHP_EOL;
    
    try {
        $dosarService = new DosarService();
        
        // Get raw SOAP response
        $reflection = new ReflectionClass($dosarService);
        $method = $reflection->getMethod('executeSoapCallWithRetry');
        $method->setAccessible(true);
        
        $searchParams = [
            'numarDosar' => $caseNumber,
            'institutie' => $institution,
            'obiectDosar' => '',
            'numeParte' => '',
            'dataStart' => null,
            'dataStop' => null,
            'dataUltimaModificareStart' => null,
            'dataUltimaModificareStop' => null
        ];
        
        $rawResponse = $method->invoke($dosarService, 'CautareDosare2', $searchParams, "Comprehensive analysis");
        
        if (!$rawResponse || !isset($rawResponse->CautareDosare2Result)) {
            echo "❌ No SOAP response data" . PHP_EOL;
            return;
        }
        
        $dosare = $rawResponse->CautareDosare2Result->Dosar ?? null;
        if (!$dosare) {
            echo "❌ No Dosar in SOAP response" . PHP_EOL;
            return;
        }
        
        if (!is_array($dosare)) {
            $dosare = [$dosare];
        }
        
        $dosar = null;
        foreach ($dosare as $d) {
            if (isset($d->numar) && $d->numar === $caseNumber && $d->institutie === $institution) {
                $dosar = $d;
                break;
            }
        }
        
        if (!$dosar) {
            echo "❌ Case not found" . PHP_EOL;
            return;
        }
        
        // Analyze SOAP API parties
        $soapParties = [];
        if (isset($dosar->parti) && isset($dosar->parti->DosarParte)) {
            $partiData = $dosar->parti->DosarParte;
            if (!is_array($partiData)) {
                $partiData = [$partiData];
            }
            $soapParties = $partiData;
        }
        
        echo "📊 SOAP API Analysis:" . PHP_EOL;
        echo "  - SOAP parties found: " . count($soapParties) . PHP_EOL;
        echo "  - Remaining needed from decision text: " . ($expectedParties - count($soapParties)) . PHP_EOL . PHP_EOL;
        
        // Analyze decision text in all sessions
        if (isset($dosar->sedinte) && isset($dosar->sedinte->DosarSedinta)) {
            $sedinte = $dosar->sedinte->DosarSedinta;
            if (!is_array($sedinte)) {
                $sedinte = [$sedinte];
            }
            
            $allDecisionText = '';
            foreach ($sedinte as $index => $sedinta) {
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $solutieText = $sedinta->solutieSumar;
                    $allDecisionText .= $solutieText . ' ';
                    
                    echo "📄 SESSION " . ($index + 1) . " ANALYSIS (" . strlen($solutieText) . " chars):" . PHP_EOL;
                    
                    // Test current extraction
                    $currentExtracted = testCurrentExtraction($solutieText);
                    echo "  - Current extraction: " . count($currentExtracted) . " parties" . PHP_EOL;
                    
                    // Comprehensive pattern analysis
                    analyzeAllPatterns($solutieText, $institution);
                    
                    echo PHP_EOL;
                }
            }
            
            // Combined analysis
            echo "🔍 COMBINED DECISION TEXT ANALYSIS:" . PHP_EOL;
            echo "  - Total decision text length: " . strlen($allDecisionText) . " chars" . PHP_EOL;
            
            // Look for ALL potential party patterns
            findAllPotentialParties($allDecisionText, $expectedParties - count($soapParties));
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . PHP_EOL;
    }
}

function testCurrentExtraction($solutieText) {
    // Test current patterns
    $parties = [];
    
    // Pattern 1: Enhanced creditors
    if (preg_match('/formulate de creditorii\s*([^.]+(?:\.[^.]*)*?)(?:\.\s*(?:Suma|Dispune|Admite|Respinge|În|Pentru)|\s*$)/is', $solutieText, $matches)) {
        $partiesText = $matches[1];
        $partyNames = explode(';', $partiesText);
        $parties = array_merge($parties, array_filter(array_map('trim', $partyNames)));
    }
    
    // Pattern 2: Enhanced appellants
    if (preg_match('/apelurile formulate de apelanţii\s*([^.]+(?:\.[^.]*)*?)(?:\.\s*(?:Dispune|Admite|Respinge|În|Pentru|împotriva)|\s*$)/is', $solutieText, $matches)) {
        $partiesText = $matches[1];
        $partyNames = explode(',', $partiesText);
        $parties = array_merge($parties, array_filter(array_map('trim', $partyNames)));
    }
    
    // Pattern 3: Anulează appellants
    if (preg_match('/Anulează apelurile formulate de apelanţii (.+?)(?:\s+împotriva|\s+pentru|\s+în|\s+privind|\.|$)/is', $solutieText, $matches)) {
        $partiesText = $matches[1];
        $partyNames = explode(',', $partiesText);
        $parties = array_merge($parties, array_filter(array_map('trim', $partyNames)));
    }
    
    return array_unique($parties);
}

function analyzeAllPatterns($solutieText, $institution) {
    echo "  🔍 Pattern Analysis:" . PHP_EOL;
    
    // Count semicolon-separated lists (creditors)
    if (preg_match_all('/([A-ZĂÂÎȘȚŢ][^;]+(?:;[^;]+){5,})/u', $solutieText, $semiMatches)) {
        foreach ($semiMatches[1] as $i => $match) {
            $count = count(explode(';', $match));
            echo "    - Semicolon list " . ($i + 1) . ": $count items - " . substr($match, 0, 100) . "..." . PHP_EOL;
        }
    }
    
    // Count comma-separated lists (appellants)
    if (preg_match_all('/([A-ZĂÂÎȘȚŢ][^,]+(?:,[^,]+){10,})/u', $solutieText, $commaMatches)) {
        foreach ($commaMatches[1] as $i => $match) {
            $count = count(explode(',', $match));
            echo "    - Comma list " . ($i + 1) . ": $count items - " . substr($match, 0, 100) . "..." . PHP_EOL;
        }
    }
    
    // Look for specific legal contexts
    $contexts = [
        'creditorii' => '/creditorii?\s+([^.]+)/i',
        'apelanţii' => '/apelanţii?\s+([^.]+)/i',
        'părţile' => '/părţile?\s+([^.]+)/i',
        'intervenienţii' => '/intervenienţii?\s+([^.]+)/i'
    ];
    
    foreach ($contexts as $context => $pattern) {
        if (preg_match_all($pattern, $solutieText, $matches)) {
            echo "    - '$context' mentions: " . count($matches[0]) . PHP_EOL;
        }
    }
}

function findAllPotentialParties($allText, $targetCount) {
    echo "  🎯 Searching for $targetCount additional parties:" . PHP_EOL;
    
    // Extract ALL potential names using comprehensive patterns
    $allPotentialNames = [];
    
    // Pattern 1: Any capitalized word followed by other capitalized words (names)
    if (preg_match_all('/\b[A-ZĂÂÎȘȚŢ][a-zăâîșțţ]+(?:\s+[A-ZĂÂÎȘȚŢ][a-zăâîșțţ-]+)*\b/u', $allText, $nameMatches)) {
        $allPotentialNames = array_merge($allPotentialNames, $nameMatches[0]);
    }
    
    // Pattern 2: Names with hyphens (common in Romanian names)
    if (preg_match_all('/\b[A-ZĂÂÎȘȚŢ][a-zăâîșțţ]+-[A-ZĂÂÎȘȚŢ][a-zăâîșțţ]+(?:\s+[A-ZĂÂÎȘȚŢ][a-zăâîșțţ-]+)*\b/u', $allText, $hyphenMatches)) {
        $allPotentialNames = array_merge($allPotentialNames, $hyphenMatches[0]);
    }
    
    // Pattern 3: Names with parentheses (maiden names, etc.)
    if (preg_match_all('/\b[A-ZĂÂÎȘȚŢ][a-zăâîșțţ]+(?:\s+\([^)]+\))?\s+[A-ZĂÂÎȘȚŢ][a-zăâîșțţ-]+/u', $allText, $parenMatches)) {
        $allPotentialNames = array_merge($allPotentialNames, $parenMatches[0]);
    }
    
    // Filter and validate potential names
    $validNames = [];
    foreach (array_unique($allPotentialNames) as $name) {
        $name = trim($name);
        if (isValidPotentialPartyName($name)) {
            $validNames[] = $name;
        }
    }
    
    echo "    - Total potential names found: " . count($allPotentialNames) . PHP_EOL;
    echo "    - Valid names after filtering: " . count($validNames) . PHP_EOL;
    echo "    - Sample valid names:" . PHP_EOL;
    
    for ($i = 0; $i < min(10, count($validNames)); $i++) {
        echo "      " . ($i + 1) . ". " . $validNames[$i] . PHP_EOL;
    }
    
    if (count($validNames) >= $targetCount) {
        echo "  ✅ SUCCESS: Found enough potential parties to reach target!" . PHP_EOL;
    } else {
        echo "  ⚠️  WARNING: Only found " . count($validNames) . " valid names, need $targetCount" . PHP_EOL;
    }
}

function isValidPotentialPartyName($name) {
    // Length check
    if (strlen($name) < 3 || strlen($name) > 150) {
        return false;
    }
    
    // Must start with capital letter
    if (!preg_match('/^[A-ZĂÂÎȘȚŢ]/', $name)) {
        return false;
    }
    
    // Must contain at least one space (first + last name)
    if (!preg_match('/\s/', $name)) {
        return false;
    }
    
    // Exclude obvious legal terms
    $blacklist = ['SUMA DE', 'CONFORM', 'ARTICOL', 'CODUL', 'TRIBUNALUL', 'DISPUNE', 'ADMITE', 'RESPINGE'];
    foreach ($blacklist as $term) {
        if (stripos($name, $term) !== false) {
            return false;
        }
    }
    
    return true;
}

// Run analysis for both test cases
echo "🚀 COMPREHENSIVE PARTY EXTRACTION ANALYSIS" . PHP_EOL;
echo "===========================================" . PHP_EOL . PHP_EOL;

analyzeCompleteDecisionText('130/98/2022', 'TribunalulIALOMITA', 380);
echo PHP_EOL . "===========================================" . PHP_EOL . PHP_EOL;
analyzeCompleteDecisionText('130/98/2022', 'CurteadeApelBUCURESTI', 380);

echo PHP_EOL . "✅ Analysis complete!" . PHP_EOL;
?>
