<?php
/**
 * Test page for session search functionality
 * This page tests the SOAP API integration for court sessions
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/SedinteService.php';

// Set content type to HTML with UTF-8 encoding
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Căutare Ședințe - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Roboto', sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .test-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px 8px 0 0;
        }
        .test-body {
            padding: 1.5rem;
        }
        .result-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .result-header {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        .result-details {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .info-message {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <div class="test-header">
                <h1 class="h3 mb-0">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Test Căutare Ședințe de Judecată
                </h1>
                <p class="mb-0 mt-2 opacity-75">
                    Testare funcționalitate SOAP API pentru căutarea ședințelor
                </p>
            </div>
            <div class="test-body">
                <?php
                echo "<div class='info-message'>";
                echo "<strong>Informații test:</strong><br>";
                echo "• Data testului: " . date('d.m.Y H:i:s') . "<br>";
                echo "• SOAP WSDL: " . SOAP_WSDL . "<br>";
                echo "• Versiune PHP: " . PHP_VERSION . "<br>";
                echo "• Extensie SOAP: " . (extension_loaded('soap') ? 'Activă' : 'Inactivă') . "<br>";
                echo "</div>";

                // Test 1: Verificare extensie SOAP
                echo "<h4><i class='fas fa-check-circle mr-2'></i>Test 1: Verificare Extensie SOAP</h4>";
                if (extension_loaded('soap')) {
                    echo "<div class='success-message'>✓ Extensia SOAP este activă</div>";
                } else {
                    echo "<div class='error-message'>✗ Extensia SOAP nu este activă</div>";
                    exit;
                }

                // Test 2: Inițializare serviciu
                echo "<h4><i class='fas fa-cog mr-2'></i>Test 2: Inițializare Serviciu Ședințe</h4>";
                try {
                    $sedinteService = new SedinteService();
                    echo "<div class='success-message'>✓ Serviciul SedinteService a fost inițializat cu succes</div>";
                } catch (Exception $e) {
                    echo "<div class='error-message'>✗ Eroare la inițializarea serviciului: " . htmlspecialchars($e->getMessage()) . "</div>";
                    exit;
                }

                // Test 3: Verificare metode disponibile
                echo "<h4><i class='fas fa-list mr-2'></i>Test 3: Metode SOAP Disponibile</h4>";
                try {
                    $methods = $sedinteService->getAvailableMethods();
                    if (!empty($methods)) {
                        echo "<div class='success-message'>✓ Găsite " . count($methods) . " metode SOAP disponibile</div>";
                        echo "<div class='result-item'>";
                        echo "<div class='result-header'>Metode disponibile:</div>";
                        echo "<div class='result-details'>";
                        foreach ($methods as $method) {
                            echo "• " . htmlspecialchars($method) . "<br>";
                        }
                        echo "</div>";
                        echo "</div>";
                    } else {
                        echo "<div class='error-message'>✗ Nu s-au găsit metode SOAP disponibile</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='error-message'>✗ Eroare la obținerea metodelor: " . htmlspecialchars($e->getMessage()) . "</div>";
                }

                // Test 4: Test căutare ședințe cu data de azi
                echo "<h4><i class='fas fa-search mr-2'></i>Test 4: Căutare Ședințe (Data de Azi)</h4>";
                try {
                    $today = date('Y-m-d') . 'T00:00:00';
                    $searchParams = [
                        'dataSedinta' => $today,
                        'institutie' => null // Toate instituțiile
                    ];

                    echo "<div class='info-message'>";
                    echo "<strong>Parametri căutare:</strong><br>";
                    echo "• Data ședință: " . $today . "<br>";
                    echo "• Instituție: Toate instituțiile<br>";
                    echo "</div>";

                    $results = $sedinteService->cautareSedinte($searchParams);
                    
                    if (!empty($results)) {
                        echo "<div class='success-message'>✓ Găsite " . count($results) . " ședințe pentru data de azi</div>";
                        
                        foreach ($results as $index => $sedinta) {
                            echo "<div class='result-item'>";
                            echo "<div class='result-header'>Ședința " . ($index + 1) . "</div>";
                            echo "<div class='result-details'>";
                            echo "<strong>Departament:</strong> " . htmlspecialchars($sedinta->departament ?? 'N/A') . "<br>";
                            echo "<strong>Complet:</strong> " . htmlspecialchars($sedinta->complet ?? 'N/A') . "<br>";
                            echo "<strong>Data:</strong> " . htmlspecialchars($sedinta->data ?? 'N/A') . "<br>";
                            echo "<strong>Ora:</strong> " . htmlspecialchars($sedinta->ora ?? 'N/A') . "<br>";
                            
                            if (!empty($sedinta->dosare)) {
                                echo "<strong>Dosare programate:</strong><br>";
                                foreach ($sedinta->dosare as $dosar) {
                                    if ($dosar && !empty($dosar->numar)) {
                                        echo "  • " . htmlspecialchars($dosar->numar) . "<br>";
                                    }
                                }
                            } else {
                                echo "<strong>Dosare programate:</strong> Nu sunt dosare programate<br>";
                            }
                            echo "</div>";
                            echo "</div>";
                        }
                    } else {
                        echo "<div class='info-message'>ℹ Nu au fost găsite ședințe pentru data de azi</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='error-message'>✗ Eroare la căutarea ședințelor: " . htmlspecialchars($e->getMessage()) . "</div>";
                }

                // Test 5: Test căutare cu instituție specifică
                echo "<h4><i class='fas fa-university mr-2'></i>Test 5: Căutare cu Instituție Specifică</h4>";
                try {
                    $tomorrow = date('Y-m-d', strtotime('+1 day')) . 'T00:00:00';
                    $searchParams = [
                        'dataSedinta' => $tomorrow,
                        'institutie' => 'TribunalulBUCURESTI' // Tribunalul București
                    ];

                    echo "<div class='info-message'>";
                    echo "<strong>Parametri căutare:</strong><br>";
                    echo "• Data ședință: " . $tomorrow . "<br>";
                    echo "• Instituție: TribunalulBUCURESTI<br>";
                    echo "</div>";

                    $results = $sedinteService->cautareSedinte($searchParams);
                    
                    if (!empty($results)) {
                        echo "<div class='success-message'>✓ Găsite " . count($results) . " ședințe la Tribunalul București pentru mâine</div>";
                    } else {
                        echo "<div class='info-message'>ℹ Nu au fost găsite ședințe la Tribunalul București pentru mâine</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='error-message'>✗ Eroare la căutarea cu instituție specifică: " . htmlspecialchars($e->getMessage()) . "</div>";
                }

                // Test 6: Verificare tipuri de date
                echo "<h4><i class='fas fa-database mr-2'></i>Test 6: Tipuri de Date SOAP</h4>";
                try {
                    $types = $sedinteService->getAvailableTypes();
                    if (!empty($types)) {
                        echo "<div class='success-message'>✓ Găsite " . count($types) . " tipuri de date SOAP</div>";
                        echo "<div class='result-item'>";
                        echo "<div class='result-header'>Tipuri de date disponibile:</div>";
                        echo "<div class='result-details'>";
                        foreach (array_slice($types, 0, 10) as $type) { // Afișăm doar primele 10
                            echo "• " . htmlspecialchars($type) . "<br>";
                        }
                        if (count($types) > 10) {
                            echo "... și încă " . (count($types) - 10) . " tipuri<br>";
                        }
                        echo "</div>";
                        echo "</div>";
                    } else {
                        echo "<div class='error-message'>✗ Nu s-au găsit tipuri de date SOAP</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='error-message'>✗ Eroare la obținerea tipurilor de date: " . htmlspecialchars($e->getMessage()) . "</div>";
                }

                echo "<div class='success-message mt-4'>";
                echo "<strong>✓ Testare completă!</strong><br>";
                echo "Funcționalitatea de căutare ședințe este operațională.<br>";
                echo "<a href='sedinte.php' class='btn btn-primary mt-2'>";
                echo "<i class='fas fa-calendar-alt mr-1'></i> Accesează Pagina de Căutare Ședințe";
                echo "</a>";
                echo "</div>";
                ?>
            </div>
        </div>
    </div>
</body>
</html>
