<?php

/**
 * <PERSON>șier de configurare pentru constante globale
 */

// URL-ul WSDL pentru API-ul SOAP
if (!defined('SOAP_WSDL')) {
    define('SOAP_WSDL', 'http://portalquery.just.ro/query.asmx?WSDL');
}

// Configurare pentru căutare
define('MAX_SEARCH_RESULTS', 1000);

// Configurare pentru loguri
define('LOG_DIR', dirname(__DIR__, 2) . '/logs');

// Configurare pentru cache
define('CACHE_DIR', dirname(__DIR__, 2) . '/cache');
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 oră

// Configurare pentru export
define('EXPORT_DIR', dirname(__DIR__, 2) . '/temp');

// Configurare pentru email
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>'); // Înlocuiți cu email-ul real
define('SMTP_PASSWORD', 'Caracatita2'); // Înlocuiți cu parola de aplicație
define('CONTACT_EMAIL', '<EMAIL>');
define('CONTACT_NAME', 'Portal Judiciar');

// Configurare pentru monitorizarea dosarelor
define('MAX_MONITORED_CASES_PER_USER', 50); // Numărul maxim de dosare pe utilizator
define('NOTIFICATION_BATCH_SIZE', 20); // Numărul de notificări procesate odată
define('CASE_CHECK_BATCH_SIZE', 10); // Numărul de dosare verificate odată
define('API_DELAY_BETWEEN_CALLS', 500000); // Întârziere între apeluri API (microsecunde)

// Configurare pentru notificări
define('MAX_NOTIFICATION_ATTEMPTS', 3); // Numărul maxim de încercări pentru trimiterea email-urilor
define('NOTIFICATION_RETRY_DELAY', 3600); // Întârziere între reîncercări (secunde)

// Configurare pentru curățarea datelor
define('SNAPSHOT_RETENTION_DAYS', 90); // Păstrarea snapshot-urilor (zile)
define('NOTIFICATION_LOG_RETENTION_DAYS', 180); // Păstrarea log-urilor de notificare (zile)

// URL-uri pentru aplicație
define('BASE_URL', 'https://just.gabrielanghel.ro/'); // URL-ul de bază al aplicației
define('PORTAL_NAME', 'Portal Judiciar România'); // Numele portalului

// Configurare pentru baza de date
if (!defined('DB_HOST')) {
    define('DB_HOST', 'localhost');
}
if (!defined('DB_NAME')) {
    define('DB_NAME', 'gabriel3_portal_judiciar');
}
if (!defined('DB_USER')) {
    define('DB_USER', 'gabriel3_portal_judiciar'); // Schimbați cu utilizatorul dvs. de bază de date
}
if (!defined('DB_PASS')) {
    define('DB_PASS', 'portal_juducuar'); // Schimbați cu parola dvs. de bază de date
}
if (!defined('DB_CHARSET')) {
    define('DB_CHARSET', 'utf8mb4');
}
