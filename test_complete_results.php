<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

// Test specific search terms to see if results are being truncated
$testTerms = [
    'POPESCU',
    'IONESCU', 
    'BUCURESTI',
    '123/2024'
];

echo "<!DOCTYPE html>
<html lang='ro'>
<head>
    <meta charset='UTF-8'>
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Test Rezultate Complete - Portal Judiciar România</title>
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
    <link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\" rel=\"stylesheet\">
    <style>
        .test-section {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: #f8f9fa;
        }
        .result-box {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .code {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class=\"container mt-4\">
        <h1 class=\"text-center mb-4\">
            <i class=\"fas fa-search me-2 text-primary\"></i>
            Test Rezultate Complete
        </h1>";

$dosarService = new DosarService();

foreach ($testTerms as $term) {
    echo "<div class='test-section'>";
    echo "<h3><i class='fas fa-search me-2'></i>Test pentru: " . htmlspecialchars($term) . "</h3>";
    
    try {
        // Test with different maxResults limits
        $limits = [10, 100, 500, 1000, 2000];
        
        foreach ($limits as $limit) {
            echo "<div class='result-box'>";
            echo "<h5>Limită: $limit rezultate</h5>";
            
            $searchParams = [
                'numarDosar' => '',
                'institutie' => null,
                'numeParte' => $term,
                'obiectDosar' => '',
                'dataStart' => '',
                'dataStop' => '',
                'dataUltimaModificareStart' => '',
                'dataUltimaModificareStop' => '',
                '_maxResults' => $limit
            ];
            
            $startTime = microtime(true);
            $results = $dosarService->cautareAvansata($searchParams);
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            $count = count($results);
            
            echo "<div class='success'>";
            echo "<i class='fas fa-check-circle me-2'></i>";
            echo "<strong>Rezultate găsite:</strong> $count";
            echo " <small>(timp: {$duration}ms)</small>";
            echo "</div>";
            
            if ($count === $limit) {
                echo "<div class='warning'>";
                echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                echo "<strong>Atenție:</strong> S-a atins limita de $limit rezultate. Pot exista mai multe rezultate disponibile.";
                echo "</div>";
            }
            
            if ($count > 0) {
                echo "<h6>Primul rezultat:</h6>";
                $firstResult = $results[0];
                echo "<div class='code'>";
                echo "Număr: " . ($firstResult->numar ?? 'N/A') . "\n";
                echo "Instanță: " . ($firstResult->institutie ?? 'N/A') . "\n";
                echo "Obiect: " . substr($firstResult->obiect ?? 'N/A', 0, 100) . "\n";
                echo "Părți: " . count($firstResult->parti ?? []) . " părți\n";
                
                // Show first few parties
                $parties = $firstResult->parti ?? [];
                if (count($parties) > 0) {
                    echo "\nPrimele părți:\n";
                    for ($i = 0; $i < min(3, count($parties)); $i++) {
                        $party = $parties[$i];
                        $name = is_object($party) ? ($party->nume ?? 'N/A') : ($party['nume'] ?? 'N/A');
                        $quality = is_object($party) ? ($party->calitate ?? 'N/A') : ($party['calitate'] ?? 'N/A');
                        echo "- $name ($quality)\n";
                    }
                }
                echo "</div>";
                
                if ($count > 1) {
                    echo "<h6>Ultimul rezultat:</h6>";
                    $lastResult = $results[$count - 1];
                    echo "<div class='code'>";
                    echo "Număr: " . ($lastResult->numar ?? 'N/A') . "\n";
                    echo "Instanță: " . ($lastResult->institutie ?? 'N/A') . "\n";
                    echo "Obiect: " . substr($lastResult->obiect ?? 'N/A', 0, 100) . "\n";
                    echo "Părți: " . count($lastResult->parti ?? []) . " părți\n";
                    echo "</div>";
                }
            }
            
            echo "</div>";
        }
        
        // Test without limit
        echo "<div class='result-box'>";
        echo "<h5>Fără limită (default)</h5>";
        
        $searchParams = [
            'numarDosar' => '',
            'institutie' => null,
            'numeParte' => $term,
            'obiectDosar' => '',
            'dataStart' => '',
            'dataStop' => '',
            'dataUltimaModificareStart' => '',
            'dataUltimaModificareStop' => ''
        ];
        
        $startTime = microtime(true);
        $results = $dosarService->cautareAvansata($searchParams);
        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2);
        
        $count = count($results);
        
        echo "<div class='success'>";
        echo "<i class='fas fa-check-circle me-2'></i>";
        echo "<strong>Rezultate găsite:</strong> $count";
        echo " <small>(timp: {$duration}ms)</small>";
        echo "</div>";
        
        if ($count === 1000) {
            echo "<div class='warning'>";
            echo "<i class='fas fa-exclamation-triangle me-2'></i>";
            echo "<strong>Atenție:</strong> S-a atins limita default de 1000 rezultate. Pot exista mai multe rezultate disponibile.";
            echo "</div>";
        }
        
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>";
        echo "<strong>Eroare:</strong> " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
    
    echo "</div>";
}

// Test direct SOAP response
echo "<div class='test-section'>";
echo "<h3><i class='fas fa-cogs me-2'></i>Test Direct SOAP API</h3>";

try {
    // Create reflection to access private method
    $reflection = new ReflectionClass($dosarService);
    $method = $reflection->getMethod('executeSoapCallWithRetry');
    $method->setAccessible(true);
    
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'numeParte' => 'POPESCU',
        'obiectDosar' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    echo "<div class='result-box'>";
    echo "<h5>Răspuns SOAP direct pentru 'POPESCU'</h5>";
    
    $startTime = microtime(true);
    $response = $method->invoke($dosarService, 'CautareDosare2', $searchParams, "Test error");
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    echo "<div class='success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "<strong>Răspuns SOAP primit</strong> <small>(timp: {$duration}ms)</small>";
    echo "</div>";
    
    if (isset($response->CautareDosare2Result->Dosar)) {
        $dosare = $response->CautareDosare2Result->Dosar;
        
        if (is_array($dosare)) {
            $count = count($dosare);
            echo "<div class='success'>";
            echo "<i class='fas fa-list me-2'></i>";
            echo "<strong>Dosare în răspuns:</strong> $count (array)";
            echo "</div>";
            
            // Check if there are pagination indicators
            echo "<h6>Verificare indicatori paginare:</h6>";
            echo "<div class='code'>";
            
            $responseObj = $response->CautareDosare2Result;
            $properties = get_object_vars($responseObj);
            
            foreach ($properties as $key => $value) {
                if ($key !== 'Dosar') {
                    echo "$key: " . (is_object($value) || is_array($value) ? json_encode($value) : $value) . "\n";
                }
            }
            
            if (count($properties) === 1) {
                echo "Nu s-au găsit indicatori de paginare în răspuns.\n";
            }
            
            echo "</div>";
            
        } else {
            echo "<div class='success'>";
            echo "<i class='fas fa-file me-2'></i>";
            echo "<strong>Un singur dosar în răspuns</strong>";
            echo "</div>";
        }
    } else {
        echo "<div class='warning'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>";
        echo "<strong>Nu s-au găsit dosare în răspuns</strong>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
    echo "<strong>Eroare SOAP:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</div>";

echo "
        <div class=\"text-center mt-4\">
            <a href=\"index.php\" class=\"btn btn-success btn-lg\">
                <i class=\"fas fa-home me-2\"></i>
                Înapoi la Portal
            </a>
        </div>
    </div>
</body>
</html>";
?>
