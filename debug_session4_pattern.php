<?php
/**
 * Debug Session 4 pattern matching specifically
 */

require_once 'bootstrap.php';

use App\Services\DosarService;

echo "=== DEBUGGING SESSION 4 PATTERN MATCHING ===" . PHP_EOL;
echo "Case: 130/98/2022 from TribunalulIALOMITA" . PHP_EOL;
echo "Target: SARAGEA TUDORIŢA" . PHP_EOL;
echo PHP_EOL;

$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';
$targetParty = 'SARAGEA TUDORIŢA';

try {
    $dosarService = new DosarService();
    
    // Get the case details using the SOAP API
    $searchParams = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    // Use reflection to access the private methods
    $reflection = new ReflectionClass($dosarService);
    $soapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $soapMethod->setAccessible(true);
    
    $response = $soapMethod->invoke($dosarService, 'CautareDosare2', $searchParams, "Test error");
    
    if (isset($response->CautareDosare2Result->Dosar)) {
        $dosare = $response->CautareDosare2Result->Dosar;
        if (!is_array($dosare)) {
            $dosare = [$dosare];
        }
        
        $dosar = $dosare[0];
        
        if (isset($dosar->sedinte) && isset($dosar->sedinte->DosarSedinta)) {
            $sedinte = $dosar->sedinte->DosarSedinta;
            if (!is_array($sedinte)) {
                $sedinte = [$sedinte];
            }
            
            // Focus on Session 4 (index 3)
            if (isset($sedinte[3])) {
                $sedinta = $sedinte[3];
                
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $solutieText = $sedinta->solutieSumar;
                    
                    echo "=== SESSION 4 ANALYSIS ===" . PHP_EOL;
                    echo "Text length: " . strlen($solutieText) . " characters" . PHP_EOL;
                    echo PHP_EOL;
                    
                    // Test Pattern 1: Creditor pattern
                    echo "--- Testing Creditor Pattern ---" . PHP_EOL;
                    if (preg_match('/formulate de creditorii ([^;]+(?:;[^;]+)*)/i', $solutieText, $matches)) {
                        echo "✅ Creditor pattern matched" . PHP_EOL;
                        echo "Match: " . substr($matches[1], 0, 200) . "..." . PHP_EOL;
                    } else {
                        echo "❌ Creditor pattern not matched" . PHP_EOL;
                    }
                    echo PHP_EOL;
                    
                    // Test Pattern 2: Intervener pattern
                    echo "--- Testing Intervener Pattern ---" . PHP_EOL;
                    $intervenerPattern = '/introducerea acestora în cauză[^.]*în calitate de intervenienţi[^.]*\./i';
                    if (preg_match($intervenerPattern, $solutieText, $matches)) {
                        echo "✅ Intervener pattern matched!" . PHP_EOL;
                        echo "Matched text: " . $matches[0] . PHP_EOL;
                        echo PHP_EOL;
                        
                        // Find the party list that precedes this statement
                        $beforeText = substr($solutieText, 0, strpos($solutieText, $matches[0]));
                        
                        echo "--- Testing Party List Patterns ---" . PHP_EOL;
                        
                        // Test the current pattern
                        $currentPattern = '/([A-ZĂÂÎȘȚŢ][A-ZĂÂÎȘȚŢ\s]*(?:,\s*[A-ZĂÂÎȘȚŢ][A-ZĂÂÎȘȚŢ\s]*)*)\s*\.\s*Dispune\s+introducerea/u';
                        if (preg_match($currentPattern, $beforeText, $partyMatches)) {
                            echo "✅ Current pattern matched!" . PHP_EOL;
                            echo "Party text: " . substr($partyMatches[1], 0, 200) . "..." . PHP_EOL;
                        } else {
                            echo "❌ Current pattern not matched" . PHP_EOL;
                            
                            // Try simpler patterns
                            echo "Trying simpler patterns..." . PHP_EOL;
                            
                            // Pattern 1: Just look for text before "Dispune"
                            $simplePattern1 = '/([A-ZĂÂÎȘȚŢ][^.]*)\s*\.\s*Dispune/u';
                            if (preg_match($simplePattern1, $beforeText, $simple1)) {
                                echo "✅ Simple pattern 1 matched: " . substr($simple1[1], -100) . PHP_EOL;
                            }
                            
                            // Pattern 2: Look for the specific text we saw in the test
                            $specificPattern = '/(SARAGEA TUDORIŢA[^.]*)\s*\.\s*Dispune/u';
                            if (preg_match($specificPattern, $beforeText, $specific)) {
                                echo "✅ Specific pattern matched: " . $specific[1] . PHP_EOL;
                            }
                            
                            // Show the last 500 characters before "Dispune"
                            $dispunePos = strpos($beforeText, 'Dispune');
                            if ($dispunePos !== false) {
                                echo PHP_EOL . "Last 500 chars before 'Dispune':" . PHP_EOL;
                                echo substr($beforeText, max(0, $dispunePos - 500), 500) . PHP_EOL;
                            }
                        }
                        
                    } else {
                        echo "❌ Intervener pattern not matched" . PHP_EOL;
                        
                        // Check for keywords
                        $keywords = ['intervenient', 'introducerea', 'acestora', 'cauză'];
                        foreach ($keywords as $keyword) {
                            if (stripos($solutieText, $keyword) !== false) {
                                echo "Found keyword: {$keyword}" . PHP_EOL;
                            }
                        }
                    }
                    
                } else {
                    echo "❌ Session 4 has no solutieSumar" . PHP_EOL;
                }
            } else {
                echo "❌ Session 4 not found" . PHP_EOL;
            }
        }
        
    } else {
        echo "❌ No SOAP response data" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
}
