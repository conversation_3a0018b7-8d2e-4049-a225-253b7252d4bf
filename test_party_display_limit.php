<?php
/**
 * Test Party Display Limit
 * Verifică dacă există vreo limitare în afișarea părților în detalii_dosar.php
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Party Display Limit</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style></head><body>";

echo "<h1>🔍 Test Party Display Limit</h1>";
echo "<p>Verifică dacă există limitări în afișarea părților pentru dosarul 130/98/2022</p>";
echo "<hr>";

try {
    $dosarService = new \App\Services\DosarService();
    
    echo "<div class='section'>";
    echo "<h2>📊 Step 1: Backend Party Count Analysis</h2>";
    
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');
    
    if (!$dosar) {
        echo "<p class='error'>❌ Nu s-au putut obține detaliile dosarului</p>";
        exit;
    }
    
    $totalParties = count($dosar->parti ?? []);
    echo "<p><strong>Total părți în backend:</strong> {$totalParties}</p>";
    
    // Analizăm sursele părților
    $soapCount = 0;
    $decisionCount = 0;
    $unknownCount = 0;
    
    foreach ($dosar->parti as $party) {
        $source = $party->source ?? 'unknown';
        switch ($source) {
            case 'soap_api': $soapCount++; break;
            case 'decision_text': $decisionCount++; break;
            default: $unknownCount++; break;
        }
    }
    
    echo "<table>";
    echo "<tr><th>Sursă</th><th>Număr părți</th><th>Procent</th></tr>";
    echo "<tr><td>SOAP API</td><td>{$soapCount}</td><td>" . round(($soapCount / max($totalParties, 1)) * 100, 1) . "%</td></tr>";
    echo "<tr><td>Decision Text</td><td>{$decisionCount}</td><td>" . round(($decisionCount / max($totalParties, 1)) * 100, 1) . "%</td></tr>";
    echo "<tr><td>Unknown</td><td>{$unknownCount}</td><td>" . round(($unknownCount / max($totalParties, 1)) * 100, 1) . "%</td></tr>";
    echo "<tr><td><strong>Total</strong></td><td><strong>{$totalParties}</strong></td><td><strong>100%</strong></td></tr>";
    echo "</table>";
    
    if ($totalParties > 100) {
        echo "<p class='success'>✅ Backend returnează {$totalParties} părți (> 100)</p>";
    } else {
        echo "<p class='warning'>⚠️ Backend returnează doar {$totalParties} părți (≤ 100)</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🔍 Step 2: Sample Parties Analysis</h2>";
    
    echo "<p>Primele 10 părți din backend:</p>";
    echo "<table>";
    echo "<tr><th>#</th><th>Nume</th><th>Calitate</th><th>Sursă</th></tr>";
    
    $displayCount = min(10, $totalParties);
    for ($i = 0; $i < $displayCount; $i++) {
        $party = $dosar->parti[$i];
        $rowClass = ($party->source ?? '') === 'soap_api' ? 'style="background-color: #e8f5e8;"' : 'style="background-color: #fff3cd;"';
        echo "<tr {$rowClass}>";
        echo "<td>" . ($i + 1) . "</td>";
        echo "<td>" . htmlspecialchars($party->nume ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($party->calitate ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($party->source ?? 'unknown') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    if ($totalParties > 10) {
        echo "<p class='info'>... și încă " . ($totalParties - 10) . " părți</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🌐 Step 3: Frontend Display Simulation</h2>";
    
    // Simulăm logica de afișare din detalii_dosar.php
    $validParti = [];
    $filteredParti = [];
    
    foreach ($dosar->parti as $parteIndex => $parte) {
        $nume = trim($parte->nume ?? '');
        $calitate = trim($parte->calitate ?? '');
        
        // Aplicăm aceeași logică de validare ca în frontend
        if (empty($nume) || strlen($nume) < 2) {
            $filteredParti[] = [
                'reason' => 'nume_gol_sau_scurt',
                'data' => $parte,
                'index' => $parteIndex
            ];
            continue;
        }
        
        // Verificăm pentru duplicate (versiune simplificată)
        $isDuplicate = false;
        foreach ($validParti as $existingParte) {
            if (strcasecmp($existingParte->nume, $nume) === 0) {
                $isDuplicate = true;
                break;
            }
        }
        
        if ($isDuplicate) {
            $filteredParti[] = [
                'reason' => 'duplicat',
                'data' => $parte,
                'index' => $parteIndex
            ];
            continue;
        }
        
        // Adăugăm la părțile valide
        $parte->originalIndex = $parteIndex;
        $parte->displayIndex = count($validParti) + 1;
        $validParti[] = $parte;
    }
    
    echo "<table>";
    echo "<tr><th>Metric</th><th>Valoare</th></tr>";
    echo "<tr><td>Părți valide pentru afișare</td><td>" . count($validParti) . "</td></tr>";
    echo "<tr><td>Părți filtrate</td><td>" . count($filteredParti) . "</td></tr>";
    echo "<tr><td>Total procesate</td><td>" . (count($validParti) + count($filteredParti)) . "</td></tr>";
    echo "</table>";
    
    if (count($validParti) === $totalParties) {
        echo "<p class='success'>✅ Toate părțile sunt valide pentru afișare</p>";
    } else {
        echo "<p class='warning'>⚠️ " . count($filteredParti) . " părți au fost filtrate</p>";
        
        if (!empty($filteredParti)) {
            echo "<p><strong>Motive de filtrare:</strong></p>";
            $filterReasons = [];
            foreach ($filteredParti as $filtered) {
                $reason = $filtered['reason'];
                if (!isset($filterReasons[$reason])) {
                    $filterReasons[$reason] = 0;
                }
                $filterReasons[$reason]++;
            }
            
            echo "<ul>";
            foreach ($filterReasons as $reason => $count) {
                echo "<li>{$reason}: {$count} părți</li>";
            }
            echo "</ul>";
        }
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🔍 Step 4: Search for Specific Party</h2>";
    
    $searchParty = 'SARAGEA TUDORIŢA';
    echo "<p>Căutăm partea: <strong>{$searchParty}</strong></p>";
    
    $found = false;
    $foundIndex = -1;
    
    foreach ($validParti as $index => $party) {
        if (stripos($party->nume, 'SARAGEA') !== false && stripos($party->nume, 'TUDORI') !== false) {
            $found = true;
            $foundIndex = $index + 1;
            echo "<p class='success'>✅ Partea găsită la poziția {$foundIndex}: " . htmlspecialchars($party->nume) . "</p>";
            echo "<p><strong>Calitate:</strong> " . htmlspecialchars($party->calitate ?? 'N/A') . "</p>";
            echo "<p><strong>Sursă:</strong> " . htmlspecialchars($party->source ?? 'N/A') . "</p>";
            break;
        }
    }
    
    if (!$found) {
        echo "<p class='error'>❌ Partea nu a fost găsită în părțile valide pentru afișare</p>";
        
        // Verificăm în părțile filtrate
        foreach ($filteredParti as $filtered) {
            $party = $filtered['data'];
            if (stripos($party->nume, 'SARAGEA') !== false && stripos($party->nume, 'TUDORI') !== false) {
                echo "<p class='warning'>⚠️ Partea a fost găsită în părțile filtrate!</p>";
                echo "<p><strong>Motiv filtrare:</strong> " . $filtered['reason'] . "</p>";
                echo "<p><strong>Nume:</strong> " . htmlspecialchars($party->nume) . "</p>";
                break;
            }
        }
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>📊 Final Assessment</h2>";
    
    if (count($validParti) >= 400) {
        echo "<p class='success'>🎉 SUCCESS: Sistemul afișează {" . count($validParti) . "} părți (≥400)</p>";
        echo "<p class='success'>✅ Nu există limitare la 100 de părți în afișare</p>";
    } elseif (count($validParti) > 100) {
        echo "<p class='warning'>⚠️ PARTIAL: Sistemul afișează {" . count($validParti) . "} părți (>100 dar <400)</p>";
    } else {
        echo "<p class='error'>❌ PROBLEM: Sistemul afișează doar {" . count($validParti) . "} părți (≤100)</p>";
        echo "<p class='error'>Există o limitare în sistem!</p>";
    }
    
    if ($found) {
        echo "<p class='success'>✅ Partea căutată este disponibilă pentru afișare</p>";
    } else {
        echo "<p class='error'>❌ Partea căutată nu este disponibilă pentru afișare</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2 class='error'>❌ Error Occurred</h2>";
    echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Test completed at " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
