-- <PERSON> Judiciar - Admin Roles Migration
-- Created: 2025-07-03
-- Description: Add admin role functionality to users table

USE portal_judiciar;

-- Create default super admin user (password: admin123)
INSERT IGNORE INTO users (
    email,
    password_hash,
    first_name,
    last_name,
    email_verified,
    admin_role,
    data_processing_consent,
    marketing_consent,
    gdpr_consent_date,
    created_at
) VALUES (
    '<EMAIL>',
    '$2y$10$8K1p/a0dhrxSUt3LxjCCa.sRlhyiXkPvpyNuHf6EEgvMkOW9q5YVe',
    'Super',
    'Administrator',
    1,
    'super_admin',
    1,
    0,
    NOW(),
    NOW()
);

-- Update existing admin user if exists
UPDATE users 
SET admin_role = 'super_admin' 
WHERE email = '<EMAIL>' AND admin_role IS NULL;

-- Create admin activity view
CREATE OR REPLACE VIEW admin_users AS
SELECT 
    id,
    email,
    first_name,
    last_name,
    admin_role,
    last_login_at,
    last_login_ip,
    created_at,
    CASE 
        WHEN admin_role = 'super_admin' THEN 'Super Administrator'
        WHEN admin_role = 'admin' THEN 'Administrator'
        WHEN admin_role = 'moderator' THEN 'Moderator'
        WHEN admin_role = 'viewer' THEN 'Viewer'
        ELSE 'Regular User'
    END as role_display_name
FROM users 
WHERE deleted_at IS NULL
ORDER BY 
    CASE admin_role
        WHEN 'super_admin' THEN 1
        WHEN 'admin' THEN 2
        WHEN 'moderator' THEN 3
        WHEN 'viewer' THEN 4
        ELSE 5
    END,
    created_at DESC;

-- Record migration
INSERT INTO schema_migrations (version, applied_at) 
VALUES ('004_add_admin_roles', NOW())
ON DUPLICATE KEY UPDATE applied_at = NOW();
