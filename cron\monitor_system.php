<?php
/**
 * System Monitoring Cron Job
 * 
 * This script monitors the health of the case monitoring system.
 * Should be run every hour to check system status and send alerts.
 * 
 * Cron configuration example:
 * 0 * * * * /usr/bin/php /path/to/just/cron/monitor_system.php >> /path/to/just/logs/system_monitor.log 2>&1
 * 
 * Portal Judiciar România - Case Monitoring System
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */

// Prevent web access
if (php_sapi_name() !== 'cli') {
    http_response_code(403);
    die('This script can only be run from command line.');
}

// Set error reporting for CLI
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Include required files
require_once dirname(__DIR__) . '/bootstrap.php';
require_once dirname(__DIR__) . '/includes/config.php';

use App\Config\Database;

/**
 * System monitoring cron job class
 */
class SystemMonitorCron
{
    private $logFile;
    private $errorLogFile;
    private $startTime;
    private $alerts = [];
    private $warnings = [];
    private $systemStatus = 'healthy';
    
    public function __construct()
    {
        $this->startTime = microtime(true);
        $this->logFile = dirname(__DIR__) . '/logs/system_monitor.log';
        $this->errorLogFile = dirname(__DIR__) . '/logs/system_monitor_errors.log';
        
        // Ensure log directory exists
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $this->log("=== System Monitor Cron Job Started ===");
        $this->log("Process ID: " . getmypid());
    }
    
    /**
     * Main execution method
     */
    public function run()
    {
        try {
            $this->log("Starting system monitoring...");
            
            // Check database health
            $this->checkDatabaseHealth();
            
            // Check cron job execution
            $this->checkCronJobExecution();
            
            // Check notification queue
            $this->checkNotificationQueue();
            
            // Check disk space
            $this->checkDiskSpace();
            
            // Check log file sizes
            $this->checkLogFileSizes();
            
            // Check system performance
            $this->checkSystemPerformance();
            
            // Generate health report
            $this->generateHealthReport();
            
            // Send alerts if necessary
            $this->sendAlertsIfNeeded();
            
        } catch (Exception $e) {
            $this->logError("Fatal error in system monitor: " . $e->getMessage());
            $this->addAlert('critical', 'System monitor crashed: ' . $e->getMessage());
        } finally {
            $this->finalize();
        }
    }
    
    /**
     * Check database health
     */
    private function checkDatabaseHealth()
    {
        try {
            // Test database connection
            $db = Database::getConnection();
            $this->log("✅ Database connection: OK");
            
            // Check table sizes
            $tables = ['monitored_cases', 'case_snapshots', 'notification_queue', 'users'];
            foreach ($tables as $table) {
                $result = Database::fetchOne("SELECT COUNT(*) as count FROM {$table}");
                $count = $result['count'];
                $this->log("📊 Table {$table}: {$count} records");
                
                // Alert if tables are growing too large
                if ($table === 'case_snapshots' && $count > 100000) {
                    $this->addWarning("Large number of case snapshots: {$count}");
                }
                if ($table === 'notification_queue' && $count > 10000) {
                    $this->addWarning("Large notification queue: {$count}");
                }
            }
            
        } catch (Exception $e) {
            $this->addAlert('critical', 'Database connection failed: ' . $e->getMessage());
            $this->logError("Database health check failed: " . $e->getMessage());
        }
    }
    
    /**
     * Check cron job execution
     */
    private function checkCronJobExecution()
    {
        $cronLogFile = dirname(__DIR__) . '/logs/cron.log';
        
        if (!file_exists($cronLogFile)) {
            $this->addAlert('warning', 'Cron log file does not exist');
            return;
        }
        
        // Check last execution time
        $lastModified = filemtime($cronLogFile);
        $timeSinceLastRun = time() - $lastModified;
        
        if ($timeSinceLastRun > 3600) { // More than 1 hour
            $this->addAlert('critical', "Cron job hasn't run in {$timeSinceLastRun} seconds");
        } elseif ($timeSinceLastRun > 2100) { // More than 35 minutes
            $this->addWarning("Cron job last ran {$timeSinceLastRun} seconds ago");
        } else {
            $this->log("✅ Cron job execution: Recent (last run {$timeSinceLastRun}s ago)");
        }
        
        // Check for errors in cron log
        $recentErrors = shell_exec("tail -100 {$cronLogFile} | grep -i error | wc -l");
        if ($recentErrors > 5) {
            $this->addWarning("Multiple errors in recent cron executions: {$recentErrors}");
        }
    }
    
    /**
     * Check notification queue
     */
    private function checkNotificationQueue()
    {
        try {
            // Check pending notifications
            $pending = Database::fetchOne("
                SELECT COUNT(*) as count 
                FROM notification_queue 
                WHERE status = 'pending' AND scheduled_for <= NOW()
            ");
            
            $pendingCount = $pending['count'];
            
            if ($pendingCount > 100) {
                $this->addAlert('warning', "Large number of pending notifications: {$pendingCount}");
            } elseif ($pendingCount > 0) {
                $this->log("📧 Pending notifications: {$pendingCount}");
            } else {
                $this->log("✅ Notification queue: Empty");
            }
            
            // Check failed notifications
            $failed = Database::fetchOne("
                SELECT COUNT(*) as count 
                FROM notification_queue 
                WHERE status = 'failed' AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ");
            
            $failedCount = $failed['count'];
            if ($failedCount > 10) {
                $this->addAlert('warning', "High number of failed notifications in last 24h: {$failedCount}");
            }
            
        } catch (Exception $e) {
            $this->addAlert('warning', 'Could not check notification queue: ' . $e->getMessage());
        }
    }
    
    /**
     * Check disk space
     */
    private function checkDiskSpace()
    {
        $projectPath = dirname(__DIR__);
        $freeBytes = disk_free_space($projectPath);
        $totalBytes = disk_total_space($projectPath);
        
        if ($freeBytes && $totalBytes) {
            $freePercent = ($freeBytes / $totalBytes) * 100;
            $freeGB = round($freeBytes / (1024 * 1024 * 1024), 2);
            
            if ($freePercent < 5) {
                $this->addAlert('critical', "Very low disk space: {$freeGB}GB ({$freePercent}%)");
            } elseif ($freePercent < 15) {
                $this->addWarning("Low disk space: {$freeGB}GB ({$freePercent}%)");
            } else {
                $this->log("✅ Disk space: {$freeGB}GB free ({$freePercent}%)");
            }
        }
    }
    
    /**
     * Check log file sizes
     */
    private function checkLogFileSizes()
    {
        $logFiles = [
            'cron.log',
            'cron_errors.log',
            'notifications.log',
            'system_monitor.log'
        ];
        
        $logDir = dirname(__DIR__) . '/logs';
        
        foreach ($logFiles as $logFile) {
            $filePath = $logDir . '/' . $logFile;
            if (file_exists($filePath)) {
                $sizeBytes = filesize($filePath);
                $sizeMB = round($sizeBytes / (1024 * 1024), 2);
                
                if ($sizeMB > 100) {
                    $this->addWarning("Large log file: {$logFile} ({$sizeMB}MB)");
                } else {
                    $this->log("📄 Log file {$logFile}: {$sizeMB}MB");
                }
            }
        }
    }
    
    /**
     * Check system performance
     */
    private function checkSystemPerformance()
    {
        // Check memory usage
        $memoryUsage = memory_get_usage();
        $memoryLimit = ini_get('memory_limit');
        
        $this->log("💾 Memory usage: " . $this->formatBytes($memoryUsage));
        
        // Check load average (Linux/Unix only)
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            if ($load[0] > 5) {
                $this->addWarning("High system load: " . round($load[0], 2));
            } else {
                $this->log("⚡ System load: " . round($load[0], 2));
            }
        }
    }
    
    /**
     * Generate health report
     */
    private function generateHealthReport()
    {
        $this->log("=== System Health Report ===");
        
        if (empty($this->alerts) && empty($this->warnings)) {
            $this->systemStatus = 'healthy';
            $this->log("✅ System Status: HEALTHY");
        } elseif (!empty($this->alerts)) {
            $this->systemStatus = 'critical';
            $this->log("🚨 System Status: CRITICAL");
        } else {
            $this->systemStatus = 'warning';
            $this->log("⚠️  System Status: WARNING");
        }
        
        if (!empty($this->warnings)) {
            $this->log("Warnings:");
            foreach ($this->warnings as $warning) {
                $this->log("  - {$warning}");
            }
        }
        
        if (!empty($this->alerts)) {
            $this->log("Alerts:");
            foreach ($this->alerts as $alert) {
                $this->log("  - {$alert['level']}: {$alert['message']}");
            }
        }
    }
    
    /**
     * Send alerts if needed
     */
    private function sendAlertsIfNeeded()
    {
        if (empty($this->alerts) && empty($this->warnings)) {
            return;
        }
        
        // In a real implementation, this would send email alerts
        // For now, we just log the need for alerts
        $this->log("📧 Alerts would be sent to administrators");
        
        // You could implement email sending here using the NotificationManager
        // or a simple mail() function to notify administrators
    }
    
    /**
     * Add an alert
     */
    private function addAlert($level, $message)
    {
        $this->alerts[] = ['level' => $level, 'message' => $message];
        $this->logError("ALERT [{$level}]: {$message}");
    }
    
    /**
     * Add a warning
     */
    private function addWarning($message)
    {
        $this->warnings[] = $message;
        $this->log("WARNING: {$message}");
    }
    
    /**
     * Finalize the monitoring
     */
    private function finalize()
    {
        $executionTime = microtime(true) - $this->startTime;
        $this->log("Execution time: " . round($executionTime, 2) . " seconds");
        $this->log("=== System Monitor Completed ===");
        $this->log("");
        
        // Exit with appropriate code
        if ($this->systemStatus === 'critical') {
            exit(2);
        } elseif ($this->systemStatus === 'warning') {
            exit(1);
        } else {
            exit(0);
        }
    }
    
    /**
     * Log a message
     */
    private function log($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
        
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
        echo $logMessage;
    }
    
    /**
     * Log an error message
     */
    private function logError($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] ERROR: {$message}" . PHP_EOL;
        
        file_put_contents($this->errorLogFile, $logMessage, FILE_APPEND | LOCK_EX);
        $this->log("ERROR: {$message}");
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}

// Execute the monitoring
$monitor = new SystemMonitorCron();
$monitor->run();
