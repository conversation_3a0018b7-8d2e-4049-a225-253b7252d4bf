/* Stiluri generale */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}



/* Stiluri pentru formulare */
.search-form {
    background-color: #fff;
    padding: 1.5rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
}

.form-row {
    display: flex;
    margin: 0 -10px;
}

.form-col {
    flex: 1;
    padding: 0 10px;
}

.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    cursor: pointer;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* Stiluri pentru rezultate */
.results {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.results-header {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.results-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.results-body {
    padding: 1rem;
}

.result-item {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.result-item:last-child {
    border-bottom: none;
}

.result-item h3 {
    margin: 0 0 0.5rem;
    font-size: 1.2rem;
}

.result-item p {
    margin: 0 0 0.5rem;
}

.result-item .meta {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Stiluri pentru paginare */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.page-link {
    padding: 0.5rem 0.75rem;
    margin: 0 0.25rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    color: #007bff;
    text-decoration: none;
}

.page-link:hover {
    background-color: #e9ecef;
}

.page-link.active {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
}

.page-link.disabled {
    color: #6c757d;
    pointer-events: none;
    cursor: default;
}

/* Stiluri pentru detalii dosar */
.dosar-details {
    background-color: #fff;
    padding: 1.5rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.dosar-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.dosar-header h2 {
    margin: 0 0 0.5rem;
}

.dosar-section {
    margin-bottom: 1.5rem;
}

.dosar-section h3 {
    margin: 0 0 1rem;
    font-size: 1.2rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.dosar-section table {
    width: 100%;
    border-collapse: collapse;
}

.dosar-section table th,
.dosar-section table td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.dosar-section table th {
    font-weight: 600;
}

/* Stiluri pentru footer */
footer {
    background-color: #343a40;
    color: #fff;
    padding: 1rem 0;
    margin-top: 2rem;
}

footer p {
    margin: 0;
}





/* Stiluri responsive */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }

    .form-col {
        margin-bottom: 1rem;
    }

    .form-col:last-child {
        margin-bottom: 0;
    }

    .card-header h2 {
        font-size: 1.5rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .btn-group .btn {
        width: auto;
        margin-bottom: 0;
    }

    .table-responsive {
        border: none;
    }

    .table th, .table td {
        font-size: 0.875rem;
        padding: 0.5rem;
    }

    .export-actions {
        flex-direction: column;
    }

    .export-actions .btn {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .card-body {
        padding: 1rem;
    }



    .dosar-header h3 {
        font-size: 1.25rem;
    }

    .dosar-section h4 {
        font-size: 1.1rem;
    }
}

/* Social Sharing Styles - Optimized for Compact Layout */
.social-sharing-section {
    margin: 0.75rem 0;
}

.social-sharing-section .card {
    border: 1px solid #e3f2fd;
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
    transition: all 0.2s ease;
}

.social-sharing-section .card:hover {
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.12);
    transform: translateY(-0.5px);
}

.social-sharing-section h6 {
    color: #2c3e50;
    font-weight: 600;
    font-size: 0.85rem;
}

.social-buttons {
    gap: 0.375rem;
}

.social-btn {
    border: 1px solid #007bff;
    color: #007bff;
    background-color: #fff;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    min-width: 100px;
    justify-content: center;
}

/* Compact Social Buttons */
.social-btn-compact {
    border: 1px solid #007bff;
    color: #007bff;
    background-color: #fff;
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    font-weight: 500;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    min-width: 80px;
    justify-content: center;
}

.social-btn:hover {
    background-color: #007bff;
    color: #fff;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.social-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 123, 255, 0.3);
}

.social-btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.social-btn i {
    font-size: 0.875rem;
}

/* Compact Button Hover States */
.social-btn-compact:hover {
    background-color: #007bff;
    color: #fff;
    border-color: #0056b3;
    transform: translateY(-0.5px);
    box-shadow: 0 1px 6px rgba(0, 123, 255, 0.25);
}

.social-btn-compact:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 123, 255, 0.25);
}

.social-btn-compact:focus {
    outline: none;
    box-shadow: 0 0 0 0.15rem rgba(0, 123, 255, 0.25);
}

.social-btn-compact i {
    font-size: 0.8rem;
}

/* Success state for buttons */
.social-btn.btn-success,
.social-btn-compact.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: #fff;
}

.social-btn.btn-success:hover,
.social-btn-compact.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

/* Specific social platform colors on hover */
.social-btn#facebookShareBtn:hover,
.social-btn-compact#facebookShareBtn:hover {
    background-color: #1877f2;
    border-color: #1877f2;
}

.social-btn#whatsappShareBtn:hover,
.social-btn-compact#whatsappShareBtn:hover {
    background-color: #25d366;
    border-color: #25d366;
}

.social-btn#emailShareBtn:hover,
.social-btn-compact#emailShareBtn:hover {
    background-color: #6c757d;
    border-color: #6c757d;
}

.social-btn#copyLinkBtn:hover,
.social-btn-compact#copyLinkBtn:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
}

/* Mobile responsive adjustments - Optimized for Compact Layout */
@media (max-width: 768px) {
    .social-sharing-section .row {
        text-align: center;
    }

    .social-sharing-section .col-md-3 {
        margin-bottom: 0.5rem;
    }

    .social-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .social-btn,
    .social-btn-compact {
        flex: 1 1 calc(50% - 0.2rem);
        min-width: 100px;
        margin-bottom: 0.25rem;
    }
}

@media (max-width: 480px) {
    .social-btn,
    .social-btn-compact {
        flex: 1 1 100%;
        min-width: auto;
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
    }

    .social-sharing-section h6 {
        font-size: 0.8rem;
    }

    .social-sharing-section {
        margin: 0.5rem 0;
    }
}
