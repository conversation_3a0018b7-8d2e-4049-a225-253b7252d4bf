<?php
/**
 * Enhanced Party Search Solution
 * Comprehensive improvements to party search and display functionality
 * Addresses the real issue: User experience when searching for parties with diacritics
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';

/**
 * Extract party name from party object/array
 */
function extractPartyName($party) {
    if (is_array($party) && isset($party['nume'])) {
        return $party['nume'];
    } elseif (is_object($party) && isset($party->nume)) {
        return $party->nume;
    }
    return '';
}

/**
 * Enhanced party search with fuzzy matching and suggestions
 * This function provides comprehensive search capabilities for party names
 */
function enhancedPartySearch($parti, $searchTerm, $options = []) {
    $results = [
        'exact_matches' => [],
        'partial_matches' => [],
        'fuzzy_matches' => [],
        'suggestions' => [],
        'total_parties' => count($parti),
        'search_term' => $searchTerm
    ];
    
    if (empty($parti) || empty($searchTerm)) {
        return $results;
    }
    
    $normalizedSearchTerm = normalizeForSearchEnhanced($searchTerm);
    $searchWords = explode(' ', $normalizedSearchTerm);
    
    foreach ($parti as $index => $party) {
        $partyName = extractPartyName($party);
        if (empty($partyName)) continue;
        
        $normalizedPartyName = normalizeForSearchEnhanced($partyName);
        $matchScore = calculateMatchScore($normalizedPartyName, $normalizedSearchTerm, $searchWords);
        
        $partyData = [
            'index' => $index + 1,
            'name' => $partyName,
            'quality' => extractPartyQuality($party),
            'source' => extractPartySource($party),
            'score' => $matchScore['score'],
            'match_type' => $matchScore['type']
        ];
        
        // Categorize matches based on score and type
        if ($matchScore['type'] === 'exact') {
            $results['exact_matches'][] = $partyData;
        } elseif ($matchScore['type'] === 'partial' && $matchScore['score'] >= 0.7) {
            $results['partial_matches'][] = $partyData;
        } elseif ($matchScore['score'] >= 0.5) {
            $results['fuzzy_matches'][] = $partyData;
        } elseif ($matchScore['score'] >= 0.3) {
            $results['suggestions'][] = $partyData;
        }
    }
    
    // Sort results by score (highest first)
    usort($results['exact_matches'], function($a, $b) { return $b['score'] <=> $a['score']; });
    usort($results['partial_matches'], function($a, $b) { return $b['score'] <=> $a['score']; });
    usort($results['fuzzy_matches'], function($a, $b) { return $b['score'] <=> $a['score']; });
    usort($results['suggestions'], function($a, $b) { return $b['score'] <=> $a['score']; });
    
    return $results;
}

/**
 * Enhanced normalization for search with comprehensive diacritics support
 */
function normalizeForSearchEnhanced($text) {
    if (empty($text)) return '';
    
    $text = trim((string) $text);
    
    // Comprehensive Romanian diacritics mapping
    $diacritics = [
        // Standard Romanian diacritics
        'ă' => 'a', 'â' => 'a', 'î' => 'i', 'ș' => 's', 'ț' => 't',
        'Ă' => 'A', 'Â' => 'A', 'Î' => 'I', 'Ș' => 'S', 'Ț' => 'T',
        
        // Legacy/alternative diacritics
        'ţ' => 't', 'ş' => 's', 'Ţ' => 'T', 'Ş' => 'S',
        
        // Additional variations
        'ã' => 'a', 'Ã' => 'A', 'ì' => 'i', 'Ì' => 'I',
        
        // Common typing mistakes
        'á' => 'a', 'à' => 'a', 'ä' => 'a', 'Á' => 'A', 'À' => 'A', 'Ä' => 'A',
        'é' => 'e', 'è' => 'e', 'ë' => 'e', 'É' => 'E', 'È' => 'E', 'Ë' => 'E',
        'í' => 'i', 'ì' => 'i', 'ï' => 'i', 'Í' => 'I', 'Ì' => 'I', 'Ï' => 'I',
        'ó' => 'o', 'ò' => 'o', 'ö' => 'o', 'Ó' => 'O', 'Ò' => 'O', 'Ö' => 'O',
        'ú' => 'u', 'ù' => 'u', 'ü' => 'u', 'Ú' => 'U', 'Ù' => 'U', 'Ü' => 'U'
    ];
    
    $normalized = strtr($text, $diacritics);
    
    // Remove extra spaces and convert to lowercase
    $normalized = preg_replace('/\s+/', ' ', $normalized);
    $normalized = mb_strtolower($normalized, 'UTF-8');
    
    return $normalized;
}

/**
 * Calculate match score between party name and search term
 */
function calculateMatchScore($partyName, $searchTerm, $searchWords) {
    $score = 0;
    $type = 'none';
    
    // Exact match (highest score)
    if ($partyName === $searchTerm) {
        return ['score' => 1.0, 'type' => 'exact'];
    }
    
    // Check if search term is contained in party name
    if (strpos($partyName, $searchTerm) !== false) {
        $score = 0.9;
        $type = 'partial';
    }
    
    // Check if party name is contained in search term
    if (strpos($searchTerm, $partyName) !== false) {
        $score = max($score, 0.85);
        $type = 'partial';
    }
    
    // Word-by-word matching
    $partyWords = explode(' ', $partyName);
    $matchedWords = 0;
    $totalWords = count($searchWords);
    
    foreach ($searchWords as $searchWord) {
        if (strlen($searchWord) < 2) continue; // Skip very short words
        
        foreach ($partyWords as $partyWord) {
            if (strpos($partyWord, $searchWord) !== false || strpos($searchWord, $partyWord) !== false) {
                $matchedWords++;
                break;
            }
        }
    }
    
    if ($totalWords > 0) {
        $wordScore = $matchedWords / $totalWords;
        if ($wordScore > $score) {
            $score = $wordScore;
            $type = $wordScore >= 0.8 ? 'partial' : 'fuzzy';
        }
    }
    
    // Levenshtein distance for fuzzy matching
    if ($score < 0.5) {
        $maxLen = max(strlen($partyName), strlen($searchTerm));
        if ($maxLen > 0) {
            $distance = levenshtein($partyName, $searchTerm);
            $similarity = 1 - ($distance / $maxLen);
            if ($similarity > $score) {
                $score = $similarity;
                $type = $similarity >= 0.7 ? 'fuzzy' : 'suggestion';
            }
        }
    }
    
    return ['score' => $score, 'type' => $type];
}

/**
 * Extract party quality with fallback
 */
function extractPartyQuality($party) {
    if (is_array($party) && isset($party['calitate'])) {
        return $party['calitate'];
    } elseif (is_object($party) && isset($party->calitate)) {
        return $party->calitate;
    }
    return 'Parte';
}

/**
 * Extract party source with fallback
 */
function extractPartySource($party) {
    if (is_array($party) && isset($party['source'])) {
        return $party['source'];
    } elseif (is_object($party) && isset($party->source)) {
        return $party->source;
    }
    return 'unknown';
}

/**
 * Generate search suggestions based on available parties
 */
function generateSearchSuggestions($parti, $searchTerm, $limit = 10) {
    $suggestions = [];
    $normalizedSearchTerm = normalizeForSearchEnhanced($searchTerm);
    
    foreach ($parti as $party) {
        $partyName = extractPartyName($party);
        if (empty($partyName)) continue;
        
        $normalizedPartyName = normalizeForSearchEnhanced($partyName);
        
        // Check for partial matches that could be suggestions
        if (strpos($normalizedPartyName, $normalizedSearchTerm) !== false ||
            strpos($normalizedSearchTerm, $normalizedPartyName) !== false) {
            
            $suggestions[] = [
                'name' => $partyName,
                'normalized' => $normalizedPartyName,
                'quality' => extractPartyQuality($party)
            ];
        }
    }
    
    // Remove duplicates and limit results
    $uniqueSuggestions = [];
    $seen = [];
    
    foreach ($suggestions as $suggestion) {
        $key = $suggestion['normalized'];
        if (!isset($seen[$key])) {
            $seen[$key] = true;
            $uniqueSuggestions[] = $suggestion;
            
            if (count($uniqueSuggestions) >= $limit) {
                break;
            }
        }
    }
    
    return $uniqueSuggestions;
}

/**
 * Test the enhanced search functionality
 */
function testEnhancedSearch() {
    echo "<h2>🧪 Testing Enhanced Party Search</h2>";
    
    // Test with case 130/98/2022
    require_once 'src/Services/DosarService.php';
    $dosarService = new \App\Services\DosarService();
    
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');
    
    if (!$dosar || empty($dosar->parti)) {
        echo "<p class='error'>❌ Could not retrieve case data</p>";
        return;
    }
    
    $testSearches = [
        'Saragea Tudorita',      // Without diacritics
        'SARAGEA TUDORIŢA',      // With diacritics
        'Saragea',               // Partial name
        'Tudorita',              // Partial surname
        'ŞERBĂNESCU ELENA',      // Another party
        'Serbanescu Elena'       // Without diacritics
    ];
    
    foreach ($testSearches as $searchTerm) {
        echo "<h3>Search: '{$searchTerm}'</h3>";
        
        $results = enhancedPartySearch($dosar->parti, $searchTerm);
        
        echo "<p><strong>Total parties:</strong> {$results['total_parties']}</p>";
        echo "<p><strong>Exact matches:</strong> " . count($results['exact_matches']) . "</p>";
        echo "<p><strong>Partial matches:</strong> " . count($results['partial_matches']) . "</p>";
        echo "<p><strong>Fuzzy matches:</strong> " . count($results['fuzzy_matches']) . "</p>";
        echo "<p><strong>Suggestions:</strong> " . count($results['suggestions']) . "</p>";
        
        // Show best matches
        $allMatches = array_merge(
            $results['exact_matches'],
            $results['partial_matches'],
            $results['fuzzy_matches']
        );
        
        if (!empty($allMatches)) {
            echo "<table border='1'>";
            echo "<tr><th>Name</th><th>Quality</th><th>Score</th><th>Type</th></tr>";
            foreach (array_slice($allMatches, 0, 5) as $match) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($match['name']) . "</td>";
                echo "<td>" . htmlspecialchars($match['quality']) . "</td>";
                echo "<td>" . round($match['score'], 3) . "</td>";
                echo "<td>" . $match['match_type'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>No matches found</p>";
        }
        
        echo "<hr>";
    }
}

// Run the test if this file is accessed directly
if (basename($_SERVER['PHP_SELF']) === 'enhanced_party_search_solution.php') {
    echo "<!DOCTYPE html>";
    echo "<html><head><title>Enhanced Party Search Solution</title>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .error { color: red; }
        .warning { color: orange; }
        .success { color: green; }
        table { border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style></head><body>";
    
    echo "<h1>🔍 Enhanced Party Search Solution</h1>";
    testEnhancedSearch();
    
    echo "</body></html>";
}
?>
