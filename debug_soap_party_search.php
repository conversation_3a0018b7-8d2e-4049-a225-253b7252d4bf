<?php
/**
 * Debug SOAP API party search to understand why we're only getting 2 results instead of 4
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';

use App\Services\DosarService;

echo "=== DEBUGGING SOAP API PARTY SEARCH ===" . PHP_EOL;
echo "Investigating why 'Saragea Tudorita' only returns 2 results instead of expected 4" . PHP_EOL;
echo PHP_EOL;

$searchTerm = 'Saragea Tudorita';

try {
    $dosarService = new DosarService();
    
    // Step 1: Get raw SOAP response without any filtering
    echo "=== STEP 1: Raw SOAP API Response ===" . PHP_EOL;
    
    $reflection = new ReflectionClass($dosarService);
    $method = $reflection->getMethod('executeSoapCallWithRetry');
    $method->setAccessible(true);
    
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => $searchTerm,
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    echo "SOAP Parameters: " . json_encode($searchParams, JSON_UNESCAPED_UNICODE) . PHP_EOL;
    
    $rawResponse = $method->invoke($dosarService, 'CautareDosare2', $searchParams, "Debug party search");
    
    if (!$rawResponse || !isset($rawResponse->CautareDosare2Result)) {
        echo "❌ No SOAP response received" . PHP_EOL;
        exit;
    }
    
    $dosare = [];
    if (isset($rawResponse->CautareDosare2Result->Dosar)) {
        $rawDosare = $rawResponse->CautareDosare2Result->Dosar;
        if (!is_array($rawDosare)) {
            $rawDosare = [$rawDosare];
        }
        $dosare = $rawDosare;
    }
    
    echo "Raw SOAP API returned " . count($dosare) . " cases" . PHP_EOL;
    echo PHP_EOL;
    
    // Step 2: Analyze each case from raw SOAP response
    echo "=== STEP 2: Analyzing Raw SOAP Cases ===" . PHP_EOL;
    
    foreach ($dosare as $index => $dosar) {
        echo "Case " . ($index + 1) . ": " . ($dosar->institutie ?? 'Unknown') . " - " . ($dosar->numar ?? 'Unknown') . PHP_EOL;
        
        // Check raw parties from SOAP
        $rawParties = [];
        if (isset($dosar->parti) && isset($dosar->parti->DosarParte)) {
            $rawParties = $dosar->parti->DosarParte;
            if (!is_array($rawParties)) {
                $rawParties = [$rawParties];
            }
        }
        
        echo "  Raw SOAP parties: " . count($rawParties) . PHP_EOL;
        
        // Look for target party in raw SOAP data
        $foundInSoap = false;
        foreach ($rawParties as $party) {
            $partyName = $party->nume ?? '';
            if (stripos($partyName, 'SARAGEA') !== false || stripos($partyName, 'TUDORITA') !== false) {
                echo "  ✅ FOUND in SOAP: $partyName" . PHP_EOL;
                $foundInSoap = true;
            }
        }
        
        if (!$foundInSoap) {
            echo "  ❌ Target party NOT FOUND in raw SOAP data" . PHP_EOL;
        }
        
        echo PHP_EOL;
    }
    
    // Step 3: Test the processed results (with hybrid extraction)
    echo "=== STEP 3: Processed Results (with hybrid extraction) ===" . PHP_EOL;
    
    $processedResults = $dosarService->cautareAvansata($searchParams);
    echo "Processed results: " . count($processedResults) . " cases" . PHP_EOL;
    echo PHP_EOL;
    
    foreach ($processedResults as $index => $dosar) {
        echo "Processed Case " . ($index + 1) . ": " . ($dosar->institutie ?? 'Unknown') . " - " . ($dosar->numar ?? 'Unknown') . PHP_EOL;
        
        $parti = $dosar->parti ?? [];
        echo "  Total parties (SOAP + decision text): " . count($parti) . PHP_EOL;
        
        $soapParties = 0;
        $decisionParties = 0;
        $foundTarget = false;
        
        foreach ($parti as $party) {
            $partyName = is_object($party) ? ($party->nume ?? '') : ($party['nume'] ?? '');
            $partySource = is_object($party) ? ($party->source ?? 'soap_api') : ($party['source'] ?? 'soap_api');
            
            if ($partySource === 'soap_api') {
                $soapParties++;
            } else {
                $decisionParties++;
            }
            
            if (stripos($partyName, 'SARAGEA') !== false || stripos($partyName, 'TUDORITA') !== false) {
                echo "  ✅ FOUND: $partyName [Source: $partySource]" . PHP_EOL;
                $foundTarget = true;
            }
        }
        
        echo "  SOAP parties: $soapParties, Decision text parties: $decisionParties" . PHP_EOL;
        
        if (!$foundTarget) {
            echo "  ❌ Target party NOT FOUND in processed results" . PHP_EOL;
        }
        
        echo PHP_EOL;
    }
    
    // Step 4: Test different search variations
    echo "=== STEP 4: Testing Search Variations ===" . PHP_EOL;
    
    $variations = [
        'SARAGEA TUDORIŢA',
        'SARAGEA TUDORITA', 
        'Saragea Tudoriţa',
        'Saragea Tudorita',
        'TUDORIŢA SARAGEA',
        'TUDORITA SARAGEA'
    ];
    
    foreach ($variations as $variation) {
        echo "Testing: '$variation'" . PHP_EOL;
        
        $testParams = $searchParams;
        $testParams['numeParte'] = $variation;
        
        try {
            $testResponse = $method->invoke($dosarService, 'CautareDosare2', $testParams, "Test variation");
            
            $testDosare = [];
            if (isset($testResponse->CautareDosare2Result->Dosar)) {
                $rawTestDosare = $testResponse->CautareDosare2Result->Dosar;
                if (!is_array($rawTestDosare)) {
                    $rawTestDosare = [$rawTestDosare];
                }
                $testDosare = $rawTestDosare;
            }
            
            echo "  Raw SOAP results: " . count($testDosare) . PHP_EOL;
            
        } catch (Exception $e) {
            echo "  Error: " . $e->getMessage() . PHP_EOL;
        }
    }
    
    echo PHP_EOL;
    echo "=== ANALYSIS COMPLETE ===" . PHP_EOL;
    echo "If raw SOAP API only returns 2 cases, then the limitation is at the SOAP API level." . PHP_EOL;
    echo "If raw SOAP returns more cases but processed results show fewer, then our filtering is too aggressive." . PHP_EOL;
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
}
