// Debug script pentru verificarea funcțiilor în index.php
// Adaugă acest script în consola browser-ului când ești pe index.php

console.log('=== DEBUG FUNCȚII INDEX.PHP ===');

// Test 1: Verifică dacă funcțiile există în window scope
console.log('1. Verificare funcții în window scope:');
console.log('   - expandAllResults:', typeof window.expandAllResults);
console.log('   - collapseAllResults:', typeof window.collapseAllResults);
console.log('   - toggleTermResults:', typeof window.toggleTermResults);
console.log('   - showNotification:', typeof window.showNotification);

// Test 2: Verifică dacă funcțiile există în scope-ul global
console.log('2. Verificare funcții în scope global:');
try {
    console.log('   - expandAllResults:', typeof expandAllResults);
} catch (e) {
    console.log('   - expandAllResults: ERROR -', e.message);
}

try {
    console.log('   - collapseAllResults:', typeof collapseAllResults);
} catch (e) {
    console.log('   - collapseAllResults: ERROR -', e.message);
}

try {
    console.log('   - toggleTermResults:', typeof toggleTermResults);
} catch (e) {
    console.log('   - toggleTermResults: ERROR -', e.message);
}

try {
    console.log('   - showNotification:', typeof showNotification);
} catch (e) {
    console.log('   - showNotification: ERROR -', e.message);
}

// Test 3: Verifică elementele DOM necesare
console.log('3. Verificare elemente DOM:');
const termContents = document.querySelectorAll('[id^="termContent"]');
const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
console.log('   - termContent elements:', termContents.length);
console.log('   - toggleIcon elements:', toggleIcons.length);

// Test 4: Verifică butoanele de expandare/restrângere
console.log('4. Verificare butoane:');
const expandButton = document.querySelector('button[onclick="expandAllResults()"]');
const collapseButton = document.querySelector('button[onclick="collapseAllResults()"]');
console.log('   - Expand button:', expandButton ? 'FOUND' : 'NOT FOUND');
console.log('   - Collapse button:', collapseButton ? 'FOUND' : 'NOT FOUND');

// Test 5: Încearcă să apeleze funcțiile direct
console.log('5. Test apelare directă:');

if (typeof expandAllResults === 'function') {
    console.log('   - Calling expandAllResults()...');
    try {
        expandAllResults();
        console.log('   - expandAllResults() SUCCESS');
    } catch (e) {
        console.log('   - expandAllResults() ERROR:', e.message);
    }
} else {
    console.log('   - expandAllResults() NOT AVAILABLE');
}

if (typeof collapseAllResults === 'function') {
    console.log('   - Calling collapseAllResults()...');
    try {
        collapseAllResults();
        console.log('   - collapseAllResults() SUCCESS');
    } catch (e) {
        console.log('   - collapseAllResults() ERROR:', e.message);
    }
} else {
    console.log('   - collapseAllResults() NOT AVAILABLE');
}

// Test 6: Verifică erori JavaScript
console.log('6. Verificare erori JavaScript:');
window.addEventListener('error', function(e) {
    console.log('   - JavaScript ERROR:', e.message, 'at', e.filename + ':' + e.lineno);
});

console.log('=== SFÂRȘIT DEBUG ===');

// Instrucțiuni pentru utilizator
console.log('\n📋 INSTRUCȚIUNI:');
console.log('1. Copiază și lipește acest script în consola browser-ului (F12)');
console.log('2. Navighează la index.php și efectuează o căutare cu mai mulți termeni');
console.log('3. Rulează din nou scriptul pentru a vedea rezultatele');
console.log('4. Testează butoanele "Expandează toate" și "Restrânge toate"');
console.log('5. Verifică dacă apar erori în consolă');
