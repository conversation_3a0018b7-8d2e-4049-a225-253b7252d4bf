<?php

/**
 * <PERSON><PERSON>ier bootstrap pentru inițializarea aplicației
 */

// Încărcăm autoloader-ul Composer
require_once __DIR__ . '/vendor/autoload.php';

// Încărcăm configurația bazei de date
if (file_exists(__DIR__ . '/database_config.php')) {
    require_once __DIR__ . '/database_config.php';
}

// Încărcăm configurațiile
require_once __DIR__ . '/src/Config/constants.php';

// Încărcăm configurația TCPDF
require_once __DIR__ . '/src/Config/tcpdf_config.php';

// Configurăm raportarea erorilor
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', LOG_DIR . '/php_errors.log');

// Creăm directoarele necesare dacă nu există
$directories = [
    LOG_DIR,
    CACHE_DIR,
    EXPORT_DIR,
    LOG_DIR . '/archived'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Inițializăm sesiunea doar dacă nu este deja pornită
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
