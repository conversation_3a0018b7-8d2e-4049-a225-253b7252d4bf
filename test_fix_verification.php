<?php
// Test the fix for wildcard search display
require_once 'bootstrap.php';

use App\Services\DosarService;

echo "<h1>Testing Fix for Wildcard Search Display</h1>";

$searchTerm = "14096/3/2024*";

try {
    $dosarService = new DosarService();
    
    // Test the backend results
    echo "<h2>1. Backend Results (Should be 3)</h2>";
    $backendResults = $dosarService->cautareAvansata(['numarDosar' => $searchTerm]);
    echo "<p><strong>Backend count: " . count($backendResults) . "</strong></p>";
    
    $literalAsteriskFound = false;
    foreach ($backendResults as $index => $dosar) {
        $hasAsterisk = strpos($dosar->numar, '*') !== false;
        if ($hasAsterisk) $literalAsteriskFound = true;
        
        echo "<div style='background: " . ($hasAsterisk ? "#fff3cd" : "#e7f3ff") . "; padding: 10px; margin: 5px 0; border: 1px solid " . ($hasAsterisk ? "#ffc107" : "#007bff") . ";'>";
        echo "<strong>Result #" . ($index + 1) . ":</strong><br>";
        echo "Case Number: " . ($dosar->numar ?? 'N/A') . "<br>";
        echo "Institution: " . ($dosar->instanta ?? 'N/A') . "<br>";
        echo "Has Asterisk: " . ($hasAsterisk ? "YES" : "NO") . "<br>";
        echo "Object: " . substr($dosar->obiect ?? 'N/A', 0, 100) . "...<br>";
        echo "</div>";
    }
    
    if ($literalAsteriskFound) {
        echo "<p style='color: green; font-weight: bold;'>✓ Literal asterisk case found in backend results</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>✗ Literal asterisk case NOT found in backend results</p>";
    }
    
    // Test the search type detection
    echo "<h2>2. Search Type Detection</h2>";
    
    function detectSearchType($term) {
        $cleanTerm = trim($term, '"\'');
        
        if (preg_match('/^\d+\/\d+(?:\/\d+)?\*$/', $cleanTerm)) {
            return 'numarDosar';
        }
        
        if (preg_match('/^\d+\/\d+(?:\/\d+)?\/[a-zA-Z0-9]+$/', $cleanTerm)) {
            return 'numarDosar';
        }
        
        if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
            return 'numarDosar';
        }
        
        if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
            return 'numarDosar';
        }
        
        return 'numeParte';
    }
    
    $detectedType = detectSearchType($searchTerm);
    echo "<p><strong>Search term:</strong> '$searchTerm'</p>";
    echo "<p><strong>Detected type:</strong> '$detectedType'</p>";
    
    if ($detectedType === 'numarDosar') {
        echo "<p style='color: green;'>✓ Search term correctly detected as case number</p>";
    } else {
        echo "<p style='color: red;'>✗ Search term incorrectly detected as: $detectedType</p>";
    }
    
    // Simulate the search results structure
    echo "<h2>3. Search Results Structure</h2>";
    
    $searchResults = [
        [
            'term' => $searchTerm,
            'type' => $detectedType,
            'results' => $backendResults,
            'count' => count($backendResults),
            'error' => null
        ]
    ];
    
    // Assign searchType to dosar objects
    foreach ($searchResults as $termResult) {
        foreach ($termResult['results'] as $dosar) {
            $dosar->searchTerm = $termResult['term'];
            $dosar->searchType = $termResult['type'];
        }
    }
    
    echo "<p><strong>Search results structure created with " . count($searchResults[0]['results']) . " results</strong></p>";
    
    // Check each result
    foreach ($searchResults[0]['results'] as $index => $dosar) {
        $hasAsterisk = strpos($dosar->numar, '*') !== false;
        echo "<div style='background: " . ($hasAsterisk ? "#d4edda" : "#f8f9fa") . "; padding: 8px; margin: 3px 0; border: 1px solid " . ($hasAsterisk ? "#c3e6cb" : "#dee2e6") . ";'>";
        echo "<strong>Dosar #" . ($index + 1) . ":</strong> " . ($dosar->numar ?? 'N/A') . "<br>";
        echo "<strong>Search Type:</strong> " . ($dosar->searchType ?? 'NOT SET') . "<br>";
        echo "<strong>Search Term:</strong> " . ($dosar->searchTerm ?? 'NOT SET') . "<br>";
        if ($hasAsterisk) {
            echo "<strong style='color: green;'>This is the literal asterisk case!</strong><br>";
        }
        echo "</div>";
    }
    
    echo "<h2>4. Expected Frontend Behavior</h2>";
    echo "<div style='background: #e7f3ff; padding: 15px; margin: 10px 0; border: 1px solid #007bff; border-radius: 5px;'>";
    echo "<h3>What should happen in the web interface:</h3>";
    echo "<ul>";
    echo "<li><strong>Message should show:</strong> \"3 rezultate găsite pentru termenul '14096/3/2024*'\"</li>";
    echo "<li><strong>All 3 cases should be visible</strong> in the table/cards</li>";
    echo "<li><strong>The literal asterisk case should be included</strong> and visible</li>";
    echo "<li><strong>Exact match filter should NOT be auto-enabled</strong></li>";
    echo "<li><strong>If exact match filter is manually enabled, all 3 cases should still be visible</strong> (because they're all case number searches)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>5. Debugging Information</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border: 1px solid #ffc107; border-radius: 5px;'>";
    echo "<h3>If the issue persists, check:</h3>";
    echo "<ul>";
    echo "<li>Browser console for JavaScript errors</li>";
    echo "<li>Whether the exact match filter checkbox is checked</li>";
    echo "<li>Whether the data-search-type attribute is correctly set on table rows</li>";
    echo "<li>Whether there are any CSS rules hiding elements</li>";
    echo "</ul>";
    echo "<p><strong>To test manually:</strong></p>";
    echo "<ol>";
    echo "<li>Go to the main page</li>";
    echo "<li>Search for '14096/3/2024*'</li>";
    echo "<li>Check if you see 3 results including the one with asterisk</li>";
    echo "<li>If not, open browser console and look for debug messages starting with 'EXACT MATCH FILTER:'</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h2>Summary</h2>";
echo "<p><strong>The fix should ensure that:</strong></p>";
echo "<ol>";
echo "<li>All 3 results are displayed for '14096/3/2024*' search</li>";
echo "<li>The exact match filter doesn't auto-enable and hide results</li>";
echo "<li>Case number searches are always included in exact match filtering</li>";
echo "<li>Better debugging information is available in the console</li>";
echo "</ol>";
?>
