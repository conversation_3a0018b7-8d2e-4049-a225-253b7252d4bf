<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "<!DOCTYPE html>
<html lang='ro'>
<head>
    <meta charset='UTF-8'>
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Test Căutare Simplă - Portal Judiciar România</title>
    <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css\" rel=\"stylesheet\">
    <link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\" rel=\"stylesheet\">
    <style>
        .test-section {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: #f8f9fa;
        }
        .result-box {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .party-details {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin: 5px 0; }
    </style>
</head>
<body>
    <div class=\"container mt-4\">
        <h1 class=\"text-center mb-4\">
            <i class=\"fas fa-search me-2 text-primary\"></i>
            Test Căutare Simplă
        </h1>";

$dosarService = new DosarService();

// Test cu un termen simplu
$testTerm = 'POPESCU';

echo "<div class='test-section'>";
echo "<h3><i class='fas fa-search me-2'></i>Test pentru: " . htmlspecialchars($testTerm) . "</h3>";

try {
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'numeParte' => $testTerm,
        'obiectDosar' => '',
        'dataStart' => '',
        'dataStop' => '',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => ''
    ];
    
    echo "<div class='result-box'>";
    echo "<h5>Parametri căutare:</h5>";
    echo "<div class='party-details'>";
    foreach ($searchParams as $key => $value) {
        echo "$key: " . ($value === null ? 'NULL' : ($value === '' ? 'EMPTY' : $value)) . "\n";
    }
    echo "</div>";
    echo "</div>";
    
    $startTime = microtime(true);
    $results = $dosarService->cautareAvansata($searchParams);
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    $count = count($results);
    
    echo "<div class='success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "<strong>Rezultate găsite:</strong> $count";
    echo " <small>(timp: {$duration}ms)</small>";
    echo "</div>";
    
    if ($count > 0) {
        echo "<h5>Analiza primelor 5 rezultate:</h5>";
        
        $displayResults = array_slice($results, 0, 5);
        foreach ($displayResults as $index => $dosar) {
            echo "<div class='result-box'>";
            echo "<h6>Rezultatul " . ($index + 1) . ":</h6>";
            
            echo "<strong>Număr dosar:</strong> " . htmlspecialchars($dosar->numar ?? 'N/A') . "<br>";
            echo "<strong>Instanță:</strong> " . htmlspecialchars($dosar->institutie ?? 'N/A') . "<br>";
            echo "<strong>Obiect:</strong> " . htmlspecialchars(substr($dosar->obiect ?? 'N/A', 0, 150)) . "<br>";
            
            $parti = $dosar->parti ?? [];
            echo "<strong>Numărul de părți:</strong> " . count($parti) . "<br>";
            
            if (count($parti) > 0) {
                echo "<strong>Detalii părți:</strong><br>";
                echo "<div class='party-details'>";
                
                foreach (array_slice($parti, 0, 10) as $partyIndex => $party) {
                    echo "Partea " . ($partyIndex + 1) . ":\n";
                    echo "  Tip obiect: " . gettype($party) . "\n";
                    
                    if (is_object($party)) {
                        echo "  Nume: " . ($party->nume ?? 'N/A') . "\n";
                        echo "  Calitate: " . ($party->calitate ?? 'N/A') . "\n";
                        echo "  Source: " . ($party->source ?? 'N/A') . "\n";
                        
                        // Test dacă numele conține termenul de căutare
                        $nume = $party->nume ?? '';
                        if (stripos($nume, $testTerm) !== false) {
                            echo "  *** POTRIVIRE GĂSITĂ ***\n";
                        }
                    } elseif (is_array($party)) {
                        echo "  Nume: " . ($party['nume'] ?? 'N/A') . "\n";
                        echo "  Calitate: " . ($party['calitate'] ?? 'N/A') . "\n";
                        echo "  Source: " . ($party['source'] ?? 'N/A') . "\n";
                        
                        // Test dacă numele conține termenul de căutare
                        $nume = $party['nume'] ?? '';
                        if (stripos($nume, $testTerm) !== false) {
                            echo "  *** POTRIVIRE GĂSITĂ ***\n";
                        }
                    } else {
                        echo "  Valoare: " . var_export($party, true) . "\n";
                    }
                    echo "\n";
                }
                
                if (count($parti) > 10) {
                    echo "... și încă " . (count($parti) - 10) . " părți\n";
                }
                
                echo "</div>";
                
                // Test funcțiile de procesare părți
                echo "<strong>Test funcții procesare:</strong><br>";
                
                // Simulez funcțiile din index.php
                $matchingParty = null;
                foreach ($parti as $party) {
                    $partyName = is_object($party) ? ($party->nume ?? '') : ($party['nume'] ?? '');
                    if (stripos($partyName, $testTerm) !== false) {
                        $matchingParty = $party;
                        break;
                    }
                }
                
                if ($matchingParty) {
                    $matchedName = is_object($matchingParty) ? ($matchingParty->nume ?? '') : ($matchingParty['nume'] ?? '');
                    $matchedQuality = is_object($matchingParty) ? ($matchingParty->calitate ?? '') : ($matchingParty['calitate'] ?? '');
                    echo "<div class='success'>Partea potrivită: " . htmlspecialchars($matchedName) . " (" . htmlspecialchars($matchedQuality) . ")</div>";
                } else {
                    echo "<div class='warning'>Nu s-a găsit partea potrivită pentru termenul '" . htmlspecialchars($testTerm) . "'</div>";
                    
                    // Afișez toate numele pentru debugging
                    echo "<div class='warning'>Toate numele din părți:<br>";
                    foreach ($parti as $party) {
                        $partyName = is_object($party) ? ($party->nume ?? '') : ($party['nume'] ?? '');
                        echo "- " . htmlspecialchars($partyName) . "<br>";
                    }
                    echo "</div>";
                }
            }
            
            echo "</div>";
        }
        
        // Test cu ultimele 5 rezultate pentru a vedea dacă sunt diferite
        if ($count > 5) {
            echo "<h5>Analiza ultimelor 5 rezultate:</h5>";
            
            $lastResults = array_slice($results, -5);
            foreach ($lastResults as $index => $dosar) {
                echo "<div class='result-box'>";
                echo "<h6>Ultimul rezultat " . ($index + 1) . ":</h6>";
                
                echo "<strong>Număr dosar:</strong> " . htmlspecialchars($dosar->numar ?? 'N/A') . "<br>";
                echo "<strong>Instanță:</strong> " . htmlspecialchars($dosar->institutie ?? 'N/A') . "<br>";
                echo "<strong>Părți:</strong> " . count($dosar->parti ?? []) . " părți<br>";
                
                echo "</div>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
    echo "<strong>Eroare:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</div>";

// Test cu un alt termen
$testTerm2 = 'IONESCU';

echo "<div class='test-section'>";
echo "<h3><i class='fas fa-search me-2'></i>Test pentru: " . htmlspecialchars($testTerm2) . "</h3>";

try {
    $searchParams2 = [
        'numarDosar' => '',
        'institutie' => null,
        'numeParte' => $testTerm2,
        'obiectDosar' => '',
        'dataStart' => '',
        'dataStop' => '',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => ''
    ];
    
    $startTime = microtime(true);
    $results2 = $dosarService->cautareAvansata($searchParams2);
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    $count2 = count($results2);
    
    echo "<div class='success'>";
    echo "<i class='fas fa-check-circle me-2'></i>";
    echo "<strong>Rezultate găsite:</strong> $count2";
    echo " <small>(timp: {$duration}ms)</small>";
    echo "</div>";
    
    if ($count2 > 0) {
        echo "<h5>Primul și ultimul rezultat:</h5>";
        
        // Primul rezultat
        $firstResult = $results2[0];
        echo "<div class='result-box'>";
        echo "<h6>Primul rezultat:</h6>";
        echo "<strong>Număr:</strong> " . htmlspecialchars($firstResult->numar ?? 'N/A') . "<br>";
        echo "<strong>Părți:</strong> " . count($firstResult->parti ?? []) . " părți<br>";
        echo "</div>";
        
        // Ultimul rezultat
        if ($count2 > 1) {
            $lastResult = $results2[$count2 - 1];
            echo "<div class='result-box'>";
            echo "<h6>Ultimul rezultat:</h6>";
            echo "<strong>Număr:</strong> " . htmlspecialchars($lastResult->numar ?? 'N/A') . "<br>";
            echo "<strong>Părți:</strong> " . count($lastResult->parti ?? []) . " părți<br>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
    echo "<strong>Eroare:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</div>";

echo "
        <div class=\"text-center mt-4\">
            <a href=\"index.php\" class=\"btn btn-success btn-lg\">
                <i class=\"fas fa-home me-2\"></i>
                Înapoi la Portal
            </a>
        </div>
    </div>
</body>
</html>";
?>
