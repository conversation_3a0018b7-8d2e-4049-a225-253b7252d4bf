<?php
/**
 * Portal Judiciar - Debug Contact Functionality
 * 
 * Pagină pentru debugging și testarea funcționalității de contact
 * cu raportare detaliată a erorilor
 */

// Încărcăm bootstrap-ul aplicației
require_once 'bootstrap.php';

// Importăm clasele necesare
use App\Helpers\SecurityHelper;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\SMTP;

// Inițializăm sesiunea dacă nu este deja inițializată
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$debugResults = [];
$testEmail = '<EMAIL>';
$testMessage = 'Acesta este un mesaj de test pentru verificarea funcționalității de contact.';

// Test 1: Verificăm constantele SMTP
$debugResults['smtp_config'] = [
    'SMTP_HOST' => SMTP_HOST,
    'SMTP_PORT' => SMTP_PORT,
    'SMTP_USERNAME' => SMTP_USERNAME,
    'SMTP_PASSWORD' => substr(SMTP_PASSWORD, 0, 3) . '***', // Ascundem parola
    'CONTACT_EMAIL' => CONTACT_EMAIL,
    'CONTACT_NAME' => CONTACT_NAME,
    'credentials_configured' => (SMTP_USERNAME !== '<EMAIL>' && SMTP_PASSWORD !== 'your-app-password')
];

// Test 2: Verificăm extensiile PHP necesare
$debugResults['php_extensions'] = [
    'openssl' => extension_loaded('openssl'),
    'mbstring' => extension_loaded('mbstring'),
    'json' => extension_loaded('json'),
    'curl' => extension_loaded('curl'),
    'sockets' => extension_loaded('sockets')
];

// Test 3: Verificăm PHPMailer
$debugResults['phpmailer'] = [
    'class_exists' => class_exists('PHPMailer\PHPMailer\PHPMailer'),
    'version' => class_exists('PHPMailer\PHPMailer\PHPMailer') ? PHPMailer::VERSION : 'N/A'
];

// Test 4: Testăm conexiunea SMTP (doar dacă credentialele sunt configurate)
if ($debugResults['smtp_config']['credentials_configured']) {
    try {
        $mail = new PHPMailer(true);
        
        // Activăm debug mode pentru SMTP
        $mail->SMTPDebug = SMTP::DEBUG_CONNECTION;
        $mail->Debugoutput = function($str, $level) use (&$debugResults) {
            if (!isset($debugResults['smtp_debug'])) {
                $debugResults['smtp_debug'] = [];
            }
            $debugResults['smtp_debug'][] = "Level $level: " . trim($str);
        };
        
        // Configurăm serverul SMTP
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = SMTP_PORT;
        $mail->Timeout = 10; // Timeout redus pentru test
        
        // Testăm doar conexiunea, nu trimitem email
        $mail->smtpConnect();
        $debugResults['smtp_connection'] = [
            'status' => 'success',
            'message' => 'Conexiunea SMTP a fost stabilită cu succes'
        ];
        $mail->smtpClose();
        
    } catch (Exception $e) {
        $debugResults['smtp_connection'] = [
            'status' => 'error',
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }
} else {
    $debugResults['smtp_connection'] = [
        'status' => 'skipped',
        'message' => 'Credentialele SMTP nu sunt configurate'
    ];
}

// Test 5: Verificăm permisiunile pentru loguri
$debugResults['file_permissions'] = [
    'logs_dir_exists' => is_dir(LOG_DIR),
    'logs_dir_writable' => is_writable(LOG_DIR),
    'contact_errors_writable' => is_writable(LOG_DIR . '/contact_errors.log') || is_writable(LOG_DIR)
];

// Test 6: Testăm SecurityHelper
try {
    $csrfToken = SecurityHelper::generateCSRFToken();
    $debugResults['security_helper'] = [
        'csrf_generation' => !empty($csrfToken),
        'csrf_validation' => SecurityHelper::validateCSRFToken($csrfToken),
        'email_validation' => SecurityHelper::validateEmail($testEmail),
        'rate_limit_check' => SecurityHelper::checkRateLimit('debug_test', 5, 3600)
    ];
} catch (Exception $e) {
    $debugResults['security_helper'] = [
        'error' => $e->getMessage()
    ];
}

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Contact - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .debug-container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .debug-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            padding: 1.5rem;
        }
        .debug-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        .debug-item:last-child {
            border-bottom: none;
        }
        .status-success {
            color: #28a745;
        }
        .status-error {
            color: #dc3545;
        }
        .status-warning {
            color: #ffc107;
        }
        .status-info {
            color: #17a2b8;
        }
        .debug-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .config-value {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="text-center mb-4">
            <h1 class="display-5">
                <i class="fas fa-bug me-2"></i>
                Debug Contact Functionality
            </h1>
            <p class="lead">Diagnosticare detaliată a problemelor de contact</p>
        </div>

        <!-- SMTP Configuration -->
        <div class="debug-section">
            <h3><i class="fas fa-server me-2"></i>Configurare SMTP</h3>
            <?php foreach ($debugResults['smtp_config'] as $key => $value): ?>
                <?php if ($key === 'credentials_configured'): ?>
                    <div class="debug-item">
                        <span>Credentiale configurate</span>
                        <span class="<?php echo $value ? 'status-success' : 'status-error'; ?>">
                            <i class="fas fa-<?php echo $value ? 'check' : 'times'; ?>"></i>
                            <?php echo $value ? 'Da' : 'Nu - Folosește valori placeholder'; ?>
                        </span>
                    </div>
                <?php else: ?>
                    <div class="debug-item">
                        <span><?php echo htmlspecialchars($key); ?></span>
                        <span class="config-value"><?php echo htmlspecialchars($value); ?></span>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>

        <!-- PHP Extensions -->
        <div class="debug-section">
            <h3><i class="fas fa-puzzle-piece me-2"></i>Extensii PHP</h3>
            <?php foreach ($debugResults['php_extensions'] as $ext => $loaded): ?>
                <div class="debug-item">
                    <span><?php echo htmlspecialchars($ext); ?></span>
                    <span class="<?php echo $loaded ? 'status-success' : 'status-error'; ?>">
                        <i class="fas fa-<?php echo $loaded ? 'check' : 'times'; ?>"></i>
                        <?php echo $loaded ? 'Încărcată' : 'Lipsește'; ?>
                    </span>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- PHPMailer -->
        <div class="debug-section">
            <h3><i class="fas fa-envelope me-2"></i>PHPMailer</h3>
            <?php foreach ($debugResults['phpmailer'] as $key => $value): ?>
                <div class="debug-item">
                    <span><?php echo htmlspecialchars(str_replace('_', ' ', $key)); ?></span>
                    <span class="config-value"><?php echo htmlspecialchars($value); ?></span>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- SMTP Connection Test -->
        <div class="debug-section">
            <h3><i class="fas fa-network-wired me-2"></i>Test Conexiune SMTP</h3>
            <div class="debug-item">
                <span>Status</span>
                <span class="<?php 
                    echo $debugResults['smtp_connection']['status'] === 'success' ? 'status-success' : 
                         ($debugResults['smtp_connection']['status'] === 'error' ? 'status-error' : 'status-warning'); 
                ?>">
                    <i class="fas fa-<?php 
                        echo $debugResults['smtp_connection']['status'] === 'success' ? 'check' : 
                             ($debugResults['smtp_connection']['status'] === 'error' ? 'times' : 'exclamation-triangle'); 
                    ?>"></i>
                    <?php echo ucfirst($debugResults['smtp_connection']['status']); ?>
                </span>
            </div>
            <div class="mt-3">
                <strong>Mesaj:</strong>
                <div class="debug-log"><?php echo htmlspecialchars($debugResults['smtp_connection']['message']); ?></div>
            </div>
            
            <?php if (isset($debugResults['smtp_debug']) && !empty($debugResults['smtp_debug'])): ?>
                <div class="mt-3">
                    <strong>Debug SMTP:</strong>
                    <div class="debug-log"><?php echo htmlspecialchars(implode("\n", $debugResults['smtp_debug'])); ?></div>
                </div>
            <?php endif; ?>
        </div>

        <!-- File Permissions -->
        <div class="debug-section">
            <h3><i class="fas fa-folder-open me-2"></i>Permisiuni Fișiere</h3>
            <?php foreach ($debugResults['file_permissions'] as $key => $value): ?>
                <div class="debug-item">
                    <span><?php echo htmlspecialchars(str_replace('_', ' ', $key)); ?></span>
                    <span class="<?php echo $value ? 'status-success' : 'status-error'; ?>">
                        <i class="fas fa-<?php echo $value ? 'check' : 'times'; ?>"></i>
                        <?php echo $value ? 'OK' : 'Eroare'; ?>
                    </span>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Security Helper -->
        <div class="debug-section">
            <h3><i class="fas fa-shield-alt me-2"></i>Security Helper</h3>
            <?php if (isset($debugResults['security_helper']['error'])): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Eroare: <?php echo htmlspecialchars($debugResults['security_helper']['error']); ?>
                </div>
            <?php else: ?>
                <?php foreach ($debugResults['security_helper'] as $key => $value): ?>
                    <div class="debug-item">
                        <span><?php echo htmlspecialchars(str_replace('_', ' ', $key)); ?></span>
                        <span class="status-success">
                            <i class="fas fa-info-circle"></i>
                            <?php echo is_bool($value) ? ($value ? 'OK' : 'Eroare') : 'OK'; ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Solutions -->
        <div class="debug-section">
            <h3><i class="fas fa-tools me-2"></i>Soluții Recomandate</h3>
            
            <?php if (!$debugResults['smtp_config']['credentials_configured']): ?>
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>Credentiale SMTP neconfigurate</h5>
                    <p>Trebuie să configurați credentialele SMTP reale în <code>src/Config/constants.php</code>:</p>
                    <ol>
                        <li>Înlocuiți <code><EMAIL></code> cu adresa dvs. de email</li>
                        <li>Înlocuiți <code>your-app-password</code> cu parola de aplicație Gmail</li>
                        <li>Pentru Gmail, activați autentificarea în 2 pași și generați o parolă de aplicație</li>
                    </ol>
                </div>
            <?php endif; ?>

            <?php if ($debugResults['smtp_connection']['status'] === 'error'): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-times me-2"></i>Eroare de conexiune SMTP</h5>
                    <p>Verificați următoarele:</p>
                    <ul>
                        <li>Credentialele SMTP sunt corecte</li>
                        <li>Serverul SMTP permite conexiuni din aplicația dvs.</li>
                        <li>Firewall-ul nu blochează portul <?php echo SMTP_PORT; ?></li>
                        <li>Pentru Gmail, folosiți o parolă de aplicație, nu parola contului</li>
                    </ul>
                </div>
            <?php endif; ?>

            <?php if (!$debugResults['php_extensions']['openssl']): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-times me-2"></i>Extensia OpenSSL lipsește</h5>
                    <p>Extensia OpenSSL este necesară pentru conexiuni SMTP securizate. Activați-o în php.ini.</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Action Buttons -->
        <div class="debug-section text-center">
            <h3><i class="fas fa-play me-2"></i>Acțiuni</h3>
            <div class="d-grid gap-2 d-md-block">
                <a href="contact.php" class="btn btn-primary">
                    <i class="fas fa-envelope me-2"></i>
                    Înapoi la Contact
                </a>
                <a href="test_contact.php" class="btn btn-secondary">
                    <i class="fas fa-vial me-2"></i>
                    Test General
                </a>
                <button onclick="location.reload()" class="btn btn-info">
                    <i class="fas fa-sync me-2"></i>
                    Reîncarcă Debug
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
