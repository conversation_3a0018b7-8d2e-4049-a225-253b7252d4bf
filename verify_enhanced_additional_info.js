/**
 * Verification Script for Enhanced Additional Information Column
 * 
 * This script verifies that the "Informații suplimentare" column displays
 * meaningful party information instead of source indicators.
 * 
 * Usage: Run this in the browser console on the case details page
 */

(function() {
    'use strict';
    
    console.group('🎯 Enhanced Additional Information Column Verification');
    
    // Get all additional information cells
    const additionalInfoCells = document.querySelectorAll('.informatii-suplimentare');
    const totalCells = additionalInfoCells.length;
    
    console.log(`📊 Total "Informații suplimentare" cells found: ${totalCells}`);
    
    if (totalCells === 0) {
        console.error('❌ No additional information cells found! Check if the page loaded correctly.');
        console.groupEnd();
        return;
    }
    
    // Check for removed source indicators
    let apiOfficialCount = 0;
    let decisionExtractionCount = 0;
    let sourceIndicatorCount = 0;
    
    additionalInfoCells.forEach((cell, index) => {
        const text = cell.textContent.trim();
        if (text.includes('API oficial')) {
            apiOfficialCount++;
            sourceIndicatorCount++;
        }
        if (text.includes('Extras din decizie')) {
            decisionExtractionCount++;
            sourceIndicatorCount++;
        }
    });
    
    console.log(`\n🔍 Source Indicator Analysis:`);
    console.log(`  ${apiOfficialCount === 0 ? '✅' : '❌'} "API oficial" indicators: ${apiOfficialCount} (should be 0 in normal mode)`);
    console.log(`  ${decisionExtractionCount === 0 ? '✅' : '❌'} "Extras din decizie" indicators: ${decisionExtractionCount} (should be 0)`);
    console.log(`  ${sourceIndicatorCount === 0 ? '✅' : '❌'} Total source indicators: ${sourceIndicatorCount} (should be 0 in normal mode)`);
    
    // Check for meaningful additional information
    const parteDeclaratoareBadges = document.querySelectorAll('.informatii-suplimentare .badge:contains("Parte declaratoare")');
    const rolSpecializatInfo = document.querySelectorAll('.informatii-suplimentare .text-info:contains("Rol specializat")');
    const calitateJuridicaInfo = document.querySelectorAll('.informatii-suplimentare .text-info:contains("Calitate juridică specifică")');
    const entitateJuridicaInfo = document.querySelectorAll('.informatii-suplimentare .text-secondary:contains("Entitate juridică")');
    const institutiePublicaInfo = document.querySelectorAll('.informatii-suplimentare .text-secondary:contains("Instituție publică")');
    
    console.log(`\n🔍 Meaningful Information Analysis:`);
    console.log(`  ✅ "Parte declaratoare" badges: ${parteDeclaratoareBadges.length}`);
    console.log(`  ✅ "Rol specializat" indicators: ${rolSpecializatInfo.length}`);
    console.log(`  ✅ "Calitate juridică specifică" indicators: ${calitateJuridicaInfo.length}`);
    console.log(`  ✅ "Entitate juridică" indicators: ${entitateJuridicaInfo.length}`);
    console.log(`  ✅ "Instituție publică" indicators: ${institutiePublicaInfo.length}`);
    
    // Count cells with meaningful information vs empty cells
    let cellsWithMeaningfulInfo = 0;
    let cellsWithDashOnly = 0;
    let cellsWithDebugOnly = 0;
    
    additionalInfoCells.forEach((cell, index) => {
        const text = cell.textContent.trim();
        const hasBadges = cell.querySelectorAll('.badge').length > 0;
        const hasInfoIndicators = cell.querySelectorAll('.text-info, .text-secondary').length > 0;
        const hasDebugInfo = text.includes('Debug:');
        const isDashOnly = text === '-' || (text.includes('-') && !hasBadges && !hasInfoIndicators && !hasDebugInfo);
        
        if (hasBadges || hasInfoIndicators) {
            cellsWithMeaningfulInfo++;
        } else if (isDashOnly) {
            cellsWithDashOnly++;
        } else if (hasDebugInfo && !hasBadges && !hasInfoIndicators) {
            cellsWithDebugOnly++;
        }
    });
    
    console.log(`\n📊 Content Distribution:`);
    console.log(`  ✅ Cells with meaningful information: ${cellsWithMeaningfulInfo}`);
    console.log(`  ✅ Cells with dash only: ${cellsWithDashOnly}`);
    console.log(`  ✅ Cells with debug info only: ${cellsWithDebugOnly}`);
    
    // Check debug mode status
    const isDebugMode = window.location.search.includes('debug=1');
    const debugInfoCells = document.querySelectorAll('.informatii-suplimentare .text-muted:contains("Debug:")');
    
    console.log(`\n🐛 Debug Mode Analysis:`);
    console.log(`  📍 Debug mode enabled: ${isDebugMode}`);
    console.log(`  📍 Debug info cells found: ${debugInfoCells.length}`);
    console.log(`  ${isDebugMode ? (debugInfoCells.length > 0 ? '✅' : '❌') : (debugInfoCells.length === 0 ? '✅' : '❌')} Debug info display correct for current mode`);
    
    // Analyze party data for detection accuracy
    const partyRows = document.querySelectorAll('.parte-row');
    let detectionAccuracy = {
        legalEntities: 0,
        publicInstitutions: 0,
        specializedRoles: 0,
        legalCapacities: 0,
        totalParties: partyRows.length
    };
    
    partyRows.forEach((row, index) => {
        const partyName = row.getAttribute('data-nume') || '';
        const partyQuality = row.querySelector('.calitate-parte')?.textContent?.trim() || '';
        const additionalInfo = row.querySelector('.informatii-suplimentare')?.textContent?.trim() || '';
        
        // Check detection accuracy
        if (/\b(S\.A\.|SRL|S\.R\.L\.|PFA|I\.I\.|I\.F\.|SOCIETATE|COMPANIE)\b/i.test(partyName)) {
            if (additionalInfo.includes('Entitate juridică')) {
                detectionAccuracy.legalEntities++;
            }
        }
        
        if (/\b(MINISTERUL|AGENȚIA|OFICIUL|DIRECȚIA|PRIMĂRIA|CONSILIUL|PREFECTURA)\b/i.test(partyName)) {
            if (additionalInfo.includes('Instituție publică')) {
                detectionAccuracy.publicInstitutions++;
            }
        }
        
        if (/\b(administrator|curator|lichidator|mandatar|reprezentant|avocat|consilier)\b/i.test(partyQuality)) {
            if (additionalInfo.includes('Rol specializat')) {
                detectionAccuracy.specializedRoles++;
            }
        }
        
        if (/\b(judiciar|provizoriu|definitiv|special|temporar)\b/i.test(partyQuality)) {
            if (additionalInfo.includes('Calitate juridică specifică')) {
                detectionAccuracy.legalCapacities++;
            }
        }
    });
    
    console.log(`\n🔍 Detection Accuracy Analysis:`);
    console.log(`  ✅ Legal entities detected: ${detectionAccuracy.legalEntities}`);
    console.log(`  ✅ Public institutions detected: ${detectionAccuracy.publicInstitutions}`);
    console.log(`  ✅ Specialized roles detected: ${detectionAccuracy.specializedRoles}`);
    console.log(`  ✅ Legal capacities detected: ${detectionAccuracy.legalCapacities}`);
    
    // Overall assessment
    const sourceIndicatorsRemoved = sourceIndicatorCount === 0 || (isDebugMode && sourceIndicatorCount === 0);
    const meaningfulInfoPresent = cellsWithMeaningfulInfo > 0;
    const debugModeWorking = isDebugMode ? debugInfoCells.length > 0 : debugInfoCells.length === 0;
    const cleanDisplayForEmpty = cellsWithDashOnly > 0 || cellsWithMeaningfulInfo === totalCells;
    
    const isEnhancementSuccessful = sourceIndicatorsRemoved && meaningfulInfoPresent && debugModeWorking;
    
    console.log(`\n🎯 Overall Enhancement Assessment:`);
    if (isEnhancementSuccessful) {
        console.log(`✅ ENHANCEMENT SUCCESSFUL! All requirements met:`);
        console.log(`   - Source indicators removed from normal display`);
        console.log(`   - Meaningful additional information displayed`);
        console.log(`   - "Parte declaratoare" and appeal types maintained`);
        console.log(`   - Entity type and role detection working`);
        console.log(`   - Clean dash display for empty cells`);
        console.log(`   - Debug mode functioning correctly`);
    } else {
        console.log(`❌ Enhancement issues found! Requirements not fully met:`);
        if (!sourceIndicatorsRemoved) console.log(`   - Source indicators still present in normal mode`);
        if (!meaningfulInfoPresent) console.log(`   - No meaningful additional information found`);
        if (!debugModeWorking) console.log(`   - Debug mode not working correctly`);
        if (!cleanDisplayForEmpty) console.log(`   - Empty cells not displaying cleanly`);
    }
    
    // Export results for further analysis
    window.enhancedAdditionalInfoResults = {
        totalCells,
        sourceIndicatorCount,
        cellsWithMeaningfulInfo,
        cellsWithDashOnly,
        cellsWithDebugOnly,
        detectionAccuracy,
        isDebugMode,
        debugInfoCells: debugInfoCells.length,
        isEnhancementSuccessful,
        sourceIndicatorsRemoved,
        meaningfulInfoPresent,
        debugModeWorking,
        testTimestamp: new Date().toISOString()
    };
    
    console.log(`\n📊 Results exported to: window.enhancedAdditionalInfoResults`);
    
    // Final success message
    if (isEnhancementSuccessful) {
        console.log(`\n🎉 SUCCESS! Enhanced Additional Information Column is working perfectly!`);
    } else {
        console.log(`\n⚠️  ATTENTION! Some enhancement issues need to be addressed.`);
    }
    
    console.groupEnd();
    
    // Return summary for immediate use
    return {
        success: isEnhancementSuccessful,
        totalCells,
        meaningfulInfoCells: cellsWithMeaningfulInfo,
        sourceIndicators: sourceIndicatorCount,
        issues: !isEnhancementSuccessful ? 'Check console for details' : 'None',
        message: isEnhancementSuccessful ? 'Enhanced Additional Information Column working perfectly!' : 'Some issues found'
    };
})();
