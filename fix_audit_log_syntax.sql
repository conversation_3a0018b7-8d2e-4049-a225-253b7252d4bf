-- Fix Audit Log Table Syntax Error
-- Run this SQL in phpMyAdmin to create a clean audit_log table

-- Drop the problematic table if it exists
DROP TABLE IF EXISTS `audit_log`;

-- Create audit_log table with correct syntax
CREATE TABLE `audit_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `table_name` varchar(100) DEFAULT NULL,
  `record_id` int unsigned DEFAULT NULL,
  `old_values` TEXT DEFAULT NULL,
  `new_values` TEXT DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Audit log for security tracking';

-- Test the table
INSERT INTO `audit_log` (`action`, `ip_address`) VALUES ('TEST', '127.0.0.1');
SELECT * FROM `audit_log` WHERE `action` = 'TEST';
DELETE FROM `audit_log` WHERE `action` = 'TEST';

-- Table is now ready for use!
