<?php
require_once 'services/SedinteService.php';

echo "Testing CurteadeApelBACU with SedinteService...\n\n";

try {
    $sedinteService = new SedinteService();
    
    $searchParams = [
        'dataSedinta' => '2024-01-15T00:00:00',
        'institutie' => 'CurteadeApelBACU'
    ];
    
    echo "Calling cautareSedinte with params:\n";
    print_r($searchParams);
    
    $results = $sedinteService->cautareSedinte($searchParams);
    
    echo "✅ SUCCESS: " . count($results) . " ședințe găsite\n";
    
    if (!empty($results)) {
        echo "Prima ședință:\n";
        print_r($results[0]);
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "\nError details:\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    
    // Let's also check the logs
    $logFile = __DIR__ . '/logs/soap_sessions.log';
    if (file_exists($logFile)) {
        echo "\nRecent SOAP logs:\n";
        $logs = file_get_contents($logFile);
        $lines = explode("\n", $logs);
        $recentLines = array_slice($lines, -10); // Last 10 lines
        foreach ($recentLines as $line) {
            if (!empty(trim($line))) {
                echo $line . "\n";
            }
        }
    }
}

echo "\nTesting the validateAndMapInstitutionCode function directly...\n";

// Let's test the mapping function directly
$reflection = new ReflectionClass('SedinteService');
$method = $reflection->getMethod('validateAndMapInstitutionCode');
$method->setAccessible(true);

$service = new SedinteService();
$mappedCode = $method->invoke($service, 'CurteadeApelBACU');

echo "Original code: CurteadeApelBACU\n";
echo "Mapped code: " . ($mappedCode ?? 'NULL') . "\n";

?>
