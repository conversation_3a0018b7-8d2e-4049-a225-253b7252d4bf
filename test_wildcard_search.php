<?php
// Test wildcard search functionality for debugging
require_once 'bootstrap.php';

use App\Services\DosarService;

echo "<h1>Wildcard Search Debug Test</h1>";

// Test case numbers
$testCases = [
    "14096/32024",      // Exact match (should work)
    "14096/32024*",     // Wildcard (the problematic case)
    "14096/3/2024",     // Alternative format
    "14096/3/2024*"     // Alternative wildcard
];

foreach ($testCases as $searchTerm) {
    echo "<hr><h2>Testing Search Term: '$searchTerm'</h2>";
    
    try {
        $dosarService = new DosarService();
        
        // Test the normalization function first
        $reflection = new ReflectionClass($dosarService);
        $normalizeMethod = $reflection->getMethod('normalizeCaseNumber');
        $normalizeMethod->setAccessible(true);
        
        $caseNumberInfo = $normalizeMethod->invoke($dosarService, $searchTerm);
        
        echo "<h3>Case Number Normalization:</h3>";
        echo "<pre>" . print_r($caseNumberInfo, true) . "</pre>";
        
        // Test the actual search
        $searchParams = [
            'numarDosar' => $searchTerm,
            'institutie' => null,
            'numeParte' => '',
            'obiectDosar' => '',
            'dataStart' => '',
            'dataStop' => '',
            'dataUltimaModificareStart' => '',
            'dataUltimaModificareStop' => '',
            'categorieInstanta' => '',
            'categorieCaz' => ''
        ];
        
        echo "<h3>Search Parameters:</h3>";
        echo "<pre>" . print_r($searchParams, true) . "</pre>";
        
        $results = $dosarService->cautareAvansata($searchParams);
        
        echo "<h3>Search Results:</h3>";
        echo "<p><strong>Number of results: " . count($results) . "</strong></p>";
        
        if (!empty($results)) {
            echo "<h4>Results Found:</h4>";
            foreach ($results as $index => $dosar) {
                echo "<div style='background: #f0f8ff; padding: 10px; margin: 5px 0; border: 1px solid #ccc;'>";
                echo "<strong>Result #" . ($index + 1) . ":</strong><br>";
                echo "Case Number: " . ($dosar->numar ?? 'N/A') . "<br>";
                echo "Institution: " . ($dosar->instanta ?? 'N/A') . "<br>";
                echo "Object: " . ($dosar->obiect ?? 'N/A') . "<br>";
                echo "</div>";
            }
        } else {
            echo "<p style='color: red; font-weight: bold;'>No results found!</p>";
        }
        
        // Test direct case number search method
        echo "<h3>Direct Case Number Search Test:</h3>";
        try {
            $directResults = $dosarService->cautareDupaNumarDosar($searchTerm);
            echo "<p><strong>Direct search results: " . count($directResults) . "</strong></p>";
            
            if (!empty($directResults)) {
                foreach ($directResults as $index => $dosar) {
                    echo "<div style='background: #f0fff0; padding: 10px; margin: 5px 0; border: 1px solid #90EE90;'>";
                    echo "<strong>Direct Result #" . ($index + 1) . ":</strong><br>";
                    echo "Case Number: " . ($dosar->numar ?? 'N/A') . "<br>";
                    echo "Institution: " . ($dosar->instanta ?? 'N/A') . "<br>";
                    echo "</div>";
                }
            } else {
                echo "<p style='color: orange;'>No direct search results found!</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>Direct search error: " . $e->getMessage() . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<h3>Error:</h3>";
        echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
        echo "<pre style='color: red; font-size: 12px;'>" . $e->getTraceAsString() . "</pre>";
    }
}

// Test the filtering function directly
echo "<hr><h2>Testing Client-Side Filtering Function</h2>";

// Mock some test data
$mockResults = [
    (object)['numar' => '14096/32024', 'instanta' => 'Test Court'],
    (object)['numar' => '14096/32024/a1', 'instanta' => 'Test Court'],
    (object)['numar' => '14096/32024/b2', 'instanta' => 'Test Court'],
    (object)['numar' => '14097/32024', 'instanta' => 'Test Court'],
    (object)['numar' => '14096/42024', 'instanta' => 'Test Court']
];

$testPattern = "14096/32024*";
$caseNumberInfo = [
    'original' => $testPattern,
    'normalized' => '14096/32024',
    'hasWildcard' => true,
    'hasSuffix' => false,
    'suffix' => ''
];

echo "<h3>Mock Data:</h3>";
echo "<pre>" . print_r($mockResults, true) . "</pre>";

echo "<h3>Test Pattern: '$testPattern'</h3>";
echo "<pre>" . print_r($caseNumberInfo, true) . "</pre>";

// Test the filtering function
try {
    $dosarService = new DosarService();
    $reflection = new ReflectionClass($dosarService);
    $filterMethod = $reflection->getMethod('filterResultsByCaseNumberPattern');
    $filterMethod->setAccessible(true);
    
    $filteredResults = $filterMethod->invoke($dosarService, $mockResults, $caseNumberInfo);
    
    echo "<h3>Filtered Results:</h3>";
    echo "<p><strong>Number of filtered results: " . count($filteredResults) . "</strong></p>";
    
    if (!empty($filteredResults)) {
        foreach ($filteredResults as $index => $dosar) {
            echo "<div style='background: #fff0f5; padding: 10px; margin: 5px 0; border: 1px solid #FFB6C1;'>";
            echo "<strong>Filtered Result #" . ($index + 1) . ":</strong><br>";
            echo "Case Number: " . ($dosar->numar ?? 'N/A') . "<br>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: red;'>No results after filtering!</p>";
    }
    
} catch (Exception $e) {
    echo "<h3>Filtering Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
