<?php
/**
 * Test script to verify parties display issue
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

try {
    $dosarService = new DosarService();
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');

    if ($dosar && !empty($dosar->parti)) {
        $totalCount = count($dosar->parti);
        echo "=== BACKEND DATA VERIFICATION ===" . PHP_EOL;
        echo "Total parties in backend: " . $totalCount . PHP_EOL;

        if ($totalCount == 387) {
            echo "✓ CONFIRMED: Backend has expected 387 parties" . PHP_EOL;
        } elseif ($totalCount == 100) {
            echo "⚠ WARNING: Backend only has 100 parties (previous count)" . PHP_EOL;
        } else {
            echo "⚠ UNEXPECTED: Backend has $totalCount parties (expected 387)" . PHP_EOL;
        }

        echo PHP_EOL . "First 10 parties:" . PHP_EOL;
        for ($i = 0; $i < min(10, $totalCount); $i++) {
            echo ($i + 1) . ". " . $dosar->parti[$i]['nume'] . PHP_EOL;
        }

        if ($totalCount > 10) {
            echo "..." . PHP_EOL;
            echo "Last 10 parties:" . PHP_EOL;
            $start = max(0, $totalCount - 10);
            for ($i = $start; $i < $totalCount; $i++) {
                echo ($i + 1) . ". " . $dosar->parti[$i]['nume'] . PHP_EOL;
            }
        }

        // Test if all parties have required data
        $validParties = 0;
        $invalidParties = 0;
        $emptyNames = [];

        foreach ($dosar->parti as $index => $parte) {
            if (!empty($parte['nume'])) {
                $validParties++;
            } else {
                $invalidParties++;
                $emptyNames[] = $index;
                if (count($emptyNames) <= 5) { // Only show first 5 invalid parties
                    echo "Invalid party at index $index: " . print_r($parte, true) . PHP_EOL;
                }
            }
        }

        echo PHP_EOL . "=== DATA QUALITY ANALYSIS ===" . PHP_EOL;
        echo "Valid parties: $validParties" . PHP_EOL;
        echo "Invalid parties: $invalidParties" . PHP_EOL;

        if ($invalidParties > 0) {
            echo "Indices with empty names: " . implode(', ', $emptyNames) . PHP_EOL;
        }

        // Check for duplicates
        $names = array_column($dosar->parti, 'nume');
        $uniqueNames = array_unique($names);
        $duplicates = count($names) - count($uniqueNames);

        echo "Duplicate names found: $duplicates" . PHP_EOL;

        if ($duplicates > 0) {
            $nameCounts = array_count_values($names);
            $duplicateNames = array_filter($nameCounts, function($count) { return $count > 1; });
            echo "Names appearing multiple times:" . PHP_EOL;
            foreach ($duplicateNames as $name => $count) {
                echo "  - '$name': $count times" . PHP_EOL;
            }
        }

    } else {
        echo "No parties found or dosar not found" . PHP_EOL;
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
}
?>
