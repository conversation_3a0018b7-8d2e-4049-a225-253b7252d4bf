<?php
/**
 * Test script pentru funcționalitatea de parsare a informațiilor despre judecători
 */

// Include funcțiile necesare
require_once 'includes/functions.php';

// Test cases pentru parsarea judecătorilor
$testCases = [
    'Judec<PERSON>tor unic: <PERSON>',
    'Complet 1: <PERSON><PERSON><PERSON><PERSON> principal: <PERSON>, <PERSON><PERSON>ător: <PERSON>',
    'Complet 2 - Judecător principal: <PERSON>, <PERSON>cător: <PERSON><PERSON>',
    'Judecător principal: <PERSON>, <PERSON>c<PERSON>tor: <PERSON><PERSON>',
    'Complet: Președinte: <PERSON>, Raportor: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>: <PERSON>',
    'Judecător unic <PERSON>',
    'Complet 3: <PERSON>, <PERSON>, <PERSON>',
    'Text invalid fără structură',
    '',
    null
];

echo "<!DOCTYPE html>\n";
echo "<html lang='ro'>\n";
echo "<head>\n";
echo "    <meta charset='UTF-8'>\n";
echo "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "    <title>Test Parsare <PERSON>i</title>\n";
echo "    <link href='https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css' rel='stylesheet'>\n";
echo "    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>\n";
echo "</head>\n";
echo "<body>\n";
echo "<div class='container mt-4'>\n";
echo "    <h1 class='text-primary mb-4'><i class='fas fa-gavel me-2'></i>Test Parsare Informații Judecători</h1>\n";

foreach ($testCases as $index => $testCase) {
    echo "    <div class='card mb-4'>\n";
    echo "        <div class='card-header'>\n";
    echo "            <h5 class='mb-0'>Test Case " . ($index + 1) . "</h5>\n";
    echo "        </div>\n";
    echo "        <div class='card-body'>\n";
    echo "            <div class='row'>\n";
    echo "                <div class='col-md-6'>\n";
    echo "                    <h6 class='text-info'>Input:</h6>\n";
    echo "                    <code>" . htmlspecialchars($testCase ?? 'null') . "</code>\n";
    echo "                </div>\n";
    echo "                <div class='col-md-6'>\n";
    echo "                    <h6 class='text-success'>Rezultat Parsat:</h6>\n";
    
    $judgeInfo = parseJudgeInformation($testCase);
    
    echo "                    <div class='mb-2'>\n";
    echo "                        <strong>Tip:</strong> " . htmlspecialchars($judgeInfo['type']) . "<br>\n";
    echo "                        <strong>Parsat cu succes:</strong> " . ($judgeInfo['parsed'] ? 'Da' : 'Nu') . "<br>\n";
    if (isset($judgeInfo['complet_number'])) {
        echo "                        <strong>Număr complet:</strong> " . htmlspecialchars($judgeInfo['complet_number']) . "<br>\n";
    }
    echo "                    </div>\n";
    
    if (!empty($judgeInfo['judges'])) {
        echo "                    <div class='mb-2'>\n";
        echo "                        <strong>Judecători:</strong>\n";
        echo "                        <ul class='list-unstyled ml-3'>\n";
        foreach ($judgeInfo['judges'] as $judge) {
            echo "                            <li><span class='text-primary'>" . htmlspecialchars($judge['role']) . ":</span> " . htmlspecialchars($judge['name']) . "</li>\n";
        }
        echo "                        </ul>\n";
        echo "                    </div>\n";
    }
    
    echo "                </div>\n";
    echo "            </div>\n";
    echo "            <hr>\n";
    echo "            <div class='row'>\n";
    echo "                <div class='col-md-6'>\n";
    echo "                    <h6 class='text-warning'>Format HTML:</h6>\n";
    echo "                    <div class='border p-2 bg-light'>\n";
    echo formatJudgeInformation($judgeInfo);
    echo "                    </div>\n";
    echo "                </div>\n";
    echo "                <div class='col-md-6'>\n";
    echo "                    <h6 class='text-warning'>Format Export:</h6>\n";
    echo "                    <div class='border p-2 bg-light'>\n";
    echo "                        <code>" . htmlspecialchars(formatJudgeInformationForExport($judgeInfo)) . "</code>\n";
    echo "                    </div>\n";
    echo "                </div>\n";
    echo "            </div>\n";
    echo "        </div>\n";
    echo "    </div>\n";
}

echo "</div>\n";
echo "</body>\n";
echo "</html>\n";
?>
