<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 ANALYZING MISSING PARTIES FOR CurteadeApelBUCURESTI\n";
echo "====================================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    echo "Current parties: " . count($dosar->parti) . "\n";
    echo "Target: 380 parties\n";
    echo "Missing: " . (380 - count($dosar->parti)) . "\n\n";
    
    // Get the decision text for analysis
    $solutieText = '';
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $sedinta) {
            if (!empty($sedinta['solutie'])) {
                $solutieText .= $sedinta['solutie'] . "\n\n";
            }
        }
    }
    
    if (empty($solutieText)) {
        echo "❌ No decision text found\n";
        exit(1);
    }
    
    echo "📄 Decision text length: " . strlen($solutieText) . " characters\n\n";
    
    // Analyze patterns that might contain additional parties
    echo "🔍 PATTERN ANALYSIS:\n";
    echo "===================\n\n";
    
    // Pattern 1: Look for semicolon-separated lists
    $semicolonMatches = [];
    if (preg_match_all('/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+(?:;\s*[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)+)/u', $solutieText, $semicolonMatches)) {
        echo "📋 Semicolon-separated lists found: " . count($semicolonMatches[1]) . "\n";
        foreach ($semicolonMatches[1] as $i => $match) {
            $names = explode(';', $match);
            echo "  List " . ($i + 1) . ": " . count($names) . " names\n";
            if (count($names) <= 10) {
                echo "    Sample: " . implode(' | ', array_slice($names, 0, 5)) . "\n";
            }
        }
        echo "\n";
    }
    
    // Pattern 2: Look for comma-separated lists
    $commaMatches = [];
    if (preg_match_all('/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+(?:,\s*[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)+)/u', $solutieText, $commaMatches)) {
        echo "📋 Comma-separated lists found: " . count($commaMatches[1]) . "\n";
        $totalCommaNames = 0;
        foreach ($commaMatches[1] as $i => $match) {
            $names = explode(',', $match);
            $totalCommaNames += count($names);
            if ($i < 5) { // Show first 5 lists
                echo "  List " . ($i + 1) . ": " . count($names) . " names\n";
                if (count($names) <= 10) {
                    echo "    Sample: " . implode(' | ', array_slice($names, 0, 5)) . "\n";
                }
            }
        }
        echo "  Total comma-separated names: {$totalCommaNames}\n\n";
    }
    
    // Pattern 3: Look for numbered lists
    $numberedMatches = [];
    if (preg_match_all('/\d+\.\s*([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)/u', $solutieText, $numberedMatches)) {
        echo "📋 Numbered list entries found: " . count($numberedMatches[1]) . "\n";
        echo "  Sample: " . implode(' | ', array_slice($numberedMatches[1], 0, 5)) . "\n\n";
    }
    
    // Pattern 4: Look for parenthetical data patterns
    $parentheticalMatches = [];
    if (preg_match_all('/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)\s*\([^)]+\)/u', $solutieText, $parentheticalMatches)) {
        echo "📋 Names with parenthetical data found: " . count($parentheticalMatches[1]) . "\n";
        echo "  Sample: " . implode(' | ', array_slice($parentheticalMatches[1], 0, 5)) . "\n\n";
    }
    
    // Pattern 5: Look for specific legal phrases that might precede party lists
    $legalPhrases = [
        'apelantii' => '/apelantii\s*([^.]+)/i',
        'intimatii' => '/intimatii\s*([^.]+)/i',
        'reclamantii' => '/reclamantii\s*([^.]+)/i',
        'paratii' => '/paratii\s*([^.]+)/i',
        'creditorii' => '/creditorii\s*([^.]+)/i',
        'debitorul' => '/debitorul\s*([^.]+)/i'
    ];
    
    echo "📋 Legal phrase analysis:\n";
    foreach ($legalPhrases as $phrase => $pattern) {
        if (preg_match_all($pattern, $solutieText, $matches)) {
            echo "  '{$phrase}': " . count($matches[1]) . " occurrences\n";
            foreach ($matches[1] as $i => $match) {
                if ($i < 3) { // Show first 3 matches
                    $cleanMatch = trim(substr($match, 0, 100));
                    echo "    " . ($i + 1) . ": {$cleanMatch}...\n";
                }
            }
        }
    }
    
    echo "\n🔍 SEARCHING FOR POTENTIAL MISSED PATTERNS:\n";
    echo "==========================================\n\n";
    
    // Look for patterns that might be specific to this case
    // Pattern: Names followed by specific legal terms
    $specificPatterns = [
        'cu domiciliul' => '/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)\s+cu domiciliul/u',
        'din localitatea' => '/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)\s+din localitatea/u',
        'reprezentat' => '/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)\s+reprezentat/u',
        'prin avocat' => '/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)\s+prin avocat/u'
    ];
    
    foreach ($specificPatterns as $patternName => $pattern) {
        if (preg_match_all($pattern, $solutieText, $matches)) {
            echo "📋 Pattern '{$patternName}': " . count($matches[1]) . " matches\n";
            if (count($matches[1]) > 0) {
                echo "  Sample: " . implode(' | ', array_slice($matches[1], 0, 5)) . "\n";
            }
        }
    }
    
    echo "\n✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
