# 🚀 Portal Judiciar România - WAMP Setup Guide

## 📋 Quick Fix for Database Connection Issues

If you're getting "Call to undefined method App\Config\Database::getConnection()" errors, follow these steps:

### Step 1: Create Database (Required)

```bash
# Navigate to your project directory
cd C:\wamp64\www\just\

# Create the database
php create_database.php
```

This will:
- ✅ Test MySQL connection
- ✅ Create `portal_judiciar` database if it doesn't exist
- ✅ Verify database configuration

### Step 2: Set Up Database Tables

```bash
# Set up all monitoring tables
php setup_monitoring_database.php
```

This will:
- ✅ Create all required tables
- ✅ Set up indexes and relationships
- ✅ Create a test user account

### Step 3: Test the System

```bash
# Run comprehensive system test
php test_monitoring_system.php
```

Should show: **"🚀 SYSTEM READY FOR PRODUCTION!"**

## 🔧 Troubleshooting Common WAMP Issues

### Issue 1: "Database connection failed"

**Solution:**
1. **Start WAMP services:**
   - Open WAMP control panel
   - Ensure MySQL icon is green
   - If red, click MySQL → Service → Start/Resume Service

2. **Check database exists:**
   - Open phpMyAdmin (http://localhost/phpmyadmin)
   - Look for `portal_judiciar` database
   - If missing, run: `php create_database.php`

3. **Verify credentials:**
   - Default WAMP: user=`root`, password=`(empty)`
   - Edit `database_config.php` if different

### Issue 2: "Access denied for user"

**Solution:**
1. **Check MySQL user:**
   ```sql
   -- In phpMyAdmin, run:
   SELECT User, Host FROM mysql.user WHERE User = 'root';
   ```

2. **Reset root password if needed:**
   - In WAMP: MySQL → MySQL Console
   - Enter current password (usually empty)
   - Run: `ALTER USER 'root'@'localhost' IDENTIFIED BY '';`

### Issue 3: "Unknown database 'portal_judiciar'"

**Solution:**
```bash
# Create database manually
php create_database.php
```

Or in phpMyAdmin:
```sql
CREATE DATABASE portal_judiciar CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 📁 File Structure After Setup

```
C:\wamp64\www\just\
├── database_config.php          # Database settings
├── create_database.php          # Database creation script
├── setup_monitoring_database.php # Table setup script
├── test_monitoring_system.php   # System test script
├── src/Config/Database.php      # Database class (fixed)
├── src/Config/constants.php     # System constants
└── logs/                        # Log files (auto-created)
```

## 🎯 Default WAMP Configuration

The system is pre-configured for standard WAMP setups:

```php
// Default settings in database_config.php
DB_HOST = 'localhost'
DB_NAME = 'portal_judiciar'
DB_USER = 'root'
DB_PASS = ''  // Empty password
DB_CHARSET = 'utf8mb4'
```

## ✅ Verification Steps

1. **WAMP Status:**
   - All services green in WAMP panel
   - Apache: http://localhost/ shows WAMP homepage
   - MySQL: phpMyAdmin accessible

2. **Database Status:**
   ```bash
   php database_config.php  # Test connection
   ```

3. **Portal Status:**
   ```bash
   php test_monitoring_system.php  # Full system test
   ```

4. **Web Interface:**
   - Visit: http://localhost/just/
   - Should show judicial portal homepage

## 🚀 Next Steps After Setup

1. **Configure Email (Optional):**
   - Edit `src/Config/constants.php`
   - Update SMTP settings for notifications

2. **Set Up Cron Jobs:**
   ```bash
   php cron/setup_cron.php
   ```

3. **Test Monitoring:**
   - Visit: http://localhost/just/monitor.php
   - Create account and add test case

## 📞 Support

If you still have issues:

1. **Check WAMP logs:**
   - C:\wamp64\logs\mysql.log
   - C:\wamp64\logs\apache_error.log

2. **Run diagnostics:**
   ```bash
   php -v                    # Check PHP version
   php -m | grep pdo         # Check PDO extension
   php test_monitoring_system.php  # Full system test
   ```

3. **Common fixes:**
   - Restart WAMP services
   - Check Windows firewall
   - Verify port 3306 not blocked
   - Run as Administrator if needed

---

**🎉 The monitoring system is now ready for use!**

Users can start monitoring court cases immediately after this setup.
