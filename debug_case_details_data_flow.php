<?php
/**
 * Debug Case Details Data Flow
 * Comprehensive test to debug the exact data flow in detalii_dosar.php
 */

// Include necessary files exactly like detalii_dosar.php
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Test case parameters
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Debug Case Details Data Flow</title>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
.warning { background: #fff3cd; border-left-color: #ffc107; }
.error { background: #f8d7da; border-left-color: #dc3545; }
.success { background: #d4edda; border-left-color: #28a745; }
.debug-info { background: #e3f2fd; border: 1px solid #2196f3; padding: 10px; margin: 10px 0; font-family: monospace; }
.code { background: #f1f3f4; padding: 10px; font-family: monospace; white-space: pre-wrap; }
</style></head><body>";

echo "<h1>🔍 Debug Case Details Data Flow</h1>";
echo "<p><strong>Case:</strong> {$numarDosar} from {$institutie}</p>";
echo "<p><strong>Objective:</strong> Debug the exact same data flow as detalii_dosar.php</p>";
echo "<hr>";

// Replicate the exact initialization from detalii_dosar.php
$dosar = null;
$error = null;

// Verification of parameters (same as detalii_dosar.php)
if (empty($numarDosar) || empty($institutie)) {
    $error = "Parametrii necesari pentru afișarea detaliilor dosarului lipsesc.";
} else {
    try {
        echo "<div class='section'>";
        echo "<h2>📊 Step 1: Service Initialization</h2>";
        
        // Initialize service exactly like detalii_dosar.php
        $dosarService = new DosarService();
        echo "<p>✅ DosarService initialized successfully</p>";
        
        echo "<h3>Step 2: Get Case Details</h3>";
        
        // Get case details exactly like detalii_dosar.php
        $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
        
        if (!$dosar) {
            $error = "Nu s-au putut găsi detalii pentru dosarul specificat.";
            echo "<div class='error'>❌ No case details found</div>";
        } else {
            echo "<p>✅ Case details retrieved successfully</p>";
            
            // Debug the dosar object structure
            echo "<h3>Step 3: Case Object Analysis</h3>";
            echo "<div class='debug-info'>";
            echo "Case object type: " . gettype($dosar) . "\n";
            echo "Case object class: " . get_class($dosar) . "\n";
            echo "Case number: " . ($dosar->numar ?? 'N/A') . "\n";
            echo "Institution: " . ($dosar->institutie ?? 'N/A') . "\n";
            echo "Parties property exists: " . (property_exists($dosar, 'parti') ? 'YES' : 'NO') . "\n";
            
            if (property_exists($dosar, 'parti')) {
                echo "Parties type: " . gettype($dosar->parti) . "\n";
                echo "Parties count: " . (is_array($dosar->parti) ? count($dosar->parti) : 'N/A') . "\n";
            }
            echo "</div>";
            
            // Analyze parties in detail
            if (property_exists($dosar, 'parti') && is_array($dosar->parti)) {
                $totalParties = count($dosar->parti);
                
                echo "<h3>Step 4: Parties Analysis</h3>";
                echo "<div class='debug-info'>";
                echo "Total parties found: {$totalParties}\n";
                
                if ($totalParties >= 340) {
                    echo "✅ SUCCESS: Found expected high party count ({$totalParties})\n";
                } elseif ($totalParties == 100) {
                    echo "❌ ISSUE: Only 100 parties (SOAP API limit)\n";
                } else {
                    echo "⚠️ UNEXPECTED: Found {$totalParties} parties\n";
                }
                echo "</div>";
                
                // Source analysis
                $soapCount = 0;
                $decisionCount = 0;
                $unknownCount = 0;
                
                foreach ($dosar->parti as $parte) {
                    $source = $parte['source'] ?? 'unknown';
                    switch ($source) {
                        case 'soap_api':
                            $soapCount++;
                            break;
                        case 'decision_text':
                            $decisionCount++;
                            break;
                        default:
                            $unknownCount++;
                            break;
                    }
                }
                
                echo "<h3>Step 5: Source Attribution Analysis</h3>";
                echo "<div class='debug-info'>";
                echo "SOAP API parties: {$soapCount}\n";
                echo "Decision text parties: {$decisionCount}\n";
                echo "Unknown source parties: {$unknownCount}\n";
                echo "Total: " . ($soapCount + $decisionCount + $unknownCount) . "\n";
                echo "</div>";
                
                // Sample parties
                echo "<h3>Step 6: Sample Parties</h3>";
                echo "<div class='debug-info'>";
                echo "First 5 parties:\n";
                for ($i = 0; $i < min(5, $totalParties); $i++) {
                    $parte = $dosar->parti[$i];
                    echo ($i + 1) . ". " . ($parte['nume'] ?? 'N/A') . " (" . ($parte['calitate'] ?? 'N/A') . ") [" . ($parte['source'] ?? 'unknown') . "]\n";
                }
                
                if ($totalParties > 5) {
                    echo "\nLast 5 parties:\n";
                    $start = max(0, $totalParties - 5);
                    for ($i = $start; $i < $totalParties; $i++) {
                        $parte = $dosar->parti[$i];
                        echo ($i + 1) . ". " . ($parte['nume'] ?? 'N/A') . " (" . ($parte['calitate'] ?? 'N/A') . ") [" . ($parte['source'] ?? 'unknown') . "]\n";
                    }
                }
                echo "</div>";
            } else {
                echo "<div class='error'>";
                echo "<h3>❌ Parties Issue</h3>";
                echo "<p>Parties property is missing or not an array</p>";
                echo "</div>";
            }
        }
        echo "</div>";
        
    } catch (Exception $e) {
        $error = "Eroare la obținerea detaliilor dosarului: " . $e->getMessage();
        echo "<div class='error'>";
        echo "<h3>❌ Exception Occurred</h3>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "</div>";
    }
}

// If we have an error, display it
if ($error) {
    echo "<div class='error'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . htmlspecialchars($error) . "</p>";
    echo "</div>";
}

// Now simulate the exact frontend rendering logic from detalii_dosar.php
if ($dosar && !$error && property_exists($dosar, 'parti') && is_array($dosar->parti)) {
    echo "<div class='section'>";
    echo "<h2>🎨 Step 7: Frontend Rendering Simulation</h2>";
    echo "<p>Simulating the exact rendering logic from detalii_dosar.php:</p>";
    
    // Initialize variables exactly like detalii_dosar.php
    $loop_index = 0;
    $totalPartiCount = count($dosar->parti);
    
    echo "<div class='debug-info'>";
    echo "Template variables:\n";
    echo "\$loop_index = {$loop_index}\n";
    echo "\$totalPartiCount = {$totalPartiCount}\n";
    echo "foreach (\$dosar->parti as \$parteIndex => \$parte) will iterate {$totalPartiCount} times\n";
    echo "</div>";
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped' id='tabelParti'>";
    echo "<thead>";
    echo "<tr>";
    echo "<th>Loop Index</th>";
    echo "<th>Array Index</th>";
    echo "<th>Nume</th>";
    echo "<th>Calitate</th>";
    echo "<th>Source</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    $renderedCount = 0;
    
    // Exact same foreach loop as in detalii_dosar.php
    foreach ($dosar->parti as $parteIndex => $parte) {
        $loop_index++;
        $renderedCount++;
        
        echo "<tr class='parte-row' data-index='{$loop_index}' data-party-id='{$parteIndex}'>";
        echo "<td><span class='badge bg-primary'>{$loop_index}</span></td>";
        echo "<td><span class='badge bg-secondary'>{$parteIndex}</span></td>";
        echo "<td class='nume-parte'>" . htmlspecialchars($parte['nume'] ?? 'N/A') . "</td>";
        echo "<td class='calitate-parte'>" . htmlspecialchars($parte['calitate'] ?? '-') . "</td>";
        echo "<td><span class='badge bg-info'>" . htmlspecialchars($parte['source'] ?? 'unknown') . "</span></td>";
        echo "</tr>";
        
        // Progress indicator every 50 parties
        if ($renderedCount % 50 == 0) {
            echo "<tr><td colspan='5' class='text-center bg-light'>";
            echo "<em>--- Rendered {$renderedCount} of {$totalPartiCount} parties ---</em>";
            echo "</td></tr>";
        }
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    echo "<div class='debug-info'>";
    echo "Rendering summary:\n";
    echo "Expected parties: {$totalPartiCount}\n";
    echo "Rendered parties: {$renderedCount}\n";
    echo "Final loop_index: {$loop_index}\n";
    echo "Rendering successful: " . ($renderedCount == $totalPartiCount ? "✅ YES" : "❌ NO") . "\n";
    echo "</div>";
    echo "</div>";
    
    // JavaScript verification
    echo "<div class='section'>";
    echo "<h2>🔧 Step 8: JavaScript DOM Verification</h2>";
    echo "<div id='jsResults'></div>";
    echo "</div>";
}

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    console.log('🔍 Debug Case Details Data Flow - JavaScript Analysis');";
echo "    ";
echo "    // Count DOM elements";
echo "    const table = document.getElementById('tabelParti');";
echo "    const tbody = table ? table.querySelector('tbody') : null;";
echo "    const allRows = tbody ? tbody.querySelectorAll('tr') : [];";
echo "    const partyRows = tbody ? tbody.querySelectorAll('tr.parte-row') : [];";
echo "    ";
echo "    console.log('DOM Analysis:', {";
echo "        tableFound: !!table,";
echo "        tbodyFound: !!tbody,";
echo "        totalRows: allRows.length,";
echo "        partyRows: partyRows.length";
echo "    });";
echo "    ";
echo "    // Display results";
echo "    const resultsDiv = document.getElementById('jsResults');";
echo "    if (resultsDiv) {";
echo "        let html = '<div class=\"alert alert-info\">';";
echo "        html += '<h5>JavaScript DOM Verification:</h5>';";
echo "        html += '<ul>';";
echo "        html += '<li><strong>Table found:</strong> ' + (table ? 'YES' : 'NO') + '</li>';";
echo "        html += '<li><strong>Total rows in DOM:</strong> ' + allRows.length + '</li>';";
echo "        html += '<li><strong>Party rows (.parte-row):</strong> ' + partyRows.length + '</li>';";
echo "        html += '</ul>';";
echo "        html += '</div>';";
echo "        resultsDiv.innerHTML = html;";
echo "    }";
echo "    ";
echo "    // Final comparison";
echo "    const expectedParties = " . (isset($totalPartiCount) ? $totalPartiCount : 0) . ";";
echo "    const actualParties = partyRows.length;";
echo "    ";
echo "    if (expectedParties > 0) {";
echo "        console.log('Final Comparison:', {";
echo "            expectedParties: expectedParties,";
echo "            actualParties: actualParties,";
echo "            match: expectedParties === actualParties";
echo "        });";
echo "        ";
echo "        if (expectedParties !== actualParties) {";
echo "            console.error('❌ MISMATCH: Expected ' + expectedParties + ' parties, but found ' + actualParties + ' in DOM');";
echo "        } else {";
echo "            console.log('✅ SUCCESS: Party count matches between backend and frontend');";
echo "        }";
echo "    }";
echo "});";
echo "</script>";

echo "</body></html>";
?>
