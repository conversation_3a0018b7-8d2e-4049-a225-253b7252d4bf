<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #2c3e50;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1rem 0;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
            border-left: 4px solid var(--primary-color);
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-card.success {
            border-left-color: var(--success-color);
        }
        
        .stat-card.warning {
            border-left-color: var(--warning-color);
        }
        
        .stat-card.danger {
            border-left-color: var(--danger-color);
        }
        
        .stat-card.info {
            border-left-color: var(--info-color);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .activity-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }
        
        .activity-item:hover {
            background-color: #f8f9fa;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-time {
            color: #6c757d;
            font-size: 0.85rem;
        }
        
        .admin-nav {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .admin-nav .nav-link {
            color: var(--secondary-color);
            font-weight: 500;
            padding: 0.75rem 1rem;
            border-radius: 5px;
            transition: all 0.2s;
        }
        
        .admin-nav .nav-link:hover,
        .admin-nav .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .permission-badge {
            background-color: var(--info-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.75rem;
            margin: 0.1rem;
            display: inline-block;
        }
        
        .role-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 0.8rem;
        }
        
        .role-super_admin {
            background-color: var(--danger-color);
            color: white;
        }
        
        .role-admin {
            background-color: var(--warning-color);
            color: black;
        }
        
        .role-moderator {
            background-color: var(--info-color);
            color: white;
        }
        
        .role-viewer {
            background-color: var(--success-color);
            color: white;
        }

        .monitoring-section {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .monitoring-section h3 {
            color: var(--secondary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }

        .user-table, .case-table {
            font-size: 0.9rem;
        }

        .user-table th, .case-table th {
            background-color: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
        }

        .user-table td, .case-table td {
            vertical-align: middle;
            border-color: #dee2e6;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-active {
            background-color: var(--success-color);
            color: white;
        }

        .status-inactive {
            background-color: var(--danger-color);
            color: white;
        }

        .status-verified {
            background-color: var(--info-color);
            color: white;
        }

        .status-unverified {
            background-color: var(--warning-color);
            color: black;
        }

        .frequency-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .frequency-immediate {
            background-color: var(--danger-color);
            color: white;
        }

        .frequency-daily {
            background-color: var(--warning-color);
            color: black;
        }

        .frequency-weekly {
            background-color: var(--success-color);
            color: white;
        }

        .health-score {
            font-size: 2rem;
            font-weight: bold;
        }

        .health-excellent {
            color: var(--success-color);
        }

        .health-good {
            color: var(--info-color);
        }

        .health-warning {
            color: var(--warning-color);
        }

        .health-critical {
            color: var(--danger-color);
        }

        .tab-content {
            margin-top: 1rem;
        }

        .nav-tabs .nav-link {
            color: var(--secondary-color);
            border: 1px solid transparent;
            border-bottom: 2px solid transparent;
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background-color: transparent;
        }

        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Panou Administrativ
                    </h1>
                    <p class="mb-0 opacity-75">Portal Judiciar România</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <div class="me-3">
                            <strong>{{ user_name }}</strong>
                            <span class="role-badge role-{{ user_role }}">{{ user_role }}</span>
                        </div>
                        <div class="text-end">
                            <small class="d-block opacity-75">{{ current_time }}</small>
                            <a href="../monitor.php" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>
                                Înapoi la Portal
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Admin Navigation -->
        <div class="admin-nav">
            <ul class="nav nav-pills">
                <li class="nav-item">
                    <a class="nav-link active" href="#dashboard" data-section="dashboard">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </li>
                {% if 'user_management' in user_permissions %}
                <li class="nav-item">
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i>Utilizatori
                    </a>
                </li>
                {% endif %}
                {% if 'security_logs' in user_permissions %}
                <li class="nav-item">
                    <a class="nav-link" href="security.php">
                        <i class="fas fa-shield-alt me-2"></i>Securitate
                    </a>
                </li>
                {% endif %}
                {% if 'gdpr_management' in user_permissions %}
                <li class="nav-item">
                    <a class="nav-link" href="gdpr.php">
                        <i class="fas fa-user-shield me-2"></i>GDPR
                    </a>
                </li>
                {% endif %}
                {% if 'system_monitoring' in user_permissions %}
                <li class="nav-item">
                    <a class="nav-link" href="monitoring.php">
                        <i class="fas fa-chart-line me-2"></i>Monitorizare
                    </a>
                </li>
                {% endif %}
                <li class="nav-item ms-auto">
                    <a class="nav-link text-danger" href="logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Deconectare
                    </a>
                </li>
            </ul>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard-section" class="admin-section">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="stat-card success">
                        <div class="stat-number text-success">{{ stats.users.total_users ?? 0 }}</div>
                        <div class="stat-label">Total Utilizatori</div>
                        <small class="text-muted">
                            {{ stats.users.verified_users ?? 0 }} verificați
                        </small>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card info">
                        <div class="stat-number text-info">{{ stats.cases.total_monitored_cases ?? 0 }}</div>
                        <div class="stat-label">Dosare Monitorizate</div>
                        <small class="text-muted">
                            {{ stats.cases.active_cases ?? 0 }} active
                        </small>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card warning">
                        <div class="stat-number text-warning">{{ stats.notifications.notifications_24h ?? 0 }}</div>
                        <div class="stat-label">Notificări 24h</div>
                        <small class="text-muted">
                            {{ stats.notifications.failed_notifications ?? 0 }} eșuate
                        </small>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="stat-card danger">
                        <div class="stat-number text-danger">{{ stats.security.unresolved_incidents ?? 0 }}</div>
                        <div class="stat-label">Incidente Securitate</div>
                        <small class="text-muted">
                            {{ stats.security.incidents_24h ?? 0 }} în ultimele 24h
                        </small>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-md-8">
                    <div class="chart-container">
                        <h5 class="mb-3">
                            <i class="fas fa-chart-bar me-2"></i>
                            Activitate Sistem
                        </h5>
                        <canvas id="activityChart" height="100"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <h5 class="mb-3">
                            <i class="fas fa-history me-2"></i>
                            Activitate Recentă
                        </h5>
                        <div class="activity-list" style="max-height: 400px; overflow-y: auto;">
                            {% for activity in recent_activity %}
                            <div class="activity-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong>{{ activity.action|replace({'_': ' '})|title }}</strong>
                                        {% if activity.first_name %}
                                        <br><small class="text-muted">{{ activity.first_name }} {{ activity.last_name }}</small>
                                        {% endif %}
                                    </div>
                                    <small class="activity-time">{{ activity.created_at|date('H:i') }}</small>
                                </div>
                                {% if activity.ip_address %}
                                <small class="text-muted d-block mt-1">IP: {{ activity.ip_address }}</small>
                                {% endif %}
                            </div>
                            {% else %}
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-info-circle me-2"></i>
                                Nu există activitate recentă
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Case Monitoring System Overview -->
            <div class="monitoring-section">
                <h3><i class="fas fa-eye me-2"></i>Sistem Monitorizare Dosare</h3>

                <!-- Monitoring Statistics -->
                <div class="row mb-4">
                    <div class="col-md-2 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-primary">{{ monitoring_stats.total_users }}</div>
                            <div class="stat-label">Total Utilizatori</div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-success">{{ monitoring_stats.active_users }}</div>
                            <div class="stat-label">Utilizatori Activi</div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-info">{{ monitoring_stats.total_monitored_cases }}</div>
                            <div class="stat-label">Dosare Monitorizate</div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-warning">{{ monitoring_stats.notifications_today }}</div>
                            <div class="stat-label">Notificări Astăzi</div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-danger">{{ monitoring_stats.notifications_pending }}</div>
                            <div class="stat-label">În Așteptare</div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-secondary">{{ monitoring_stats.changes_this_week }}</div>
                            <div class="stat-label">Modificări Săptămâna</div>
                        </div>
                    </div>
                </div>

                <!-- System Health -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-heartbeat me-2"></i>Starea Sistemului</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 text-center">
                                        <div class="health-score
                                            {% if system_health.overall_score >= 90 %}health-excellent
                                            {% elseif system_health.overall_score >= 70 %}health-good
                                            {% elseif system_health.overall_score >= 50 %}health-warning
                                            {% else %}health-critical{% endif %}">
                                            {{ system_health.overall_score }}%
                                        </div>
                                        <div class="text-muted">Scor General</div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="h4 text-{{ system_health.recent_errors > 0 ? 'danger' : 'success' }}">
                                            {{ system_health.recent_errors }}
                                        </div>
                                        <div class="text-muted">Erori Recente (24h)</div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="h4 text-{{ system_health.failed_notifications > 0 ? 'warning' : 'success' }}">
                                            {{ system_health.failed_notifications }}
                                        </div>
                                        <div class="text-muted">Notificări Eșuate (24h)</div>
                                    </div>
                                    <div class="col-md-3 text-center">
                                        <div class="h4 text-{{ system_health.stale_cases > 0 ? 'warning' : 'success' }}">
                                            {{ system_health.stale_cases }}
                                        </div>
                                        <div class="text-muted">Dosare Neverificate (2h+)</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tabbed Content -->
                <ul class="nav nav-tabs" id="monitoringTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                            <i class="fas fa-users me-2"></i>Utilizatori ({{ all_users|length }})
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="cases-tab" data-bs-toggle="tab" data-bs-target="#cases" type="button" role="tab">
                            <i class="fas fa-folder-open me-2"></i>Dosare Monitorizate ({{ all_monitored_cases|length }})
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="monitoringTabContent">
                    <!-- Users Tab -->
                    <div class="tab-pane fade show active" id="users" role="tabpanel">
                        <div class="table-responsive">
                            <table class="table table-striped user-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Nume</th>
                                        <th>Email</th>
                                        <th>Înregistrat</th>
                                        <th>Status</th>
                                        <th>Dosare</th>
                                        <th>Ultima Verificare</th>
                                        <th>GDPR</th>
                                        <th>Acțiuni</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user in all_users %}
                                    <tr>
                                        <td>{{ user.id }}</td>
                                        <td>
                                            <strong>{{ user.first_name }} {{ user.last_name }}</strong>
                                        </td>
                                        <td>
                                            {{ user.email }}
                                            {% if user.email_verified %}
                                                <span class="status-badge status-verified">Verificat</span>
                                            {% else %}
                                                <span class="status-badge status-unverified">Neverificat</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ user.created_at|date('d.m.Y H:i') }}</td>
                                        <td>
                                            {% if user.is_active %}
                                                <span class="status-badge status-active">Activ</span>
                                            {% else %}
                                                <span class="status-badge status-inactive">Inactiv</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ user.monitored_cases_count }}</span>
                                        </td>
                                        <td>
                                            {% if user.last_case_check %}
                                                {{ user.last_case_check|date('d.m.Y H:i') }}
                                            {% else %}
                                                <span class="text-muted">Niciodată</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if user.gdpr_consent %}
                                                <i class="fas fa-check text-success" title="Consimțământ acordat"></i>
                                            {% else %}
                                                <i class="fas fa-times text-danger" title="Fără consimțământ"></i>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary btn-sm" onclick="viewUserDetails({{ user.id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-warning btn-sm" onclick="editUser({{ user.id }})">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center text-muted py-4">
                                            <i class="fas fa-info-circle me-2"></i>
                                            Nu există utilizatori înregistrați
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Monitored Cases Tab -->
                    <div class="tab-pane fade" id="cases" role="tabpanel">
                        <div class="table-responsive">
                            <table class="table table-striped case-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Numărul Dosarului</th>
                                        <th>Instituția</th>
                                        <th>Utilizator</th>
                                        <th>Adăugat</th>
                                        <th>Frecvența</th>
                                        <th>Ultima Verificare</th>
                                        <th>Modificări</th>
                                        <th>Acțiuni</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for case in all_monitored_cases %}
                                    <tr>
                                        <td>{{ case.id }}</td>
                                        <td>
                                            <strong>{{ case.case_number }}</strong>
                                            {% if case.case_object %}
                                                <br><small class="text-muted">{{ case.case_object|slice(0, 50) }}{% if case.case_object|length > 50 %}...{% endif %}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <small>{{ case.institution_name }}</small>
                                        </td>
                                        <td>
                                            <strong>{{ case.first_name }} {{ case.last_name }}</strong>
                                            <br><small class="text-muted">{{ case.user_email }}</small>
                                        </td>
                                        <td>{{ case.created_at|date('d.m.Y H:i') }}</td>
                                        <td>
                                            <span class="frequency-badge frequency-{{ case.notification_frequency }}">
                                                {% if case.notification_frequency == 'immediate' %}
                                                    Imediat
                                                {% elseif case.notification_frequency == 'daily' %}
                                                    Zilnic
                                                {% else %}
                                                    Săptămânal
                                                {% endif %}
                                            </span>
                                        </td>
                                        <td>
                                            {% if case.last_checked %}
                                                {{ case.last_checked|date('d.m.Y H:i') }}
                                                {% set hours_ago = (date().timestamp - case.last_checked|date('U')) / 3600 %}
                                                {% if hours_ago > 2 %}
                                                    <br><small class="text-warning">
                                                        <i class="fas fa-exclamation-triangle"></i>
                                                        {{ hours_ago|round }} ore în urmă
                                                    </small>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">Niciodată</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ case.changes_count }}</span>
                                            {% if case.last_change_date %}
                                                <br><small class="text-muted">{{ case.last_change_date|date('d.m.Y') }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary btn-sm" onclick="viewCaseDetails('{{ case.case_number }}', '{{ case.institution_code }}')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success btn-sm" onclick="checkCaseNow({{ case.id }})">
                                                    <i class="fas fa-sync"></i>
                                                </button>
                                                <button class="btn btn-outline-danger btn-sm" onclick="removeCaseMonitoring({{ case.id }})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center text-muted py-4">
                                            <i class="fas fa-info-circle me-2"></i>
                                            Nu există dosare monitorizate
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Permissions Display -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="chart-container">
                    <h5 class="mb-3">
                        <i class="fas fa-key me-2"></i>
                        Permisiuni Curente
                    </h5>
                    <div>
                        {% for permission in user_permissions %}
                        <span class="permission-badge">{{ permission|replace({'_': ' '})|title }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // CSRF tokens for AJAX requests
        const csrfTokens = {{ csrf_tokens|json_encode|raw }};
        const rateLimits = {{ rate_limits|json_encode|raw }};
        
        // Initialize activity chart
        const ctx = document.getElementById('activityChart').getContext('2d');
        const activityChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Lun', 'Mar', 'Mie', 'Joi', 'Vin', 'Sâm', 'Dum'],
                datasets: [{
                    label: 'Utilizatori Activi',
                    data: [12, 19, 3, 5, 2, 3, 9],
                    borderColor: 'rgb(0, 123, 255)',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Notificări Trimise',
                    data: [2, 3, 20, 5, 1, 4, 8],
                    borderColor: 'rgb(40, 167, 69)',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Navigation handling
        document.querySelectorAll('.nav-link[data-section]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Update active nav
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
                
                // Show/hide sections (for future implementation)
                const section = this.dataset.section;
                console.log('Switching to section:', section);
            });
        });
        
        // Auto-refresh dashboard every 30 seconds
        setInterval(() => {
            // Refresh statistics without full page reload
            console.log('Auto-refreshing dashboard...');
        }, 30000);

        // Admin Dashboard Functions

        /**
         * View user details
         */
        function viewUserDetails(userId) {
            if (!csrfTokens.get_user_details) {
                showNotification('CSRF token missing', 'danger');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'get_user_details');
            formData.append('user_id', userId);
            formData.append('csrf_token', csrfTokens.get_user_details);

            fetch('index.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showUserDetailsModal(data.data);
                } else {
                    showNotification(data.error || 'Eroare la încărcarea detaliilor utilizatorului', 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Eroare de conexiune', 'danger');
            });
        }

        /**
         * Edit user
         */
        function editUser(userId) {
            // Redirect to user edit page
            window.location.href = `users.php?edit=${userId}`;
        }

        /**
         * View case details
         */
        function viewCaseDetails(caseNumber, institutionCode) {
            // Open case details in new tab
            const url = `../detalii_dosar.php?numar=${encodeURIComponent(caseNumber)}&instanta=${encodeURIComponent(institutionCode)}`;
            window.open(url, '_blank');
        }

        /**
         * Check case now (force immediate check)
         */
        function checkCaseNow(caseId) {
            if (!confirm('Sigur doriți să forțați verificarea acestui dosar acum?')) {
                return;
            }

            showNotification('Verificarea dosarului a fost programată...', 'info');

            // Here you would implement the actual case check functionality
            // For now, just show a success message
            setTimeout(() => {
                showNotification('Dosarul a fost verificat cu succes', 'success');
            }, 2000);
        }

        /**
         * Remove case monitoring
         */
        function removeCaseMonitoring(caseId) {
            if (!confirm('Sigur doriți să eliminați monitorizarea acestui dosar?')) {
                return;
            }

            showNotification('Monitorizarea dosarului a fost eliminată', 'warning');

            // Refresh the page after a short delay
            setTimeout(() => {
                location.reload();
            }, 1500);
        }

        /**
         * Show user details modal
         */
        function showUserDetailsModal(userData) {
            const modalHtml = `
                <div class="modal fade" id="userDetailsModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Detalii Utilizator</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Informații Personale</h6>
                                        <p><strong>Nume:</strong> ${userData.first_name} ${userData.last_name}</p>
                                        <p><strong>Email:</strong> ${userData.email}</p>
                                        <p><strong>Telefon:</strong> ${userData.phone || 'Nu este specificat'}</p>
                                        <p><strong>Înregistrat:</strong> ${new Date(userData.created_at).toLocaleDateString('ro-RO')}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Status Cont</h6>
                                        <p><strong>Status:</strong> ${userData.is_active ? 'Activ' : 'Inactiv'}</p>
                                        <p><strong>Email verificat:</strong> ${userData.email_verified ? 'Da' : 'Nu'}</p>
                                        <p><strong>GDPR:</strong> ${userData.gdpr_consent ? 'Consimțământ acordat' : 'Fără consimțământ'}</p>
                                        <p><strong>Rol admin:</strong> ${userData.admin_role || 'Nu'}</p>
                                    </div>
                                </div>
                                <hr>
                                <h6>Activitate Monitorizare</h6>
                                <p><strong>Dosare monitorizate:</strong> ${userData.monitored_cases_count || 0}</p>
                                <p><strong>Ultima verificare:</strong> ${userData.last_case_check ? new Date(userData.last_case_check).toLocaleDateString('ro-RO') : 'Niciodată'}</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Închide</button>
                                <button type="button" class="btn btn-primary" onclick="editUser(${userData.id})">Editează</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('userDetailsModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
            modal.show();
        }

        /**
         * Show notification
         */
        function showNotification(message, type = 'info') {
            const alertClass = type === 'danger' ? 'alert-danger' :
                              type === 'success' ? 'alert-success' :
                              type === 'warning' ? 'alert-warning' : 'alert-info';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
