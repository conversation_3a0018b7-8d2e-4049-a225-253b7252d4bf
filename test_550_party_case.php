<?php
/**
 * Test the specific case that should have 550+ parties
 * Case: 130/98/2022 from Curtea de Apel București
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<h1>🎯 Testing Case with 550+ Parties</h1>\n";
echo "<p><strong>Case:</strong> 130/98/2022 from Curtea de Apel București (CurteadeApelBUCURESTI)</p>\n";
echo "<p><strong>Expected:</strong> 550+ parties</p>\n";

try {
    $dosarService = new DosarService();
    $numarDosar = '130/98/2022';
    $institutie = 'CurteadeApelBUCURESTI';
    
    echo "<h2>🔍 Current System Test</h2>\n";
    
    // Clear debug log
    error_log("=== 550+ PARTY CASE TEST START ===");
    
    $startTime = microtime(true);
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    $endTime = microtime(true);
    
    error_log("=== 550+ PARTY CASE TEST END ===");
    
    if ($dosar && !empty($dosar->numar)) {
        $totalParties = count($dosar->parti ?? []);
        $processingTime = round(($endTime - $startTime) * 1000, 2);
        
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; margin: 15px 0; border-radius: 8px;'>\n";
        echo "<h3 style='margin-top: 0; color: #495057;'>📊 Current Results</h3>\n";
        echo "<p><strong>Total Parties Found:</strong> <span style='font-size: 1.8em; color: " . ($totalParties >= 550 ? '#28a745' : ($totalParties >= 200 ? '#ffc107' : '#dc3545')) . ";'>{$totalParties}</span></p>\n";
        echo "<p><strong>Processing Time:</strong> {$processingTime}ms</p>\n";
        echo "<p><strong>Target:</strong> 550+ parties</p>\n";
        
        if ($totalParties >= 550) {
            echo "<p style='color: #28a745; font-weight: bold;'>✅ TARGET ACHIEVED!</p>\n";
        } else {
            $deficit = 550 - $totalParties;
            echo "<p style='color: #dc3545; font-weight: bold;'>❌ DEFICIT: {$deficit} parties missing</p>\n";
        }
        echo "</div>\n";
        
        // Analyze source breakdown
        $soapCount = 0;
        $decisionCount = 0;
        $unknownCount = 0;
        
        foreach ($dosar->parti as $parte) {
            switch ($parte->source ?? 'unknown') {
                case 'soap_api': $soapCount++; break;
                case 'decision_text': $decisionCount++; break;
                default: $unknownCount++; break;
            }
        }
        
        echo "<h3>📈 Source Analysis</h3>\n";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>\n";
        echo "<thead style='background: #e9ecef;'>\n";
        echo "<tr><th style='padding: 12px;'>Source</th><th style='padding: 12px;'>Count</th><th style='padding: 12px;'>Percentage</th><th style='padding: 12px;'>Status</th></tr>\n";
        echo "</thead>\n";
        echo "<tbody>\n";
        
        $soapPercentage = $totalParties > 0 ? round(($soapCount / $totalParties) * 100, 1) : 0;
        $decisionPercentage = $totalParties > 0 ? round(($decisionCount / $totalParties) * 100, 1) : 0;
        $unknownPercentage = $totalParties > 0 ? round(($unknownCount / $totalParties) * 100, 1) : 0;
        
        echo "<tr><td style='padding: 10px;'>SOAP API</td><td style='padding: 10px; text-align: center;'>{$soapCount}</td><td style='padding: 10px; text-align: center;'>{$soapPercentage}%</td><td style='padding: 10px;'>" . ($soapCount >= 100 ? "⚠️ Limit reached" : "✅ Normal") . "</td></tr>\n";
        echo "<tr><td style='padding: 10px;'>Decision Text</td><td style='padding: 10px; text-align: center; color: " . ($decisionCount > 0 ? '#28a745' : '#dc3545') . ";'><strong>{$decisionCount}</strong></td><td style='padding: 10px; text-align: center;'>{$decisionPercentage}%</td><td style='padding: 10px;'>" . ($decisionCount > 0 ? "✅ Working" : "❌ Not working") . "</td></tr>\n";
        echo "<tr><td style='padding: 10px;'>Unknown</td><td style='padding: 10px; text-align: center;'>{$unknownCount}</td><td style='padding: 10px; text-align: center;'>{$unknownPercentage}%</td><td style='padding: 10px;'>-</td></tr>\n";
        echo "<tr style='background: #f8f9fa; font-weight: bold;'><td style='padding: 10px;'>Total</td><td style='padding: 10px; text-align: center;'>{$totalParties}</td><td style='padding: 10px; text-align: center;'>100%</td><td style='padding: 10px;'>" . ($totalParties >= 550 ? "🎯 Target met" : "❌ Below target") . "</td></tr>\n";
        echo "</tbody>\n";
        echo "</table>\n";
        
        // Hybrid extraction assessment
        echo "<h3>🔄 Hybrid Extraction Assessment</h3>\n";
        
        if ($soapCount >= 100 && $decisionCount > 0) {
            echo "<div style='background: #d1ecf1; border-left: 4px solid #17a2b8; padding: 15px; margin: 10px 0;'>\n";
            echo "<h4 style='color: #0c5460; margin: 0 0 10px 0;'>✅ Hybrid Extraction Active</h4>\n";
            echo "<p style='color: #0c5460; margin: 0;'>SOAP API limit reached ({$soapCount} parties) and decision text extraction found {$decisionCount} additional parties. System is working as designed.</p>\n";
            echo "</div>\n";
        } elseif ($soapCount >= 100 && $decisionCount == 0) {
            echo "<div style='background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 10px 0;'>\n";
            echo "<h4 style='color: #856404; margin: 0 0 10px 0;'>⚠️ Decision Text Extraction Issue</h4>\n";
            echo "<p style='color: #856404; margin: 0;'>SOAP API limit reached ({$soapCount} parties) but no decision text parties found. Need to investigate decision text content and extraction patterns.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0;'>\n";
            echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ SOAP API Under Limit</h4>\n";
            echo "<p style='color: #721c24; margin: 0;'>SOAP API returned only {$soapCount} parties (under 100 limit). This suggests the case may not have 550+ parties, or there's an issue with the SOAP response.</p>\n";
            echo "</div>\n";
        }
        
        // Show sample parties from each source
        echo "<h3>👥 Sample Parties by Source</h3>\n";
        
        // SOAP API parties
        echo "<h4>SOAP API Parties (first 5):</h4>\n";
        echo "<ul>\n";
        $soapSampleCount = 0;
        foreach ($dosar->parti as $parte) {
            if ($parte->source === 'soap_api' && $soapSampleCount < 5) {
                echo "<li><strong>" . htmlspecialchars($parte->nume) . "</strong> (" . $parte->calitate . ")</li>\n";
                $soapSampleCount++;
            }
        }
        if ($soapSampleCount == 0) {
            echo "<li style='color: #dc3545;'>No SOAP API parties found</li>\n";
        }
        echo "</ul>\n";
        
        // Decision text parties
        echo "<h4>Decision Text Parties (first 10):</h4>\n";
        echo "<ul>\n";
        $decisionSampleCount = 0;
        foreach ($dosar->parti as $parte) {
            if ($parte->source === 'decision_text' && $decisionSampleCount < 10) {
                echo "<li><strong>" . htmlspecialchars($parte->nume) . "</strong> (" . $parte->calitate . ")</li>\n";
                $decisionSampleCount++;
            }
        }
        if ($decisionSampleCount == 0) {
            echo "<li style='color: #dc3545;'>No decision text parties found</li>\n";
        }
        echo "</ul>\n";
        
        // Case details
        echo "<h3>📋 Case Information</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Case Number:</strong> " . htmlspecialchars($dosar->numar) . "</li>\n";
        echo "<li><strong>Institution:</strong> " . htmlspecialchars($dosar->institutie ?? 'N/A') . "</li>\n";
        echo "<li><strong>Object:</strong> " . htmlspecialchars($dosar->obiect ?? 'N/A') . "</li>\n";
        echo "<li><strong>Stage:</strong> " . htmlspecialchars($dosar->stadiu ?? 'N/A') . "</li>\n";
        echo "</ul>\n";
        
        // Next steps based on results
        echo "<h3>🔧 Next Steps</h3>\n";
        
        if ($totalParties >= 550) {
            echo "<div style='background: #d4edda; border-left: 4px solid #28a745; padding: 15px; margin: 10px 0;'>\n";
            echo "<p style='color: #155724; margin: 0;'><strong>✅ SUCCESS:</strong> Target achieved! The system is working correctly and extracting 550+ parties.</p>\n";
            echo "</div>\n";
        } elseif ($decisionCount == 0) {
            echo "<div style='background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 10px 0;'>\n";
            echo "<p style='color: #856404; margin: 0;'><strong>🔍 INVESTIGATE:</strong> Need to analyze decision text content to understand why no parties are being extracted.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0;'>\n";
            echo "<p style='color: #721c24; margin: 0;'><strong>🔧 ENHANCE:</strong> Decision text extraction is working but needs enhancement to capture all 550+ parties.</p>\n";
            echo "</div>\n";
        }
        
    } else {
        echo "<div style='background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0;'>\n";
        echo "<p style='color: #721c24; margin: 0;'><strong>❌ ERROR:</strong> Case not found. Please verify the case number and institution code.</p>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0;'>\n";
    echo "<p style='color: #721c24; margin: 0;'><strong>❌ EXCEPTION:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}

echo "<h2>🌐 Test URLs</h2>\n";
echo "<ul>\n";
echo "<li><strong>Legacy:</strong> <a href='detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI' target='_blank'>detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI</a></li>\n";
echo "<li><strong>PSR-4:</strong> <a href='public/detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI' target='_blank'>public/detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI</a></li>\n";
echo "</ul>\n";
?>
