<?php
// Simple debug to check current party extraction

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "🔍 CURRENT PARTY EXTRACTION STATUS" . PHP_EOL;
echo "==================================" . PHP_EOL;

try {
    $dosarService = new DosarService();
    
    // Test TribunalulIALOMITA
    echo PHP_EOL . "Case 1: TribunalulIALOMITA" . PHP_EOL;
    $dosar1 = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');
    
    if ($dosar1) {
        echo "Data type: " . gettype($dosar1) . PHP_EOL;
        if (is_object($dosar1) && isset($dosar1->parti)) {
            echo "Parties found: " . count($dosar1->parti) . " / 380 expected" . PHP_EOL;
            echo "Missing: " . (380 - count($dosar1->parti)) . PHP_EOL;
        } elseif (is_array($dosar1) && isset($dosar1['parti'])) {
            echo "Parties found: " . count($dosar1['parti']) . " / 380 expected" . PHP_EOL;
            echo "Missing: " . (380 - count($dosar1['parti'])) . PHP_EOL;
        } else {
            echo "❌ No parti field found" . PHP_EOL;
            if (is_object($dosar1)) {
                echo "Available properties: " . implode(', ', array_keys(get_object_vars($dosar1))) . PHP_EOL;
            } elseif (is_array($dosar1)) {
                echo "Available keys: " . implode(', ', array_keys($dosar1)) . PHP_EOL;
            }
        }
    } else {
        echo "❌ No data returned" . PHP_EOL;
    }

    // Test CurteadeApelBUCURESTI
    echo PHP_EOL . "Case 2: CurteadeApelBUCURESTI" . PHP_EOL;
    $dosar2 = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');

    if ($dosar2) {
        echo "Data type: " . gettype($dosar2) . PHP_EOL;
        if (is_object($dosar2) && isset($dosar2->parti)) {
            echo "Parties found: " . count($dosar2->parti) . " / 380 expected" . PHP_EOL;
            echo "Missing: " . (380 - count($dosar2->parti)) . PHP_EOL;
        } elseif (is_array($dosar2) && isset($dosar2['parti'])) {
            echo "Parties found: " . count($dosar2['parti']) . " / 380 expected" . PHP_EOL;
            echo "Missing: " . (380 - count($dosar2['parti'])) . PHP_EOL;
        } else {
            echo "❌ No parti field found" . PHP_EOL;
            if (is_object($dosar2)) {
                echo "Available properties: " . implode(', ', array_keys(get_object_vars($dosar2))) . PHP_EOL;
            } elseif (is_array($dosar2)) {
                echo "Available keys: " . implode(', ', array_keys($dosar2)) . PHP_EOL;
            }
        }
    } else {
        echo "❌ No data returned" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
}

echo PHP_EOL . "✅ Done" . PHP_EOL;
?>
