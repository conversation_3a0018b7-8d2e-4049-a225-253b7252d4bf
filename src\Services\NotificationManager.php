<?php

namespace App\Services;

use App\Config\Database;
use App\Services\EmailTemplateEngine;
use App\Services\UserPreferencesManager;
use Exception;

/**
 * Notification Manager Service
 * 
 * Handles email notification queuing, processing, and delivery
 * for case monitoring system.
 */
class NotificationManager
{
    private $emailTemplateEngine;
    private $userPreferencesManager;
    private $logFile;

    public function __construct()
    {
        $this->emailTemplateEngine = new EmailTemplateEngine();
        $this->userPreferencesManager = new UserPreferencesManager();
        $this->logFile = LOG_DIR . '/notifications.log';

        // Ensure log directory exists
        if (!is_dir(LOG_DIR)) {
            mkdir(LOG_DIR, 0755, true);
        }
    }

    /**
     * Queue notification for case added to monitoring
     * 
     * @param int $userId User ID
     * @param int $monitoredCaseId Monitored case ID
     * @param int $changeId Change ID
     * @return int Notification queue ID
     */
    public function queueCaseAddedNotification(int $userId, int $monitoredCaseId, int $changeId): int
    {
        // Get case details
        $case = Database::fetchOne(
            "SELECT * FROM monitored_cases WHERE id = ?",
            [$monitoredCaseId]
        );

        if (!$case) {
            throw new Exception("Monitored case not found");
        }

        // Get user details
        $user = Database::fetchOne(
            "SELECT * FROM active_users WHERE id = ? AND email_verified = 1",
            [$userId]
        );

        if (!$user) {
            throw new Exception("User not found or inactive");
        }

        // Generate email content
        $subject = "Dosar {$case['case_number']} - Adăugat în monitorizare";
        $emailData = [
            'user_name' => $user['first_name'] . ' ' . $user['last_name'],
            'case_number' => $case['case_number'],
            'institution_name' => $case['institution_name'],
            'case_object' => $case['case_object'],
            'monitoring_reason' => $case['monitoring_reason'],
            'notification_frequency' => $case['notification_frequency']
        ];

        $textBody = $this->emailTemplateEngine->renderCaseAddedText($emailData);
        $htmlBody = $this->emailTemplateEngine->renderCaseAddedHtml($emailData);

        // Queue notification
        return Database::insert('notification_queue', [
            'user_id' => $userId,
            'monitored_case_id' => $monitoredCaseId,
            'case_change_id' => $changeId,
            'notification_type' => 'immediate',
            'email_subject' => $subject,
            'email_body' => $textBody,
            'email_html_body' => $htmlBody,
            'priority' => 1,
            'status' => 'pending',
            'scheduled_for' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Queue notification for case change
     * 
     * @param int $userId User ID
     * @param int $monitoredCaseId Monitored case ID
     * @param int $changeId Change ID
     * @return int Notification queue ID
     */
    public function queueChangeNotification(int $userId, int $monitoredCaseId, int $changeId): int
    {
        // Get case and change details
        $sql = "SELECT mc.*, cc.change_type, cc.change_description, cc.change_details
                FROM monitored_cases mc
                JOIN case_changes cc ON cc.monitored_case_id = mc.id
                WHERE mc.id = ? AND cc.id = ?";
        
        $result = Database::fetchOne($sql, [$monitoredCaseId, $changeId]);
        
        if (!$result) {
            throw new Exception("Case or change not found");
        }

        // Get user details
        $user = Database::fetchOne(
            "SELECT * FROM active_users WHERE id = ? AND email_verified = 1",
            [$userId]
        );

        if (!$user) {
            throw new Exception("User not found or inactive");
        }

        // Generate email content
        $subject = "Dosar {$result['case_number']} - {$result['change_description']}";
        $emailData = [
            'user_name' => $user['first_name'] . ' ' . $user['last_name'],
            'case_number' => $result['case_number'],
            'institution_name' => $result['institution_name'],
            'case_object' => $result['case_object'],
            'change_type' => $result['change_type'],
            'change_description' => $result['change_description'],
            'change_details' => json_decode($result['change_details'], true)
        ];

        $textBody = $this->emailTemplateEngine->renderChangeNotificationText($emailData);
        $htmlBody = $this->emailTemplateEngine->renderChangeNotificationHtml($emailData);

        // Queue notification
        return Database::insert('notification_queue', [
            'user_id' => $userId,
            'monitored_case_id' => $monitoredCaseId,
            'case_change_id' => $changeId,
            'notification_type' => 'immediate',
            'email_subject' => $subject,
            'email_body' => $textBody,
            'email_html_body' => $htmlBody,
            'priority' => 1,
            'status' => 'pending',
            'scheduled_for' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Queue daily digest notifications
     * 
     * @return int Number of notifications queued
     */
    public function queueDailyDigestNotifications(): int
    {
        // Get users who want daily digest using preferences manager
        $users = $this->userPreferencesManager->getUsersForNotificationType('daily', true);

        // Filter to only users with monitored cases
        $usersWithCases = [];
        foreach ($users as $user) {
            $hasCases = Database::fetchOne(
                "SELECT COUNT(*) as count FROM monitored_cases
                 WHERE user_id = ? AND is_active = 1 AND notification_frequency = 'daily'",
                [$user['id']]
            );

            if (($hasCases['count'] ?? 0) > 0) {
                $usersWithCases[] = $user;
            }
        }

        $queued = 0;
        $scheduledFor = date('Y-m-d 08:00:00'); // 8 AM

        foreach ($usersWithCases as $user) {
            // Check if user has reached daily limit
            if ($this->userPreferencesManager->hasReachedDailyLimit($user['id'])) {
                $this->logInfo("Skipping daily digest for user {$user['id']} - daily limit reached");
                continue;
            }
            try {
                // Get user's cases with recent changes
                $cases = $this->getUserCasesForDigest($user['id']);
                
                if (empty($cases)) {
                    continue; // No changes to report
                }

                // Generate digest content
                $subject = "Raport zilnic - Dosarele monitorizate (" . count($cases) . " dosare)";
                $emailData = [
                    'user_name' => $user['first_name'] . ' ' . $user['last_name'],
                    'cases' => $cases,
                    'total_cases' => count($cases),
                    'date' => date('d.m.Y')
                ];

                $textBody = $this->emailTemplateEngine->renderDailyDigestText($emailData);
                $htmlBody = $this->emailTemplateEngine->renderDailyDigestHtml($emailData);

                // Queue notification
                Database::insert('notification_queue', [
                    'user_id' => $user['id'],
                    'monitored_case_id' => 0, // Digest covers multiple cases
                    'case_change_id' => null,
                    'notification_type' => 'daily_digest',
                    'email_subject' => $subject,
                    'email_body' => $textBody,
                    'email_html_body' => $htmlBody,
                    'priority' => 5,
                    'status' => 'pending',
                    'scheduled_for' => $scheduledFor
                ]);

                $queued++;

            } catch (Exception $e) {
                error_log("Failed to queue daily digest for user {$user['id']}: " . $e->getMessage());
            }
        }

        return $queued;
    }

    /**
     * Queue weekly summary notifications
     * 
     * @return int Number of notifications queued
     */
    public function queueWeeklySummaryNotifications(): int
    {
        // Get users who want weekly summary
        $users = Database::fetchAll(
            "SELECT DISTINCT u.* FROM users u
             JOIN monitored_cases mc ON mc.user_id = u.id
             WHERE u.is_active = 1 
             AND u.email_verified = 1
             AND mc.is_active = 1
             AND mc.notification_frequency = 'weekly'
             AND JSON_EXTRACT(u.notification_preferences, '$.weekly_summary') = true"
        );

        $queued = 0;
        $scheduledFor = date('Y-m-d 08:00:00'); // Monday 8 AM

        foreach ($users as $user) {
            try {
                // Get user's cases with changes from last week
                $cases = $this->getUserCasesForWeeklySummary($user['id']);
                
                if (empty($cases)) {
                    continue; // No changes to report
                }

                // Generate summary content
                $subject = "Raport săptămânal - Dosarele monitorizate (" . count($cases) . " dosare)";
                $emailData = [
                    'user_name' => $user['first_name'] . ' ' . $user['last_name'],
                    'cases' => $cases,
                    'total_cases' => count($cases),
                    'week_start' => date('d.m.Y', strtotime('-7 days')),
                    'week_end' => date('d.m.Y')
                ];

                $textBody = $this->emailTemplateEngine->renderWeeklySummaryText($emailData);
                $htmlBody = $this->emailTemplateEngine->renderWeeklySummaryHtml($emailData);

                // Queue notification
                Database::insert('notification_queue', [
                    'user_id' => $user['id'],
                    'monitored_case_id' => 0, // Summary covers multiple cases
                    'case_change_id' => null,
                    'notification_type' => 'weekly_summary',
                    'email_subject' => $subject,
                    'email_body' => $textBody,
                    'email_html_body' => $htmlBody,
                    'priority' => 7,
                    'status' => 'pending',
                    'scheduled_for' => $scheduledFor
                ]);

                $queued++;

            } catch (Exception $e) {
                error_log("Failed to queue weekly summary for user {$user['id']}: " . $e->getMessage());
            }
        }

        return $queued;
    }

    /**
     * Process pending notifications
     * 
     * @param int $limit Maximum number of notifications to process
     * @return array Processing summary
     */
    public function processPendingNotifications(int $limit = NOTIFICATION_BATCH_SIZE): array
    {
        $startTime = microtime(true);
        $this->logInfo("Starting to process pending notifications (limit: {$limit})");

        $summary = [
            'processed' => 0,
            'sent' => 0,
            'failed' => 0,
            'retried' => 0,
            'errors' => []
        ];

        try {
            // Get pending notifications
            $notifications = Database::fetchAll(
                "SELECT nq.*, u.email, u.first_name, u.last_name
                 FROM notification_queue nq
                 JOIN users u ON u.id = nq.user_id
                 WHERE nq.status = 'pending'
                 AND nq.scheduled_for <= NOW()
                 AND nq.attempts < nq.max_attempts
                 ORDER BY nq.priority ASC, nq.scheduled_for ASC
                 LIMIT ?",
                [$limit]
            );

            $this->logInfo("Found " . count($notifications) . " pending notifications to process");

            foreach ($notifications as $notification) {
                $summary['processed']++;
                $notificationId = $notification['id'];

                try {
                    // Mark as processing
                    Database::update('notification_queue',
                        ['status' => 'processing', 'attempts' => $notification['attempts'] + 1],
                        ['id' => $notificationId]
                    );

                    $attemptNumber = $notification['attempts'] + 1;
                    $this->logInfo("Processing notification #{$notificationId} for {$notification['email']} (attempt {$attemptNumber})");

                    // Check user's email format preference
                    $emailFormat = $this->userPreferencesManager->getEmailFormat($notification['user_id']);
                    $htmlBody = ($emailFormat === 'html') ? $notification['email_html_body'] : null;

                    // Send email
                    $sent = $this->sendEmail(
                        $notification['email'],
                        $notification['email_subject'],
                        $notification['email_body'],
                        $htmlBody
                    );

                    if ($sent) {
                        // Mark as sent
                        Database::update('notification_queue',
                            ['status' => 'sent', 'sent_at' => date('Y-m-d H:i:s')],
                            ['id' => $notificationId]
                        );
                        $summary['sent']++;
                        $this->logInfo("Notification #{$notificationId} sent successfully");
                    } else {
                        throw new Exception("Email sending failed");
                    }
                } catch (Exception $e) {
                    $summary['failed']++;
                    $errorMessage = "Notification #{$notificationId}: " . $e->getMessage();
                    $summary['errors'][] = $errorMessage;
                    $this->logError($errorMessage);

                    // Calculate next retry time with exponential backoff
                    $retryDelay = NOTIFICATION_RETRY_DELAY * pow(2, $notification['attempts']);
                    $nextRetry = date('Y-m-d H:i:s', strtotime("+{$retryDelay} seconds"));

                    // Update notification status
                    if ($notification['attempts'] >= $notification['max_attempts']) {
                        // Mark as failed if max attempts reached
                        Database::update('notification_queue',
                            [
                                'status' => 'failed',
                                'error_message' => $e->getMessage()
                            ],
                            ['id' => $notificationId]
                        );
                        $attemptCount = $notification['attempts'];
                        $this->logError("Notification #{$notificationId} failed permanently after {$attemptCount} attempts");
                    } else {
                        // Mark for retry
                        Database::update('notification_queue',
                            [
                                'status' => 'pending',
                                'scheduled_for' => $nextRetry,
                                'error_message' => $e->getMessage()
                            ],
                            ['id' => $notificationId]
                        );
                        $summary['retried']++;
                        $this->logInfo("Notification #{$notificationId} scheduled for retry at {$nextRetry}");
                    }
                }

                // Add a small delay between sending emails to avoid overwhelming the mail server
                if ($summary['processed'] < count($notifications)) {
                    usleep(100000); // 100ms delay
                }
            }
        } catch (Exception $e) {
            $this->logError("Error processing notification queue: " . $e->getMessage());
            $summary['errors'][] = "Queue processing error: " . $e->getMessage();
        }

        $executionTime = round(microtime(true) - $startTime, 2);
        $this->logInfo("Finished processing notifications. Processed: {$summary['processed']}, Sent: {$summary['sent']}, Failed: {$summary['failed']}, Retried: {$summary['retried']}, Time: {$executionTime}s");

        return $summary;
    }

    /**
     * Get user cases for daily digest
     * 
     * @param int $userId User ID
     * @return array Cases with recent changes
     */
    private function getUserCasesForDigest(int $userId): array
    {
        return Database::fetchAll(
            "SELECT mc.*, 
                    COUNT(cc.id) as changes_count,
                    GROUP_CONCAT(cc.change_description SEPARATOR '; ') as recent_changes
             FROM monitored_cases mc
             LEFT JOIN case_changes cc ON cc.monitored_case_id = mc.id 
                 AND cc.detected_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
             WHERE mc.user_id = ? AND mc.is_active = 1
             GROUP BY mc.id
             HAVING changes_count > 0
             ORDER BY changes_count DESC, mc.case_number",
            [$userId]
        );
    }

    /**
     * Get user cases for weekly summary
     * 
     * @param int $userId User ID
     * @return array Cases with changes from last week
     */
    private function getUserCasesForWeeklySummary(int $userId): array
    {
        return Database::fetchAll(
            "SELECT mc.*, 
                    COUNT(cc.id) as changes_count,
                    GROUP_CONCAT(cc.change_description SEPARATOR '; ') as recent_changes
             FROM monitored_cases mc
             LEFT JOIN case_changes cc ON cc.monitored_case_id = mc.id 
                 AND cc.detected_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
             WHERE mc.user_id = ? AND mc.is_active = 1
             GROUP BY mc.id
             HAVING changes_count > 0
             ORDER BY changes_count DESC, mc.case_number",
            [$userId]
        );
    }

    /**
     * Send email with improved error handling and logging
     *
     * @param string $to Recipient email
     * @param string $subject Email subject
     * @param string $textBody Plain text body
     * @param string $htmlBody HTML body
     * @return bool Success status
     */
    private function sendEmail(string $to, string $subject, string $textBody, string $htmlBody = null): bool
    {
        try {
            // Validate email address
            if (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
                $this->logError("Invalid email address: {$to}");
                return false;
            }

            // Prepare headers
            $headers = [
                'From: ' . CONTACT_NAME . ' <' . CONTACT_EMAIL . '>',
                'Reply-To: ' . CONTACT_EMAIL,
                'X-Mailer: Portal Judiciar Monitoring System v2.0',
                'X-Priority: 3',
                'MIME-Version: 1.0'
            ];

            // Prepare body
            if ($htmlBody) {
                $boundary = uniqid('boundary_');
                $headers[] = 'Content-Type: multipart/alternative; boundary="' . $boundary . '"';

                $body = "--{$boundary}\r\n";
                $body .= "Content-Type: text/plain; charset=UTF-8\r\n";
                $body .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
                $body .= $textBody . "\r\n\r\n";

                $body .= "--{$boundary}\r\n";
                $body .= "Content-Type: text/html; charset=UTF-8\r\n";
                $body .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
                $body .= $htmlBody . "\r\n\r\n";

                $body .= "--{$boundary}--";
            } else {
                $headers[] = 'Content-Type: text/plain; charset=UTF-8';
                $body = $textBody;
            }

            // Send email
            $result = mail($to, $subject, $body, implode("\r\n", $headers));

            if ($result) {
                $this->logInfo("Email sent successfully to: {$to}");
            } else {
                $this->logError("Failed to send email to: {$to}");
            }

            return $result;

        } catch (Exception $e) {
            $this->logError("Email sending exception: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Process notification queue for cron job
     *
     * @param int $limit Maximum number of notifications to process
     * @return int Number of notifications processed
     */
    public function processQueue(int $limit = 50): int
    {
        $summary = $this->processPendingNotifications($limit);
        return $summary['processed'];
    }

    /**
     * Queue a case change notification
     *
     * @param int $userId User ID
     * @param string $caseNumber Case number
     * @param string $institutionCode Institution code
     * @return bool Success status
     */
    public function queueCaseChangeNotification(int $userId, string $caseNumber, string $institutionCode): bool
    {
        try {
            // Get the monitored case ID
            $monitoredCase = Database::fetchOne(
                "SELECT id FROM monitored_cases WHERE user_id = ? AND case_number = ? AND institution_code = ?",
                [$userId, $caseNumber, $institutionCode]
            );

            if (!$monitoredCase) {
                return false;
            }

            // Get the latest case change
            $latestChange = Database::fetchOne(
                "SELECT id FROM case_changes WHERE monitored_case_id = ? ORDER BY created_at DESC LIMIT 1",
                [$monitoredCase['id']]
            );

            if (!$latestChange) {
                return false;
            }

            // Generate email content
            $emailData = [
                'user_id' => $userId,
                'case_number' => $caseNumber,
                'institution_code' => $institutionCode,
                'change_id' => $latestChange['id']
            ];

            $subject = "Modificare detectată - Dosarul {$caseNumber}";
            $textBody = $this->emailTemplateEngine->renderChangeNotificationText($emailData);
            $htmlBody = $this->emailTemplateEngine->renderChangeNotificationHtml($emailData);

            // Queue the notification
            Database::insert('notification_queue', [
                'user_id' => $userId,
                'monitored_case_id' => $monitoredCase['id'],
                'case_change_id' => $latestChange['id'],
                'notification_type' => 'case_change',
                'email_subject' => $subject,
                'email_body' => $textBody,
                'email_html_body' => $htmlBody,
                'priority' => 1, // High priority for immediate notifications
                'status' => 'pending',
                'scheduled_for' => date('Y-m-d H:i:s')
            ]);

            return true;

        } catch (Exception $e) {
            error_log("Failed to queue case change notification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean up old notification logs
     *
     * @param int $daysToKeep Number of days to keep logs
     * @return int Number of records deleted
     */
    public function cleanupOldLogs(int $daysToKeep = 90): int
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));

        // Clean up old notification queue entries that are completed or failed
        $deletedQueue = Database::execute(
            "DELETE FROM notification_queue WHERE created_at < ? AND status IN ('sent', 'failed')",
            [$cutoffDate]
        );

        // Clean up old notification logs
        $deletedLogs = Database::execute(
            "DELETE FROM notification_logs WHERE created_at < ?",
            [$cutoffDate]
        );

        return $deletedQueue + $deletedLogs;
    }

    /**
     * Log info message
     *
     * @param string $message Message to log
     */
    private function logInfo(string $message): void
    {
        $this->writeLog('INFO', $message);
    }

    /**
     * Log error message
     *
     * @param string $message Message to log
     */
    private function logError(string $message): void
    {
        $this->writeLog('ERROR', $message);
        error_log("NotificationManager Error: {$message}");
    }

    /**
     * Write log message to file
     *
     * @param string $level Log level
     * @param string $message Message to log
     */
    private function writeLog(string $level, string $message): void
    {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;

        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Get notification statistics
     *
     * @return array Statistics about notifications
     */
    public function getNotificationStats(): array
    {
        try {
            $stats = Database::fetchOne(
                "SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                    SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                 FROM notification_queue
                 WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)"
            );

            return $stats ?: [
                'total' => 0, 'pending' => 0, 'processing' => 0,
                'sent' => 0, 'failed' => 0, 'cancelled' => 0
            ];

        } catch (Exception $e) {
            $this->logError("Failed to get notification stats: " . $e->getMessage());
            return [
                'total' => 0, 'pending' => 0, 'processing' => 0,
                'sent' => 0, 'failed' => 0, 'cancelled' => 0
            ];
        }
    }
}
