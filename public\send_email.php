<?php

/**
 * Portal Judiciar - Trimitere email
 */

// Încărcăm bootstrap-ul aplicației
require_once dirname(__DIR__) . '/bootstrap.php';

// Importăm clasele necesare
use App\Helpers\TemplateEngine;
use App\Services\DosarService;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Verificăm dacă cererea este de tip POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    // Redirecționăm către pagina principală
    header('Location: index.php');
    exit;
}

// Parametrii pentru email
$numarDosar = $_POST['numarDosar'] ?? '';
$institutie = $_POST['institutie'] ?? '';
$emailTo = $_POST['emailTo'] ?? '';
$emailFrom = $_POST['emailFrom'] ?? '';
$emailSubject = $_POST['emailSubject'] ?? '';
$emailMessage = $_POST['emailMessage'] ?? '';

// Verificăm dacă avem parametrii necesari
if (empty($numarDosar) || empty($institutie) || empty($emailTo) || empty($emailFrom)) {
    // Adăugăm un mesaj flash cu eroarea
    TemplateEngine::addFlashMessage('danger', 'Toate câmpurile obligatorii trebuie completate.');
    
    // Redirecționăm către pagina de detalii dosar
    header("Location: detalii_dosar.php?numar={$numarDosar}&institutie={$institutie}");
    exit;
}

// Verificăm dacă adresele de email sunt valide
if (!filter_var($emailTo, FILTER_VALIDATE_EMAIL) || !filter_var($emailFrom, FILTER_VALIDATE_EMAIL)) {
    // Adăugăm un mesaj flash cu eroarea
    TemplateEngine::addFlashMessage('danger', 'Adresele de email nu sunt valide.');
    
    // Redirecționăm către pagina de detalii dosar
    header("Location: detalii_dosar.php?numar={$numarDosar}&institutie={$institutie}");
    exit;
}

// Verificăm limitarea de rate (maximum 5 email-uri pe oră per IP)
$ipAddress = $_SERVER['REMOTE_ADDR'];
$rateLimit = 5;
$rateLimitPeriod = 3600; // 1 oră în secunde

// Verificăm dacă există fișierul de rate limiting
$rateLimitFile = LOG_DIR . '/email_rate_limit.json';
if (!file_exists($rateLimitFile)) {
    file_put_contents($rateLimitFile, json_encode([]));
}

// Citim datele de rate limiting
$rateLimitData = json_decode(file_get_contents($rateLimitFile), true);

// Curățăm datele vechi
$currentTime = time();
foreach ($rateLimitData as $ip => $data) {
    foreach ($data as $timestamp => $count) {
        if ($currentTime - $timestamp > $rateLimitPeriod) {
            unset($rateLimitData[$ip][$timestamp]);
        }
    }
    
    if (empty($rateLimitData[$ip])) {
        unset($rateLimitData[$ip]);
    }
}

// Verificăm dacă IP-ul a depășit limita
$ipCount = 0;
if (isset($rateLimitData[$ipAddress])) {
    foreach ($rateLimitData[$ipAddress] as $timestamp => $count) {
        $ipCount += $count;
    }
}

if ($ipCount >= $rateLimit) {
    // Adăugăm un mesaj flash cu eroarea
    TemplateEngine::addFlashMessage('danger', 'Ați depășit limita de email-uri trimise. Vă rugăm să încercați din nou mai târziu.');
    
    // Redirecționăm către pagina de detalii dosar
    header("Location: detalii_dosar.php?numar={$numarDosar}&institutie={$institutie}");
    exit;
}

try {
    // Inițializăm serviciul de dosare
    $dosarService = new DosarService();
    
    // Obținem detaliile dosarului
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    // Verificăm dacă am găsit dosarul
    if (empty((array)$dosar)) {
        throw new Exception('Nu au fost găsite detalii pentru dosarul specificat.');
    }
    
    // Obținem lista instanțelor din funcția centralizată
    require_once '../includes/functions.php';
    $instante = getInstanteList();
    
    // Datele pentru șablon
    $data = [
        'instante' => $instante,
        'dosar' => $dosar,
        'message' => $emailMessage,
        'sender' => $emailFrom
    ];
    
    // Inițializăm motorul de șabloane
    $templateEngine = new TemplateEngine();
    
    // Generăm conținutul email-ului
    $emailContent = $templateEngine->render('email/dosar.twig', $data);
    
    // Inițializăm PHPMailer
    $mail = new PHPMailer(true);
    
    // Configurăm serverul SMTP
    $mail->isSMTP();
    $mail->Host = 'smtp.example.com'; // Înlocuiți cu serverul SMTP real
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>'; // Înlocuiți cu username-ul real
    $mail->Password = 'password'; // Înlocuiți cu parola reală
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = 587;
    
    // Configurăm email-ul
    $mail->setFrom($emailFrom);
    $mail->addAddress($emailTo);
    $mail->Subject = $emailSubject;
    $mail->isHTML(true);
    $mail->CharSet = 'UTF-8';
    $mail->Body = $emailContent;
    
    // Trimitem email-ul
    $mail->send();
    
    // Actualizăm datele de rate limiting
    if (!isset($rateLimitData[$ipAddress])) {
        $rateLimitData[$ipAddress] = [];
    }
    
    if (!isset($rateLimitData[$ipAddress][$currentTime])) {
        $rateLimitData[$ipAddress][$currentTime] = 0;
    }
    
    $rateLimitData[$ipAddress][$currentTime]++;
    
    // Salvăm datele de rate limiting
    file_put_contents($rateLimitFile, json_encode($rateLimitData));
    
    // Adăugăm un mesaj flash de succes
    TemplateEngine::addFlashMessage('success', 'Email-ul a fost trimis cu succes.');
} catch (Exception $e) {
    // Logăm eroarea
    $errorLogFile = LOG_DIR . '/email_errors.log';
    $errorLogData = date('Y-m-d H:i:s') . " - Eroare la trimiterea email-ului: " . $e->getMessage() . "\n";
    file_put_contents($errorLogFile, $errorLogData, FILE_APPEND);
    
    // Adăugăm un mesaj flash cu eroarea
    TemplateEngine::addFlashMessage('danger', 'Eroare la trimiterea email-ului: ' . $e->getMessage());
}

// Redirecționăm către pagina de detalii dosar
header("Location: detalii_dosar.php?numar={$numarDosar}&institutie={$institutie}");
exit;
