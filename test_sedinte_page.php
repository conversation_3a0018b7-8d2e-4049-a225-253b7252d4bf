<?php
// Simulăm o cerere POST către sedinte.php cu CurteadeApelBACU
$_POST = [
    'dataSedinta' => '15.01.2024',
    'institutie' => 'CurteadeApelBACU'
];

// Simulăm metoda POST
$_SERVER['REQUEST_METHOD'] = 'POST';

// Capturăm output-ul
ob_start();

// Includem pagina sedinte.php
include 'sedinte.php';

$output = ob_get_clean();

// Verificăm dacă există erori în output
if (strpos($output, 'Eroare la căutarea ședințelor') !== false) {
    echo "❌ EROARE GĂSITĂ în pagina sedinte.php:\n";
    // Extragem mesajul de eroare
    if (preg_match('/Eroare la căutarea ședințelor: ([^<]+)/', $output, $matches)) {
        echo "Mesaj eroare: " . trim($matches[1]) . "\n";
    }
} else if (strpos($output, 'ședințe găsite') !== false) {
    echo "✅ SUCCESS: Pagina sedinte.php funcționează cu CurteadeApelBACU\n";
    // Extragem numărul de rezultate
    if (preg_match('/(\d+) ședințe găsite/', $output, $matches)) {
        echo "Numărul de ședințe găsite: " . $matches[1] . "\n";
    }
} else {
    // Check for search results section
    if (strpos($output, 'rezultate-container') !== false) {
        echo "✅ SUCCESS: Pagina sedinte.php s-a încărcat cu succes pentru CurteadeApelBACU\n";

        // Check if there are actual results
        if (strpos($output, '0 ședințe găsite') !== false) {
            echo "ℹ️  0 ședințe găsite pentru CurteadeApelBACU pe data specificată (normal)\n";
        } else if (preg_match('/(\d+) ședințe găsite/', $output, $matches)) {
            echo "ℹ️  " . $matches[1] . " ședințe găsite pentru CurteadeApelBACU\n";
        }

        echo "✅ Fallback mechanism funcționează corect!\n";
    } else {
        echo "⚠️ Nu s-au găsit indicatori clari de succes sau eroare\n";

        // Check for specific search-related text
        if (strpos($output, 'Rezultate căutare') !== false) {
            echo "ℹ️  Secțiunea de rezultate găsită\n";
        }
        if (strpos($output, 'CurteadeApelBACU') !== false) {
            echo "ℹ️  Codul instituției CurteadeApelBACU găsit în output\n";
        }
        if (strpos($output, '15.01.2024') !== false) {
            echo "ℹ️  Data 15.01.2024 găsită în output\n";
        }
        if (strpos($output, 'Eroare') !== false) {
            echo "❌ Eroare găsită în output\n";
            // Extract error message
            if (preg_match('/Eroare[^<]*/', $output, $matches)) {
                echo "Error message: " . trim($matches[0]) . "\n";
            }
        }
        if (strpos($output, 'găsite pentru') !== false) {
            echo "✅ Mesaj de rezultate găsit - căutarea a fost executată\n";
        }

        echo "Output preview (first 1000 chars):\n";
        echo substr($output, 0, 1000) . "\n...\n";
    }
}

// Verificăm dacă există notificarea de fallback
if (strpos($output, 'nu este recunoscut de API-ul SOAP pentru ședințe') !== false) {
    echo "ℹ️ Notificare fallback detectată - funcționează corect\n";
}

echo "\nTest completat!\n";
?>
