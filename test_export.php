<?php
/**
 * Test page for session search export functionality
 * This page tests both TXT and Excel export features
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';

// Set content type to HTML with UTF-8 encoding
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Export Ședințe - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Roboto', sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .test-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px 8px 0 0;
        }
        .test-body {
            padding: 1.5rem;
        }
        .test-step {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .test-step-header {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        .test-step-content {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .test-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            text-decoration: none;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: all 0.3s ease;
        }
        .test-link:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
        }
        .test-link.excel {
            background: #28a745;
        }
        .test-link.excel:hover {
            background: #1e7e34;
        }
        .test-link.txt {
            background: #6c757d;
        }
        .test-link.txt:hover {
            background: #545b62;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .info-message {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .warning-message {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
            overflow-x: auto;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        .checklist li:last-child {
            border-bottom: none;
        }
        .checklist li::before {
            content: "☐ ";
            color: #007bff;
            font-weight: bold;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <div class="test-header">
                <h1 class="h3 mb-0">
                    <i class="fas fa-download mr-2"></i>
                    Test Export Funcționalitate Ședințe
                </h1>
                <p class="mb-0 mt-2 opacity-75">
                    Testare completă a funcționalității de export TXT și Excel pentru ședințele de judecată
                </p>
            </div>
            <div class="test-body">
                <div class="info-message">
                    <strong>Scopul testului:</strong><br>
                    Acest test verifică dacă funcționalitatea de export pentru rezultatele căutării ședințelor funcționează corect pentru ambele formate: TXT și Excel.
                </div>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-play-circle mr-2"></i>
                        Pasul 1: Pregătire pentru test
                    </div>
                    <div class="test-step-content">
                        Înainte de a testa exportul, trebuie să ai rezultate de ședințe disponibile.
                        <br><br>
                        <a href="sedinte.php" class="test-link">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            Accesează Căutare Ședințe
                        </a>
                        <br><br>
                        <strong>Instrucțiuni:</strong>
                        <ul>
                            <li>Introdu data de azi: <code><?php echo date('d.m.Y'); ?></code></li>
                            <li>Selectează o instituție sau lasă "Toate instituțiile"</li>
                            <li>Efectuează căutarea</li>
                        </ul>
                    </div>
                </div>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-file-alt mr-2"></i>
                        Pasul 2: Test Export TXT
                    </div>
                    <div class="test-step-content">
                        <strong>Ce să testezi pentru export TXT:</strong>
                        <ul class="checklist">
                            <li>Butonul "Export TXT" apare doar când există rezultate</li>
                            <li>Click pe "Export TXT" descarcă un fișier</li>
                            <li>Numele fișierului: <code>Sedinte_DD-MM-YYYY_YYYY-MM-DD_HH-mm-ss.txt</code></li>
                            <li>Fișierul se deschide corect în editor de text</li>
                            <li>Conținutul include toate informațiile ședințelor</li>
                            <li>Caracterele românești (ă, â, î, ș, ț) sunt afișate corect</li>
                            <li>Formatarea este clară și lizibilă</li>
                        </ul>
                        
                        <div class="warning-message">
                            <strong>Verificare specială:</strong> Deschide fișierul TXT în Notepad++ sau alt editor și verifică că encoding-ul este UTF-8.
                        </div>
                    </div>
                </div>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-file-excel mr-2"></i>
                        Pasul 3: Test Export Excel
                    </div>
                    <div class="test-step-content">
                        <strong>Ce să testezi pentru export Excel:</strong>
                        <ul class="checklist">
                            <li>Butonul "Export Excel" apare doar când există rezultate</li>
                            <li>Click pe "Export Excel" descarcă un fișier .xlsx</li>
                            <li>Numele fișierului: <code>Sedinte_DD-MM-YYYY_YYYY-MM-DD_HH-mm-ss.xlsx</code></li>
                            <li>Fișierul se deschide corect în Excel/LibreOffice</li>
                            <li>Header-ul principal este formatat (albastru, text alb)</li>
                            <li>Coloanele au header-uri clare</li>
                            <li>Datele sunt organizate în coloane separate</li>
                            <li>Caracterele românești sunt afișate corect</li>
                            <li>Lățimea coloanelor este ajustată automat</li>
                        </ul>
                    </div>
                </div>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Pasul 4: Test Scenarii de Eroare
                    </div>
                    <div class="test-step-content">
                        <strong>Testează următoarele scenarii de eroare:</strong>
                        
                        <h6 class="mt-3">4.1. Export fără rezultate</h6>
                        <ul>
                            <li>Efectuează o căutare care nu returnează rezultate</li>
                            <li>Verifică că butoanele de export NU apar</li>
                        </ul>
                        
                        <h6 class="mt-3">4.2. Export cu parametri incorecți</h6>
                        <p>Testează aceste URL-uri direct:</p>
                        <div class="code-block">
                            <a href="sedinte.php?export=txt&session_results=1&dataSedinta=&institutie=" class="test-link txt">
                                Test Export TXT fără parametri
                            </a><br>
                            <a href="sedinte.php?export=xlsx&session_results=1&dataSedinta=invalid&institutie=" class="test-link excel">
                                Test Export Excel cu dată invalidă
                            </a>
                        </div>
                        
                        <h6 class="mt-3">4.3. Export cu date valide</h6>
                        <p>Testează aceste URL-uri cu parametri corecți:</p>
                        <div class="code-block">
                            <a href="sedinte.php?export=txt&session_results=1&dataSedinta=<?php echo date('d.m.Y'); ?>&institutie=" class="test-link txt">
                                Test Export TXT cu data de azi
                            </a><br>
                            <a href="sedinte.php?export=xlsx&session_results=1&dataSedinta=<?php echo date('d.m.Y'); ?>&institutie=" class="test-link excel">
                                Test Export Excel cu data de azi
                            </a>
                        </div>
                    </div>
                </div>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-mobile-alt mr-2"></i>
                        Pasul 5: Test Responsivitate Mobile
                    </div>
                    <div class="test-step-content">
                        <strong>Testează pe dispozitive mobile:</strong>
                        <ul>
                            <li>Deschide Developer Tools (F12)</li>
                            <li>Activează "Device Toolbar" (Ctrl+Shift+M)</li>
                            <li>Selectează un dispozitiv mobil</li>
                            <li>Verifică că butoanele de export sunt vizibile și accesibile</li>
                            <li>Testează descărcarea pe mobile</li>
                        </ul>
                    </div>
                </div>

                <h4 class="mt-4 mb-3">
                    <i class="fas fa-clipboard-check mr-2"></i>
                    Checklist Final de Verificare
                </h4>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-check-double mr-2"></i>
                        Verificări Obligatorii
                    </div>
                    <div class="test-step-content">
                        <ul class="checklist">
                            <li>Export TXT funcționează și produce fișiere corecte</li>
                            <li>Export Excel funcționează și produce fișiere .xlsx valide</li>
                            <li>Butoanele apar doar când există rezultate</li>
                            <li>Numele fișierelor respectă formatul specificat</li>
                            <li>Caracterele românești sunt preservate în ambele formate</li>
                            <li>Gestionarea erorilor funcționează corect</li>
                            <li>Funcționalitatea este responsivă pe mobile</li>
                            <li>Nu apar erori JavaScript în consolă</li>
                            <li>Fișierele se descarcă fără contaminare HTML</li>
                            <li>Conținutul exportat include toate datele ședințelor</li>
                        </ul>
                    </div>
                </div>

                <div class="success-message">
                    <strong>✓ Testare completă!</strong><br>
                    Dacă toate verificările de mai sus sunt îndeplinite, funcționalitatea de export pentru ședințe este operațională și gata pentru utilizare.
                </div>

                <h4 class="mt-4 mb-3">
                    <i class="fas fa-tools mr-2"></i>
                    Informații Tehnice
                </h4>

                <div class="test-step">
                    <div class="test-step-header">
                        <i class="fas fa-info-circle mr-2"></i>
                        Detalii Implementare
                    </div>
                    <div class="test-step-content">
                        <strong>Funcții de export implementate:</strong>
                        <ul>
                            <li><code>handleSessionTxtExportOnly()</code> - Export TXT cu UTF-8 și formatare avansată</li>
                            <li><code>handleSessionExcelExportOnly()</code> - Export Excel cu PhpSpreadsheet</li>
                            <li><code>generateSessionTxtContent()</code> - Generare conținut TXT formatat</li>
                            <li><code>generateSessionExcelFile()</code> - Generare fișier Excel cu stilizare</li>
                        </ul>
                        
                        <strong>Caracteristici tehnice:</strong>
                        <ul>
                            <li>Suport complet UTF-8 pentru caractere românești</li>
                            <li>Gestionare erorilor cu pagini HTML informative</li>
                            <li>Export curat fără contaminare HTML</li>
                            <li>Formatare profesională pentru ambele tipuri de fișiere</li>
                            <li>Validare parametri și gestionare cazuri edge</li>
                        </ul>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-4">
                        <a href="sedinte.php" class="test-link" style="width: 100%; text-align: center;">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            Căutare Ședințe
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="test_sessions.php" class="test-link" style="width: 100%; text-align: center;">
                            <i class="fas fa-vial mr-2"></i>
                            Test SOAP API
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="test_navigation.php" class="test-link" style="width: 100%; text-align: center;">
                            <i class="fas fa-route mr-2"></i>
                            Test Navigare
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
