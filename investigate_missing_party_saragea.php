<?php
/**
 * Comprehensive Investigation: Missing Party "Saragea Tudorita" in Case 130/98/2022
 * This script performs deep analysis to identify why specific parties are missing
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

// Target case and missing party
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';
$missingParty = 'Saragea Tudorita';

echo "<!DOCTYPE html>";
echo "<html><head><title>Missing Party Investigation</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    .highlight { background: yellow; font-weight: bold; }
    .code { background: #f8f9fa; padding: 10px; font-family: monospace; white-space: pre-wrap; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style></head><body>";

echo "<h1>🔍 Missing Party Investigation</h1>";
echo "<p><strong>Case:</strong> {$numarDosar} from {$institutie}</p>";
echo "<p><strong>Missing Party:</strong> <span class='highlight'>{$missingParty}</span></p>";
echo "<hr>";

try {
    $dosarService = new \App\Services\DosarService();
    
    echo "<div class='section'>";
    echo "<h2>📡 Step 1: Raw SOAP API Analysis</h2>";
    
    // Get raw SOAP response using reflection
    $reflection = new ReflectionClass($dosarService);
    $method = $reflection->getMethod('executeSoapCallWithRetry');
    $method->setAccessible(true);
    
    $searchParams = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $rawResponse = $method->invoke($dosarService, 'CautareDosare2', $searchParams, "Missing party investigation");
    
    if (!$rawResponse || !isset($rawResponse->CautareDosare2Result)) {
        echo "<p class='error'>❌ No SOAP response received</p>";
        exit;
    }
    
    $dosare = [];
    if (isset($rawResponse->CautareDosare2Result->Dosar)) {
        $rawDosare = $rawResponse->CautareDosare2Result->Dosar;
        if (!is_array($rawDosare)) {
            $rawDosare = [$rawDosare];
        }
        $dosare = $rawDosare;
    }
    
    $targetDosar = null;
    foreach ($dosare as $dosar) {
        if ($dosar->numar === $numarDosar) {
            $targetDosar = $dosar;
            break;
        }
    }
    
    if (!$targetDosar) {
        echo "<p class='error'>❌ Target case not found in SOAP response</p>";
        exit;
    }
    
    echo "<p class='success'>✅ Target case found in SOAP response</p>";
    
    // Analyze raw SOAP parties
    $rawSoapParties = [];
    if (isset($targetDosar->parti) && isset($targetDosar->parti->DosarParte)) {
        $parti = $targetDosar->parti->DosarParte;
        if (!is_array($parti)) {
            $parti = [$parti];
        }
        $rawSoapParties = $parti;
    }
    
    echo "<p><strong>Raw SOAP Parties Count:</strong> " . count($rawSoapParties) . "</p>";
    
    // Search for missing party in raw SOAP data
    $foundInSoap = false;
    $soapMatches = [];
    foreach ($rawSoapParties as $index => $party) {
        $partyName = $party->nume ?? '';
        if (stripos($partyName, $missingParty) !== false || 
            stripos($missingParty, $partyName) !== false ||
            similar_text(strtolower($partyName), strtolower($missingParty)) > 10) {
            $foundInSoap = true;
            $soapMatches[] = [
                'index' => $index + 1,
                'name' => $partyName,
                'quality' => $party->calitateParte ?? '',
                'similarity' => similar_text(strtolower($partyName), strtolower($missingParty))
            ];
        }
    }
    
    if ($foundInSoap) {
        echo "<p class='success'>✅ Found potential matches in raw SOAP data:</p>";
        echo "<table>";
        echo "<tr><th>Index</th><th>Name</th><th>Quality</th><th>Similarity</th></tr>";
        foreach ($soapMatches as $match) {
            echo "<tr>";
            echo "<td>{$match['index']}</td>";
            echo "<td class='highlight'>" . htmlspecialchars($match['name']) . "</td>";
            echo "<td>" . htmlspecialchars($match['quality']) . "</td>";
            echo "<td>{$match['similarity']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ Missing party NOT found in raw SOAP data</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>📄 Step 2: Decision Text Analysis</h2>";
    
    // Analyze decision text content
    $allTextSources = [];
    if (isset($targetDosar->sedinte) && isset($targetDosar->sedinte->DosarSedinta)) {
        $sedinte = $targetDosar->sedinte->DosarSedinta;
        if (!is_array($sedinte)) {
            $sedinte = [$sedinte];
        }
        
        foreach ($sedinte as $sedinta) {
            if (isset($sedinta->solutie) && !empty($sedinta->solutie)) {
                $allTextSources[] = ['type' => 'solutie', 'content' => $sedinta->solutie];
            }
            if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                $allTextSources[] = ['type' => 'solutieSumar', 'content' => $sedinta->solutieSumar];
            }
        }
    }
    
    echo "<p><strong>Text Sources Found:</strong> " . count($allTextSources) . "</p>";
    
    $foundInText = false;
    $textMatches = [];
    
    foreach ($allTextSources as $sourceIndex => $source) {
        $content = $source['content'];
        $type = $source['type'];
        
        // Search for the missing party in text
        if (stripos($content, $missingParty) !== false) {
            $foundInText = true;
            
            // Find the context around the match
            $position = stripos($content, $missingParty);
            $contextStart = max(0, $position - 100);
            $contextEnd = min(strlen($content), $position + strlen($missingParty) + 100);
            $context = substr($content, $contextStart, $contextEnd - $contextStart);
            
            $textMatches[] = [
                'source' => $type,
                'position' => $position,
                'context' => $context
            ];
        }
        
        // Also search for partial matches
        $words = explode(' ', $missingParty);
        foreach ($words as $word) {
            if (strlen($word) > 3 && stripos($content, $word) !== false) {
                $position = stripos($content, $word);
                $contextStart = max(0, $position - 50);
                $contextEnd = min(strlen($content), $position + strlen($word) + 50);
                $context = substr($content, $contextStart, $contextEnd - $contextStart);
                
                $textMatches[] = [
                    'source' => $type . ' (partial: ' . $word . ')',
                    'position' => $position,
                    'context' => $context
                ];
            }
        }
    }
    
    if ($foundInText) {
        echo "<p class='success'>✅ Found matches in decision text:</p>";
        foreach ($textMatches as $match) {
            echo "<div class='code'>";
            echo "<strong>Source:</strong> {$match['source']}<br>";
            echo "<strong>Context:</strong> " . htmlspecialchars($match['context']) . "<br>";
            echo "</div>";
        }
    } else {
        echo "<p class='warning'>⚠️ Missing party NOT found in decision text</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🔄 Step 3: Enhanced Extraction Process Analysis</h2>";
    
    // Get the processed case data
    $processedDosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if (!$processedDosar) {
        echo "<p class='error'>❌ Failed to get processed case data</p>";
        exit;
    }
    
    $processedParties = $processedDosar->parti ?? [];
    echo "<p><strong>Processed Parties Count:</strong> " . count($processedParties) . "</p>";
    
    // Search for missing party in processed data
    $foundInProcessed = false;
    $processedMatches = [];
    
    foreach ($processedParties as $index => $party) {
        $partyName = $party->nume ?? '';
        if (stripos($partyName, $missingParty) !== false || 
            stripos($missingParty, $partyName) !== false ||
            similar_text(strtolower($partyName), strtolower($missingParty)) > 10) {
            $foundInProcessed = true;
            $processedMatches[] = [
                'index' => $index + 1,
                'name' => $partyName,
                'quality' => $party->calitate ?? '',
                'source' => $party->source ?? '',
                'similarity' => similar_text(strtolower($partyName), strtolower($missingParty))
            ];
        }
    }
    
    if ($foundInProcessed) {
        echo "<p class='success'>✅ Found matches in processed data:</p>";
        echo "<table>";
        echo "<tr><th>Index</th><th>Name</th><th>Quality</th><th>Source</th><th>Similarity</th></tr>";
        foreach ($processedMatches as $match) {
            echo "<tr>";
            echo "<td>{$match['index']}</td>";
            echo "<td class='highlight'>" . htmlspecialchars($match['name']) . "</td>";
            echo "<td>" . htmlspecialchars($match['quality']) . "</td>";
            echo "<td>" . htmlspecialchars($match['source']) . "</td>";
            echo "<td>{$match['similarity']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ Missing party NOT found in processed data</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2 class='error'>❌ Error Occurred</h2>";
    echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p class='info'>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Investigation completed at " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
