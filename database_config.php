<?php
/**
 * Database Configuration for Portal Judiciar România
 * 
 * This file contains the database connection settings.
 * Copy this file and customize the values for your environment.
 */

// Database connection settings for WAMP64/XAMPP
if (!defined('DB_HOST')) {
    define('DB_HOST', 'localhost');
}

if (!defined('DB_NAME')) {
    define('DB_NAME', 'portal_judiciar');
}

if (!defined('DB_USER')) {
    define('DB_USER', 'root'); // Default WAMP user
}

if (!defined('DB_PASS')) {
    define('DB_PASS', ''); // Default WAMP password (empty)
}

if (!defined('DB_CHARSET')) {
    define('DB_CHARSET', 'utf8mb4');
}

// Test database connection
function testDatabaseConnection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        echo "✅ Database connection successful!\n";
        echo "   Host: " . DB_HOST . "\n";
        echo "   Database: " . DB_NAME . "\n";
        echo "   User: " . DB_USER . "\n";
        echo "   Charset: " . DB_CHARSET . "\n";
        
        return true;
    } catch (PDOException $e) {
        echo "❌ Database connection failed!\n";
        echo "   Error: " . $e->getMessage() . "\n";
        echo "\n";
        echo "🔧 Troubleshooting:\n";
        echo "   1. Make sure MySQL/MariaDB is running in WAMP\n";
        echo "   2. Check if database '" . DB_NAME . "' exists\n";
        echo "   3. Verify username and password\n";
        echo "   4. Create database if it doesn't exist:\n";
        echo "      CREATE DATABASE " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\n";
        
        return false;
    }
}

// If this file is run directly, test the connection
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    echo "🗄️ TESTING DATABASE CONNECTION\n";
    echo "==============================\n\n";
    testDatabaseConnection();
}
?>
