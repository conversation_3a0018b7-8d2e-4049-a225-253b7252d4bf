/**
 * Stiluri specifice pentru butoane
 * Acest fișier conține stiluri care suprascriu orice stil conflictual
 */

/* Stiluri de bază pentru butoane */
.btn {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 600 !important;
    text-align: center !important;
    white-space: nowrap !important;
    vertical-align: middle !important;
    user-select: none !important;
    border: 1px solid transparent !important;
    padding: 0.75rem 1.5rem !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
    border-radius: 0.25rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    min-height: 44px !important;
    text-decoration: none !important;
    box-sizing: border-box !important;
    position: relative !important;
    overflow: hidden !important;
    z-index: 1 !important;
    margin: 0 !important;
}

/* Stiluri pentru butonul primary */
.btn-primary {
    color: #fff !important;
    background-color: #3498db !important;
    border-color: #3498db !important;
    box-shadow: 0 4px 6px rgba(52, 152, 219, 0.2) !important;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
    color: #fff !important;
    background-color: #2c3e50 !important;
    border-color: #2c3e50 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 8px rgba(44, 62, 80, 0.2) !important;
}

/* Stiluri pentru butonul secondary */
.btn-secondary {
    color: #fff !important;
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    box-shadow: 0 4px 6px rgba(108, 117, 125, 0.2) !important;
}

.btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active {
    color: #fff !important;
    background-color: #5a6268 !important;
    border-color: #545b62 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 8px rgba(90, 98, 104, 0.2) !important;
}

/* Stiluri pentru butonul de căutare */
.btn-search {
    color: #fff !important;
    background-color: #3498db !important;
    border-color: #3498db !important;
    padding: 0.75rem 2rem !important;
    font-weight: 600 !important;
    box-shadow: 0 4px 6px rgba(52, 152, 219, 0.2) !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    min-width: 180px !important;
}

.btn-search:hover {
    color: #fff !important;
    background-color: #2c3e50 !important;
    border-color: #2c3e50 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 8px rgba(44, 62, 80, 0.2) !important;
}

.btn-search:focus {
    color: #fff !important;
    background-color: #2980b9 !important;
    border-color: #2980b9 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.5) !important;
    outline: none !important;
}

.btn-search:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(44, 62, 80, 0.2) !important;
}

/* Efect ripple pentru butonul de căutare */
.btn-search::after {
    content: "" !important;
    display: block !important;
    position: absolute !important;
    width: 100% !important;
    height: 100% !important;
    top: 0 !important;
    left: 0 !important;
    pointer-events: none !important;
    background-image: radial-gradient(circle, #fff 10%, transparent 10.01%) !important;
    background-repeat: no-repeat !important;
    background-position: 50% !important;
    transform: scale(10, 10) !important;
    opacity: 0 !important;
    transition: transform 0.5s, opacity 1s !important;
}

.btn-search:active::after {
    transform: scale(0, 0) !important;
    opacity: 0.3 !important;
    transition: 0s !important;
}

/* Stiluri pentru butonul în stare de încărcare */
.btn-loading {
    pointer-events: none !important;
    opacity: 0.85 !important;
    background-color: #3498db !important;
    border-color: #3498db !important;
    color: #fff !important;
    box-shadow: none !important;
    transform: none !important;
    position: relative !important;
    transition: all 0.3s ease !important;
}

.btn-loading::before {
    content: "" !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.1) !important;
    z-index: 0 !important;
}

.btn-loading .fa-spinner {
    animation: spin 1s linear infinite !important;
    position: relative !important;
    z-index: 1 !important;
}

.btn-loading span {
    position: relative !important;
    z-index: 1 !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Stiluri pentru butonul de scroll sus */
.scroll-top-btn {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    width: 44px !important;
    height: 44px !important;
    border-radius: 50% !important;
    background-color: #2c3e50 !important;
    color: white !important;
    border: none !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    z-index: 1000 !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
}

.scroll-top-btn.show {
    opacity: 1 !important;
    visibility: visible !important;
}

.scroll-top-btn:hover, .scroll-top-btn:focus, .scroll-top-btn:active {
    background-color: #3498db !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Stiluri pentru link-uri care arată ca butoane */
a.btn {
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
}

/* Stiluri pentru container-ul de butoane */
.button-container {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
    justify-content: center !important;
    margin-top: 1.5rem !important;
}

/* Media queries pentru responsive design */
@media (max-width: 767.98px) {
    .button-container {
        flex-direction: column !important;
        align-items: center !important;
        width: 100% !important;
    }

    .btn {
        width: 100% !important;
        margin-bottom: 0.75rem !important;
        min-height: 48px !important; /* Mărim înălțimea pentru touch pe mobile */
        font-size: 1.05rem !important; /* Mărim puțin fontul pentru lizibilitate */
    }

    .btn-search {
        margin-bottom: 0.75rem !important;
        padding: 0.85rem 1rem !important; /* Padding mai mare pe mobile */
        min-width: 100% !important; /* Asigurăm lățimea completă */
        font-size: 1.1rem !important; /* Font mai mare pentru lizibilitate */
        letter-spacing: 0.5px !important; /* Spațiere între litere pentru lizibilitate */
        font-weight: 600 !important; /* Font mai îngroșat pentru vizibilitate */
        box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3) !important; /* Umbră mai pronunțată */
    }

    /* Îmbunătățim stilul pentru butonul de încărcare pe mobile */
    .btn-loading {
        min-height: 48px !important;
    }
}

/* Optimizări pentru ecrane foarte mici */
@media (max-width: 575.98px) {
    .button-container {
        padding: 0 0.5rem !important;
    }

    .btn-search, .btn {
        border-radius: 6px !important; /* Colțuri mai rotunjite pe mobile */
    }
}

/* Stiluri pentru butonul de filtre avansate */
#advancedFiltersToggle {
    display: flex !important;
    align-items: center !important;
    color: #3498db !important;
    text-decoration: none !important;
    padding: 0.5rem 0 !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

#advancedFiltersToggle:hover, #advancedFiltersToggle:focus, #advancedFiltersToggle:active {
    color: #2c3e50 !important;
    text-decoration: none !important;
}

#advancedFiltersToggle i {
    transition: transform 0.3s ease !important;
}
