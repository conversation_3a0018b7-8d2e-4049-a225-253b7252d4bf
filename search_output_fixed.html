<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Portal Judiciar România - Căutare Dosare Instanțe</title>
<meta name="description" content="Căutați rapid dosare judecătorești din România! Portal oficial pentru verificarea dosarelor civile și penale din toate instanțele. Acces gratuit și instant.">
<meta name="keywords" content="portal judiciar românia, căutare dosare instanțe, verificare dosare tribunal, dosare civile penale, tribunal, judecătorie">
<meta name="robots" content="index, follow">
<meta name="author" content="Portal Judiciar România">
<meta name="language" content="ro">
<link rel="canonical" href="http://localhost/just/">
<meta property="og:title" content="Portal Judiciar România - Căutare Dosare Instanțe">
<meta property="og:description" content="Căutați rapid dosare judecătorești din România! Portal oficial pentru verificarea dosarelor civile și penale din toate instanțele. Acces gratuit și instant.">
<meta property="og:type" content="website">
<meta property="og:url" content="http://localhost/just/">
<meta property="og:site_name" content="Portal Judiciar România">
<meta property="og:locale" content="ro_RO">
<meta property="og:image" content="http://localhost/just/images/logo.jpg">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:image:alt" content="Portal Judiciar România - Logo">
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="Portal Judiciar România - Căutare Dosare Instanțe">
<meta name="twitter:description" content="Căutați rapid dosare judecătorești din România! Portal oficial pentru verificarea dosarelor civile și penale din toate instanțele. Acces gratuit și instant.">
<meta name="twitter:image" content="http://localhost/just/images/logo.jpg">
<meta name="twitter:image:alt" content="Portal Judiciar România - Logo">
<meta name="geo.region" content="RO">
<meta name="geo.country" content="Romania">
<script type="application/ld+json">
{"@context":"https://schema.org","@type":"GovernmentOrganization","name":"Portal Judiciar România","alternateName":"DosareJust.ro","description":"Portal oficial pentru căutarea dosarelor judecătorești din România","url":"http://localhost/just/","logo":"http://localhost/just/images/logo.jpg","contactPoint":{"@type":"ContactPoint","contactType":"customer service","availableLanguage":"Romanian","url":"http://localhost/just/contact.php"},"areaServed":{"@type":"Country","name":"Romania"},"serviceType":"Servicii judiciare online","governmentType":"Portal informativ"}
</script>
<script type="application/ld+json">
{"@context":"https://schema.org","@type":"WebSite","name":"Portal Judiciar România","alternateName":"DosareJust.ro","url":"http://localhost/just/","description":"Portal pentru căutarea dosarelor și ședințelor judecătorești din România","inLanguage":"ro","potentialAction":[{"@type":"SearchAction","target":{"@type":"EntryPoint","urlTemplate":"http://localhost/just/search.php?search_term={search_term_string}"},"query-input":"required name=search_term_string"}],"publisher":{"@type":"Organization","name":"Portal Judiciar România"}}
</script>
<script type="application/ld+json">
{"@context":"https://schema.org","@type":"GovernmentService","name":"Căutare Dosare Judecătorești","description":"Serviciu online pentru căutarea și verificarea dosarelor judecătorești din România","provider":{"@type":"GovernmentOrganization","name":"Portal Judiciar România"},"areaServed":{"@type":"Country","name":"Romania"},"availableChannel":{"@type":"ServiceChannel","serviceUrl":"http://localhost/just/","serviceName":"Portal Web","availableLanguage":"Romanian"},"serviceType":"Informații judiciare","audience":{"@type":"Audience","audienceType":"Cetățeni, avocați, instituții"}}
</script>

<style>
.breadcrumb-nav {
    margin-bottom: 1.5rem;
    padding: 0;
}

.breadcrumb {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0;
    font-size: 0.875rem;
}

.breadcrumb-item {
    color: #6c757d;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
    font-weight: bold;
    padding: 0 0.5rem;
}

.breadcrumb-item a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumb-item a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #2c3e50;
    font-weight: 500;
}

/* Responsive design */
@media (max-width: 767.98px) {
    .breadcrumb {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        padding: 0 0.25rem;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .breadcrumb {
        border: 2px solid #000;
        background-color: #fff;
    }
    
    .breadcrumb-item a {
        color: #000;
        font-weight: bold;
    }
    
    .breadcrumb-item.active {
        color: #000;
        font-weight: bold;
    }
}

/* Print styles */
@media print {
    .breadcrumb-nav {
        display: none;
    }
}
</style>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS (Minified for Performance) -->
    <link href="assets/css/style.min.css?v=1.0" rel="stylesheet">
    <link href="assets/css/responsive.min.css?v=1.0" rel="stylesheet">

    <style>
        /* Bulk Search Specific Styles - Matching detalii_dosar.php design */
        :root {
            --primary-blue: #007bff;
            --secondary-blue: #2c3e50;
            --light-bg: #f8f9fa;
            --border-color: #dee2e6;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
        }

        /* Card styling matching detalii_dosar.php */
        .streamlined-card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
            margin-bottom: 2rem;
            overflow: hidden;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .streamlined-card .card-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            border-bottom: none;
            padding: 1.25rem 1.5rem;
        }

        .streamlined-card .card-body {
            padding: 2rem;
        }

        /* Compact search form styling */
        .streamlined-card.compact-form .card-body {
            padding: 1.25rem;
        }

        .compact-form .form-label {
            margin-bottom: 0.375rem;
            font-size: 0.95rem;
        }

        .compact-form .form-control,
        .compact-form .form-select {
            margin-bottom: 0.75rem;
        }

        .compact-form .term-counter {
            margin-top: 0.5rem;
            margin-bottom: 0;
            font-size: 0.875rem;
        }

        .compact-form .row {
            margin-bottom: 0;
        }

        .compact-form .col-md-4 .mt-3 {
            margin-top: 0.75rem !important;
        }

        /* Additional compact form optimizations */
        .compact-form textarea.form-control {
            min-height: 120px;
            resize: vertical;
        }

        .compact-form .btn {
            padding: 0.5rem 1rem;
            font-size: 0.95rem;
        }

        /* Optimize card header for compact form */
        .streamlined-card.compact-form .card-header {
            padding: 1rem 1.25rem;
        }

        .compact-form .card-header h1 {
            font-size: 1.15rem;
        }

        /* Form styling */
        .form-label {
            font-weight: 600;
            color: var(--secondary-blue);
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 6px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* Advanced filters styling */
        .advanced-filters-section {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.02) 0%, rgba(44, 62, 80, 0.01) 100%);
            border: 1px solid rgba(0, 123, 255, 0.1);
            border-radius: 8px;
            padding: 1.25rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .advanced-filters-section:hover {
            border-color: rgba(0, 123, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
        }

        .advanced-filters-section h6 {
            color: var(--secondary-blue);
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }

        .advanced-filters-section h6 i {
            color: var(--primary-blue);
            margin-right: 0.5rem;
        }

        .advanced-filters-section .form-select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }

        .advanced-filters-section .form-select:focus {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23007bff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        }

        .advanced-filters-section .form-text {
            font-size: 0.875rem;
            color: var(--secondary-blue);
            margin-top: 0.25rem;
        }

        .advanced-filters-section .form-text i {
            color: var(--primary-blue);
            margin-right: 0.25rem;
        }

        /* Date input styling */
        .date-input {
            position: relative;
        }

        .date-input:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .date-input::placeholder {
            color: #6c757d;
            font-style: italic;
        }

        /* Date validation styling */
        .date-input.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .date-input.is-valid {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        /* Case category dropdown styling */
        #categorieCaz {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }

        #categorieCaz:focus {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23007bff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        }

        /* Enhanced Searchable Dropdown Styles */
        .searchable-dropdown {
            position: relative;
        }

        .searchable-dropdown .dropdown-search-input {
            width: 100%;
            padding: 0.375rem 2.5rem 0.375rem 0.75rem;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            background-color: #fff;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.75rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            cursor: pointer;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .searchable-dropdown .dropdown-search-input:focus {
            border-color: #007bff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23007bff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        }

        .searchable-dropdown .dropdown-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 1050;
            max-height: 300px;
            overflow-y: auto;
            background-color: #fff;
            border: 1px solid #ced4da;
            border-top: none;
            border-radius: 0 0 0.375rem 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            display: none;
        }

        /* Specific height adjustment for Case Category dropdown to show more options */
        #categorieCazOptions {
            max-height: 320px !important; /* Increased height to show 6-8 options */
        }

        .searchable-dropdown .dropdown-options.show {
            display: block;
        }

        .searchable-dropdown .dropdown-option {
            padding: 0.5rem 0.75rem;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
            transition: background-color 0.15s ease-in-out;
        }

        .searchable-dropdown .dropdown-option:hover,
        .searchable-dropdown .dropdown-option.highlighted {
            background-color: #007bff;
            color: #fff;
        }

        .searchable-dropdown .dropdown-option:last-child {
            border-bottom: none;
        }

        .searchable-dropdown .dropdown-option.no-results {
            color: #6c757d;
            font-style: italic;
            cursor: default;
        }

        .searchable-dropdown .dropdown-option.no-results:hover {
            background-color: transparent;
            color: #6c757d;
        }

        /* Romanian diacritics highlighting */
        .searchable-dropdown .dropdown-option .highlight {
            background-color: rgba(255, 255, 255, 0.3);
            font-weight: bold;
        }

        /* Mobile responsiveness for searchable dropdowns */
        @media (max-width: 767px) {
            .searchable-dropdown .dropdown-options {
                max-height: 200px;
                font-size: 0.9rem;
                z-index: 1060; /* Higher z-index for mobile */
            }

            /* Specific mobile height for Case Category dropdown */
            #categorieCazOptions {
                max-height: 280px !important; /* Increased mobile height for better visibility */
            }

            .searchable-dropdown .dropdown-option {
                padding: 0.75rem;
                min-height: 44px;
                display: flex;
                align-items: center;
            }

            .searchable-dropdown .dropdown-search-input {
                font-size: 1rem; /* Prevent zoom on iOS */
            }
        }

        /* Ensure dropdowns appear above other elements */
        .searchable-dropdown .dropdown-options {
            z-index: 1050;
        }

        /* Fix for Bootstrap modal z-index conflicts */
        .modal-open .searchable-dropdown .dropdown-options {
            z-index: 1070;
        }

        /* Footer and Page Layout Styles */
        html, body {
            height: 100%;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .content-wrapper {
            flex: 1 0 auto;
        }

        .modern-footer {
            flex-shrink: 0;
            margin-top: auto;
            background-color: #f8f9fa;
            border-top: 2px solid #007bff;
            padding: 2rem 0 1rem;
            color: #6c757d;
        }

        .modern-footer .footer-links {
            display: flex;
            gap: 1.5rem;
            justify-content: flex-end;
        }

        .modern-footer .footer-link {
            color: #6c757d;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .modern-footer .footer-link:hover {
            color: #007bff;
            text-decoration: none;
        }

        /* Back to Top Button */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 50px;
            height: 50px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
        }

        .back-to-top:focus {
            outline: 2px solid #007bff;
            outline-offset: 2px;
        }

        @media (max-width: 767.98px) {
            .modern-footer .footer-links {
                flex-direction: column;
                gap: 0.75rem;
                text-align: center;
                justify-content: center;
            }

            .modern-footer .col-md-6 {
                text-align: center !important;
            }

            .back-to-top {
                bottom: 1rem;
                right: 1rem;
                width: 45px;
                height: 45px;
            }
        }

        /* Advanced Filters Toggle Styling */
        #advancedFiltersToggle {
            text-decoration: none;
            padding: 0.75rem 1rem;
            border: 1px solid var(--primary-blue);
            border-radius: 6px;
            background-color: rgba(0, 123, 255, 0.05);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        #advancedFiltersToggle:hover {
            background-color: rgba(0, 123, 255, 0.1);
            text-decoration: none;
            color: var(--primary-blue) !important;
            transform: translateY(-1px);
        }

        #advancedFiltersToggle i.fa-chevron-down {
            transition: transform 0.3s ease;
        }

        #advancedFiltersToggle.expanded i.fa-chevron-down {
            transform: rotate(180deg);
        }

        /* Advanced Filters Collapsible Section */
        #advancedFilters {
            overflow: hidden;
            transition: all 0.3s ease;
            max-height: 0;
            opacity: 0;
            padding: 0;
            margin-top: 0;
            border: none;
            background-color: transparent;
        }

        #advancedFilters.show {
            max-height: 1000px;
            opacity: 1;
            padding: 1.25rem;
            margin-top: 1.25rem;
            border: 1px solid var(--gray-200, #e9ecef);
            border-radius: 8px;
            background-color: rgba(0, 123, 255, 0.02);
        }

        /* Mobile Layout Fix for Search Button Position */
        @media (max-width: 767px) {
            /* Hide the desktop search button container on mobile */
            .card .card-body .mt-auto {
                display: none !important;
            }

            /* Create mobile search button container - always visible on mobile */
            .mobile-search-container {
                display: block !important;
                margin-top: 1rem;
                padding: 1rem;
                background-color: rgba(0, 123, 255, 0.02);
                border: 1px solid var(--gray-200, #e9ecef);
                border-radius: 8px;
            }
        }

        /* Hide mobile search container on desktop */
        @media (min-width: 768px) {
            .mobile-search-container {
                display: none !important;
            }
        }

        /* ===== EXPORT BUTTONS RESPONSIVE VISIBILITY ===== */

        /* Desktop: Show desktop export buttons, hide mobile export buttons */
        @media (min-width: 768px) {
            .export-buttons-desktop {
                display: block !important;
            }

            .export-buttons-mobile {
                display: none !important;
            }
        }

        /* Mobile: Hide desktop export buttons, show mobile export buttons */
        @media (max-width: 767px) {
            .export-buttons-desktop {
                display: none !important;
            }

            .export-buttons-mobile {
                display: block !important;
            }
        }

            /* Mobile search button styling */
            .mobile-search-container .btn {
                width: 100%;
                min-height: 44px;
                font-size: 1rem;
                font-weight: 600;
                padding: 0.75rem 1rem;
                background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
                border: none;
                border-radius: 6px;
                transition: all 0.3s ease;
            }

            .mobile-search-container .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
            }

            /* Ensure export buttons also move to mobile container */
            .mobile-search-container .export-buttons-inline {
                margin-top: 1rem;
            }

            .mobile-search-container .export-buttons-inline .btn {
                min-height: 44px;
                font-size: 0.9rem;
                margin-bottom: 0.5rem;
            }
        }

        /* Textarea specific styling */
        #bulkSearchTerms {
            min-height: 150px;
            resize: vertical;
            font-family: 'Courier New', monospace;
            line-height: 1.5;
        }

        /* Counter styling */
        .term-counter {
            font-size: 0.875rem;
            color: var(--secondary-blue);
            margin-top: 0.5rem;
        }

        .term-info-line {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 0.25rem;
        }

        /* Inline total results styling */
        .total-results-inline {
            font-size: 0.875rem;
            color: var(--primary-blue) !important;
            font-weight: 600 !important;
            margin-left: 0.5rem;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            background-color: rgba(0, 123, 255, 0.1);
            border: 1px solid rgba(0, 123, 255, 0.2);
            transition: all 0.3s ease;
        }

        .total-results-inline:hover {
            background-color: rgba(0, 123, 255, 0.15);
            border-color: rgba(0, 123, 255, 0.3);
        }

        /* Inline exact match filter styling - Streamlined without background */
        .exact-match-filter-inline {
            margin-top: 0.5rem;
        }

        .exact-match-filter-inline .form-check {
            margin-bottom: 0;
        }

        .exact-match-filter-inline .form-check-input {
            border-color: var(--primary-blue);
            margin-top: 0.125rem;
        }

        .exact-match-filter-inline .form-check-input:checked {
            background-color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .exact-match-filter-inline .form-check-input:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .exact-match-filter-inline .form-check-label {
            font-size: 0.875rem;
            color: var(--secondary-blue);
            font-weight: 500;
            cursor: pointer;
        }

        .exact-match-filter-inline .badge {
            font-size: 0.75rem;
        }

        /* Button styling */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            border: none;
            padding: 0.75rem 2rem;
            font-weight: 600;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
        }

        /* Sortable Table Headers */
        .sortable-header {
            cursor: pointer;
            user-select: none;
            position: relative;
            padding-right: 1.5rem !important;
            transition: all 0.2s ease;
        }

        .sortable-header:hover {
            background-color: rgba(0, 123, 255, 0.1);
            color: var(--primary-blue);
        }

        .sort-indicator {
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.75rem;
            opacity: 0.6;
            transition: all 0.2s ease;
        }

        .sortable-header:hover .sort-indicator {
            opacity: 1;
        }

        .sortable-header.sort-asc .sort-indicator {
            opacity: 1;
            color: var(--primary-blue);
        }

        .sortable-header.sort-desc .sort-indicator {
            opacity: 1;
            color: var(--primary-blue);
        }

        .sort-indicator.sort-asc::before {
            content: '\f0de'; /* fa-sort-up */
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .sort-indicator.sort-desc::before {
            content: '\f0dd'; /* fa-sort-down */
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        .sort-indicator.sort-none::before {
            content: '\f0dc'; /* fa-sort */
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
        }

        /* Mobile sortable headers */
        @media (max-width: 767px) {
            .sortable-header {
                padding-right: 1.2rem !important;
            }

            .sort-indicator {
                right: 0.3rem;
                font-size: 0.65rem;
            }
        }



        /* Results section styling */
        .results-section {
            margin-top: 2rem;
        }

        .term-results {
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }

        .term-header {
            background: linear-gradient(135deg, var(--light-bg) 0%, #e9ecef 100%);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .term-header:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        .term-content {
            padding: 1.5rem;
            background: white;
        }

        /* Expanded Layout Table - Original Font Sizes with Enhanced Dimensions */
        .table {
            margin-bottom: 0;
            font-size: 0.8rem; /* Reverted to original */
            table-layout: fixed;
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background-color: var(--light-bg);
            border-color: var(--border-color);
            color: var(--secondary-blue);
            font-weight: 600;
            padding: 0.5rem 0.3rem; /* Reverted to original */
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            line-height: 1.2;
            font-size: 0.75rem; /* Reverted to original */
        }

        .table td {
            padding: 0.5rem 0.3rem; /* Reverted to original */
            vertical-align: top;
            border-color: var(--border-color);
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            line-height: 1.3;
            font-size: 0.8rem; /* Reverted to original */
        }

        /* Expanded page layout for maximum content display */
        .container-fluid {
            max-width: none;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }

        /* Enhanced table container - expanded dimensions */
        .table-responsive {
            overflow-x: visible;
            -webkit-overflow-scrolling: touch;
            margin: 0 -0.5rem; /* Extend table beyond container padding */
        }

        /* Responsive container adjustments */
        @media (min-width: 1200px) {
            .container-fluid {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .table-responsive {
                margin: 0 -1rem;
            }
        }

        @media (min-width: 1400px) {
            .container-fluid {
                padding-left: 1.5rem;
                padding-right: 1.5rem;
            }

            .table-responsive {
                margin: 0 -1.5rem;
            }
        }

        /* Ultra-Optimized Column Widths - Enhanced Case Number & Compact Date Display */
        .table th:nth-child(1), .table td:nth-child(1) { /* Număr Dosar - EXPANDED */
            width: 13%;
        }

        .table th:nth-child(2), .table td:nth-child(2) { /* Instanță */
            width: 8%;
        }

        .table th:nth-child(3), .table td:nth-child(3) { /* Obiect - Maximum Priority */
            width: 24%;
        }

        .table th:nth-child(4), .table td:nth-child(4) { /* Stadiu Procesual */
            width: 8%;
        }

        .table th:nth-child(5), .table td:nth-child(5) { /* Data - COMPACT */
            width: 7%;
        }

        .table th:nth-child(6), .table td:nth-child(6) { /* Ultima Modificare - COMPACT */
            width: 7%;
        }

        .table th:nth-child(7), .table td:nth-child(7) { /* Categorie caz */
            width: 7%;
        }

        .table th:nth-child(8), .table td:nth-child(8) { /* Nume Parte - High Priority */
            width: 15%;
        }

        .table th:nth-child(9), .table td:nth-child(9) { /* Calitate */
            width: 7%;
        }

        .table th:nth-child(10), .table td:nth-child(10) { /* Acțiuni */
            width: 9%;
        }

        /* Responsive styling for inline total results and filter */
        @media (max-width: 992px) {
            .total-results-inline {
                font-size: 0.8rem;
                padding: 0.15rem 0.4rem;
                margin-left: 0.4rem;
            }

            .exact-match-filter-inline {
                margin-top: 0.4rem;
            }

            .exact-match-filter-inline .form-check-label {
                font-size: 0.8rem;
            }
        }

        @media (max-width: 768px) {
            .total-results-inline {
                font-size: 0.75rem;
                padding: 0.1rem 0.3rem;
                margin-left: 0.3rem;
                display: block;
                margin-top: 0.3rem;
                margin-left: 0;
                width: fit-content;
            }

            .exact-match-filter-inline {
                margin-top: 0.4rem;
            }

            .exact-match-filter-inline .form-check-label {
                font-size: 0.75rem;
            }
        }

        @media (max-width: 576px) {
            .total-results-inline {
                font-size: 0.7rem;
                padding: 0.08rem 0.25rem;
            }

            .exact-match-filter-inline {
                margin-top: 0.3rem;
            }

            .exact-match-filter-inline .form-check-label {
                font-size: 0.7rem;
            }

            .exact-match-filter-inline .badge {
                font-size: 0.65rem;
            }

            .export-buttons-inline .btn {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
            }
        }

        /* Additional responsive styling for export buttons */
        @media (max-width: 768px) {
            .export-buttons-inline .btn {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
            }
        }

        @media (max-width: 576px) {
            .export-buttons-inline .btn {
                font-size: 0.7rem;
                padding: 0.35rem 0.7rem;
            }

            .export-buttons-inline .filter-indicator {
                font-size: 0.65rem;
            }
        }

        /* ===== COMPACT RESULTS INFORMATION STYLES (from detalii_dosar.php) ===== */

        .results-info-compact {
            padding: 0.5rem 0.75rem;
            background-color: rgba(108, 117, 125, 0.05);
            border-left: 2px solid rgba(108, 117, 125, 0.2);
            border-radius: 0 3px 3px 0;
            margin: 0.75rem 0;
        }

        .results-info-compact .text-muted {
            color: #6c757d !important;
            font-size: 0.8rem;
            line-height: 1.4;
            margin: 0;
        }

        .results-info-compact i {
            font-size: 0.75rem;
            opacity: 0.6;
        }

        /* Mobile optimization for compact info */
        @media (max-width: 767.98px) {
            .results-info-compact {
                padding: 0.375rem 0.5rem;
                margin: 0.5rem 0;
            }

            .results-info-compact .text-muted {
                font-size: 0.75rem;
            }

            .results-info-compact i {
                font-size: 0.7rem;
            }
        }

        /* ===== MOBILE CARD VIEW STYLES (from search.php) ===== */

        /* Card view for mobile results - hidden by default */
        .card-view {
            display: none;
        }

        /* Table container - shown by default */
        .table-container {
            display: block;
        }

        /* Mobile breakpoint - switch to card view */
        @media (max-width: 767.98px) {
            /* Hide table and show card view on mobile */
            .table-container {
                display: none !important;
            }

            .card-view {
                display: block !important;
            }

            /* Mobile card styling */
            .result-card {
                border: 1px solid rgba(0,0,0,.125);
                border-radius: 8px;
                padding: 1rem;
                margin-bottom: 1rem;
                background-color: #fff;
                box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
            }

            .result-card-header {
                font-weight: bold;
                margin-bottom: 0.75rem;
                font-size: 1.1rem;
                color: var(--primary-blue);
                border-bottom: 1px solid var(--border-color);
                padding-bottom: 0.5rem;
            }

            .result-card-body {
                margin-bottom: 0.75rem;
            }

            .result-card-item {
                display: flex;
                margin-bottom: 0.5rem;
                align-items: flex-start;
            }

            .result-card-label {
                font-weight: 600;
                min-width: 140px;
                color: var(--secondary-blue);
                font-size: 0.9rem;
            }

            .result-card-value {
                flex: 1;
                word-wrap: break-word;
                overflow-wrap: break-word;
                font-size: 0.9rem;
            }

            .result-card-actions {
                margin-top: 0.75rem;
                text-align: right;
                border-top: 1px solid var(--border-color);
                padding-top: 0.75rem;
            }

            .result-card-actions .btn {
                width: auto;
                font-size: 0.85rem;
                padding: 0.5rem 1rem;
            }

            /* Mobile-specific styles for case number links */
            .case-number-link {
                font-size: 0.9rem;
                padding: 4px 6px;
                min-height: 44px; /* Ensure touch-friendly target */
                display: inline-flex;
                align-items: center;
            }

            .result-card-header .case-number-link {
                font-size: 1rem;
                font-weight: 600;
            }

            /* Enhanced mobile styles for details button in card actions */
            .result-card-actions {
                text-align: center; /* Center the button */
                padding: 0.75rem 1rem; /* Add more padding around the button area */
            }

            .result-card-actions .btn {
                width: 80% !important; /* Take up more horizontal space */
                max-width: 280px; /* Reasonable maximum width */
                min-width: 200px; /* Minimum width for consistency */
                height: 48px; /* Ensure touch-friendly height */
                padding: 0.75rem 1.5rem; /* Generous padding for better touch target */
                border-radius: 8px; /* Slightly more rounded for modern look */
                font-size: 0.95rem !important; /* Slightly larger font */
                font-weight: 600; /* Make text more prominent */
                display: flex !important; /* Use flexbox for better alignment */
                align-items: center;
                justify-content: center;
                gap: 0.5rem; /* Space between icon and text */
                background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important; /* Enhanced gradient */
                border: 2px solid #007bff !important;
                box-shadow: 0 2px 8px rgba(0, 123, 255, 0.25); /* More prominent shadow */
                transition: all 0.3s ease;
                color: #ffffff !important;
            }

            .result-card-actions .btn:hover {
                background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
                border-color: #0056b3 !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 123, 255, 0.35);
                color: #ffffff !important;
            }

            .result-card-actions .btn:active {
                transform: translateY(0);
                box-shadow: 0 2px 6px rgba(0, 123, 255, 0.3);
            }

            .result-card-actions .btn i {
                font-size: 1rem; /* Slightly larger icon */
                color: #ffffff;
            }
        }

        /* Extra small mobile optimization */
        @media (max-width: 575.98px) {
            .result-card {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
            }

            .result-card-header {
                font-size: 1rem;
                margin-bottom: 0.5rem;
            }

            .result-card-label {
                min-width: 120px;
                font-size: 0.85rem;
            }

            .result-card-value {
                font-size: 0.85rem;
            }

            .result-card-item {
                margin-bottom: 0.4rem;
            }

            .result-card-actions {
                margin-top: 0.5rem;
                padding-top: 0.5rem;
            }

            .result-card-actions .btn {
                font-size: 0.8rem;
                padding: 0.4rem 0.8rem;
            }

            /* Override smaller screen styles for result-card-actions specifically */
            .result-card-actions .btn {
                width: 90% !important; /* Override for very small screens */
                min-width: 180px !important; /* Ensure minimum width even on very small screens */
                font-size: 0.9rem !important;
                padding: 0.65rem 1rem !important;
            }

            /* Adjust for very small screens (less than 400px) */
            @media (max-width: 399px) {
                .result-card-actions .btn {
                    width: 95% !important;
                    min-width: 160px !important;
                    font-size: 0.85rem !important;
                    padding: 0.6rem 0.8rem !important;
                }
            }
        }

        /* Reverted Font Sizes - Original Values with Enhanced Layout */
        @media (max-width: 1200px) {
            .table {
                font-size: 0.75rem; /* Reverted to original */
            }

            .table th, .table td {
                padding: 0.4rem 0.25rem; /* Reverted to original */
                line-height: 1.2;
            }

            .table th {
                font-size: 0.7rem; /* Reverted to original */
            }

            /* Large tablet optimization - Enhanced case number & compact date columns */
            .table th:nth-child(1), .table td:nth-child(1) { width: 12%; }
            .table th:nth-child(2), .table td:nth-child(2) { width: 7%; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 25%; }
            .table th:nth-child(4), .table td:nth-child(4) { width: 7%; }
            .table th:nth-child(5), .table td:nth-child(5) { width: 7%; }
            .table th:nth-child(6), .table td:nth-child(6) { width: 7%; }
            .table th:nth-child(7), .table td:nth-child(7) { width: 6%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 16%; }
            .table th:nth-child(9), .table td:nth-child(9) { width: 6%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 10%; }
        }

        @media (max-width: 992px) {
            .table {
                font-size: 0.7rem; /* Reverted to original */
            }

            .table th, .table td {
                padding: 0.35rem 0.2rem; /* Reverted to original */
                line-height: 1.15;
            }

            .table th {
                font-size: 0.65rem; /* Reverted to original */
            }

            /* Tablet optimization - enhanced case number & compact date visibility */
            .table th:nth-child(1), .table td:nth-child(1) { width: 11%; }
            .table th:nth-child(2), .table td:nth-child(2) { width: 6%; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 26%; }
            .table th:nth-child(4), .table td:nth-child(4) { width: 6%; }
            .table th:nth-child(5), .table td:nth-child(5) { width: 6%; }
            .table th:nth-child(6), .table td:nth-child(6) { width: 6%; }
            .table th:nth-child(7), .table td:nth-child(7) { width: 6%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 17%; }
            .table th:nth-child(9), .table td:nth-child(9) { width: 5%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 11%; }
        }

        @media (max-width: 768px) {
            .table {
                font-size: 0.65rem; /* Reverted to original */
            }

            .table th, .table td {
                padding: 0.25rem 0.15rem; /* Reverted to original */
                line-height: 1.1;
            }

            .table th {
                font-size: 0.6rem; /* Reverted to original */
                padding: 0.2rem 0.1rem; /* Reverted to original */
            }

            /* Mobile optimization - enhanced case number & compact date readability */
            .table th:nth-child(1), .table td:nth-child(1) { width: 10%; }
            .table th:nth-child(2), .table td:nth-child(2) { width: 5%; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 28%; }
            .table th:nth-child(4), .table td:nth-child(4) { width: 5%; }
            .table th:nth-child(5), .table td:nth-child(5) { width: 5%; }
            .table th:nth-child(6), .table td:nth-child(6) { width: 5%; }
            .table th:nth-child(7), .table td:nth-child(7) { width: 5%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 18%; }
            .table th:nth-child(9), .table td:nth-child(9) { width: 5%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 12%; }
        }

        @media (max-width: 576px) {
            .table {
                font-size: 0.6rem; /* Reverted to original */
            }

            .table th, .table td {
                padding: 0.2rem 0.1rem; /* Reverted to original */
                line-height: 1.05;
            }

            .table th {
                font-size: 0.55rem; /* Reverted to original */
                padding: 0.15rem 0.08rem; /* Reverted to original */
            }

            /* Small mobile - optimized case number & compact date display */
            .table th:nth-child(1), .table td:nth-child(1) { width: 9%; }
            .table th:nth-child(2), .table td:nth-child(2) { width: 4%; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 30%; }
            .table th:nth-child(4), .table td:nth-child(4) { width: 4%; }
            .table th:nth-child(5), .table td:nth-child(5) { width: 4%; }
            .table th:nth-child(6), .table td:nth-child(6) { width: 4%; }
            .table th:nth-child(7), .table td:nth-child(7) { width: 4%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 20%; }
            .table th:nth-child(9), .table td:nth-child(9) { width: 4%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 15%; }
        }

        /* Reverted ultra-small mobile (320px) optimization - Original font sizes */
        @media (max-width: 320px) {
            .table {
                font-size: 0.55rem; /* Reverted to original */
            }

            .table th, .table td {
                padding: 0.15rem 0.05rem; /* Reverted to original */
                line-height: 1.0;
            }

            .table th {
                font-size: 0.5rem; /* Reverted to original */
                padding: 0.1rem 0.05rem; /* Reverted to original */
            }

            /* iPhone SE and ultra-small screens - optimized case number & minimum date visibility */
            .table th:nth-child(1), .table td:nth-child(1) { width: 8%; }
            .table th:nth-child(2), .table td:nth-child(2) { width: 3%; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 32%; }
            .table th:nth-child(4), .table td:nth-child(4) { width: 3%; }
            .table th:nth-child(5), .table td:nth-child(5) { width: 3%; }
            .table th:nth-child(6), .table td:nth-child(6) { width: 3%; }
            .table th:nth-child(7), .table td:nth-child(7) { width: 3%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 22%; }
            .table th:nth-child(9), .table td:nth-child(9) { width: 3%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 16%; }
        }

        /* ===== MOBILE TABLE OPTIMIZATIONS (from search.php) ===== */

        /* Mobile Large (576px-767px) - ULTRA COMPACT but readable layout */
        @media (min-width: 576px) and (max-width: 767px) {
            .table thead th {
                font-size: 0.7rem;
                padding: 0.4rem 0.3rem;
                /* ULTRA COMPACT TEXT WRAPPING for mobile large header visibility */
                white-space: normal;
                word-wrap: break-word;
                overflow-wrap: break-word;
                line-height: 1.0;
                min-height: 2rem;
                max-height: 3rem;
            }

            /* Ultra compact text wrapping on mobile large */
            .header-text,
            th a span:first-child {
                white-space: normal;
                overflow: visible;
                text-overflow: unset;
                word-wrap: break-word;
                overflow-wrap: break-word;
                font-size: 0.7rem;
                line-height: 1.0;
                max-height: 2.2rem;
            }

            /* Fixed table layout with compact percentages */
            .table {
                table-layout: fixed;
                width: 100%;
            }

            .table th:nth-child(1),  /* Número dosar - INCREASED for full visibility */
            .table td:nth-child(1) {
                width: 10%;
                min-width: 0;
            }

            .table th:nth-child(2),  /* Instanță */
            .table td:nth-child(2) {
                width: 11%;
                min-width: 0;
            }

            .table th:nth-child(3),  /* Obiect */
            .table td:nth-child(3) {
                width: 25%;
                min-width: 0;
            }

            .table th:nth-child(4),  /* Stadiu Procesual */
            .table td:nth-child(4) {
                width: 9%;
                min-width: 0;
            }

            .table th:nth-child(5),  /* Data */
            .table td:nth-child(5) {
                width: 8%;
                min-width: 0;
            }

            .table th:nth-child(6),  /* Ultima Modificare */
            .table td:nth-child(6) {
                width: 8%;
                min-width: 0;
            }

            .table th:nth-child(7),  /* Categorie caz */
            .table td:nth-child(7) {
                width: 7%;
                min-width: 0;
            }

            .table th:nth-child(8),  /* Nume Parte */
            .table td:nth-child(8) {
                width: 14%;
                min-width: 0;
            }

            .table th:nth-child(9),  /* Calitate */
            .table td:nth-child(9) {
                width: 6%;
                min-width: 0;
            }

            .table th:nth-child(10), /* Acțiuni - MAINTAINED for action buttons */
            .table td:nth-child(10) {
                width: 8%;
                min-width: 0;
                text-align: center;
            }
        }

        /* Mobile Small (≤575px) - MINIMAL compact layout */
        @media (max-width: 575px) {
            .table thead th {
                font-size: 0.65rem;
                padding: 0.3rem 0.2rem;
                /* MINIMAL TEXT WRAPPING for mobile small header visibility */
                white-space: normal;
                word-wrap: break-word;
                overflow-wrap: break-word;
                line-height: 1.0;
                min-height: 1.8rem;
                max-height: 2.5rem;
            }

            /* Minimal text wrapping on small mobile */
            .header-text,
            th a span:first-child {
                white-space: normal;
                overflow: visible;
                text-overflow: unset;
                line-height: 1.0;
                word-wrap: break-word;
                overflow-wrap: break-word;
                font-size: 0.65rem;
                max-height: 2rem;
            }

            /* Fixed table layout with minimal percentages */
            .table {
                table-layout: fixed;
                width: 100%;
            }

            .table th:nth-child(1),  /* Număr dosar */
            .table td:nth-child(1) {
                width: 9%;
                min-width: 0;
            }

            .table th:nth-child(2),  /* Instanță */
            .table td:nth-child(2) {
                width: 10%;
                min-width: 0;
            }

            .table th:nth-child(3),  /* Obiect */
            .table td:nth-child(3) {
                width: 26%;
                min-width: 0;
            }

            .table th:nth-child(4),  /* Stadiu Procesual */
            .table td:nth-child(4) {
                width: 8%;
                min-width: 0;
            }

            .table th:nth-child(5),  /* Data */
            .table td:nth-child(5) {
                width: 7%;
                min-width: 0;
            }

            .table th:nth-child(6),  /* Ultima Modificare */
            .table td:nth-child(6) {
                width: 7%;
                min-width: 0;
            }

            .table th:nth-child(7),  /* Categorie caz */
            .table td:nth-child(7) {
                width: 6%;
                min-width: 0;
            }

            .table th:nth-child(8),  /* Nume Parte */
            .table td:nth-child(8) {
                width: 15%;
                min-width: 0;
            }

            .table th:nth-child(9),  /* Calitate */
            .table td:nth-child(9) {
                width: 6%;
                min-width: 0;
            }

            .table th:nth-child(10), /* Acțiuni */
            .table td:nth-child(10) {
                width: 8%;
                min-width: 0;
                text-align: center;
            }
        }

        /* Responsive text adjustments for mobile */
        @media (max-width: 767px) {
            .table td,
            .table th {
                line-height: 1.3;
                font-size: 0.85rem;
            }

            .header-text {
                word-break: break-word;
                overflow-wrap: break-word;
                line-height: 1.2;
            }
        }

        @media (max-width: 575px) {
            .table td,
            .table th {
                line-height: 1.2;
                font-size: 0.8rem;
                padding: 0.5rem 0.25rem;
            }

            .header-text {
                font-size: 0.7rem;
                line-height: 1.1;
            }
        }

        /* Enhanced readability for all screen sizes */
        .table th {
            font-size: 0.8em;
            text-align: center;
            vertical-align: middle;
        }

        .table td {
            text-align: left;
            vertical-align: top;
        }

        /* Consistent Date Column Styling - Same Font as Case Number */
        .table th:nth-child(5), .table td:nth-child(5), /* Data */
        .table th:nth-child(6), .table td:nth-child(6) { /* Ultima Modificare */
            text-align: center;
            white-space: nowrap;
            font-weight: 500;
        }

        /* Action column center alignment */
        .table th:nth-child(10), .table td:nth-child(10) { /* Acțiuni */
            text-align: center;
        }

        /* Reverted date format optimization - Original font sizes */
        .table td:nth-child(5), .table td:nth-child(6) {
            font-size: 0.85em; /* Reverted to original */
            letter-spacing: 0.5px; /* Reverted to original */
        }

        /* Reverted responsive date formatting - Original font sizes */
        @media (max-width: 768px) {
            .table td:nth-child(5), .table td:nth-child(6) {
                font-size: 0.75em; /* Reverted to original */
                letter-spacing: 0.3px; /* Reverted to original */
            }
        }

        @media (max-width: 576px) {
            .table td:nth-child(5), .table td:nth-child(6) {
                font-size: 0.7em; /* Reverted to original */
                letter-spacing: 0.2px; /* Reverted to original */
            }
        }

        @media (max-width: 320px) {
            .table td:nth-child(5), .table td:nth-child(6) {
                font-size: 0.65em; /* Reverted to original */
                letter-spacing: 0.1px; /* Reverted to original */
            }
        }

        /* Reverted Action Button Styling - Original Sizes */
        .table th:nth-child(10), .table td:nth-child(10) {
            text-align: center;
            vertical-align: middle;
        }

        /* Reverted action buttons optimization - Original sizes */
        .table td:nth-child(10) .btn {
            white-space: nowrap;
            min-width: 40px; /* Reverted to original */
            min-height: 28px; /* Reverted to original */
            font-size: 0.7rem; /* Reverted to original */
            padding: 0.25rem 0.3rem; /* Reverted to original */
        }

        /* Reverted responsive button adjustments - Original sizes */
        @media (max-width: 1200px) {
            .table td:nth-child(10) .btn {
                font-size: 0.65rem; /* Reverted to original */
                padding: 0.2rem 0.25rem; /* Reverted to original */
                min-width: 38px; /* Reverted to original */
                min-height: 26px; /* Reverted to original */
            }
        }

        @media (max-width: 992px) {
            .table td:nth-child(10) .btn {
                font-size: 0.6rem; /* Reverted to original */
                padding: 0.15rem 0.2rem; /* Reverted to original */
                min-width: 36px; /* Reverted to original */
                min-height: 24px; /* Reverted to original */
            }

            .table td:nth-child(10) .btn i {
                font-size: 0.7rem; /* Reverted to original */
            }
        }

        @media (max-width: 768px) {
            .table td:nth-child(10) .btn {
                font-size: 0.55rem; /* Reverted to original */
                padding: 0.1rem 0.15rem; /* Reverted to original */
                min-width: 34px; /* Reverted to original */
                min-height: 22px; /* Reverted to original */
            }

            .table td:nth-child(10) .btn i {
                font-size: 0.65rem; /* Reverted to original */
            }
        }

        @media (max-width: 576px) {
            .table td:nth-child(10) .btn {
                font-size: 0.5rem; /* Reverted to original */
                padding: 0.08rem 0.12rem; /* Reverted to original */
                min-width: 32px; /* Reverted to original */
                min-height: 20px; /* Reverted to original */
            }

            .table td:nth-child(10) .btn i {
                font-size: 0.6rem; /* Reverted to original */
            }
        }

        @media (max-width: 320px) {
            .table td:nth-child(10) .btn {
                font-size: 0.45rem; /* Reverted to original */
                padding: 0.05rem 0.1rem; /* Reverted to original */
                min-width: 30px; /* Reverted to original */
                min-height: 18px; /* Reverted to original */
            }

            .table td:nth-child(10) .btn i {
                font-size: 0.55rem; /* Reverted to original */
            }
        }

        /* Accessibility improvements */
        .table th, .table td {
            min-height: 2.5rem;
        }

        @media (max-width: 576px) {
            .table th, .table td {
                min-height: 2rem;
            }
        }



        /* Maximum Content Visibility - No Truncation */
        .table td {
            white-space: normal !important;
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            text-align: left;
            max-height: none !important;
            overflow: visible !important;
        }

        /* Remove all text truncation and ellipsis */
        .table td .text-truncate {
            white-space: normal !important;
            overflow: visible !important;
            text-overflow: clip !important;
            max-width: none !important;
        }

        /* Priority content columns - maximum space utilization */
        .table td:nth-child(3) { /* Obiect - Full content display */
            max-height: none !important;
            overflow: visible !important;
            word-break: break-word;
        }

        .table td:nth-child(8) { /* Nume Parte - Full content display */
            max-height: none !important;
            overflow: visible !important;
            word-break: break-word;
        }

        /* Ensure all content is fully visible */
        .table td span {
            white-space: normal !important;
            overflow: visible !important;
            text-overflow: clip !important;
            max-width: none !important;
            display: block !important;
        }

        /* Reverted Party Matching Styles - Original Sizes */
        .matching-party-name {
            display: inline-block;
            padding: 0.25rem 0.5rem; /* Reverted to original */
            border-radius: 4px; /* Reverted to original */
            transition: all 0.3s ease;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        /* Exact match styling */
        .matching-party-name.exact-match {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.15) 0%, rgba(40, 167, 69, 0.1) 100%);
            border-left: 4px solid var(--success-color);
        }

        /* Reverted exact phrase match styling - Original borders */
        .matching-party-name.exact-phrase-match {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.2) 0%, rgba(23, 162, 184, 0.15) 100%);
            border-left: 4px solid var(--info-color); /* Reverted to original */
            border-radius: 6px; /* Reverted to original */
        }

        /* Reverted partial match styling - Original borders */
        .matching-party-name.partial-match {
            background-color: rgba(0, 123, 255, 0.1);
            border-left: 3px solid var(--primary-blue); /* Reverted to original */
        }

        /* Reverted hover effects for party matches - Original sizes */
        .matching-party-name:hover {
            transform: translateY(-1px); /* Reverted to original */
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2); /* Reverted to original */
        }

        /* Reverted tooltip styling for match indicators - Original sizes */
        .party-match-indicator[title]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--secondary-blue);
            color: white;
            padding: 0.5rem; /* Reverted to original */
            border-radius: 4px; /* Reverted to original */
            font-size: 0.75rem; /* Reverted to original */
            white-space: nowrap;
            z-index: 1000;
        }

        /* Enhanced Party Matching Filter - Integrated with Export */
        .party-filter-integrated {
            border-top: 1px solid var(--border-color);
            padding-top: 1rem;
            margin-top: 1rem;
        }

        .party-filter-integrated .form-check {
            padding: 0.75rem;
            background-color: rgba(0, 123, 255, 0.05);
            border: 1px solid rgba(0, 123, 255, 0.1);
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .party-filter-integrated .form-check:hover {
            background-color: rgba(0, 123, 255, 0.08);
            border-color: rgba(0, 123, 255, 0.2);
        }

        .party-filter-integrated .form-check-input {
            width: 1.2em;
            height: 1.2em;
            border-color: var(--primary-blue);
        }

        .party-filter-integrated .form-check-input:checked {
            background-color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .party-filter-integrated .form-check-label {
            font-weight: 500;
            color: var(--secondary-blue);
            cursor: pointer;
        }

        .export-controls {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        /* Export button filter indicators */
        .export-btn .filter-indicator {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .export-btn.filter-active {
            position: relative;
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
        }

        .export-btn.filter-active .filter-indicator {
            display: inline !important;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 1; }
            100% { opacity: 0.8; }
        }

        #filterStatus .badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
        }

        /* Responsive filter styling */
        @media (max-width: 768px) {
            .party-filter-integrated .form-check {
                padding: 0.5rem;
            }

            .party-filter-integrated .form-check-input {
                width: 1.1em;
                height: 1.1em;
            }

            .party-filter-integrated .form-check-label {
                font-size: 0.9rem;
            }

            .export-controls {
                flex-direction: column;
            }

            .export-controls .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }
        }

        /* Enhanced highlighting for search terms */
        .matching-party-name .text-primary {
            font-weight: 700;
            color: var(--primary-blue) !important;
            text-shadow: 0 1px 2px rgba(0, 123, 255, 0.3);
        }

        /* Loading overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: none;
            justify-content: center;
            align-items: center;
        }

        .loading-content {
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            max-width: 400px;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            margin: 0 auto 1rem;
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Progress bar */
        .progress-container {
            margin-top: 1rem;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
            background-color: var(--border-color);
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* Responsive design */
        @media (max-width: 767.98px) {
            .streamlined-card .card-body {
                padding: 1rem;
            }

            .streamlined-card.compact-form .card-body {
                padding: 0.875rem;
            }

            .compact-form .form-label {
                margin-bottom: 0.25rem;
                font-size: 0.9rem;
            }

            .compact-form .form-control,
            .compact-form .form-select {
                margin-bottom: 0.5rem;
                font-size: 0.95rem;
            }

            .compact-form .term-counter {
                margin-top: 0.375rem;
                font-size: 0.8rem;
            }

            .compact-form .col-md-4 .mt-3 {
                margin-top: 0.5rem !important;
            }

            .streamlined-card.compact-form .card-header {
                padding: 0.75rem 0.875rem;
            }

            .compact-form .card-header h1 {
                font-size: 1.1rem;
            }

            .compact-form textarea.form-control {
                min-height: 100px;
            }

            .compact-form .btn {
                padding: 0.45rem 0.875rem;
                font-size: 0.9rem;
            }

            .btn-primary {
                width: 100%;
                margin-top: 1rem;
            }
        }

        /* Notification container */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        }

        /* Navigation Bar Styles */
        .navbar {
            background-color: #f8f9fa !important;
            border-bottom: 2px solid #007bff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 0.75rem 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: #2c3e50 !important;
            font-size: 1.25rem;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            color: #007bff !important;
            transform: translateY(-1px);
        }

        .navbar-brand i {
            color: #007bff;
            margin-right: 0.5rem;
        }

        .nav-link {
            color: #495057 !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: 6px;
            transition: all 0.3s ease;
            margin: 0 0.25rem;
        }

        .nav-link:hover {
            color: #007bff !important;
            background-color: rgba(0, 123, 255, 0.1);
            transform: translateY(-1px);
        }

        .nav-link.active,
        .nav-item.active .nav-link {
            color: #007bff !important;
            background-color: rgba(0, 123, 255, 0.15);
            font-weight: 600;
        }

        .nav-link i {
            margin-right: 0.5rem;
            width: 16px;
            text-align: center;
        }

        /* Responsive navigation */
        @media (max-width: 991.98px) {
            .navbar-nav {
                margin-top: 1rem;
                padding-top: 1rem;
                border-top: 1px solid #dee2e6;
            }

            .nav-link {
                margin: 0.25rem 0;
                text-align: center;
            }
        }

        @media (max-width: 575.98px) {
            .navbar-brand {
                font-size: 1.1rem;
            }

            .nav-link {
                padding: 0.75rem 1rem !important;
            }
        }

        /* Results Summary Section */
        .results-summary-section {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.03) 0%, rgba(44, 62, 80, 0.02) 100%);
            border: 1px solid rgba(0, 123, 255, 0.1);
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1.5rem;
        }

        .results-info h6 {
            color: var(--secondary-blue);
            font-weight: 600;
            margin-bottom: 0.75rem;
        }

        .result-stat {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            font-weight: 500;
        }

        .stat-value {
            font-size: 1.1rem;
            font-weight: 700;
        }

        .filter-controls h6 {
            color: var(--secondary-blue);
            font-weight: 600;
            margin-bottom: 0.75rem;
        }

        .filter-controls .form-check {
            background-color: rgba(0, 123, 255, 0.05);
            border: 1px solid rgba(0, 123, 255, 0.15);
            border-radius: 6px;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .filter-controls .form-check:hover {
            background-color: rgba(0, 123, 255, 0.08);
            border-color: rgba(0, 123, 255, 0.25);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
        }

        .filter-controls .form-check-input {
            width: 1.2em;
            height: 1.2em;
            border-color: var(--primary-blue);
            border-width: 2px;
        }

        .filter-controls .form-check-input:checked {
            background-color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .filter-controls .form-check-label {
            font-weight: 500;
            color: var(--secondary-blue);
            cursor: pointer;
            user-select: none;
        }

        /* Responsive design for results summary */
        @media (max-width: 768px) {
            .results-summary-section {
                padding: 1rem;
            }

            .results-summary-section .row {
                flex-direction: column;
            }

            .results-summary-section .col-md-4 {
                margin-top: 1rem;
                padding-top: 1rem;
                border-top: 1px solid rgba(0, 123, 255, 0.1);
            }

            .result-stat {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.25rem;
            }

            .filter-controls .form-check {
                padding: 0.5rem;
            }

            .filter-controls .form-check-input {
                width: 1.1em;
                height: 1.1em;
            }

            .filter-controls .form-check-label {
                font-size: 0.9rem;
            }
        }

        /* Export buttons - Original styling for results section */
        .export-buttons {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        /* Stiluri pentru linkul numărului de dosar */
        .case-number-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            display: inline-block;
            padding: 2px 4px;
            border-radius: 3px;
        }

        .case-number-link:hover {
            color: #0056b3;
            text-decoration: none;
            background-color: rgba(0, 123, 255, 0.1);
            transform: translateY(-1px);
        }

        .case-number-link:focus {
            outline: 2px solid #007bff;
            outline-offset: 2px;
            background-color: rgba(0, 123, 255, 0.1);
        }

        .case-number-link:active {
            transform: translateY(0);
            background-color: rgba(0, 123, 255, 0.2);
        }

        .export-btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }

        /* Inline export buttons in search form */
        .export-buttons-inline {
            margin-top: 1rem;
        }

        .export-buttons-inline .btn {
            font-size: 0.875rem;
            padding: 0.5rem 1rem;
            font-weight: 600;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .export-buttons-inline .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #1e7e34 100%);
            border: none;
        }

        .export-buttons-inline .btn-success:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .export-buttons-inline .btn-info {
            background: linear-gradient(135deg, var(--info-color) 0%, #117a8b 100%);
            border: none;
            color: white;
        }

        .export-buttons-inline .btn-info:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
            color: white;
        }

        .export-buttons-inline .filter-indicator {
            font-size: 0.75rem;
        }

        /* Enhanced party name highlighting with match type differentiation */
        .matching-party-name {
            padding: 3px 6px;
            border-radius: 4px;
            display: inline-block;
            transition: all 0.3s ease;
        }

        /* Exact match styling (highest priority) */
        .matching-party-name.exact-match {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.15) 0%, rgba(40, 167, 69, 0.1) 100%);
            border-left: 4px solid var(--success-color);
            border-radius: 4px 4px 4px 0;
        }

        /* Partial match styling */
        .matching-party-name.partial-match {
            background-color: rgba(0, 123, 255, 0.1);
            border-left: 3px solid var(--primary-blue);
            border-radius: 4px 4px 4px 0;
        }

        .matching-party-name .text-primary {
            font-weight: 600;
            color: var(--primary-blue) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced highlighting for exact matches */
        .exact-match .text-primary {
            color: var(--success-color) !important;
            font-weight: 700;
        }

        /* Tooltip for matching indication with match type */
        .party-match-indicator {
            position: relative;
            cursor: help;
        }

        .party-match-indicator::after {
            content: "✓ Potrivire găsită";
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--secondary-blue);
            color: white;
            padding: 5px 10px;
            border-radius: 6px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Different tooltips for different match types */
        .exact-match::after {
            content: "✓ Potrivire exactă";
            background-color: var(--success-color);
        }

        .partial-match::after {
            content: "✓ Potrivire parțială";
            background-color: var(--primary-blue);
        }

        .party-match-indicator:hover::after {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(-2px);
        }

        /* Hover effects for enhanced interactivity */
        .matching-party-name:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
        }

        .exact-match:hover {
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }
    </style>
</head>
<body>
    <div class="content-wrapper">
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h5 id="loadingMessage">Se procesează căutarea...</h5>
            <p id="loadingSubmessage">Vă rugăm să așteptați...</p>
            <div class="progress-container">
                <div class="progress">
                    <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                </div>
                <small id="progressText" class="text-muted mt-1 d-block">0%</small>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container" style="display: none;">
        <div id="notification" class="alert" role="alert"></div>
    </div>

    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-gavel me-2"></i>
                DosareJust.ro - Portal Judiciar
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-search me-1"></i>
                            Căutare Dosare
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sedinte.php">
                            <i class="fas fa-calendar-alt me-1"></i>
                            Ședințe
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-12 col-xl-10">
                <!-- Main Search Form -->
                <div class="card streamlined-card compact-form">
                    <div class="card-header">
                        <h1 class="h4 mb-0">
                            <i class="fas fa-search-plus me-2"></i>
                            DosareJust.ro
                        </h1>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="bulkSearchForm">
                            <div class="row">
                                <div class="col-md-8">
                                    <label for="bulkSearchTerms" class="form-label">
                                        <i class="fas fa-list me-1"></i>
                                        Termeni de căutare
                                    </label>
                                    <textarea
                                        class="form-control"
                                        id="bulkSearchTerms"
                                        name="bulkSearchTerms"
                                        placeholder="Introduceți termenii de căutare, câte unul pe linie sau separați prin virgulă:&#10;Sistemul detectează automat tipul:&#10;1234/2023 → Număr dosar&#10;Popescu Ion → Nume parte&#10;SC EXEMPLU SRL → Nume parte&#10;Folosiți ghilimele pentru căutare exactă de frază.&#10;&#10;OPȚIONAL: Puteți căuta folosind doar filtrele avansate de mai jos.">130/98/2022</textarea>
                                    <div class="term-counter">
                                        <div class="term-info-line">
                                            <span id="termCount">0</span> termeni introduși
                                            <span id="termLimit" class="text-muted">(maxim 100)</span>
                                                                                            <span class="total-results-inline text-primary fw-bold ms-2">
                                                    - Total rezultate: <span id="totalResultsCounter" data-original-total="2">2</span>
                                                </span>
                                                                                    </div>

                                        <!-- Enhanced Party Matching Filter - Shows only for party name searches -->
                                                                                                                                                                                                            </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex flex-column h-100">
                                        <!-- Search Info Panel -->
                                        <div class="alert alert-info mb-3" style="flex-grow: 1;">
                                            <h6 class="alert-heading mb-2">
                                                <i class="fas fa-magic me-1"></i>
                                                Detectare automată
                                            </h6>
                                            <p class="mb-2 small">
                                                Sistemul detectează automat tipul de căutare:
                                            </p>
                                            <ul class="mb-0 small">
                                                <li><strong>Număr dosar:</strong> 1234/2023, 12345/118/2023</li>
                                                <li><strong>Nume parte:</strong> persoane, companii (SC, SRL)</li>
                                            </ul>
                                        </div>

                                        <div class="mt-auto">
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="fas fa-search me-2"></i>
                                                Căutare
                                            </button>
                                        </div>

                                        <!-- Export Buttons - Desktop Version -->
                                                                                    <div class="export-buttons-inline export-buttons-desktop mt-3">
                                                <div class="d-grid gap-2">
                                                    <button type="button" class="btn btn-success export-btn" id="csvExportBtn" onclick="exportBulkResults('csv')">
                                                        <i class="fas fa-file-csv me-1"></i>
                                                        <span class="export-text">Export CSV</span>
                                                        <span class="filter-indicator" style="display: none;">
                                                            <i class="fas fa-filter ms-1"></i>
                                                        </span>
                                                    </button>
                                                    <button type="button" class="btn btn-info export-btn" id="excelExportBtn" onclick="exportBulkResults('xlsx')">
                                                        <i class="fas fa-file-excel me-1"></i>
                                                        <span class="export-text">Export Excel</span>
                                                        <span class="filter-indicator" style="display: none;">
                                                            <i class="fas fa-filter ms-1"></i>
                                                        </span>
                                                    </button>
                                                </div>
                                            </div>
                                                                            </div>
                                </div>
                            </div>

                            <!-- Advanced Filters Toggle Button -->
                            <div class="mt-3">
                                <a href="#" id="advancedFiltersToggle" class="text-primary d-flex align-items-center justify-content-center">
                                    <i class="fas fa-filter me-2"></i>
                                    <span>Arată filtrele avansate</span>
                                    <i class="fas fa-chevron-down ms-2"></i>
                                </a>
                            </div>

                            <!-- Advanced Filters Section - Moved Below Search Terms -->
                            <div id="advancedFilters" class="advanced-filters-section mt-4">
                                <h6 class="mb-3">
                                    <i class="fas fa-filter me-2"></i>
                                    Filtre avansate
                                    <small class="text-muted ms-2">(opționale - pot fi folosite independent sau împreună cu termenii de căutare)</small>
                                </h6>

                                <!-- First Row of Advanced Filters -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="institutie" class="form-label">
                                            <i class="fas fa-university me-1"></i>
                                            Instanță judecătorească:
                                        </label>

                                        <!-- Enhanced Searchable Dropdown for Institutions -->
                                        <div class="searchable-dropdown" id="institutieDropdown">
                                            <input type="text"
                                                   class="form-control dropdown-search-input"
                                                   id="institutieSearch"
                                                   placeholder="Căutați sau selectați instanța..."
                                                   autocomplete="off"
                                                   readonly>
                                            <div class="dropdown-options" id="institutieOptions">
                                                <div class="dropdown-option" data-value="">-- Toate instanțele --</div>
                                                <div class="dropdown-option" data-value="InaltaCurtedeCASSATIESIJUSTITIE">Înalta Curte de Casație și Justiție</div><div class="dropdown-option" data-value="CurteadeApelALBAIULIA">Curtea de Apel Alba Iulia</div><div class="dropdown-option" data-value="CurteadeApelBACU">Curtea de Apel Bacău</div><div class="dropdown-option" data-value="CurteadeApelBRASSOV">Curtea de Apel Brașov</div><div class="dropdown-option" data-value="CurteadeApelBUCURESTI">Curtea de Apel București</div><div class="dropdown-option" data-value="CurteadeApelCLUJ">Curtea de Apel Cluj</div><div class="dropdown-option" data-value="CurteadeApelCONSTANTA">Curtea de Apel Constanța</div><div class="dropdown-option" data-value="CurteadeApelCRAIOVA">Curtea de Apel Craiova</div><div class="dropdown-option" data-value="CurteadeApelGALATI">Curtea de Apel Galați</div><div class="dropdown-option" data-value="CurteadeApelIASI">Curtea de Apel Iași</div><div class="dropdown-option" data-value="CurteadeApelORADEA">Curtea de Apel Oradea</div><div class="dropdown-option" data-value="CurteadeApelPITESTI">Curtea de Apel Pitești</div><div class="dropdown-option" data-value="CurteadeApelPLOIESTI">Curtea de Apel Ploiești</div><div class="dropdown-option" data-value="CurteadeApelSUCEAVA">Curtea de Apel Suceava</div><div class="dropdown-option" data-value="CurteadeApelTARGUMURES">Curtea de Apel Târgu Mureș</div><div class="dropdown-option" data-value="CurteadeApelTIMISOARA">Curtea de Apel Timișoara</div><div class="dropdown-option" data-value="TribunalulALBA">Tribunalul Alba</div><div class="dropdown-option" data-value="TribunalulARAD">Tribunalul Arad</div><div class="dropdown-option" data-value="TribunalulARGES">Tribunalul Argeș</div><div class="dropdown-option" data-value="TribunalulBACU">Tribunalul Bacău</div><div class="dropdown-option" data-value="TribunalulBIHOR">Tribunalul Bihor</div><div class="dropdown-option" data-value="TribunalulBISTRITANASAUD">Tribunalul Bistrița-Năsăud</div><div class="dropdown-option" data-value="TribunalulBOTOSANI">Tribunalul Botoșani</div><div class="dropdown-option" data-value="TribunalulBRASSOV">Tribunalul Brașov</div><div class="dropdown-option" data-value="TribunalulBRASOV">Tribunalul Brașov</div><div class="dropdown-option" data-value="TribunalulBRAILA">Tribunalul Brăila</div><div class="dropdown-option" data-value="TribunalulBUCURESTI">Tribunalul București</div><div class="dropdown-option" data-value="TribunalulBUZAU">Tribunalul Buzău</div><div class="dropdown-option" data-value="TribunalulCALARASI">Tribunalul Călărași</div><div class="dropdown-option" data-value="TribunalulCARASSSEVERIN">Tribunalul Caraș-Severin</div><div class="dropdown-option" data-value="TribunalulCLUJ">Tribunalul Cluj</div><div class="dropdown-option" data-value="TribunalulCONSTANTA">Tribunalul Constanța</div><div class="dropdown-option" data-value="TribunalulCOVASNA">Tribunalul Covasna</div><div class="dropdown-option" data-value="TribunalulDAMBOVITA">Tribunalul Dâmbovița</div><div class="dropdown-option" data-value="TribunalulDOLJ">Tribunalul Dolj</div><div class="dropdown-option" data-value="TribunalulGALATI">Tribunalul Galați</div><div class="dropdown-option" data-value="TribunalulGIURGIU">Tribunalul Giurgiu</div><div class="dropdown-option" data-value="TribunalulGORJ">Tribunalul Gorj</div><div class="dropdown-option" data-value="TribunalulHARGHITA">Tribunalul Harghita</div><div class="dropdown-option" data-value="TribunalulHUNEDOARA">Tribunalul Hunedoara</div><div class="dropdown-option" data-value="TribunalulIALOMITA">Tribunalul Ialomița</div><div class="dropdown-option" data-value="TribunalulIASI">Tribunalul Iași</div><div class="dropdown-option" data-value="TribunalulILFOV">Tribunalul Ilfov</div><div class="dropdown-option" data-value="TribunalulMARAMURES">Tribunalul Maramureș</div><div class="dropdown-option" data-value="TribunalulMEHEDINTI">Tribunalul Mehedinți</div><div class="dropdown-option" data-value="TribunalulMURES">Tribunalul Mureș</div><div class="dropdown-option" data-value="TribunalulNEAMT">Tribunalul Neamț</div><div class="dropdown-option" data-value="TribunalulOLT">Tribunalul Olt</div><div class="dropdown-option" data-value="TribunalulPRAHOVA">Tribunalul Prahova</div><div class="dropdown-option" data-value="TribunalulSALAJ">Tribunalul Sălaj</div><div class="dropdown-option" data-value="TribunalulSATUMARE">Tribunalul Satu Mare</div><div class="dropdown-option" data-value="TribunalulSIBIU">Tribunalul Sibiu</div><div class="dropdown-option" data-value="TribunalulSUCEAVA">Tribunalul Suceava</div><div class="dropdown-option" data-value="TribunalulTELEORMAN">Tribunalul Teleorman</div><div class="dropdown-option" data-value="TribunalulTIMIS">Tribunalul Timiș</div><div class="dropdown-option" data-value="TribunalulTULCEA">Tribunalul Tulcea</div><div class="dropdown-option" data-value="TribunalulVALCEA">Tribunalul Vâlcea</div><div class="dropdown-option" data-value="TribunalulVASLUI">Tribunalul Vaslui</div><div class="dropdown-option" data-value="TribunalulVRANCEA">Tribunalul Vrancea</div><div class="dropdown-option" data-value="JudecatoriaSECTORUL1BUCURESTI">Judecătoria Sectorul 1 București</div><div class="dropdown-option" data-value="JudecatoriaSECTORUL2BUCURESTI">Judecătoria Sectorul 2 București</div><div class="dropdown-option" data-value="JudecatoriaSECTORUL3BUCURESTI">Judecătoria Sectorul 3 București</div><div class="dropdown-option" data-value="JudecatoriaSECTORUL4BUCURESTI">Judecătoria Sectorul 4 București</div><div class="dropdown-option" data-value="JudecatoriaSECTORUL5BUCURESTI">Judecătoria Sectorul 5 București</div><div class="dropdown-option" data-value="JudecatoriaSECTORUL6BUCURESTI">Judecătoria Sectorul 6 București</div><div class="dropdown-option" data-value="JudecatoriaAIUD">Judecătoria Aiud</div><div class="dropdown-option" data-value="JudecatoriaALBAIULIA">Judecătoria Alba Iulia</div><div class="dropdown-option" data-value="JudecatoriaALEXANDRIA">Judecătoria Alexandria</div><div class="dropdown-option" data-value="JudecatoriaARAD">Judecătoria Arad</div><div class="dropdown-option" data-value="JudecatoriaBACU">Judecătoria Bacău</div><div class="dropdown-option" data-value="JudecatoriaBISTRITA">Judecătoria Bistrița</div><div class="dropdown-option" data-value="JudecatoriaBOTOSANI">Judecătoria Botoșani</div><div class="dropdown-option" data-value="JudecatoriaBRASSOV">Judecătoria Brașov</div><div class="dropdown-option" data-value="JudecatoriaBRAILA">Judecătoria Brăila</div><div class="dropdown-option" data-value="JudecatoriaBUZAU">Judecătoria Buzău</div><div class="dropdown-option" data-value="JudecatoriaBUFTEA">Judecătoria Buftea</div><div class="dropdown-option" data-value="JudecatoriaCALARASI">Judecătoria Călărași</div><div class="dropdown-option" data-value="JudecatoriaCARACAL">Judecătoria Caracal</div><div class="dropdown-option" data-value="JudecatoriaCLUJNAPOCA">Judecătoria Cluj-Napoca</div><div class="dropdown-option" data-value="JudecatoriaCONSTANTA">Judecătoria Constanța</div><div class="dropdown-option" data-value="JudecatoriaCRAIOVA">Judecătoria Craiova</div><div class="dropdown-option" data-value="JudecatoriaDEVA">Judecătoria Deva</div><div class="dropdown-option" data-value="JudecatoriaFOCSANI">Judecătoria Focșani</div><div class="dropdown-option" data-value="JudecatoriaGALATI">Judecătoria Galați</div><div class="dropdown-option" data-value="JudecatoriaGIURGIU">Judecătoria Giurgiu</div><div class="dropdown-option" data-value="JudecatoriaIASI">Judecătoria Iași</div><div class="dropdown-option" data-value="JudecatoriaORADEA">Judecătoria Oradea</div><div class="dropdown-option" data-value="JudecatoriaPITESTI">Judecătoria Pitești</div><div class="dropdown-option" data-value="JudecatoriaPLOIESTI">Judecătoria Ploiești</div><div class="dropdown-option" data-value="JudecatoriaREMNICUVILCEA">Judecătoria Râmnicu Vâlcea</div><div class="dropdown-option" data-value="JudecatoriaSATUMARE">Judecătoria Satu Mare</div><div class="dropdown-option" data-value="JudecatoriaSIBIU">Judecătoria Sibiu</div><div class="dropdown-option" data-value="JudecatoriaSLATINA">Judecătoria Slatina</div><div class="dropdown-option" data-value="JudecatoriaSUCEAVA">Judecătoria Suceava</div><div class="dropdown-option" data-value="JudecatoriaTARGOVISTE">Judecătoria Târgoviște</div><div class="dropdown-option" data-value="JudecatoriaTARGUJIU">Judecătoria Târgu Jiu</div><div class="dropdown-option" data-value="JudecatoriaTARGUMURES">Judecătoria Târgu Mureș</div><div class="dropdown-option" data-value="JudecatoriaTIMISOARA">Judecătoria Timișoara</div><div class="dropdown-option" data-value="JudecatoriaTULCEA">Judecătoria Tulcea</div><div class="dropdown-option" data-value="JudecatoriaVASLUI">Judecătoria Vaslui</div><div class="dropdown-option" data-value="JudecatoriaZALAU">Judecătoria Zalău</div><div class="dropdown-option" data-value="TribunalulComercialBUCURESTI">Tribunalul Comercial București</div><div class="dropdown-option" data-value="TribunalulComercialCLUJ">Tribunalul Comercial Cluj</div><div class="dropdown-option" data-value="TribunalulComercialCONSTANTA">Tribunalul Comercial Constanța</div><div class="dropdown-option" data-value="TribunalulComercialTIMISOARA">Tribunalul Comercial Timișoara</div><div class="dropdown-option" data-value="TribunalulMilitarBUCURESTI">Tribunalul Militar București</div><div class="dropdown-option" data-value="TribunalulMilitarCLUJ">Tribunalul Militar Cluj</div><div class="dropdown-option" data-value="TribunalulPentruMinoriSiFamilieBUCURESTI">Tribunalul pentru Minori și Familie București</div><div class="dropdown-option" data-value="TribunalulPentruMinoriSiFamilieCLUJ">Tribunalul pentru Minori și Familie Cluj</div><div class="dropdown-option" data-value="CurteadeApelBUCURESTISECTIACOMERCIALA">Curtea de Apel București - Secția Comercială</div><div class="dropdown-option" data-value="CurteadeApelBUCURESTISECTIAPENALA">Curtea de Apel București - Secția Penală</div><div class="dropdown-option" data-value="CurteadeApelBUCURESTISECTIACIVILA">Curtea de Apel București - Secția Civilă</div><div class="dropdown-option" data-value="JudecatoriaBUCURESTI">Judecătoria București</div><div class="dropdown-option" data-value="TribunalulMUNICIPIULUIBUCURESTI">Tribunalul Municipiului București</div><div class="dropdown-option" data-value="JudecatoriaCAMPINA">Judecătoria Câmpina</div><div class="dropdown-option" data-value="JudecatoriaCOMPIEGNE">Judecătoria Compiègne</div><div class="dropdown-option" data-value="JudecatoriaDROBETATURNUSEVERIN">Judecătoria Drobeta-Turnu Severin</div><div class="dropdown-option" data-value="JudecatoriaHUNEDOARA">Judecătoria Hunedoara</div><div class="dropdown-option" data-value="JudecatoriaMANGALIA">Judecătoria Mangalia</div><div class="dropdown-option" data-value="JudecatoriaMEDGIDIA">Judecătoria Medgidia</div><div class="dropdown-option" data-value="JudecatoriaNAVODARI">Judecătoria Năvodari</div><div class="dropdown-option" data-value="JudecatoriaOTOPENI">Judecătoria Otopeni</div><div class="dropdown-option" data-value="JudecatoriaPANTELIMON">Judecătoria Pantelimon</div><div class="dropdown-option" data-value="JudecatoriaRAMNICUVALCEA">Judecătoria Râmnicu Vâlcea</div><div class="dropdown-option" data-value="JudecatoriaROMAN">Judecătoria Roman</div><div class="dropdown-option" data-value="JudecatoriaTURDA">Judecătoria Turda</div><div class="dropdown-option" data-value="JudecatoriaVOLUNTARI">Judecătoria Voluntari</div>                                            </div>
                                            <!-- Hidden input to store the actual selected value -->
                                            <input type="hidden" id="institutie" name="institutie" value="">
                                        </div>

                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Selectați instanța pentru a filtra rezultatele după instanța judecătorească specifică
                                        </small>
                                        <small class="form-text text-warning" id="institutionWarning" style="display: none;">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            Unele coduri de instituții pot să nu fie recunoscute de API-ul SOAP. În acest caz, filtrarea se va face local.
                                        </small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="categorieInstanta" class="form-label">
                                            <i class="fas fa-filter me-1"></i>
                                            Categorie instanță:
                                        </label>
                                        <select class="form-select" id="categorieInstanta" name="categorieInstanta">
                                            <option value="">-- Toate categoriile --</option>
                                            <option value="curtea_suprema" >Înalta Curte de Casație și Justiție</option>
                                            <option value="curte_apel" >Curți de Apel</option>
                                            <option value="tribunal" >Tribunale</option>
                                            <option value="judecatorie" >Judecătorii</option>
                                        </select>
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Filtrați după tipul de instanță pentru a restrânge opțiunile
                                        </small>
                                    </div>
                                </div>

                                <!-- Second Row of Advanced Filters -->
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="categorieCaz" class="form-label">
                                            <i class="fas fa-gavel me-1"></i>
                                            Categorie caz:
                                        </label>

                                        <!-- Enhanced Searchable Dropdown for Case Categories -->
                                        <div class="searchable-dropdown" id="categorieCazDropdown">
                                            <input type="text"
                                                   class="form-control dropdown-search-input"
                                                   id="categorieCazSearch"
                                                   placeholder="Căutați sau selectați categoria..."
                                                   autocomplete="off"
                                                   readonly>
                                            <div class="dropdown-options" id="categorieCazOptions">
                                                <div class="dropdown-option" data-value="">-- Toate categoriile --</div>
                                                <div class="dropdown-option" data-value="civil" >Civil</div>
                                                <div class="dropdown-option" data-value="penal" >Penal</div>
                                                <div class="dropdown-option" data-value="comercial" >Comercial</div>
                                                <div class="dropdown-option" data-value="contencios_administrativ" >Contencios Administrativ</div>
                                                <div class="dropdown-option" data-value="fiscal" >Fiscal</div>
                                                <div class="dropdown-option" data-value="munca" >Muncă și Asigurări Sociale</div>
                                                <div class="dropdown-option" data-value="familie" >Familie și Minori</div>
                                                <div class="dropdown-option" data-value="executare" >Executare</div>
                                                <div class="dropdown-option" data-value="insolventa" >Insolvență</div>
                                                <div class="dropdown-option" data-value="litigii_profesionisti" >Litigii cu profesioniștii</div>
                                            </div>
                                            <!-- Hidden input to store the actual selected value -->
                                            <input type="hidden" id="categorieCaz" name="categorieCaz" value="">
                                        </div>

                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Filtrați după categoria juridică a cazului
                                        </small>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="dataInceput" class="form-label">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            Data început:
                                        </label>
                                        <input type="text"
                                               class="form-control date-input"
                                               id="dataInceput"
                                               name="dataInceput"
                                               placeholder="ZZ.LL.AAAA (ex: 15.03.2023)"
                                               value=""
                                               pattern="\d{1,2}\.\d{1,2}\.\d{4}"
                                               title="Introduceți data în format ZZ.LL.AAAA">
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Data de început pentru intervalul de căutare
                                        </small>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="dataSfarsit" class="form-label">
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            Data sfârșit:
                                        </label>
                                        <input type="text"
                                               class="form-control date-input"
                                               id="dataSfarsit"
                                               name="dataSfarsit"
                                               placeholder="ZZ.LL.AAAA (ex: 31.12.2023)"
                                               value=""
                                               pattern="\d{1,2}\.\d{1,2}\.\d{4}"
                                               title="Introduceți data în format ZZ.LL.AAAA">
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Data de sfârșit pentru intervalul de căutare
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <!-- Mobile Search Container - Only visible on mobile when filters are expanded -->
                            <div class="mobile-search-container">
                                <button type="submit" class="btn btn-primary" form="bulkSearchForm">
                                    <i class="fas fa-search me-2"></i>
                                    Caută
                                </button>

                                <!-- Export Buttons for Mobile - Mobile Version -->
                                                                    <div class="export-buttons-inline export-buttons-mobile">
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-success export-btn" id="csvExportBtnMobile" onclick="exportBulkResults('csv')">
                                                <i class="fas fa-file-csv me-1"></i>
                                                <span class="export-text">Export CSV</span>
                                                <span class="filter-indicator" style="display: none;">
                                                    <i class="fas fa-filter ms-1"></i>
                                                </span>
                                            </button>
                                            <button type="button" class="btn btn-info export-btn" id="excelExportBtnMobile" onclick="exportBulkResults('xlsx')">
                                                <i class="fas fa-file-excel me-1"></i>
                                                <span class="export-text">Export Excel</span>
                                                <span class="filter-indicator" style="display: none;">
                                                    <i class="fas fa-filter ms-1"></i>
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                                            </div>
                        </form>
                    </div>
                </div>

                
                    <!-- Results Section -->
                    <div class="results-section">
                                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    Rezultate detaliate
                                </h5>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="expandAllResults()">
                                        <i class="fas fa-expand-alt me-1"></i>
                                        Expandează toate
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAllResults()">
                                        <i class="fas fa-compress-alt me-1"></i>
                                        Restrânge toate
                                    </button>
                                </div>
                            </div>
                        
                                                    <div class="term-results">
                                <div class="term-header" onclick="toggleTermResults(0)">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">
                                                <i class="fas fa-search me-2"></i>
                                                130/98/2022                                                                                                <span class="badge bg-success ms-2" title="Tip detectat automat">
                                                    <i class="fas fa-folder me-1"></i>
                                                    Număr dosar                                                </span>
                                            </h6>
                                            <small class="text-muted">
                                                <span id="resultMessage0" data-term="130/98/2022" data-original-count="2">
                                                    2 rezultate găsite pentru termenul '130/98/2022'                                                </span>
                                                                                            </small>
                                        </div>
                                        <div>
                                            <i class="fas fa-chevron-down toggle-icon" id="toggleIcon0"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="term-content" id="termContent0" style="display: none;">
                                                                            <!-- Desktop/Tablet Table View -->
                                        <div class="table-container table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead>
                                                    <tr>
                                                        <th class="sortable-header" data-column="numar" data-term-index="0">
                                                            Număr Dosar
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="instanta" data-term-index="0">
                                                            Instanță
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="obiect" data-term-index="0">
                                                            Obiect
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="stadiu" data-term-index="0">
                                                            Stadiu Procesual
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="data" data-term-index="0">
                                                            Data
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="dataModificare" data-term-index="0">
                                                            Ultima Modificare
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="categorie" data-term-index="0">
                                                            Categorie caz
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="nume" data-term-index="0">
                                                            Nume Parte
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th class="sortable-header" data-column="calitate" data-term-index="0">
                                                            Calitate
                                                            <span class="sort-indicator sort-none"></span>
                                                        </th>
                                                        <th>Acțiuni</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                                                                            <br />
<font size='1'><table class='xdebug-error xe-uncaught-exception' dir='ltr' border='1' cellspacing='0' cellpadding='1'>
<tr><th align='left' bgcolor='#f57900' colspan="5"><span style='background-color: #cc0000; color: #fce94f; font-size: x-large;'>( ! )</span> Fatal error: Uncaught Error: Cannot use object of type stdClass as array in C:\wamp64\www\just\index.php on line <i>1205</i></th></tr>
<tr><th align='left' bgcolor='#f57900' colspan="5"><span style='background-color: #cc0000; color: #fce94f; font-size: x-large;'>( ! )</span> Error: Cannot use object of type stdClass as array in C:\wamp64\www\just\index.php on line <i>1205</i></th></tr>
<tr><th align='left' bgcolor='#e9b96e' colspan='5'>Call Stack</th></tr>
<tr><th align='center' bgcolor='#eeeeec'>#</th><th align='left' bgcolor='#eeeeec'>Time</th><th align='left' bgcolor='#eeeeec'>Memory</th><th align='left' bgcolor='#eeeeec'>Function</th><th align='left' bgcolor='#eeeeec'>Location</th></tr>
<tr><td bgcolor='#eeeeec' align='center'>1</td><td bgcolor='#eeeeec' align='center'>0.0139</td><td bgcolor='#eeeeec' align='right'>657144</td><td bgcolor='#eeeeec'>{main}(  )</td><td title='C:\wamp64\www\just\index.php' bgcolor='#eeeeec'>...\index.php<b>:</b>0</td></tr>
<tr><td bgcolor='#eeeeec' align='center'>2</td><td bgcolor='#eeeeec' align='center'>0.8701</td><td bgcolor='#eeeeec' align='right'>1067256</td><td bgcolor='#eeeeec'>getRelevantPartyName( <span>$parti = </span><span>[0 =&gt; class stdClass { public $nume = &#39;CONSTANTIN VASILICA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 1 =&gt; class stdClass { public $nume = &#39;FILIPACHE ALEXANDRU&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 2 =&gt; class stdClass { public $nume = &#39;FILIPACHE ELENA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 3 =&gt; class stdClass { public $nume = &#39;PARASCHIV ELENA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 4 =&gt; class stdClass { public $nume = &#39;POPESCU CONSTANTIN&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 5 =&gt; class stdClass { public $nume = &#39;POPESCU STANA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 6 =&gt; class stdClass { public $nume = &#39;POPIAG DORINA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 7 =&gt; class stdClass { public $nume = &#39;POPOVICI ŞTEFANIA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 8 =&gt; class stdClass { public $nume = &#39;PUŞCOI TIŢA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 9 =&gt; class stdClass { public $nume = &#39;RADU SORICA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 10 =&gt; class stdClass { public $nume = &#39;RĂCEANU NELA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 11 =&gt; class stdClass { public $nume = &#39;SAMIT STERE&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 12 =&gt; class stdClass { public $nume = &#39;ŞERBAN MIHAELA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 13 =&gt; class stdClass { public $nume = &#39;VOINEA VASILICA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 14 =&gt; class stdClass { public $nume = &#39;ZAHARIA MIRELA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 15 =&gt; class stdClass { public $nume = &#39;ZAINEA COSTIN&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 16 =&gt; class stdClass { public $nume = &#39;ZAINA IULIA GABRIELA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 17 =&gt; class stdClass { public $nume = &#39;ZAINEA MANUELA CORNELIA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 18 =&gt; class stdClass { public $nume = &#39;ZAINEA NICOLAE&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 19 =&gt; class stdClass { public $nume = &#39;ZAMFIRACHE IOANA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 20 =&gt; class stdClass { public $nume = &#39;ZAINESCU EMILIA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 21 =&gt; class stdClass { public $nume = &#39;ZIDPRESCU MIRELA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 22 =&gt; class stdClass { public $nume = &#39;MĂCELARU VANGHELITA NICOLAETA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 23 =&gt; class stdClass { public $nume = &#39;MĂCELARU ALEXANDRU&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 24 =&gt; class stdClass { public $nume = &#39;SIMION DUMITRU&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 25 =&gt; class stdClass { public $nume = &#39;ARON VIORICA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 26 =&gt; class stdClass { public $nume = &#39;MĂCEAŞA NICULINA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 27 =&gt; class stdClass { public $nume = &#39;MĂCEAŞA PETRE&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 28 =&gt; class stdClass { public $nume = &#39;VULCAN GEORGETA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 29 =&gt; class stdClass { public $nume = &#39;STOICA ION&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 30 =&gt; class stdClass { public $nume = &#39;COSTACHE PAULA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 31 =&gt; class stdClass { public $nume = &#39;CRÂNGAŞU DOMNICA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 32 =&gt; class stdClass { public $nume = &#39;NAUM DUMITRU&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 33 =&gt; class stdClass { public $nume = &#39;TEODORESCU ECATERINA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 34 =&gt; class stdClass { public $nume = &#39;VULCAN GEORGETA, STOICA ION, MODOLEA (FOSTĂ STOICA) OANA, MODOLEA ANDREI OCTAVIAN, COSTACHE PAULA, CRÂNGAŞU DOMNICA, NAUM DUMITRU, TEODORESCU ECATERINA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 35 =&gt; class stdClass { public $nume = &#39;CRISTEA DANIELA, DUNEL MARIA, DUNEL CONSTANTIN, STANCU BOGDAN&#39;; public $calitate = &#39;Intimat Intervenient în numele altei persoane&#39;; public $source = &#39;soap_api&#39; }, 36 =&gt; class stdClass { public $nume = &#39;ŢÎMBALARIU ELENA, STANCIU ENE, CÂRSTEA ELENA, SAVU ALEXANDRINA, TRIFU IOANA, CEACU MAGDALENA, BOŞCĂ MARIA, PÂRVU TEODORA, CÂRNU VICTORIA&#39;; public $calitate = &#39;Intimat Intervenient în numele altei persoane&#39;; public $source = &#39;soap_api&#39; }, 37 =&gt; class stdClass { public $nume = &#39;AGAPIE CRISTINA, AGAPIE ŞTEFAN&#39;; public $calitate = &#39;Petent&#39;; public $source = &#39;soap_api&#39; }, 38 =&gt; class stdClass { public $nume = &#39;ŞERBĂNESCU ELENA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 39 =&gt; class stdClass { public $nume = &#39;PASCU NICOLETA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 40 =&gt; class stdClass { public $nume = &#39;ASOCIAŢIA CAR ÎNVĂŢĂMÂNT SLOBOZIA&#39;; public $calitate = &#39;Intimat Pârât&#39;; public $source = &#39;soap_api&#39; }, 41 =&gt; class stdClass { public $nume = &#39;ALECU NICULAE&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 42 =&gt; class stdClass { public $nume = &#39;ACHIM MARILENA, ALECU NICULAE S.A.&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 43 =&gt; class stdClass { public $nume = &#39;ALEXANDRESCU BOGDAN IONUŢ&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 44 =&gt; class stdClass { public $nume = &#39;ALEXE LIXANDRA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 45 =&gt; class stdClass { public $nume = &#39;ALEXE MARIA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 46 =&gt; class stdClass { public $nume = &#39;ANAGNOSTE GHEORGHE&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 47 =&gt; class stdClass { public $nume = &#39;ARDELEANU ADRIANA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 48 =&gt; class stdClass { public $nume = &#39;ARDELEAN AMALIA LOREDANA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 49 =&gt; class stdClass { public $nume = &#39;ARITON ZINICA&#39;; public $calitate = &#39;Intimat Reclamant&#39;; public $source = &#39;soap_api&#39; }, 50 =&gt; class stdClass { public $nume = &#39;ARON BOGDAN CRISTIAN&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 51 =&gt; class stdClass { public $nume = &#39;AVRAM GEORGETA&#39;; public $calitate = &#39;Intimat Reclamant&#39;; public $source = &#39;soap_api&#39; }, 52 =&gt; class stdClass { public $nume = &#39;BACIU SONIA ŞTEFANIA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 53 =&gt; class stdClass { public $nume = &#39;BADEA EMIL&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 54 =&gt; class stdClass { public $nume = &#39;BADEA MIRELA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 55 =&gt; class stdClass { public $nume = &#39;BALA CĂTĂLIN DUMITRU&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 56 =&gt; class stdClass { public $nume = &#39;BALA NICOLAE&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 57 =&gt; class stdClass { public $nume = &#39;BALA ZOIŢA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 58 =&gt; class stdClass { public $nume = &#39;BARBU ADRIAN CRISTIAN&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 59 =&gt; class stdClass { public $nume = &#39;BARBU MIHAIL&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 60 =&gt; class stdClass { public $nume = &#39;BARBU VASILICA STELUŢA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 61 =&gt; class stdClass { public $nume = &#39;BĂDIC ANGELA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 62 =&gt; class stdClass { public $nume = &#39;BĂILĂ NICOLAE EUGEN&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 63 =&gt; class stdClass { public $nume = &#39;BĂLAN ALEXANDRA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 64 =&gt; class stdClass { public $nume = &#39;VULCAN GEORGETA, ş.a.-la av.Turache Ramona&#39;; public $calitate = &#39;Intimat Reclamant&#39;; public $source = &#39;soap_api&#39; }, 65 =&gt; class stdClass { public $nume = &#39;BĂLAN DANIEL&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 66 =&gt; class stdClass { public $nume = &#39;BĂNICĂ GABRIELA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 67 =&gt; class stdClass { public $nume = &#39;BĂNICĂ GENICA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 68 =&gt; class stdClass { public $nume = &#39;BĂTRÎNU CONSTANTIN&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 69 =&gt; class stdClass { public $nume = &#39;BĂTRÎNU MARIA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 70 =&gt; class stdClass { public $nume = &#39;BIVOLARU DUMITRU&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 71 =&gt; class stdClass { public $nume = &#39;BÎRSAN ANDREI VLĂDUŢ&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 72 =&gt; class stdClass { public $nume = &#39;BÎRSAN MARIA MAGDALENA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 73 =&gt; class stdClass { public $nume = &#39;BOERESCU EMILIA ADRIANA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 74 =&gt; class stdClass { public $nume = &#39;BOTEA GEORGETA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 75 =&gt; class stdClass { public $nume = &#39;BOSNEANU VICTORIA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 76 =&gt; class stdClass { public $nume = &#39;BRATU ELENA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 77 =&gt; class stdClass { public $nume = &#39;BRATU IULIANA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 78 =&gt; class stdClass { public $nume = &#39;BRĂCACI EUGENIA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 79 =&gt; class stdClass { public $nume = &#39;BURDULEA ELENA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 80 =&gt; class stdClass { public $nume = &#39;BURDUŞELU MIHAI CĂTĂLIN&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 81 =&gt; class stdClass { public $nume = &#39;BURDUŞELU TUDORIŢA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 82 =&gt; class stdClass { public $nume = &#39;CARNICIU ELENA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 83 =&gt; class stdClass { public $nume = &#39;CÂLŢEA LICĂ&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 84 =&gt; class stdClass { public $nume = &#39;CHECIU IONELA LUCIANA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 85 =&gt; class stdClass { public $nume = &#39;CHIRIŢĂ VIORICA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 86 =&gt; class stdClass { public $nume = &#39;CHIŢU EUGENIA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 87 =&gt; class stdClass { public $nume = &#39;CHIŢU GHEORGHE&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 88 =&gt; class stdClass { public $nume = &#39;CIOBAN GABRIEL STELIAN&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 89 =&gt; class stdClass { public $nume = &#39;CIOCHINARU ELENA&#39;; public $calitate = &#39;Intimat Reclamant&#39;; public $source = &#39;soap_api&#39; }, 90 =&gt; class stdClass { public $nume = &#39;Anonimizat 1&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 91 =&gt; class stdClass { public $nume = &#39;CÎRCIUMARU DANA ANDREEA&#39;; public $calitate = &#39;Intimat Reclamant&#39;; public $source = &#39;soap_api&#39; }, 92 =&gt; class stdClass { public $nume = &#39;COJOCARU ION&#39;; public $calitate = &#39;Intimat Reclamant&#39;; public $source = &#39;soap_api&#39; }, 93 =&gt; class stdClass { public $nume = &#39;COMŞA ILEANA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 94 =&gt; class stdClass { public $nume = &#39;CONSTANTIN ELEONORA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 95 =&gt; class stdClass { public $nume = &#39;CONSTANTIN IOANA&#39;; public $calitate = &#39;Intimat Reclamant&#39;; public $source = &#39;soap_api&#39; }, 96 =&gt; class stdClass { public $nume = &#39;CONSTANTIN SĂNDICA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 97 =&gt; class stdClass { public $nume = &#39;CONSTANTIN TUDOREL&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 98 =&gt; class stdClass { public $nume = &#39;CONSTANTINESCU OCTAVIAN FLORIN&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 99 =&gt; class stdClass { public $nume = &#39;CORNĂŢEANU ŞTEFANIA&#39;; public $calitate = &#39;Apelant Reclamant&#39;; public $source = &#39;soap_api&#39; }, 100 =&gt; class stdClass { public $nume = &#39;Aron Bogdan-Cristian&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 101 =&gt; class stdClass { public $nume = &#39;Barbu Adrian-Cristian&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 102 =&gt; class stdClass { public $nume = &#39;Barbu Vasilica-Steluţa&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 103 =&gt; class stdClass { public $nume = &#39;Boerescu Emilia-Adriana&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 104 =&gt; class stdClass { public $nume = &#39;Chira (fostă Graur) Adina-Tatiana&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 105 =&gt; class stdClass { public $nume = &#39;Cioban Gabriel-Stelian&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 106 =&gt; class stdClass { public $nume = &#39;Copilău Gheorghe&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 107 =&gt; class stdClass { public $nume = &#39;Creţu Aurica-Lili&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 108 =&gt; class stdClass { public $nume = &#39;Creţu Neagu-Stelian&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 109 =&gt; class stdClass { public $nume = &#39;Danciu Elena&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 110 =&gt; class stdClass { public $nume = &#39;Filimon Constantin&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 111 =&gt; class stdClass { public $nume = &#39;Ghimpe Neculai&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 112 =&gt; class stdClass { public $nume = &#39;Godeanu Frusina&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 113 =&gt; class stdClass { public $nume = &#39;Goţea Ionela-Nicoleta&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 114 =&gt; class stdClass { public $nume = &#39;Graur Nina&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 115 =&gt; class stdClass { public $nume = &#39;Iancu Marioara&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 116 =&gt; class stdClass { public $nume = &#39;Iancu Tănase&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 117 =&gt; class stdClass { public $nume = &#39;Ion Mariana&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 118 =&gt; class stdClass { public $nume = &#39;Ion Paraschiv&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 119 =&gt; class stdClass { public $nume = &#39;Ion Paul-Marius&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 120 =&gt; class stdClass { public $nume = &#39;Iorga Elena-Chiriaca&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 121 =&gt; class stdClass { public $nume = &#39;Lemnaru Neculai&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 122 =&gt; class stdClass { public $nume = &#39;Lupa Anghelina&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 123 =&gt; class stdClass { public $nume = &#39;Nan Barbu&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 124 =&gt; class stdClass { public $nume = &#39;Năstase Alexandra-Florina&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 125 =&gt; class stdClass { public $nume = &#39;Necula Ioana&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 126 =&gt; class stdClass { public $nume = &#39;Oprea Costica&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, 127 =&gt; class stdClass { public $nume = &#39;Panţuru Ion&#39;; public $calitate = &#39;Apelant&#39;; public $source = &#39;decision_text&#39; }, ...]</span>, <span>$searchTerm = </span><span>&#39;130/98/2022&#39;</span>, <span>$searchType = </span><span>&#39;numarDosar&#39;</span> )</td><td title='C:\wamp64\www\just\index.php' bgcolor='#eeeeec'>...\index.php<b>:</b>4646</td></tr>
</table></font>
