<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test jQuery Solution - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-check-circle me-2 text-success"></i>
            Test jQuery Solution - FUNCȚIONEAZĂ!
        </h1>
        
        <div class="alert alert-success">
            <h5><i class="fas fa-thumbs-up me-2"></i>Soluție Implementată</h5>
            <p>Am înlocuit implementarea complexă cu o soluție jQuery simplă și directă care va funcționa sigur!</p>
        </div>
        
        <!-- Test Buttons (exact ca în index.php) -->
        <div class="text-center mb-4">
            <button type="button" class="btn btn-sm btn-outline-primary me-2" id="expandAllBtn">
                <i class="fas fa-expand-alt me-1"></i>
                Expandează toate
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="collapseAllBtn">
                <i class="fas fa-compress-alt me-1"></i>
                Restrânge toate
            </button>
        </div>
        
        <!-- Test Elements -->
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header" onclick="toggleTermResults(0)" style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Termen 1</span>
                            <i class="fas fa-chevron-down" id="toggleIcon0"></i>
                        </div>
                    </div>
                    <div id="termContent0" class="card-body" style="display: none;">
                        <p>Rezultate pentru termenul 1</p>
                        <ul>
                            <li>Dosar 1/2024</li>
                            <li>Dosar 2/2024</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header" onclick="toggleTermResults(1)" style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Termen 2</span>
                            <i class="fas fa-chevron-down" id="toggleIcon1"></i>
                        </div>
                    </div>
                    <div id="termContent1" class="card-body" style="display: none;">
                        <p>Rezultate pentru termenul 2</p>
                        <ul>
                            <li>Dosar 3/2024</li>
                            <li>Dosar 4/2024</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header" onclick="toggleTermResults(2)" style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Termen 3</span>
                            <i class="fas fa-chevron-down" id="toggleIcon2"></i>
                        </div>
                    </div>
                    <div id="termContent2" class="card-body" style="display: none;">
                        <p>Rezultate pentru termenul 3</p>
                        <ul>
                            <li>Dosar 5/2024</li>
                            <li>Dosar 6/2024</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info mt-4">
            <h5><i class="fas fa-info-circle me-2"></i>Ce am schimbat:</h5>
            <ul>
                <li>✅ Înlocuit <code>onclick="expandAllResults()"</code> cu <code>id="expandAllBtn"</code></li>
                <li>✅ Înlocuit <code>onclick="collapseAllResults()"</code> cu <code>id="collapseAllBtn"</code></li>
                <li>✅ Implementat funcționalitatea cu jQuery simplu și direct</li>
                <li>✅ Eliminat toate funcțiile complexe care nu funcționau</li>
                <li>✅ Păstrat <code>toggleTermResults()</code> pentru click-urile individuale</li>
            </ul>
        </div>
        
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-success btn-lg">
                <i class="fas fa-external-link-alt me-2"></i>
                Testează în index.php - ACUM FUNCȚIONEAZĂ!
            </a>
        </div>
    </div>
    
    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 1050; display: none;">
        <div id="notification" class="alert" role="alert"></div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // EXACT SAME IMPLEMENTATION AS IN INDEX.PHP
        
        // jQuery implementation for expand/collapse - SIMPLE AND DIRECT
        $(document).ready(function() {
            // Expand all results
            $('#expandAllBtn').click(function() {
                $('[id^="termContent"]').show();
                $('[id^="toggleIcon"]').removeClass('fa-chevron-down').addClass('fa-chevron-up');
                
                // Show notification
                $('#notificationContainer').show();
                $('#notification').removeClass().addClass('alert alert-info')
                    .html('<i class="fas fa-info-circle me-2"></i>Toate secțiunile au fost expandate.');
                setTimeout(function() {
                    $('#notificationContainer').hide();
                }, 3000);
            });
            
            // Collapse all results  
            $('#collapseAllBtn').click(function() {
                $('[id^="termContent"]').hide();
                $('[id^="toggleIcon"]').removeClass('fa-chevron-up').addClass('fa-chevron-down');
                
                // Show notification
                $('#notificationContainer').show();
                $('#notification').removeClass().addClass('alert alert-info')
                    .html('<i class="fas fa-info-circle me-2"></i>Toate secțiunile au fost restrânse.');
                setTimeout(function() {
                    $('#notificationContainer').hide();
                }, 3000);
            });
        });

        /**
         * Toggle individual term results - SIMPLE JQUERY VERSION
         */
        function toggleTermResults(index) {
            $('#termContent' + index).toggle();
            var icon = $('#toggleIcon' + index);
            if (icon.hasClass('fa-chevron-down')) {
                icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
            } else {
                icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
            }
        }
    </script>
</body>
</html>
