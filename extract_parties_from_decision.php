<?php
/**
 * Extract parties from court decision text to verify total count
 */

require_once 'config/config.php';

$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "=== EXTRACTING PARTIES FROM COURT DECISION ===" . PHP_EOL;
echo "Case: $numarDosar" . PHP_EOL;
echo "Institution: $institutie" . PHP_EOL;
echo PHP_EOL;

try {
    // Read the SOAP response file
    $responseFile = 'soap_response_2025-07-04_17-14-14.xml';
    if (!file_exists($responseFile)) {
        echo "ERROR: SOAP response file not found: $responseFile" . PHP_EOL;
        exit;
    }
    
    $xmlContent = file_get_contents($responseFile);

    // Extract solutieSumar content using regex (avoid XML parsing issues)
    if (preg_match('/<solutieSumar>(.*?)<\/solutieSumar>/s', $xmlContent, $matches)) {
        $solutieSumar = $matches[1];
    } else {
        echo "ERROR: Could not find solutieSumar in XML" . PHP_EOL;
        exit;
    }
    
    echo "=== COURT DECISION ANALYSIS ===" . PHP_EOL;
    echo "Decision summary length: " . strlen($solutieSumar) . " characters" . PHP_EOL;
    echo PHP_EOL;
    
    // Extract the section with creditor names
    // Look for the pattern that lists all creditors
    $pattern = '/formulate de creditorii ([^;]+(?:;[^;]+)*)/';
    
    if (preg_match($pattern, $solutieSumar, $matches)) {
        $creditorsText = $matches[1];
        
        echo "Found creditors section:" . PHP_EOL;
        echo "Length: " . strlen($creditorsText) . " characters" . PHP_EOL;
        echo PHP_EOL;
        
        // Split by semicolon and clean up names
        $creditorNames = explode(';', $creditorsText);
        $cleanedNames = [];
        
        foreach ($creditorNames as $name) {
            $name = trim($name);
            // Remove common prefixes and suffixes
            $name = preg_replace('/\s*\(date\)\s*$/', '', $name);
            $name = preg_replace('/^\s*și\s+/', '', $name);
            $name = trim($name);
            
            if (!empty($name) && strlen($name) > 2) {
                $cleanedNames[] = $name;
            }
        }
        
        echo "=== CREDITORS FROM COURT DECISION ===" . PHP_EOL;
        echo "Total creditors found: " . count($cleanedNames) . PHP_EOL;
        echo PHP_EOL;
        
        echo "First 20 creditors:" . PHP_EOL;
        for ($i = 0; $i < min(20, count($cleanedNames)); $i++) {
            echo ($i + 1) . ". " . $cleanedNames[$i] . PHP_EOL;
        }
        
        if (count($cleanedNames) > 40) {
            echo "..." . PHP_EOL;
            echo "Last 20 creditors:" . PHP_EOL;
            $start = max(0, count($cleanedNames) - 20);
            for ($i = $start; $i < count($cleanedNames); $i++) {
                echo ($i + 1) . ". " . $cleanedNames[$i] . PHP_EOL;
            }
        }
        
        // Compare with SOAP API parties
        echo PHP_EOL . "=== COMPARISON WITH SOAP API PARTIES ===" . PHP_EOL;

        // Extract parties from SOAP API response using regex
        $soapParties = [];
        if (preg_match_all('/<nume>(.*?)<\/nume>/', $xmlContent, $numeMatches)) {
            $soapParties = $numeMatches[1];
        }
        
        echo "SOAP API parties count: " . count($soapParties) . PHP_EOL;
        echo "Court decision creditors count: " . count($cleanedNames) . PHP_EOL;
        echo "Difference: " . (count($cleanedNames) - count($soapParties)) . PHP_EOL;
        echo PHP_EOL;
        
        // Check if SOAP parties are a subset of decision creditors
        $soapInDecision = 0;
        $decisionInSoap = 0;
        
        foreach ($soapParties as $soapParty) {
            $found = false;
            foreach ($cleanedNames as $decisionName) {
                if (stripos($decisionName, $soapParty) !== false || 
                    stripos($soapParty, $decisionName) !== false) {
                    $found = true;
                    break;
                }
            }
            if ($found) {
                $soapInDecision++;
            }
        }
        
        echo "SOAP parties found in decision: $soapInDecision / " . count($soapParties) . PHP_EOL;
        
        // Find parties in decision but not in SOAP
        $missingFromSoap = [];
        foreach ($cleanedNames as $decisionName) {
            $found = false;
            foreach ($soapParties as $soapParty) {
                if (stripos($decisionName, $soapParty) !== false || 
                    stripos($soapParty, $decisionName) !== false) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $missingFromSoap[] = $decisionName;
            }
        }
        
        echo "Parties in decision but missing from SOAP API: " . count($missingFromSoap) . PHP_EOL;
        
        if (count($missingFromSoap) > 0) {
            echo PHP_EOL . "First 10 missing parties:" . PHP_EOL;
            for ($i = 0; $i < min(10, count($missingFromSoap)); $i++) {
                echo ($i + 1) . ". " . $missingFromSoap[$i] . PHP_EOL;
            }
        }
        
    } else {
        echo "Could not find creditors section in court decision" . PHP_EOL;
        
        // Try alternative patterns
        echo "Trying alternative extraction methods..." . PHP_EOL;
        
        // Look for any pattern with multiple names separated by semicolons
        if (preg_match_all('/([A-ZĂÂÎȘȚ][A-ZĂÂÎȘȚ\s\-]+)\s*;/u', $solutieSumar, $allMatches)) {
            $alternativeNames = array_unique($allMatches[1]);
            echo "Found " . count($alternativeNames) . " potential party names using alternative method" . PHP_EOL;
            
            echo "Sample names:" . PHP_EOL;
            for ($i = 0; $i < min(10, count($alternativeNames)); $i++) {
                echo ($i + 1) . ". " . trim($alternativeNames[$i]) . PHP_EOL;
            }
        }
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . PHP_EOL;
}

echo PHP_EOL . "=== ANALYSIS COMPLETE ===" . PHP_EOL;
?>
