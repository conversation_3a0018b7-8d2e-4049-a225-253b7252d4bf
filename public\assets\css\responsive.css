/* 
 * Portal Judiciar - Stiluri responsive
 */

/* Extra small devices (phones, less than 576px) */
@media (max-width: 575.98px) {
    header h1 {
        font-size: 1.25rem;
    }
    
    .nav-link {
        padding: 0.25rem 0.5rem;
    }
    
    .card-title {
        font-size: 1rem;
    }
    
    .table-responsive {
        margin-bottom: 0;
    }
    
    .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .breadcrumb {
        font-size: 0.875rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
    
    /* Transformăm tabelele în format card pe dispozitive mobile */
    .table-responsive-card thead {
        display: none;
    }
    
    .table-responsive-card tbody tr {
        display: block;
        margin-bottom: 1rem;
        border: 1px solid #dee2e6;
        border-radius: var(--border-radius);
    }
    
    .table-responsive-card tbody td {
        display: block;
        text-align: right;
        padding: 0.5rem;
        border: none;
        position: relative;
        padding-left: 50%;
    }
    
    .table-responsive-card tbody td:before {
        content: attr(data-label);
        position: absolute;
        left: 0.5rem;
        width: 45%;
        text-align: left;
        font-weight: bold;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    header h1 {
        font-size: 1.5rem;
    }
    
    .card-title {
        font-size: 1.1rem;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .container {
        max-width: 95%;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
    .container {
        max-width: 90%;
    }
}

/* Touch optimization for mobile devices */
@media (pointer: coarse) {
    /* Increase touch target size for better usability */
    .btn, .nav-link, .dropdown-item, a {
        min-height: 44px;
        min-width: 44px;
        display: inline-flex;
        align-items: center;
    }
    
    /* Increase form control height */
    .form-control, .form-select {
        min-height: 44px;
    }
    
    /* Increase spacing between items */
    .nav-item, .form-group, .mb-3 {
        margin-bottom: 1rem !important;
    }
}
