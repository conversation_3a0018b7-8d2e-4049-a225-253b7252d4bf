<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Live Functionality - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .console-log {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 15px;
            border-radius: 8px;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .test-section {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-pass { background-color: #28a745; }
        .status-fail { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-bug me-2 text-primary"></i>
            Live Functionality Test - Portal Judiciar România
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>Testing Protocol</h5>
            <p>This page will test the expand/collapse functionality with the exact same DOM structure as index.php search results.</p>
        </div>
        
        <!-- Test Console -->
        <div class="test-section">
            <h3><i class="fas fa-terminal me-2"></i>Test Console</h3>
            <div id="testConsole" class="console-log">Starting functionality test...\n</div>
            <button class="btn btn-secondary btn-sm mt-2" onclick="clearTestConsole()">
                <i class="fas fa-trash me-1"></i>Clear Console
            </button>
        </div>
        
        <!-- Test Status -->
        <div class="test-section">
            <h3><i class="fas fa-check-circle me-2"></i>Test Status</h3>
            <div id="testStatus">
                <div><span class="status-indicator status-pending"></span>Button Elements Detection</div>
                <div><span class="status-indicator status-pending"></span>JavaScript Function Loading</div>
                <div><span class="status-indicator status-pending"></span>Event Listener Attachment</div>
                <div><span class="status-indicator status-pending"></span>DOM Element Detection</div>
                <div><span class="status-indicator status-pending"></span>Expand All Functionality</div>
                <div><span class="status-indicator status-pending"></span>Collapse All Functionality</div>
                <div><span class="status-indicator status-pending"></span>Individual Toggle Functionality</div>
                <div><span class="status-indicator status-pending"></span>Notification System</div>
            </div>
        </div>
        
        <!-- Exact Replica of index.php Structure -->
        <div class="test-section">
            <h3><i class="fas fa-search me-2"></i>Simulated Search Results</h3>
            
            <!-- Buttons (exact replica from index.php) -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Rezultate detaliate
                </h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-primary me-2" id="expandAllBtn">
                        <i class="fas fa-expand-alt me-1"></i>
                        Expandează toate
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="collapseAllBtn">
                        <i class="fas fa-compress-alt me-1"></i>
                        Restrânge toate
                    </button>
                </div>
            </div>
            
            <!-- Term Results (exact replica from index.php) -->
            <div class="term-results">
                <div class="term-header" onclick="toggleTermResults(0)">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">
                                <i class="fas fa-search me-2"></i>
                                POPESCU
                                <span class="badge bg-primary ms-2">
                                    <i class="fas fa-user me-1"></i>
                                    Nume parte
                                </span>
                            </h6>
                            <small class="text-muted">
                                <span>3 rezultate găsite pentru "POPESCU"</span>
                            </small>
                        </div>
                        <div>
                            <i class="fas fa-chevron-down toggle-icon" id="toggleIcon0"></i>
                        </div>
                    </div>
                </div>
                <div class="term-content" id="termContent0" style="display: none;">
                    <div class="table-container table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Număr Dosar</th>
                                    <th>Instanța</th>
                                    <th>Nume Parte</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1/2024</td>
                                    <td>Judecătoria Sector 1</td>
                                    <td>POPESCU MARIA</td>
                                </tr>
                                <tr>
                                    <td>15/2024</td>
                                    <td>Tribunalul București</td>
                                    <td>POPESCU GHEORGHE</td>
                                </tr>
                                <tr>
                                    <td>23/2024</td>
                                    <td>Curtea de Apel București</td>
                                    <td>SC POPESCU SRL</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="term-results">
                <div class="term-header" onclick="toggleTermResults(1)">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">
                                <i class="fas fa-search me-2"></i>
                                IONESCU
                                <span class="badge bg-primary ms-2">
                                    <i class="fas fa-user me-1"></i>
                                    Nume parte
                                </span>
                            </h6>
                            <small class="text-muted">
                                <span>2 rezultate găsite pentru "IONESCU"</span>
                            </small>
                        </div>
                        <div>
                            <i class="fas fa-chevron-down toggle-icon" id="toggleIcon1"></i>
                        </div>
                    </div>
                </div>
                <div class="term-content" id="termContent1" style="display: none;">
                    <div class="table-container table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Număr Dosar</th>
                                    <th>Instanța</th>
                                    <th>Nume Parte</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>5/2024</td>
                                    <td>Judecătoria Sector 2</td>
                                    <td>IONESCU ELENA</td>
                                </tr>
                                <tr>
                                    <td>12/2024</td>
                                    <td>Tribunalul București</td>
                                    <td>IONESCU ADRIAN</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="term-results">
                <div class="term-header" onclick="toggleTermResults(2)">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">
                                <i class="fas fa-search me-2"></i>
                                BUCURESTI
                                <span class="badge bg-info ms-2">
                                    <i class="fas fa-gavel me-1"></i>
                                    Obiect dosar
                                </span>
                            </h6>
                            <small class="text-muted">
                                <span>4 rezultate găsite pentru "BUCURESTI"</span>
                            </small>
                        </div>
                        <div>
                            <i class="fas fa-chevron-down toggle-icon" id="toggleIcon2"></i>
                        </div>
                    </div>
                </div>
                <div class="term-content" id="termContent2" style="display: none;">
                    <div class="table-container table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Număr Dosar</th>
                                    <th>Instanța</th>
                                    <th>Nume Parte</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>8/2024</td>
                                    <td>Primăria București</td>
                                    <td>CONSTRUCTOR SRL</td>
                                </tr>
                                <tr>
                                    <td>18/2024</td>
                                    <td>Sectorul 1 București</td>
                                    <td>CETĂȚEAN X</td>
                                </tr>
                                <tr>
                                    <td>25/2024</td>
                                    <td>Universitatea București</td>
                                    <td>STUDENT Y</td>
                                </tr>
                                <tr>
                                    <td>30/2024</td>
                                    <td>Spitalul București</td>
                                    <td>PACIENT Z</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Manual Test Controls -->
        <div class="test-section">
            <h3><i class="fas fa-hand-pointer me-2"></i>Manual Test Controls</h3>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-primary w-100 mb-2" onclick="runAutomaticTests()">
                        <i class="fas fa-play me-1"></i>Run Automatic Tests
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-success w-100 mb-2" onclick="testExpandAll()">
                        <i class="fas fa-expand-alt me-1"></i>Test Expand All
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-warning w-100 mb-2" onclick="testCollapseAll()">
                        <i class="fas fa-compress-alt me-1"></i>Test Collapse All
                    </button>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-success btn-lg">
                <i class="fas fa-external-link-alt me-2"></i>
                Test in Live index.php
            </a>
        </div>
    </div>
    
    <!-- Notification Container (exact replica from index.php) -->
    <div id="notificationContainer" class="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 1050; display: none;">
        <div id="notification" class="alert" role="alert"></div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Test console functions
        function logToTest(message) {
            const console = document.getElementById('testConsole');
            const timestamp = new Date().toLocaleTimeString();
            console.textContent += `[${timestamp}] ${message}\n`;
            console.scrollTop = console.scrollHeight;
        }
        
        function clearTestConsole() {
            document.getElementById('testConsole').textContent = 'Console cleared...\n';
        }
        
        function updateTestStatus(testName, passed) {
            const statusDiv = document.getElementById('testStatus');
            const statusLines = statusDiv.children;
            
            for (let line of statusLines) {
                if (line.textContent.includes(testName)) {
                    const indicator = line.querySelector('.status-indicator');
                    indicator.className = 'status-indicator ' + (passed ? 'status-pass' : 'status-fail');
                    break;
                }
            }
        }
        
        // EXACT COPY OF IMPLEMENTATION FROM INDEX.PHP
        
        // ROBUST VANILLA JAVASCRIPT SOLUTION - GUARANTEED TO WORK
        function initExpandCollapseButtons() {
            logToTest('Initializing expand/collapse buttons...');
            
            const expandBtn = document.getElementById('expandAllBtn');
            const collapseBtn = document.getElementById('collapseAllBtn');
            
            updateTestStatus('Button Elements Detection', expandBtn && collapseBtn);
            
            if (expandBtn) {
                expandBtn.addEventListener('click', function() {
                    logToTest('Expand All clicked');
                    
                    // Find all term content elements
                    const termContents = document.querySelectorAll('[id^="termContent"]');
                    const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
                    
                    logToTest('Found ' + termContents.length + ' content elements');
                    
                    // Show all content
                    termContents.forEach(function(element) {
                        element.style.display = 'block';
                    });
                    
                    // Update all icons
                    toggleIcons.forEach(function(icon) {
                        icon.className = 'fas fa-chevron-up toggle-icon';
                    });
                    
                    // Show notification
                    showSimpleNotification('Toate secțiunile au fost expandate.');
                    logToTest('All sections expanded successfully');
                });
                updateTestStatus('Event Listener Attachment', true);
            } else {
                logToTest('ERROR: expandAllBtn not found');
                updateTestStatus('Event Listener Attachment', false);
            }
            
            if (collapseBtn) {
                collapseBtn.addEventListener('click', function() {
                    logToTest('Collapse All clicked');
                    
                    // Find all term content elements
                    const termContents = document.querySelectorAll('[id^="termContent"]');
                    const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
                    
                    logToTest('Found ' + termContents.length + ' content elements');
                    
                    // Hide all content
                    termContents.forEach(function(element) {
                        element.style.display = 'none';
                    });
                    
                    // Update all icons
                    toggleIcons.forEach(function(icon) {
                        icon.className = 'fas fa-chevron-down toggle-icon';
                    });
                    
                    // Show notification
                    showSimpleNotification('Toate secțiunile au fost restrânse.');
                    logToTest('All sections collapsed successfully');
                });
            } else {
                logToTest('ERROR: collapseAllBtn not found');
            }
            
            updateTestStatus('JavaScript Function Loading', true);
        }
        
        // Simple notification function
        function showSimpleNotification(message) {
            const container = document.getElementById('notificationContainer');
            const notification = document.getElementById('notification');
            
            if (container && notification) {
                notification.className = 'alert alert-info';
                notification.innerHTML = '<i class="fas fa-info-circle me-2"></i>' + message;
                container.style.display = 'block';
                
                setTimeout(function() {
                    container.style.display = 'none';
                }, 3000);
                
                logToTest('Notification shown: ' + message);
                updateTestStatus('Notification System', true);
            } else {
                // Fallback to alert if notification elements don't exist
                alert(message);
                logToTest('Fallback alert shown: ' + message);
                updateTestStatus('Notification System', false);
            }
        }

        /**
         * Toggle individual term results - SIMPLE VERSION
         */
        function toggleTermResults(index) {
            logToTest('toggleTermResults called with index: ' + index);
            
            const content = document.getElementById('termContent' + index);
            const icon = document.getElementById('toggleIcon' + index);
            
            if (content) {
                const isVisible = content.style.display !== 'none';
                
                if (isVisible) {
                    content.style.display = 'none';
                    if (icon) icon.className = 'fas fa-chevron-down toggle-icon';
                    logToTest('Section ' + index + ' collapsed');
                } else {
                    content.style.display = 'block';
                    if (icon) icon.className = 'fas fa-chevron-up toggle-icon';
                    logToTest('Section ' + index + ' expanded');
                }
                updateTestStatus('Individual Toggle Functionality', true);
            } else {
                logToTest('ERROR: termContent' + index + ' not found');
                updateTestStatus('Individual Toggle Functionality', false);
            }
        }
        
        // Test functions
        function runAutomaticTests() {
            logToTest('=== RUNNING AUTOMATIC TESTS ===');
            
            // Test DOM element detection
            const termContents = document.querySelectorAll('[id^="termContent"]');
            const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
            logToTest('DOM scan: ' + termContents.length + ' termContent, ' + toggleIcons.length + ' toggleIcon');
            updateTestStatus('DOM Element Detection', termContents.length > 0 && toggleIcons.length > 0);
            
            // Test expand functionality
            setTimeout(() => {
                logToTest('Testing expand functionality...');
                document.getElementById('expandAllBtn').click();
                
                setTimeout(() => {
                    const visibleSections = Array.from(termContents).filter(el => el.style.display === 'block').length;
                    const upIcons = Array.from(toggleIcons).filter(el => el.classList.contains('fa-chevron-up')).length;
                    logToTest('After expand: ' + visibleSections + ' visible sections, ' + upIcons + ' up icons');
                    updateTestStatus('Expand All Functionality', visibleSections === termContents.length);
                    
                    // Test collapse functionality
                    setTimeout(() => {
                        logToTest('Testing collapse functionality...');
                        document.getElementById('collapseAllBtn').click();
                        
                        setTimeout(() => {
                            const hiddenSections = Array.from(termContents).filter(el => el.style.display === 'none').length;
                            const downIcons = Array.from(toggleIcons).filter(el => el.classList.contains('fa-chevron-down')).length;
                            logToTest('After collapse: ' + hiddenSections + ' hidden sections, ' + downIcons + ' down icons');
                            updateTestStatus('Collapse All Functionality', hiddenSections === termContents.length);
                            
                            logToTest('=== AUTOMATIC TESTS COMPLETED ===');
                        }, 500);
                    }, 1000);
                }, 500);
            }, 1000);
        }
        
        function testExpandAll() {
            logToTest('Manual expand test triggered');
            document.getElementById('expandAllBtn').click();
        }
        
        function testCollapseAll() {
            logToTest('Manual collapse test triggered');
            document.getElementById('collapseAllBtn').click();
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            logToTest('DOM loaded, initializing...');
            initExpandCollapseButtons();
            
            // Initial DOM scan
            const termContents = document.querySelectorAll('[id^="termContent"]');
            const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
            logToTest('Initial scan: ' + termContents.length + ' termContent elements, ' + toggleIcons.length + ' toggleIcon elements');
            
            logToTest('Initialization complete - ready for testing!');
            logToTest('Click "Run Automatic Tests" to start comprehensive testing');
        });
    </script>
</body>
</html>
