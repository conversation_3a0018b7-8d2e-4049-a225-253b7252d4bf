<?php

/**
 * Portal Judiciar România - Administrative Dashboard
 * 
 * Main admin interface for system monitoring, user management,
 * and GDPR compliance tools.
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */

require_once dirname(__DIR__, 2) . '/bootstrap.php';
require_once dirname(__DIR__, 2) . '/includes/config.php';

use App\Helpers\TemplateEngine;
use App\Services\AdminAuthService;
use App\Services\CaseMonitoringService;
use App\Services\NotificationManager;
use App\Config\Database;
use App\Security\CSRFProtection;
use App\Security\RateLimiter;
use App\Security\GDPRCompliance;

// Session is already started in bootstrap.php

// Initialize services
$templateEngine = new TemplateEngine();

// Require admin access
AdminAuthService::requireAdmin();

$userId = $_SESSION['user_id'];
$userRole = AdminAuthService::getAdminRole($userId);

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'];
        
        // Validate CSRF token
        if (!CSRFProtection::validateRequest($_POST, $action)) {
            throw new Exception('Invalid CSRF token');
        }
        
        // Check rate limiting
        $rateLimitKey = "admin_{$userId}";
        if (!RateLimiter::checkLimit($action, $rateLimitKey)) {
            throw new Exception('Rate limit exceeded. Please try again later.');
        }
        
        switch ($action) {
            case 'get_user_details':
                if (!AdminAuthService::hasPermission($userId, 'user_management')) {
                    throw new Exception('Insufficient permissions');
                }
                
                $targetUserId = (int)$_POST['user_id'];
                $userDetails = AdminAuthService::getUserDetails($targetUserId);
                
                echo json_encode([
                    'success' => true,
                    'data' => $userDetails
                ]);
                break;
                
            case 'update_user_status':
                if (!AdminAuthService::hasPermission($userId, 'user_management')) {
                    throw new Exception('Insufficient permissions');
                }
                
                $targetUserId = (int)$_POST['user_id'];
                $status = $_POST['status'];
                
                $result = AdminAuthService::updateUserStatus($targetUserId, $status);
                
                if ($result) {
                    AdminAuthService::logAdminAction($userId, 'user_status_updated', [
                        'target_user_id' => $targetUserId,
                        'new_status' => $status
                    ]);
                    
                    echo json_encode(['success' => true]);
                } else {
                    throw new Exception('Failed to update user status');
                }
                break;
                
            case 'export_gdpr_data':
                if (!AdminAuthService::hasPermission($userId, 'gdpr_management')) {
                    throw new Exception('Insufficient permissions');
                }
                
                $targetUserId = (int)$_POST['user_id'];
                $exportData = GDPRCompliance::exportUserData($targetUserId);
                
                AdminAuthService::logAdminAction($userId, 'gdpr_data_exported', [
                    'target_user_id' => $targetUserId
                ]);
                
                echo json_encode([
                    'success' => true,
                    'data' => $exportData
                ]);
                break;
                
            case 'resolve_security_incident':
                if (!AdminAuthService::hasPermission($userId, 'security_logs')) {
                    throw new Exception('Insufficient permissions');
                }
                
                $incidentId = (int)$_POST['incident_id'];
                $result = AdminAuthService::resolveSecurityIncident($incidentId, $userId);
                
                if ($result) {
                    echo json_encode(['success' => true]);
                } else {
                    throw new Exception('Failed to resolve security incident');
                }
                break;
                
            default:
                throw new Exception('Unknown action');
        }
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    
    exit;
}

// Get dashboard statistics
$stats = AdminAuthService::getDashboardStats();
$recentActivity = AdminAuthService::getRecentActivity(15);

// Get monitoring system statistics
$monitoringStats = getMonitoringSystemStats();
$allUsers = getAllUsersWithMonitoring();
$allMonitoredCases = getAllMonitoredCases();
$systemHealth = getSystemHealthMetrics();

// Get CSRF tokens for various actions
$csrfTokens = [
    'get_user_details' => CSRFProtection::generateToken('get_user_details'),
    'update_user_status' => CSRFProtection::generateToken('update_user_status'),
    'export_gdpr_data' => CSRFProtection::generateToken('export_gdpr_data'),
    'resolve_security_incident' => CSRFProtection::generateToken('resolve_security_incident')
];

// Check rate limits for UI display
$rateLimitKey = "admin_{$userId}";
$rateLimits = [
    'get_user_details' => RateLimiter::getRemainingAttempts('get_user_details', $rateLimitKey),
    'update_user_status' => RateLimiter::getRemainingAttempts('update_user_status', $rateLimitKey),
    'export_gdpr_data' => RateLimiter::getRemainingAttempts('export_gdpr_data', $rateLimitKey)
];

// Prepare template data
$templateData = [
    'page_title' => 'Panou Administrativ - Portal Judiciar România',
    'user_name' => $_SESSION['user_name'] ?? 'Administrator',
    'user_role' => $userRole,
    'user_permissions' => AdminAuthService::PERMISSIONS[$userRole] ?? [],
    'stats' => $stats,
    'recent_activity' => $recentActivity,
    'monitoring_stats' => $monitoringStats,
    'all_users' => $allUsers,
    'all_monitored_cases' => $allMonitoredCases,
    'system_health' => $systemHealth,
    'csrf_tokens' => $csrfTokens,
    'rate_limits' => $rateLimits,
    'current_time' => date('Y-m-d H:i:s')
];

// Render template
echo $templateEngine->render('admin/dashboard.twig', $templateData);

/**
 * Get monitoring system statistics
 */
function getMonitoringSystemStats(): array
{
    try {
        $stats = [];

        // Total users
        $stats['total_users'] = Database::fetchOne("SELECT COUNT(*) as count FROM users WHERE deleted_at IS NULL")['count'] ?? 0;

        // Active users (with monitored cases)
        $stats['active_users'] = Database::fetchOne("
            SELECT COUNT(DISTINCT user_id) as count
            FROM monitored_cases
            WHERE is_active = 1
        ")['count'] ?? 0;

        // Total monitored cases
        $stats['total_monitored_cases'] = Database::fetchOne("
            SELECT COUNT(*) as count
            FROM monitored_cases
            WHERE is_active = 1
        ")['count'] ?? 0;

        // Notifications sent today
        $stats['notifications_today'] = Database::fetchOne("
            SELECT COUNT(*) as count
            FROM notification_queue
            WHERE status = 'sent'
            AND DATE(sent_at) = CURDATE()
        ")['count'] ?? 0;

        // Notifications pending
        $stats['notifications_pending'] = Database::fetchOne("
            SELECT COUNT(*) as count
            FROM notification_queue
            WHERE status = 'pending'
        ")['count'] ?? 0;

        // Cases checked today
        $stats['cases_checked_today'] = Database::fetchOne("
            SELECT COUNT(*) as count
            FROM monitored_cases
            WHERE DATE(last_checked) = CURDATE()
        ")['count'] ?? 0;

        // Changes detected this week
        $stats['changes_this_week'] = Database::fetchOne("
            SELECT COUNT(*) as count
            FROM case_changes
            WHERE detected_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ")['count'] ?? 0;

        return $stats;

    } catch (Exception $e) {
        error_log("Error getting monitoring stats: " . $e->getMessage());
        return [
            'total_users' => 0,
            'active_users' => 0,
            'total_monitored_cases' => 0,
            'notifications_today' => 0,
            'notifications_pending' => 0,
            'cases_checked_today' => 0,
            'changes_this_week' => 0
        ];
    }
}

/**
 * Get all users with monitoring information
 */
function getAllUsersWithMonitoring(): array
{
    try {
        return Database::fetchAll("
            SELECT
                u.id,
                u.email,
                u.first_name,
                u.last_name,
                u.created_at,
                u.email_verified,
                u.gdpr_consent,
                u.is_active,
                COUNT(mc.id) as monitored_cases_count,
                MAX(mc.last_checked) as last_case_check,
                u.notification_preferences
            FROM users u
            LEFT JOIN monitored_cases mc ON u.id = mc.user_id AND mc.is_active = 1
            WHERE u.deleted_at IS NULL
            GROUP BY u.id
            ORDER BY u.created_at DESC
            LIMIT 100
        ");

    } catch (Exception $e) {
        error_log("Error getting users with monitoring: " . $e->getMessage());
        return [];
    }
}

/**
 * Get all monitored cases
 */
function getAllMonitoredCases(): array
{
    try {
        return Database::fetchAll("
            SELECT
                mc.*,
                u.email as user_email,
                u.first_name,
                u.last_name,
                COUNT(cc.id) as changes_count,
                MAX(cc.detected_at) as last_change_date
            FROM monitored_cases mc
            JOIN users u ON mc.user_id = u.id
            LEFT JOIN case_changes cc ON mc.id = cc.monitored_case_id
            WHERE mc.is_active = 1 AND u.deleted_at IS NULL
            GROUP BY mc.id
            ORDER BY mc.created_at DESC
            LIMIT 200
        ");

    } catch (Exception $e) {
        error_log("Error getting monitored cases: " . $e->getMessage());
        return [];
    }
}

/**
 * Get system health metrics
 */
function getSystemHealthMetrics(): array
{
    try {
        $health = [];

        // Database connection health
        $health['database_status'] = 'healthy';

        // Recent errors
        $health['recent_errors'] = Database::fetchOne("
            SELECT COUNT(*) as count
            FROM system_logs
            WHERE level IN ('error', 'critical')
            AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ")['count'] ?? 0;

        // Failed notifications
        $health['failed_notifications'] = Database::fetchOne("
            SELECT COUNT(*) as count
            FROM notification_queue
            WHERE status = 'failed'
            AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ")['count'] ?? 0;

        // Stale cases (not checked in 2+ hours)
        $health['stale_cases'] = Database::fetchOne("
            SELECT COUNT(*) as count
            FROM monitored_cases
            WHERE is_active = 1
            AND (last_checked IS NULL OR last_checked < DATE_SUB(NOW(), INTERVAL 2 HOUR))
        ")['count'] ?? 0;

        // Overall health score
        $healthScore = 100;
        if ($health['recent_errors'] > 10) $healthScore -= 20;
        if ($health['failed_notifications'] > 5) $healthScore -= 15;
        if ($health['stale_cases'] > 50) $healthScore -= 10;

        $health['overall_score'] = max(0, $healthScore);

        return $health;

    } catch (Exception $e) {
        error_log("Error getting system health: " . $e->getMessage());
        return [
            'database_status' => 'error',
            'recent_errors' => 0,
            'failed_notifications' => 0,
            'stale_cases' => 0,
            'overall_score' => 0
        ];
    }
}
