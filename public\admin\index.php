<?php

/**
 * Portal Judiciar România - Administrative Dashboard
 * 
 * Main admin interface for system monitoring, user management,
 * and GDPR compliance tools.
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */

require_once dirname(__DIR__, 2) . '/bootstrap.php';
require_once dirname(__DIR__, 2) . '/includes/config.php';

use App\Helpers\TemplateEngine;
use App\Services\AdminAuthService;
use App\Security\CSRFProtection;
use App\Security\RateLimiter;
use App\Security\GDPRCompliance;

// Session is already started in bootstrap.php

// Initialize services
$templateEngine = new TemplateEngine();

// Require admin access
AdminAuthService::requireAdmin();

$userId = $_SESSION['user_id'];
$userRole = AdminAuthService::getAdminRole($userId);

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'];
        
        // Validate CSRF token
        if (!CSRFProtection::validateRequest($_POST, $action)) {
            throw new Exception('Invalid CSRF token');
        }
        
        // Check rate limiting
        $rateLimitKey = "admin_{$userId}";
        if (!RateLimiter::checkLimit($action, $rateLimitKey)) {
            throw new Exception('Rate limit exceeded. Please try again later.');
        }
        
        switch ($action) {
            case 'get_user_details':
                if (!AdminAuthService::hasPermission($userId, 'user_management')) {
                    throw new Exception('Insufficient permissions');
                }
                
                $targetUserId = (int)$_POST['user_id'];
                $userDetails = AdminAuthService::getUserDetails($targetUserId);
                
                echo json_encode([
                    'success' => true,
                    'data' => $userDetails
                ]);
                break;
                
            case 'update_user_status':
                if (!AdminAuthService::hasPermission($userId, 'user_management')) {
                    throw new Exception('Insufficient permissions');
                }
                
                $targetUserId = (int)$_POST['user_id'];
                $status = $_POST['status'];
                
                $result = AdminAuthService::updateUserStatus($targetUserId, $status);
                
                if ($result) {
                    AdminAuthService::logAdminAction($userId, 'user_status_updated', [
                        'target_user_id' => $targetUserId,
                        'new_status' => $status
                    ]);
                    
                    echo json_encode(['success' => true]);
                } else {
                    throw new Exception('Failed to update user status');
                }
                break;
                
            case 'export_gdpr_data':
                if (!AdminAuthService::hasPermission($userId, 'gdpr_management')) {
                    throw new Exception('Insufficient permissions');
                }
                
                $targetUserId = (int)$_POST['user_id'];
                $exportData = GDPRCompliance::exportUserData($targetUserId);
                
                AdminAuthService::logAdminAction($userId, 'gdpr_data_exported', [
                    'target_user_id' => $targetUserId
                ]);
                
                echo json_encode([
                    'success' => true,
                    'data' => $exportData
                ]);
                break;
                
            case 'resolve_security_incident':
                if (!AdminAuthService::hasPermission($userId, 'security_logs')) {
                    throw new Exception('Insufficient permissions');
                }
                
                $incidentId = (int)$_POST['incident_id'];
                $result = AdminAuthService::resolveSecurityIncident($incidentId, $userId);
                
                if ($result) {
                    echo json_encode(['success' => true]);
                } else {
                    throw new Exception('Failed to resolve security incident');
                }
                break;
                
            default:
                throw new Exception('Unknown action');
        }
        
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    
    exit;
}

// Get dashboard statistics
$stats = AdminAuthService::getDashboardStats();
$recentActivity = AdminAuthService::getRecentActivity(15);

// Get CSRF tokens for various actions
$csrfTokens = [
    'get_user_details' => CSRFProtection::generateToken('get_user_details'),
    'update_user_status' => CSRFProtection::generateToken('update_user_status'),
    'export_gdpr_data' => CSRFProtection::generateToken('export_gdpr_data'),
    'resolve_security_incident' => CSRFProtection::generateToken('resolve_security_incident')
];

// Check rate limits for UI display
$rateLimitKey = "admin_{$userId}";
$rateLimits = [
    'get_user_details' => RateLimiter::getRemainingAttempts('get_user_details', $rateLimitKey),
    'update_user_status' => RateLimiter::getRemainingAttempts('update_user_status', $rateLimitKey),
    'export_gdpr_data' => RateLimiter::getRemainingAttempts('export_gdpr_data', $rateLimitKey)
];

// Prepare template data
$templateData = [
    'page_title' => 'Panou Administrativ - Portal Judiciar România',
    'user_name' => $_SESSION['user_name'] ?? 'Administrator',
    'user_role' => $userRole,
    'user_permissions' => AdminAuthService::PERMISSIONS[$userRole] ?? [],
    'stats' => $stats,
    'recent_activity' => $recentActivity,
    'csrf_tokens' => $csrfTokens,
    'rate_limits' => $rateLimits,
    'current_time' => date('Y-m-d H:i:s')
];

// Render template
echo $templateEngine->render('admin/dashboard.twig', $templateData);
