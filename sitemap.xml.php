<?php
/**
 * Generare dinamică sitemap.xml pentru Portal Judiciar
 */

// Setăm header-ul pentru XML
header('Content-Type: application/xml; charset=utf-8');

// URL-ul de bază al site-ului
$baseUrl = 'http://localhost/just';

// Definim paginile statice cu prioritatea și frecvența de actualizare
$staticPages = [
    [
        'url' => $baseUrl . '/',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'daily',
        'priority' => '1.0'
    ],
    [
        'url' => $baseUrl . '/index.php',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'daily',
        'priority' => '1.0'
    ],
    [
        'url' => $baseUrl . '/contact.php',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'monthly',
        'priority' => '0.8'
    ],
    [
        'url' => $baseUrl . '/sedinte.php',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'daily',
        'priority' => '0.9'
    ],
    [
        'url' => $baseUrl . '/search.php',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'daily',
        'priority' => '0.9'
    ],
    [
        'url' => $baseUrl . '/avans.php',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'weekly',
        'priority' => '0.7'
    ],
    [
        'url' => $baseUrl . '/avansat.php',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'weekly',
        'priority' => '0.7'
    ],
    [
        'url' => $baseUrl . '/public/',
        'lastmod' => date('Y-m-d'),
        'changefreq' => 'daily',
        'priority' => '0.8'
    ]
];

// Începem XML-ul
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

// Adăugăm paginile statice
foreach ($staticPages as $page) {
    echo '  <url>' . "\n";
    echo '    <loc>' . htmlspecialchars($page['url']) . '</loc>' . "\n";
    echo '    <lastmod>' . $page['lastmod'] . '</lastmod>' . "\n";
    echo '    <changefreq>' . $page['changefreq'] . '</changefreq>' . "\n";
    echo '    <priority>' . $page['priority'] . '</priority>' . "\n";
    echo '  </url>' . "\n";
}

// Adăugăm URL-uri pentru instanțe populare (pentru SEO)
require_once 'includes/functions.php';
$allInstitutions = getInstanteList();

// Selectăm doar instituțiile populare pentru sitemap (pentru a nu face sitemap-ul prea mare)
$popularInstitutionCodes = [
    'JudecatoriaSECTORUL1BUCURESTI',
    'JudecatoriaSECTORUL2BUCURESTI',
    'TribunalulBUCURESTI',
    'CurteadeApelBUCURESTI',
    'JudecatoriaCLUJNAPOCA',
    'TribunalulCLUJ',
    'CurteadeApelCLUJ',
    'JudecatoriaCONSTANTA',
    'TribunalulCONSTANTA',
    'JudecatoriaTIMISOARA',
    'TribunalulTIMIS',
    'JudecatoriaIASI',
    'TribunalulIASI'
];

$popularInstitutions = [];
foreach ($popularInstitutionCodes as $code) {
    if (isset($allInstitutions[$code])) {
        $popularInstitutions[$code] = $allInstitutions[$code];
    }
}

// Adăugăm URL-uri pentru căutări după instituție
foreach ($popularInstitutions as $institutionCode => $institutionName) {
    // URL pentru căutare ședințe după instituție
    echo '  <url>' . "\n";
    echo '    <loc>' . htmlspecialchars($baseUrl . '/sedinte.php?institutie=' . urlencode($institutionCode)) . '</loc>' . "\n";
    echo '    <lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
    echo '    <changefreq>daily</changefreq>' . "\n";
    echo '    <priority>0.6</priority>' . "\n";
    echo '  </url>' . "\n";
}

// Adăugăm URL-uri pentru căutări după dată (ultimele 30 de zile)
for ($i = 0; $i < 30; $i++) {
    $date = date('d.m.Y', strtotime("-$i days"));
    echo '  <url>' . "\n";
    echo '    <loc>' . htmlspecialchars($baseUrl . '/sedinte.php?data=' . urlencode($date)) . '</loc>' . "\n";
    echo '    <lastmod>' . date('Y-m-d', strtotime("-$i days")) . '</lastmod>' . "\n";
    echo '    <changefreq>daily</changefreq>' . "\n";
    echo '    <priority>0.5</priority>' . "\n";
    echo '  </url>' . "\n";
}

// Adăugăm URL-uri pentru căutări populare
$popularSearchTerms = [
    'divorț',
    'executare silită',
    'contencios administrativ',
    'penal',
    'civil',
    'comercial',
    'faliment',
    'insolvență',
    'muncă',
    'asigurări sociale'
];

foreach ($popularSearchTerms as $term) {
    echo '  <url>' . "\n";
    echo '    <loc>' . htmlspecialchars($baseUrl . '/search.php?search_term=' . urlencode($term)) . '</loc>' . "\n";
    echo '    <lastmod>' . date('Y-m-d') . '</lastmod>' . "\n";
    echo '    <changefreq>weekly</changefreq>' . "\n";
    echo '    <priority>0.4</priority>' . "\n";
    echo '  </url>' . "\n";
}

// Închidere XML
echo '</urlset>' . "\n";
?>
