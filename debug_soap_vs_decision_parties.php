<?php
/**
 * Debug SOAP API vs Decision Text party extraction
 */

require_once 'bootstrap.php';

use App\Services\DosarService;

echo "=== DEBUGGING SOAP API vs DECISION TEXT PARTY EXTRACTION ===" . PHP_EOL;
echo "Case: 130/98/2022 from TribunalulIALOMITA" . PHP_EOL;
echo "Target: SARAGEA TUDORIŢA" . PHP_EOL;
echo PHP_EOL;

$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';
$targetParty = 'SARAGEA TUDORIŢA';

try {
    $dosarService = new DosarService();
    
    // Get raw SOAP response to examine original data
    echo "=== STEP 1: Getting Raw SOAP Response ===" . PHP_EOL;
    
    // We need to call the SOAP API directly to see the original data
    $soapClient = new SoapClient(SOAP_WSDL, [
        'trace' => 1,
        'exceptions' => true,
        'cache_wsdl' => WSDL_CACHE_NONE,
        'connection_timeout' => 30,
        'user_agent' => 'Portal Judiciar Romania'
    ]);
    
    $params = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'numeCompletParte' => '',
        'obiectDosar' => '',
        'dataStart' => '',
        'dataStop' => ''
    ];
    
    $response = $soapClient->cautareDosar($params);
    
    if (isset($response->return) && !empty($response->return)) {
        $dosare = $response->return;
        if (!is_array($dosare)) {
            $dosare = [$dosare];
        }
        
        $dosar = $dosare[0]; // Get the first (and likely only) case
        
        echo "✅ Retrieved raw SOAP response" . PHP_EOL;
        echo "Case number: " . ($dosar->numar ?? 'N/A') . PHP_EOL;
        
        // Check SOAP API parties
        echo PHP_EOL;
        echo "=== STEP 2: Analyzing SOAP API Parties ===" . PHP_EOL;
        
        $soapPartyFound = false;
        $soapPartyCount = 0;
        
        if (isset($dosar->parti) && isset($dosar->parti->DosarParte)) {
            $parti = $dosar->parti->DosarParte;
            if (!is_array($parti)) {
                $parti = [$parti];
            }
            
            $soapPartyCount = count($parti);
            echo "SOAP API returned {$soapPartyCount} parties" . PHP_EOL;
            
            foreach ($parti as $index => $parte) {
                if (isset($parte->nume) && stripos($parte->nume, $targetParty) !== false) {
                    $soapPartyFound = true;
                    echo "✅ FOUND in SOAP API at position " . ($index + 1) . ":" . PHP_EOL;
                    echo "   Name: '{$parte->nume}'" . PHP_EOL;
                    echo "   Quality: '{$parte->calitateParte}'" . PHP_EOL;
                    break;
                }
            }
            
            if (!$soapPartyFound) {
                echo "❌ NOT FOUND in SOAP API parties (first {$soapPartyCount})" . PHP_EOL;
            }
        } else {
            echo "❌ No SOAP API parties found" . PHP_EOL;
        }
        
        // Check decision text
        echo PHP_EOL;
        echo "=== STEP 3: Analyzing Decision Text ===" . PHP_EOL;
        
        $decisionPartyFound = false;
        
        if (isset($dosar->sedinte) && isset($dosar->sedinte->DosarSedinta)) {
            $sedinte = $dosar->sedinte->DosarSedinta;
            if (!is_array($sedinte)) {
                $sedinte = [$sedinte];
            }
            
            foreach ($sedinte as $sedinta) {
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $solutieText = $sedinta->solutieSumar;
                    
                    if (stripos($solutieText, $targetParty) !== false) {
                        $decisionPartyFound = true;
                        echo "✅ FOUND in decision text" . PHP_EOL;
                        
                        // Extract the relevant part of the text
                        $start = max(0, stripos($solutieText, $targetParty) - 100);
                        $length = 200;
                        $excerpt = substr($solutieText, $start, $length);
                        
                        echo "Text excerpt:" . PHP_EOL;
                        echo "..." . $excerpt . "..." . PHP_EOL;
                        break;
                    }
                }
            }
            
            if (!$decisionPartyFound) {
                echo "❌ NOT FOUND in decision text" . PHP_EOL;
            }
        } else {
            echo "❌ No decision text found" . PHP_EOL;
        }
        
        echo PHP_EOL;
        echo "=== STEP 4: Analysis Summary ===" . PHP_EOL;
        echo "SOAP API parties: {$soapPartyCount}" . PHP_EOL;
        echo "Target party in SOAP API: " . ($soapPartyFound ? "YES" : "NO") . PHP_EOL;
        echo "Target party in decision text: " . ($decisionPartyFound ? "YES" : "NO") . PHP_EOL;
        
        if ($soapPartyFound && $decisionPartyFound) {
            echo "🔍 ISSUE: Party exists in both SOAP API and decision text" . PHP_EOL;
            echo "   This suggests the hybrid extraction is incorrectly overriding SOAP API data" . PHP_EOL;
        } elseif (!$soapPartyFound && $decisionPartyFound) {
            echo "✅ EXPECTED: Party only in decision text (beyond 100-party SOAP limit)" . PHP_EOL;
            echo "   But quality should be extracted from decision text, not defaulted to 'Creditor'" . PHP_EOL;
        } elseif ($soapPartyFound && !$decisionPartyFound) {
            echo "✅ EXPECTED: Party only in SOAP API" . PHP_EOL;
        } else {
            echo "❌ UNEXPECTED: Party not found in either source" . PHP_EOL;
        }
        
    } else {
        echo "❌ No SOAP response data" . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . PHP_EOL;
}
