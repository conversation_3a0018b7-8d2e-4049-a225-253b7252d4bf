<?php
// Test the specific filtering bug
require_once 'bootstrap.php';

// Copy the problematic function from index.php
function filterResultsByCaseNumberPattern($results, $caseNumberInfo) {
    if (empty($results) || empty($caseNumberInfo)) {
        return $results;
    }

    echo "FILTERING: " . count($results) . " results for pattern '{$caseNumberInfo['original']}'<br>";

    $filteredResults = [];
    $originalPattern = $caseNumberInfo['original'];
    $hasWildcard = $caseNumberInfo['hasWildcard'];
    $hasSuffix = $caseNumberInfo['hasSuffix'];
    $suffix = $caseNumberInfo['suffix'];

    foreach ($results as $dosar) {
        $caseNumber = $dosar->numar ?? '';
        $shouldInclude = false;

        echo "Checking case: '$caseNumber'<br>";

        if ($hasWildcard) {
            // This is the problematic line!
            $basePattern = str_replace('*', '', $originalPattern);
            echo "Base pattern: '$basePattern'<br>";
            echo "Does '$caseNumber' start with '$basePattern'? ";
            
            if (strpos($caseNumber, $basePattern) === 0) {
                $shouldInclude = true;
                echo "YES - INCLUDED<br>";
            } else {
                echo "NO - EXCLUDED<br>";
            }
        } elseif ($hasSuffix) {
            if ($caseNumber === $originalPattern) {
                $shouldInclude = true;
                echo "Suffix exact match - INCLUDED<br>";
            } else {
                echo "Suffix no match - EXCLUDED<br>";
            }
        } else {
            if ($caseNumber === $originalPattern) {
                $shouldInclude = true;
                echo "Exact match - INCLUDED<br>";
            } else {
                echo "No exact match - EXCLUDED<br>";
            }
        }

        if ($shouldInclude) {
            $filteredResults[] = $dosar;
        }
        echo "<br>";
    }

    echo "RESULT: Filtered from " . count($results) . " to " . count($filteredResults) . " results<br>";
    return $filteredResults;
}

echo "<h1>Testing Filtering Bug</h1>";

// Create mock results that match our real scenario
$mockResults = [
    (object)['numar' => '14096/3/2024'],
    (object)['numar' => '14096/3/2024'],  // Duplicate (different case)
    (object)['numar' => '14096/3/2024*']  // This should be included but gets filtered out
];

$caseNumberInfo = [
    'original' => '14096/3/2024*',
    'normalized' => '14096/3/2024',
    'hasWildcard' => true,
    'hasSuffix' => false,
    'suffix' => ''
];

echo "<h2>Mock Data:</h2>";
echo "<pre>" . print_r($mockResults, true) . "</pre>";

echo "<h2>Case Number Info:</h2>";
echo "<pre>" . print_r($caseNumberInfo, true) . "</pre>";

echo "<h2>Filtering Process:</h2>";
$filteredResults = filterResultsByCaseNumberPattern($mockResults, $caseNumberInfo);

echo "<h2>Final Results:</h2>";
echo "<pre>" . print_r($filteredResults, true) . "</pre>";

echo "<h2>Analysis:</h2>";
echo "<p style='color: red;'><strong>BUG CONFIRMED:</strong> The case '14096/3/2024*' is being filtered out!</p>";
echo "<p>The problem is that the function checks if case numbers START WITH the base pattern '14096/3/2024'.</p>";
echo "<p>But '14096/3/2024*' does NOT start with '14096/3/2024' - it starts with '14096/3/2024*'.</p>";
echo "<p>The function needs to be fixed to properly handle literal asterisks in case numbers.</p>";
?>
