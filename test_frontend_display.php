<?php
/**
 * Simple test page to isolate the parties display issue
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

try {
    $dosarService = new DosarService();
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
} catch (Exception $e) {
    $error = "Eroare: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Parties Display</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h1>Test Parties Display for Case <?php echo htmlspecialchars($numarDosar); ?></h1>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php elseif ($dosar && !empty($dosar->parti)): ?>
            <div class="alert alert-info">
                Backend retrieved: <strong><?php echo count($dosar->parti); ?> parties</strong>
            </div>
            
            <div class="table-responsive">
                <table class="table table-striped" id="tabelParti">
                    <thead>
                        <tr>
                            <th>Index</th>
                            <th>Nume</th>
                            <th>Calitate</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $loop_index = 0;
                        $totalPartiCount = count($dosar->parti);
                        
                        foreach ($dosar->parti as $parteIndex => $parte):
                            $loop_index++;
                        ?>
                            <tr class="parte-row" 
                                data-nume="<?php echo htmlspecialchars($parte['nume']); ?>"
                                data-calitate="<?php echo !empty($parte['calitate']) ? htmlspecialchars($parte['calitate']) : ''; ?>"
                                data-index="<?php echo $loop_index; ?>"
                                data-party-id="<?php echo $parteIndex; ?>">
                                <td><?php echo $loop_index; ?></td>
                                <td class="nume-parte">
                                    <?php echo htmlspecialchars($parte['nume']); ?>
                                </td>
                                <td class="calitate-parte">
                                    <?php echo !empty($parte['calitate']) ? htmlspecialchars($parte['calitate']) : '<span class="text-muted">-</span>'; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="alert alert-success">
                PHP generated: <strong><?php echo $loop_index; ?> table rows</strong> for <strong><?php echo $totalPartiCount; ?> parties</strong>
            </div>
            
        <?php else: ?>
            <div class="alert alert-warning">No parties found or dosar not found</div>
        <?php endif; ?>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('=== FRONTEND PARTIES TEST ===');
        
        const tabelParti = document.getElementById('tabelParti');
        if (tabelParti) {
            const tbody = tabelParti.querySelector('tbody');
            if (tbody) {
                const allRows = tbody.querySelectorAll('tr');
                const parteRows = tbody.querySelectorAll('tr.parte-row');
                
                console.log('Total rows in tbody:', allRows.length);
                console.log('Rows with .parte-row class:', parteRows.length);
                
                // Check visibility
                let visibleCount = 0;
                let hiddenCount = 0;
                
                allRows.forEach((row, index) => {
                    const computedStyle = getComputedStyle(row);
                    const isVisible = row.style.display !== 'none' && 
                                     computedStyle.display !== 'none' &&
                                     computedStyle.visibility !== 'hidden';
                    
                    if (isVisible) {
                        visibleCount++;
                    } else {
                        hiddenCount++;
                        console.log(`Hidden row ${index + 1}:`, {
                            element: row,
                            style_display: row.style.display,
                            computed_display: computedStyle.display,
                            computed_visibility: computedStyle.visibility
                        });
                    }
                });
                
                console.log('Visible rows:', visibleCount);
                console.log('Hidden rows:', hiddenCount);
                
                // Update page with results
                const resultDiv = document.createElement('div');
                resultDiv.className = 'alert alert-info mt-3';
                resultDiv.innerHTML = `
                    <strong>Frontend JavaScript Results:</strong><br>
                    Total rows in tbody: ${allRows.length}<br>
                    Rows with .parte-row class: ${parteRows.length}<br>
                    Visible rows: ${visibleCount}<br>
                    Hidden rows: ${hiddenCount}
                `;
                document.querySelector('.container').appendChild(resultDiv);
            }
        }
        
        console.log('=== END FRONTEND TEST ===');
    });
    </script>
</body>
</html>
