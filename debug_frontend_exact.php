<?php
// Debug the exact frontend issue
require_once 'bootstrap.php';

use App\Services\DosarService;

// Simulate the exact POST request from the web interface
$_POST['bulkSearchTerms'] = "14096/3/2024*";

echo "<h1>🔍 Debug Frontend Issue - Exact Simulation</h1>";

// Include the exact functions from index.php
function parseBulkSearchTerms($input) {
    $input = str_replace(',', "\n", $input);
    $terms = explode("\n", $input);
    $cleanTerms = [];

    foreach ($terms as $term) {
        $term = trim($term);
        if (!empty($term) && strlen($term) >= 2) {
            $cleanTerms[] = [
                'term' => $term,
                'type' => detectSearchType($term)
            ];
        }
    }

    // Eliminăm duplicatele pe baza termenului
    $uniqueTerms = [];
    $seenTerms = [];

    foreach ($cleanTerms as $termData) {
        $termKey = strtolower($termData['term']);
        if (!in_array($termKey, $seenTerms)) {
            $uniqueTerms[] = $termData;
            $seenTerms[] = $termKey;
        }
    }

    return $uniqueTerms;
}

function detectSearchType($term) {
    $cleanTerm = trim($term, '"\'');
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\*$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\/[a-zA-Z0-9]+$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
        return 'numarDosar';
    }
    
    return 'numeParte';
}

function performBulkSearchWithFilters($searchTermsData, $filters) {
    $dosarService = new DosarService();
    $results = [];

    foreach ($searchTermsData as $termData) {
        $term = $termData['term'];
        $searchType = $termData['type'];

        try {
            $searchParams = [
                'numarDosar' => ($searchType === 'numarDosar') ? $term : '',
                'institutie' => $filters['institutie'] ?? null,
                'numeParte' => ($searchType === 'numeParte') ? $term : '',
                'obiectDosar' => '',
                'dataStart' => '',
                'dataStop' => '',
                'dataUltimaModificareStart' => '',
                'dataUltimaModificareStop' => '',
                'categorieInstanta' => '',
                'categorieCaz' => ''
            ];

            $termResults = $dosarService->cautareAvansata($searchParams);

            // Add search metadata to each result
            foreach ($termResults as $dosar) {
                $dosar->searchTerm = $term;
                $dosar->searchType = $searchType;
            }

            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => $termResults,
                'count' => count($termResults),
                'error' => null
            ];

        } catch (Exception $e) {
            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => [],
                'count' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    return $results;
}

try {
    $bulkSearchTerms = $_POST['bulkSearchTerms'] ?? '';
    
    if (!empty($bulkSearchTerms)) {
        echo "<h2>Step 1: Parse Search Terms</h2>";
        $searchTermsData = parseBulkSearchTerms($bulkSearchTerms);
        
        echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
        echo "<strong>Input:</strong> '$bulkSearchTerms'<br>";
        echo "<strong>Parsed terms:</strong> " . count($searchTermsData) . "<br>";
        foreach ($searchTermsData as $termData) {
            echo "- Term: '{$termData['term']}', Type: '{$termData['type']}'<br>";
        }
        echo "</div>";
        
        echo "<h2>Step 2: Perform Search</h2>";
        $searchResults = performBulkSearchWithFilters($searchTermsData, []);
        
        echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
        echo "<strong>Search Results:</strong><br>";
        
        $totalResults = 0;
        foreach ($searchResults as $termResult) {
            $count = count($termResult['results']);
            $totalResults += $count;
            echo "- Term: '{$termResult['term']}' → {$count} results<br>";
        }
        echo "<strong>Total results: $totalResults</strong><br>";
        echo "</div>";
        
        echo "<h2>Step 3: Simulate HTML Generation (The Critical Part)</h2>";
        
        // This is where the frontend issue likely occurs
        foreach ($searchResults as $termIndex => $termResult) {
            echo "<h3>Term #{$termIndex}: {$termResult['term']} ({$termResult['count']} results)</h3>";
            
            if ($termResult['count'] > 0) {
                // Simulate the result message generation
                echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border: 1px solid #ffc107;'>";
                echo "<strong>Result Message (what should appear):</strong><br>";
                echo "\"<span id='resultMessage{$termIndex}' data-term='{$termResult['term']}' data-original-count='{$termResult['count']}'>";
                echo "{$termResult['count']} rezultate găsite pentru termenul '{$termResult['term']}'</span>\"<br>";
                echo "</div>";
                
                // Simulate the table generation
                echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
                echo "<strong>Table Rows (what should be generated):</strong><br>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 5px 0;'>";
                echo "<tr style='background: #e9ecef;'>";
                echo "<th>Row #</th><th>Case Number</th><th>Institution</th><th>Data Attributes</th><th>Visible?</th>";
                echo "</tr>";
                
                foreach ($termResult['results'] as $index => $dosar) {
                    $caseNumber = htmlspecialchars($dosar->numar ?? '');
                    $institution = htmlspecialchars($dosar->instanta ?? '');
                    $searchType = htmlspecialchars($dosar->searchType ?? '');
                    
                    $hasAsterisk = strpos($dosar->numar, '*') !== false;
                    $rowStyle = $hasAsterisk ? "background: #fff3cd;" : "background: #f8f9fa;";
                    
                    echo "<tr style='$rowStyle'>";
                    echo "<td>" . ($index + 1) . "</td>";
                    echo "<td>$caseNumber" . ($hasAsterisk ? " <strong>(ASTERISK!)</strong>" : "") . "</td>";
                    echo "<td>$institution</td>";
                    echo "<td>data-numar=\"$caseNumber\" data-search-type=\"$searchType\"</td>";
                    echo "<td style='color: green;'><strong>YES</strong></td>";
                    echo "</tr>";
                }
                
                echo "</table>";
                echo "</div>";
                
                // Check for potential issues
                echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
                echo "<strong>Potential Frontend Issues to Check:</strong><br>";
                echo "1. Are all " . count($termResult['results']) . " table rows actually generated in HTML?<br>";
                echo "2. Is the result message showing the correct count (" . $termResult['count'] . ")?<br>";
                echo "3. Are any CSS rules hiding rows?<br>";
                echo "4. Is JavaScript filtering removing rows after page load?<br>";
                echo "5. Are data attributes correctly set on all rows?<br>";
                echo "</div>";
            }
        }
        
        echo "<h2>Step 4: Check for Common Frontend Issues</h2>";
        
        // Check for issues that could cause the problem
        $issues = [];
        
        if ($totalResults == 3) {
            echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border: 1px solid #c3e6cb;'>";
            echo "<strong>✅ Backend is perfect:</strong> Returns exactly 3 results as expected<br>";
            echo "</div>";
            
            echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border: 1px solid #ffc107;'>";
            echo "<strong>🔍 Frontend Investigation Needed:</strong><br>";
            echo "Since backend returns 3 results but you see only 2, check:<br>";
            echo "1. <strong>HTML Source:</strong> View page source and count actual table rows<br>";
            echo "2. <strong>JavaScript Console:</strong> Look for errors or filtering messages<br>";
            echo "3. <strong>CSS Display:</strong> Check if any rows have display:none<br>";
            echo "4. <strong>Result Counter:</strong> Check if the message shows '2' instead of '3'<br>";
            echo "5. <strong>Exact Match Filter:</strong> Check if it's auto-enabled and hiding results<br>";
            echo "</div>";
            
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border: 1px solid #f5c6cb;'>";
            echo "<strong>❌ Backend Issue:</strong> Expected 3 results, got $totalResults<br>";
            echo "</div>";
        }
        
        echo "<h2>Step 5: Debugging Instructions</h2>";
        
        echo "<div style='background: #e7f3ff; padding: 15px; margin: 10px 0; border: 1px solid #007bff;'>";
        echo "<h3>🔧 How to Debug the Frontend Issue:</h3>";
        echo "<ol>";
        echo "<li><strong>Open the web interface</strong> and search for '14096/3/2024*'</li>";
        echo "<li><strong>Right-click → View Page Source</strong> and search for '14096/3/2024'</li>";
        echo "<li><strong>Count the table rows</strong> - should be 3 rows with data-numar attributes</li>";
        echo "<li><strong>Open Developer Tools (F12)</strong> and go to Console tab</li>";
        echo "<li><strong>Look for JavaScript errors</strong> or messages about filtering</li>";
        echo "<li><strong>In Console, run:</strong> <code>document.querySelectorAll('table tbody tr').length</code></li>";
        echo "<li><strong>Check result message:</strong> Should say '3 rezultate găsite'</li>";
        echo "<li><strong>Check exact match filter:</strong> Should NOT be checked automatically</li>";
        echo "</ol>";
        echo "</div>";
        
    } else {
        echo "<p>No search terms provided.</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb;'>";
    echo "<h3>❌ Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}

echo "<hr>";
echo "<div style='background: #d4edda; padding: 20px; margin: 20px 0; border: 1px solid #c3e6cb; border-radius: 5px; text-align: center;'>";
echo "<h2 style='color: #155724; margin: 0;'>🎯 Conclusion</h2>";
echo "<p style='color: #155724; margin: 10px 0 0 0;'>Backend works perfectly. The issue is 100% in the frontend display logic.</p>";
echo "</div>";
?>
