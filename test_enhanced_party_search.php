<?php
/**
 * Test the enhanced party search functionality
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';

use App\Services\DosarService;

echo "=== TESTING ENHANCED PARTY SEARCH ===" . PHP_EOL;
echo "Testing search for 'Saragea Tudorita' with enhanced flexible matching" . PHP_EOL;
echo "Expected: 4 specific cases including Bucharest cases with similar name components" . PHP_EOL;
echo PHP_EOL;

$searchTerm = 'Saragea Tudorita';

$expectedCases = [
    'CurteadeApelBUCURESTI - 130/98/2022/a3',
    'CurteadeApelBUCURESTI - 130/98/2022', 
    'CurteadeApelSUCEAVA - 2177/40/2019**',
    'TribunalulIALOMITA - 130/98/2022'
];

try {
    $dosarService = new DosarService();
    
    echo "=== ENHANCED PARTY SEARCH RESULTS ===" . PHP_EOL;
    
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => $searchTerm,
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $results = $dosarService->cautareAvansata($searchParams);
    echo "Found " . count($results) . " results" . PHP_EOL;
    echo PHP_EOL;
    
    $foundExpectedCases = [];
    
    foreach ($results as $index => $dosar) {
        $institutie = $dosar->institutie ?? 'Unknown';
        $numar = $dosar->numar ?? 'Unknown';
        $caseIdentifier = "$institutie - $numar";
        
        echo "Result " . ($index + 1) . ": $caseIdentifier" . PHP_EOL;
        
        // Check if this is one of the expected cases
        $isExpected = false;
        foreach ($expectedCases as $expectedCase) {
            if (stripos($caseIdentifier, str_replace('**', '', $expectedCase)) !== false || 
                $caseIdentifier === $expectedCase) {
                $isExpected = true;
                $foundExpectedCases[] = $expectedCase;
                echo "  ✅ EXPECTED CASE FOUND!" . PHP_EOL;
                break;
            }
        }
        
        if (!$isExpected) {
            echo "  ℹ️ Additional case (not in original expected list)" . PHP_EOL;
        }
        
        // Show matching parties
        $parti = $dosar->parti ?? [];
        echo "  Total parties: " . count($parti) . PHP_EOL;
        
        $matchingParties = [];
        foreach ($parti as $party) {
            $partyName = is_object($party) ? ($party->nume ?? '') : ($party['nume'] ?? '');
            $partySource = is_object($party) ? ($party->source ?? 'soap_api') : ($party['source'] ?? 'soap_api');
            
            // Check for matches with search components
            if (!empty($partyName)) {
                if (stripos($partyName, 'SARAGEA') !== false || 
                    stripos($partyName, 'TUDORITA') !== false ||
                    stripos($partyName, 'TUDOREL') !== false ||
                    stripos($partyName, 'TUDO') !== false) {
                    $matchingParties[] = "$partyName [Source: $partySource]";
                }
            }
        }
        
        if (!empty($matchingParties)) {
            echo "  Matching parties:" . PHP_EOL;
            foreach ($matchingParties as $matchingParty) {
                echo "    - $matchingParty" . PHP_EOL;
            }
        } else {
            echo "  ⚠️ No obvious matching parties found" . PHP_EOL;
        }
        
        echo PHP_EOL;
    }
    
    echo "=== SUMMARY ===" . PHP_EOL;
    echo "Expected cases found: " . count($foundExpectedCases) . " out of " . count($expectedCases) . PHP_EOL;
    
    foreach ($expectedCases as $expectedCase) {
        $found = false;
        foreach ($foundExpectedCases as $foundCase) {
            if (stripos($foundCase, str_replace('**', '', $expectedCase)) !== false) {
                $found = true;
                break;
            }
        }
        
        if ($found) {
            echo "✅ $expectedCase - FOUND" . PHP_EOL;
        } else {
            echo "❌ $expectedCase - NOT FOUND" . PHP_EOL;
        }
    }
    
    if (count($foundExpectedCases) === count($expectedCases)) {
        echo PHP_EOL;
        echo "🎉 SUCCESS: All expected cases are now being returned!" . PHP_EOL;
    } else {
        echo PHP_EOL;
        echo "⚠️ Some expected cases are still missing. May need further adjustment." . PHP_EOL;
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
}
