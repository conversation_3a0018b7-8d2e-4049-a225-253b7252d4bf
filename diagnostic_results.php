<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

// Include necessary functions from index.php
function validateAndMapInstitutionCode($institutionCode) {
    if (empty($institutionCode)) {
        return null;
    }
    
    // Simple validation - return as is for now
    return $institutionCode;
}

function validateRomanianDate($dateString) {
    if (empty($dateString)) {
        return ['valid' => false, 'date' => null];
    }
    
    // Try to parse DD.MM.YYYY format
    if (preg_match('/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/', $dateString, $matches)) {
        $day = (int)$matches[1];
        $month = (int)$matches[2];
        $year = (int)$matches[3];
        
        if (checkdate($month, $day, $year)) {
            return [
                'valid' => true,
                'date' => sprintf('%04d-%02d-%02d', $year, $month, $day)
            ];
        }
    }
    
    return ['valid' => false, 'date' => null];
}

function performBulkSearchWithFilters($searchTermsData, $advancedFilters = []) {
    $dosarService = new DosarService();
    $results = [];

    // Validăm și mapăm codul instituției
    $mappedInstitutionCode = validateAndMapInstitutionCode($advancedFilters['institutie'] ?? null);

    foreach ($searchTermsData as $termData) {
        $term = $termData['term'];
        $searchType = $termData['type'];

        try {
            // Procesăm datele pentru SOAP API
            $dataStart = '';
            $dataStop = '';

            if (!empty($advancedFilters['dataInceput'])) {
                $startValidation = validateRomanianDate($advancedFilters['dataInceput']);
                if ($startValidation['valid']) {
                    $dataStart = $startValidation['date'] . 'T00:00:00';
                }
            }

            if (!empty($advancedFilters['dataSfarsit'])) {
                $endValidation = validateRomanianDate($advancedFilters['dataSfarsit']);
                if ($endValidation['valid']) {
                    $dataStop = $endValidation['date'] . 'T23:59:59';
                }
            }

            // Construim parametrii de căutare
            $searchParams = [
                'numarDosar' => '',
                'institutie' => $mappedInstitutionCode,
                'numeParte' => '',
                'obiectDosar' => '',
                'dataStart' => $dataStart,
                'dataStop' => $dataStop,
                'dataUltimaModificareStart' => '',
                'dataUltimaModificareStop' => '',
                'categorieInstanta' => $advancedFilters['categorieInstanta'] ?? '',
                'categorieCaz' => $advancedFilters['categorieCaz'] ?? ''
            ];

            // Setăm parametrul corespunzător tipului de căutare detectat automat
            switch ($searchType) {
                case 'numarDosar':
                    $searchParams['numarDosar'] = $term;
                    break;
                case 'obiectDosar':
                    $searchParams['obiectDosar'] = $term;
                    break;
                case 'filter_only':
                    break;
                case 'numeParte':
                default:
                    $searchParams['numeParte'] = $term;
                    break;
            }

            // Efectuăm căutarea
            $termResults = $dosarService->cautareAvansata($searchParams);

            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => $termResults ?: [],
                'count' => count($termResults ?: []),
                'error' => null,
                'filters' => $advancedFilters,
                'search_params' => $searchParams
            ];

        } catch (Exception $e) {
            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => [],
                'count' => 0,
                'error' => $e->getMessage(),
                'filters' => $advancedFilters,
                'search_params' => $searchParams ?? []
            ];
        }
    }

    return $results;
}

function parseBulkSearchTerms($bulkSearchTerms) {
    $terms = [];
    $lines = explode("\n", $bulkSearchTerms);
    
    foreach ($lines as $line) {
        $term = trim($line);
        if (!empty($term)) {
            // Simple type detection
            if (preg_match('/^\d+\/\d+\/\d+$/', $term)) {
                $type = 'numarDosar';
            } elseif (preg_match('/^[A-ZĂÂÎȘȚ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+$/', $term)) {
                $type = 'numeParte';
            } else {
                $type = 'obiectDosar';
            }
            
            $terms[] = [
                'term' => $term,
                'type' => $type
            ];
        }
    }
    
    return $terms;
}

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Rezultate - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .diagnostic-section {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: #f8f9fa;
        }
        .result-details {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .error-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-search me-2 text-primary"></i>
            Diagnostic Rezultate Căutare
        </h1>
        
        <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
            <?php
            $bulkSearchTerms = $_POST['bulkSearchTerms'] ?? '';
            $advancedFilters = [];
            
            if (!empty($bulkSearchTerms)) {
                $searchTermsData = parseBulkSearchTerms($bulkSearchTerms);
                $searchResults = performBulkSearchWithFilters($searchTermsData, $advancedFilters);
                
                $totalResults = array_sum(array_map(function($result) {
                    return count($result['results']);
                }, $searchResults));
            ?>
            
            <div class="diagnostic-section">
                <h3><i class="fas fa-chart-bar me-2"></i>Sumar Rezultate</h3>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo count($searchTermsData); ?></h5>
                                <p class="card-text">Termeni căutați</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo $totalResults; ?></h5>
                                <p class="card-text">Total rezultate</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo count(array_filter($searchResults, function($r) { return !empty($r['error']); })); ?></h5>
                                <p class="card-text">Erori</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo count(array_filter($searchResults, function($r) { return empty($r['error']) && $r['count'] > 0; })); ?></h5>
                                <p class="card-text">Căutări cu rezultate</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="diagnostic-section">
                <h3><i class="fas fa-list me-2"></i>Detalii pe Termen</h3>
                <?php foreach ($searchResults as $index => $result): ?>
                    <div class="result-details">
                        <h5>
                            <i class="fas fa-search me-2"></i>
                            <?php echo htmlspecialchars($result['term']); ?>
                            <span class="badge bg-<?php echo $result['type'] === 'numeParte' ? 'primary' : ($result['type'] === 'numarDosar' ? 'success' : 'info'); ?>">
                                <?php echo $result['type']; ?>
                            </span>
                        </h5>
                        
                        <?php if (!empty($result['error'])): ?>
                            <div class="error-box">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Eroare:</strong> <?php echo htmlspecialchars($result['error']); ?>
                            </div>
                        <?php else: ?>
                            <div class="success-box">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Rezultate găsite:</strong> <?php echo $result['count']; ?>
                            </div>
                            
                            <?php if ($result['count'] === 1000): ?>
                                <div class="warning-box">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Atenție:</strong> S-a atins limita de 1000 de rezultate. Pot exista mai multe rezultate disponibile.
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($result['count'] > 0): ?>
                                <h6>Primele 3 rezultate:</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>Număr Dosar</th>
                                                <th>Instanță</th>
                                                <th>Obiect</th>
                                                <th>Părți</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $displayResults = array_slice($result['results'], 0, 3);
                                            foreach ($displayResults as $dosar): 
                                            ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($dosar->numar ?? ''); ?></td>
                                                    <td><?php echo htmlspecialchars($dosar->institutie ?? ''); ?></td>
                                                    <td><?php echo htmlspecialchars(substr($dosar->obiect ?? '', 0, 50)) . (strlen($dosar->obiect ?? '') > 50 ? '...' : ''); ?></td>
                                                    <td><?php echo count($dosar->parti ?? []); ?> părți</td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                        <h6>Parametri SOAP utilizați:</h6>
                        <div class="code-block"><?php echo json_encode($result['search_params'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Nu au fost introduși termeni de căutare.
                </div>
            <?php endif; ?>
        <?php endif; ?>
        
        <div class="diagnostic-section">
            <h3><i class="fas fa-play me-2"></i>Test Căutare</h3>
            <form method="POST">
                <div class="mb-3">
                    <label for="bulkSearchTerms" class="form-label">Termeni de căutare (unul pe linie):</label>
                    <textarea class="form-control" id="bulkSearchTerms" name="bulkSearchTerms" rows="5" placeholder="POPESCU&#10;IONESCU&#10;123/2024"><?php echo htmlspecialchars($_POST['bulkSearchTerms'] ?? 'POPESCU
IONESCU
BUCURESTI'); ?></textarea>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>
                    Efectuează Diagnostic
                </button>
            </form>
        </div>
        
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-success btn-lg">
                <i class="fas fa-home me-2"></i>
                Înapoi la Portal
            </a>
        </div>
    </div>
</body>
</html>
