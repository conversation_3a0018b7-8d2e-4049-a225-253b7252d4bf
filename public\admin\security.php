<?php

/**
 * Portal Judiciar România - Security Management
 * 
 * Administrative interface for security settings, monitoring,
 * and access control management. Requires super_admin role.
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */

require_once dirname(__DIR__, 2) . '/bootstrap.php';
require_once dirname(__DIR__, 2) . '/includes/config.php';

use App\Helpers\TemplateEngine;
use App\Services\AdminAuthService;
use App\Security\CSRFProtection;
use App\Security\RateLimiter;
use App\Config\Database;

// Session is already started in bootstrap.php

// Initialize services
$templateEngine = new TemplateEngine();

// Require super admin access with system_settings permission
AdminAuthService::requireAdmin('system_settings');

$userId = $_SESSION['user_id'];
$userRole = AdminAuthService::getAdminRole($userId);

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'];
        
        // Validate CSRF token
        if (!CSRFProtection::validateRequest($_POST, $action)) {
            throw new Exception('Invalid CSRF token');
        }
        
        // Check rate limiting
        $rateLimitKey = "admin_security_{$userId}";
        if (!RateLimiter::checkLimit($action, $rateLimitKey)) {
            throw new Exception('Rate limit exceeded. Please try again later.');
        }
        
        switch ($action) {
            case 'update_password_policy':
                $result = updatePasswordPolicy($_POST);
                break;
                
            case 'update_session_settings':
                $result = updateSessionSettings($_POST);
                break;
                
            case 'update_rate_limits':
                $result = updateRateLimits($_POST);
                break;
                
            case 'update_csrf_settings':
                $result = updateCSRFSettings($_POST);
                break;
                
            case 'get_security_logs':
                $result = getSecurityLogs($_POST);
                break;
                
            case 'resolve_security_incident':
                $result = resolveSecurityIncident($_POST, $userId);
                break;
                
            case 'update_ip_whitelist':
                $result = updateIPWhitelist($_POST);
                break;
                
            case 'update_access_control':
                $result = updateAccessControl($_POST);
                break;
                
            default:
                throw new Exception('Unknown action');
        }
        
        // Log admin action
        AdminAuthService::logAdminAction($userId, $action, [
            'success' => $result['success'] ?? false,
            'details' => $result['message'] ?? ''
        ]);
        
        echo json_encode($result);
        
    } catch (Exception $e) {
        RateLimiter::recordAttempt($_POST['action'] ?? 'unknown', $rateLimitKey, false, [
            'error' => $e->getMessage()
        ]);
        
        AdminAuthService::logAdminAction($userId, $_POST['action'] ?? 'unknown', [
            'success' => false,
            'error' => $e->getMessage()
        ]);
        
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// Get current security settings
$securitySettings = getSecuritySettings();
$securityStats = getSecurityStatistics();
$recentIncidents = getRecentSecurityIncidents();
$ipWhitelist = getIPWhitelist();

// Prepare template data
$data = [
    'page_title' => 'Administrare Securitate - Portal Judiciar România',
    'user_name' => $_SESSION['user_name'] ?? 'Administrator',
    'user_role' => $userRole,
    'security_settings' => $securitySettings,
    'security_stats' => $securityStats,
    'recent_incidents' => $recentIncidents,
    'ip_whitelist' => $ipWhitelist,
    'csrf_tokens' => [
        'update_password_policy' => CSRFProtection::generateToken('update_password_policy'),
        'update_session_settings' => CSRFProtection::generateToken('update_session_settings'),
        'update_rate_limits' => CSRFProtection::generateToken('update_rate_limits'),
        'update_csrf_settings' => CSRFProtection::generateToken('update_csrf_settings'),
        'get_security_logs' => CSRFProtection::generateToken('get_security_logs'),
        'resolve_security_incident' => CSRFProtection::generateToken('resolve_security_incident'),
        'update_ip_whitelist' => CSRFProtection::generateToken('update_ip_whitelist'),
        'update_access_control' => CSRFProtection::generateToken('update_access_control')
    ]
];

// Render template
echo $templateEngine->render('admin/security.twig', $data);

/**
 * Get current security settings
 */
function getSecuritySettings(): array
{
    try {
        $settings = Database::fetchAll("
            SELECT setting_key, setting_value, description, updated_at
            FROM system_settings 
            WHERE category = 'security'
            ORDER BY setting_key
        ");
        
        $result = [];
        foreach ($settings as $setting) {
            $result[$setting['setting_key']] = [
                'value' => $setting['setting_value'],
                'description' => $setting['description'],
                'updated_at' => $setting['updated_at']
            ];
        }
        
        // Set defaults if not found
        $defaults = [
            'password_min_length' => ['value' => '8', 'description' => 'Lungimea minimă a parolei'],
            'password_require_uppercase' => ['value' => '1', 'description' => 'Necesită litere mari'],
            'password_require_lowercase' => ['value' => '1', 'description' => 'Necesită litere mici'],
            'password_require_numbers' => ['value' => '1', 'description' => 'Necesită cifre'],
            'password_require_symbols' => ['value' => '1', 'description' => 'Necesită simboluri'],
            'session_timeout' => ['value' => '3600', 'description' => 'Timeout sesiune (secunde)'],
            'max_login_attempts' => ['value' => '5', 'description' => 'Încercări maxime de login'],
            'lockout_duration' => ['value' => '900', 'description' => 'Durata blocare (secunde)'],
            'csrf_token_lifetime' => ['value' => '3600', 'description' => 'Durata token CSRF (secunde)'],
            'rate_limit_login' => ['value' => '5', 'description' => 'Limite rate login per oră'],
            'rate_limit_api' => ['value' => '100', 'description' => 'Limite rate API per oră'],
            'enable_2fa' => ['value' => '0', 'description' => 'Activează autentificare cu doi factori']
        ];
        
        return array_merge($defaults, $result);
        
    } catch (Exception $e) {
        error_log("Failed to get security settings: " . $e->getMessage());
        return [];
    }
}

/**
 * Get security statistics
 */
function getSecurityStatistics(): array
{
    try {
        $stats = Database::fetchOne("
            SELECT 
                COUNT(*) as total_incidents,
                COUNT(CASE WHEN severity = 'high' OR severity = 'critical' THEN 1 END) as high_severity,
                COUNT(CASE WHEN resolved = 0 THEN 1 END) as unresolved,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as last_24h,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as last_7d
            FROM security_incidents
        ");
        
        $loginStats = Database::fetchOne("
            SELECT
                COUNT(*) as total_attempts,
                COUNT(CASE WHEN success = 0 THEN 1 END) as failed_attempts,
                COUNT(CASE WHEN attempted_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as attempts_24h
            FROM login_attempts
            WHERE attempted_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");
        
        return [
            'incidents' => $stats ?: [],
            'login_attempts' => $loginStats ?: []
        ];
        
    } catch (Exception $e) {
        error_log("Failed to get security statistics: " . $e->getMessage());
        return ['incidents' => [], 'login_attempts' => []];
    }
}

/**
 * Get recent security incidents
 */
function getRecentSecurityIncidents($limit = 10): array
{
    try {
        return Database::fetchAll("
            SELECT 
                id, incident_type, severity, description, ip_address, 
                user_agent, resolved, created_at, resolved_at
            FROM security_incidents
            ORDER BY created_at DESC
            LIMIT ?
        ", [$limit]);
        
    } catch (Exception $e) {
        error_log("Failed to get recent security incidents: " . $e->getMessage());
        return [];
    }
}

/**
 * Get IP whitelist
 */
function getIPWhitelist(): array
{
    try {
        return Database::fetchAll("
            SELECT ip_address, description, created_at, created_by
            FROM ip_whitelist
            WHERE is_active = 1
            ORDER BY created_at DESC
        ");

    } catch (Exception $e) {
        error_log("Failed to get IP whitelist: " . $e->getMessage());
        return [];
    }
}

/**
 * Update password policy settings
 */
function updatePasswordPolicy($data): array
{
    try {
        $settings = [
            'password_min_length' => (int)($data['min_length'] ?? 8),
            'password_require_uppercase' => isset($data['require_uppercase']) ? 1 : 0,
            'password_require_lowercase' => isset($data['require_lowercase']) ? 1 : 0,
            'password_require_numbers' => isset($data['require_numbers']) ? 1 : 0,
            'password_require_symbols' => isset($data['require_symbols']) ? 1 : 0
        ];

        foreach ($settings as $key => $value) {
            Database::execute("
                INSERT INTO system_settings (setting_key, setting_value, category, updated_at)
                VALUES (?, ?, 'security', NOW())
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
            ", [$key, $value]);
        }

        return ['success' => true, 'message' => 'Politica de parole a fost actualizată cu succes'];

    } catch (Exception $e) {
        error_log("Failed to update password policy: " . $e->getMessage());
        return ['success' => false, 'error' => 'Eroare la actualizarea politicii de parole'];
    }
}

/**
 * Update session settings
 */
function updateSessionSettings($data): array
{
    try {
        $settings = [
            'session_timeout' => (int)($data['timeout'] ?? 3600),
            'max_login_attempts' => (int)($data['max_attempts'] ?? 5),
            'lockout_duration' => (int)($data['lockout_duration'] ?? 900)
        ];

        foreach ($settings as $key => $value) {
            Database::execute("
                INSERT INTO system_settings (setting_key, setting_value, category, updated_at)
                VALUES (?, ?, 'security', NOW())
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
            ", [$key, $value]);
        }

        return ['success' => true, 'message' => 'Setările de sesiune au fost actualizate cu succes'];

    } catch (Exception $e) {
        error_log("Failed to update session settings: " . $e->getMessage());
        return ['success' => false, 'error' => 'Eroare la actualizarea setărilor de sesiune'];
    }
}

/**
 * Update rate limiting settings
 */
function updateRateLimits($data): array
{
    try {
        $settings = [
            'rate_limit_login' => (int)($data['login_limit'] ?? 5),
            'rate_limit_api' => (int)($data['api_limit'] ?? 100),
            'rate_limit_contact' => (int)($data['contact_limit'] ?? 5)
        ];

        foreach ($settings as $key => $value) {
            Database::execute("
                INSERT INTO system_settings (setting_key, setting_value, category, updated_at)
                VALUES (?, ?, 'security', NOW())
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
            ", [$key, $value]);
        }

        return ['success' => true, 'message' => 'Limitele de rate au fost actualizate cu succes'];

    } catch (Exception $e) {
        error_log("Failed to update rate limits: " . $e->getMessage());
        return ['success' => false, 'error' => 'Eroare la actualizarea limitelor de rate'];
    }
}

/**
 * Update CSRF settings
 */
function updateCSRFSettings($data): array
{
    try {
        $settings = [
            'csrf_token_lifetime' => (int)($data['token_lifetime'] ?? 3600),
            'csrf_strict_mode' => isset($data['strict_mode']) ? 1 : 0
        ];

        foreach ($settings as $key => $value) {
            Database::execute("
                INSERT INTO system_settings (setting_key, setting_value, category, updated_at)
                VALUES (?, ?, 'security', NOW())
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
            ", [$key, $value]);
        }

        return ['success' => true, 'message' => 'Setările CSRF au fost actualizate cu succes'];

    } catch (Exception $e) {
        error_log("Failed to update CSRF settings: " . $e->getMessage());
        return ['success' => false, 'error' => 'Eroare la actualizarea setărilor CSRF'];
    }
}

/**
 * Get security logs
 */
function getSecurityLogs($data): array
{
    try {
        $page = (int)($data['page'] ?? 1);
        $limit = 20;
        $offset = ($page - 1) * $limit;

        $logs = Database::fetchAll("
            SELECT
                dpl.action, dpl.context, dpl.created_at, dpl.ip_address,
                u.first_name, u.last_name, u.email
            FROM data_processing_logs dpl
            LEFT JOIN users u ON u.id = dpl.user_id
            WHERE dpl.action LIKE '%security%' OR dpl.action LIKE '%admin%'
            ORDER BY dpl.created_at DESC
            LIMIT ? OFFSET ?
        ", [$limit, $offset]);

        $total = Database::fetchOne("
            SELECT COUNT(*) as total
            FROM data_processing_logs
            WHERE action LIKE '%security%' OR action LIKE '%admin%'
        ")['total'] ?? 0;

        return [
            'success' => true,
            'logs' => $logs,
            'pagination' => [
                'current_page' => $page,
                'total_pages' => ceil($total / $limit),
                'total_records' => $total
            ]
        ];

    } catch (Exception $e) {
        error_log("Failed to get security logs: " . $e->getMessage());
        return ['success' => false, 'error' => 'Eroare la încărcarea jurnalelor de securitate'];
    }
}

/**
 * Resolve security incident
 */
function resolveSecurityIncident($data, $userId): array
{
    try {
        $incidentId = (int)($data['incident_id'] ?? 0);
        $resolution = trim($data['resolution'] ?? '');

        if (!$incidentId) {
            throw new Exception('ID incident invalid');
        }

        $result = Database::update('security_incidents', [
            'resolved' => 1,
            'resolved_at' => date('Y-m-d H:i:s'),
            'resolved_by' => $userId,
            'resolution' => $resolution
        ], ['id' => $incidentId]);

        if ($result > 0) {
            return ['success' => true, 'message' => 'Incidentul de securitate a fost rezolvat cu succes'];
        } else {
            throw new Exception('Incidentul nu a fost găsit');
        }

    } catch (Exception $e) {
        error_log("Failed to resolve security incident: " . $e->getMessage());
        return ['success' => false, 'error' => 'Eroare la rezolvarea incidentului de securitate'];
    }
}

/**
 * Update IP whitelist
 */
function updateIPWhitelist($data): array
{
    try {
        $action = $data['whitelist_action'] ?? '';
        $ipAddress = trim($data['ip_address'] ?? '');
        $description = trim($data['description'] ?? '');

        if (!$ipAddress) {
            throw new Exception('Adresa IP este obligatorie');
        }

        // Validate IP address
        if (!filter_var($ipAddress, FILTER_VALIDATE_IP)) {
            throw new Exception('Adresa IP nu este validă');
        }

        if ($action === 'add') {
            // Check if IP already exists
            $existing = Database::fetchOne("
                SELECT id FROM ip_whitelist WHERE ip_address = ? AND is_active = 1
            ", [$ipAddress]);

            if ($existing) {
                throw new Exception('Adresa IP este deja în whitelist');
            }

            Database::insert('ip_whitelist', [
                'ip_address' => $ipAddress,
                'description' => $description,
                'created_by' => $_SESSION['user_id'],
                'created_at' => date('Y-m-d H:i:s'),
                'is_active' => 1
            ]);

            return ['success' => true, 'message' => 'Adresa IP a fost adăugată în whitelist cu succes'];

        } elseif ($action === 'remove') {
            $result = Database::update('ip_whitelist', [
                'is_active' => 0,
                'updated_at' => date('Y-m-d H:i:s')
            ], ['ip_address' => $ipAddress]);

            if ($result > 0) {
                return ['success' => true, 'message' => 'Adresa IP a fost eliminată din whitelist cu succes'];
            } else {
                throw new Exception('Adresa IP nu a fost găsită în whitelist');
            }
        } else {
            throw new Exception('Acțiune necunoscută');
        }

    } catch (Exception $e) {
        error_log("Failed to update IP whitelist: " . $e->getMessage());
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Update access control settings
 */
function updateAccessControl($data): array
{
    try {
        $settings = [
            'enable_2fa' => isset($data['enable_2fa']) ? 1 : 0,
            'require_email_verification' => isset($data['require_email_verification']) ? 1 : 0,
            'enable_ip_whitelist' => isset($data['enable_ip_whitelist']) ? 1 : 0,
            'enable_geo_blocking' => isset($data['enable_geo_blocking']) ? 1 : 0
        ];

        foreach ($settings as $key => $value) {
            Database::execute("
                INSERT INTO system_settings (setting_key, setting_value, category, updated_at)
                VALUES (?, ?, 'security', NOW())
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
            ", [$key, $value]);
        }

        return ['success' => true, 'message' => 'Setările de control acces au fost actualizate cu succes'];

    } catch (Exception $e) {
        error_log("Failed to update access control: " . $e->getMessage());
        return ['success' => false, 'error' => 'Eroare la actualizarea setărilor de control acces'];
    }
}
