<?php
require_once 'src/Helpers/SEOHelper.php';
use App\Helpers\SEOHelper;

echo "=== ORGANIZATION SCHEMA TEST ===\n";
$orgSchema = SEOHelper::generateOrganizationSchema();
echo $orgSchema . "\n\n";

echo "=== WEBSITE SCHEMA TEST ===\n";
$websiteSchema = SEOHelper::generateWebSiteSchema();
echo $websiteSchema . "\n\n";

echo "=== JSON VALIDATION ===\n";
$orgData = json_decode($orgSchema, true);
$websiteData = json_decode($websiteSchema, true);

if (json_last_error() === JSON_ERROR_NONE) {
    echo "✓ Organization Schema: JSON valid\n";
    echo "  - @type: " . $orgData['@type'] . "\n";
    echo "  - name: " . ($orgData['name'] ?? 'MISSING') . "\n";
    echo "  - url: " . ($orgData['url'] ?? 'MISSING') . "\n";
    echo "  - logo: " . ($orgData['logo'] ?? 'MISSING') . "\n";
} else {
    echo "✗ Organization Schema: JSON invalid - " . json_last_error_msg() . "\n";
}

if (json_last_error() === JSON_ERROR_NONE) {
    echo "✓ WebSite Schema: JSON valid\n";
    echo "  - @type: " . $websiteData['@type'] . "\n";
    echo "  - name: " . ($websiteData['name'] ?? 'MISSING') . "\n";
    echo "  - url: " . ($websiteData['url'] ?? 'MISSING') . "\n";
    echo "  - potentialAction: " . (isset($websiteData['potentialAction']) ? 'PRESENT' : 'MISSING') . "\n";
    if (isset($websiteData['potentialAction'][0]['target']['urlTemplate'])) {
        echo "  - urlTemplate: " . $websiteData['potentialAction'][0]['target']['urlTemplate'] . "\n";
    }
} else {
    echo "✗ WebSite Schema: JSON invalid - " . json_last_error_msg() . "\n";
}

echo "\n=== GOOGLE STRUCTURED DATA REQUIREMENTS ===\n";
echo "Organization Schema Requirements:\n";
echo "- @type: GovernmentOrganization ✓\n";
echo "- name: " . (isset($orgData['name']) && !empty($orgData['name']) ? '✓' : '✗') . "\n";
echo "- url: " . (isset($orgData['url']) && !empty($orgData['url']) ? '✓' : '✗') . "\n";
echo "- logo: " . (isset($orgData['logo']) && !empty($orgData['logo']) ? '✓' : '✗') . "\n";

echo "\nWebSite Schema Requirements:\n";
echo "- @type: WebSite ✓\n";
echo "- name: " . (isset($websiteData['name']) && !empty($websiteData['name']) ? '✓' : '✗') . "\n";
echo "- url: " . (isset($websiteData['url']) && !empty($websiteData['url']) ? '✓' : '✗') . "\n";
echo "- potentialAction: " . (isset($websiteData['potentialAction']) ? '✓' : '✗') . "\n";
?>
