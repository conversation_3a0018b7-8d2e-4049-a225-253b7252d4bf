<?php
/**
 * Test admin simple - fără dependențe de tabele
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Test Admin Simple</h1>";

try {
    require_once __DIR__ . '/bootstrap.php';
    echo "<p style='color: green;'>✅ Bootstrap loaded</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Bootstrap failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}

// Simulez o sesiune admin
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Test Admin';
$_SESSION['admin_role'] = 'super_admin';

echo "<p>Session user_id: " . ($_SESSION['user_id'] ?? 'not set') . "</p>";
echo "<p>Session admin_role: " . ($_SESSION['admin_role'] ?? 'not set') . "</p>";

try {
    use App\Helpers\TemplateEngine;
    use App\Services\AdminAuthService;
    
    $templateEngine = new TemplateEngine();
    echo "<p style='color: green;'>✅ TemplateEngine created</p>";
    
    // Test template rendering cu date simple
    $templateData = [
        'page_title' => 'Test Admin Dashboard',
        'user_name' => 'Test Admin',
        'user_role' => 'super_admin',
        'user_permissions' => ['view_users', 'manage_users'],
        'stats' => [
            'total_users' => 5,
            'active_users' => 3,
            'total_sessions' => 10
        ],
        'recent_activity' => [
            ['action' => 'User login', 'timestamp' => date('Y-m-d H:i:s')],
            ['action' => 'Data export', 'timestamp' => date('Y-m-d H:i:s')]
        ],
        'monitoring_stats' => [
            'total_users' => 0,
            'active_users' => 0,
            'total_monitored_cases' => 0,
            'notifications_today' => 0,
            'notifications_pending' => 0,
            'cases_checked_today' => 0,
            'changes_this_week' => 0
        ],
        'all_users' => [],
        'all_monitored_cases' => [],
        'system_health' => [
            'database_status' => 'healthy',
            'recent_errors' => 0,
            'failed_notifications' => 0,
            'stale_cases' => 0,
            'overall_score' => 100
        ],
        'csrf_tokens' => [
            'get_user_details' => 'test_token_123',
            'update_user_status' => 'test_token_456'
        ],
        'rate_limits' => [
            'get_user_details' => 10,
            'update_user_status' => 5
        ],
        'current_time' => date('Y-m-d H:i:s')
    ];
    
    echo "<p style='color: green;'>✅ Template data prepared</p>";
    
    $output = $templateEngine->render('admin/dashboard.twig', $templateData);
    echo "<p style='color: green;'>✅ Template rendered successfully</p>";
    echo "<p>Output length: " . strlen($output) . " characters</p>";
    
    if (strlen($output) > 1000) {
        echo "<h3>Template Output (first 1000 chars):</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 300px; overflow: auto;'>";
        echo htmlspecialchars(substr($output, 0, 1000));
        echo "</pre>";
    } else {
        echo "<h3>Full Template Output:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 300px; overflow: auto;'>";
        echo htmlspecialchars($output);
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='public/admin/'>Try real admin page</a></p>";
?>
