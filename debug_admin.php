<?php
/**
 * Debug script pentru admin
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== DEBUG ADMIN PORTAL ===\n";
echo "PHP Version: " . phpversion() . "\n";
echo "Current directory: " . getcwd() . "\n";
echo "Script path: " . __FILE__ . "\n\n";

// Test 1: Bootstrap loading
echo "1. Testing bootstrap loading...\n";
try {
    require_once __DIR__ . '/bootstrap.php';
    echo "   ✅ Bootstrap loaded successfully\n";
} catch (Exception $e) {
    echo "   ❌ Bootstrap failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Database connection
echo "\n2. Testing database connection...\n";
try {
    $db = App\Config\Database::getConnection();
    echo "   ✅ Database connection successful\n";
} catch (Exception $e) {
    echo "   ❌ Database connection failed: " . $e->getMessage() . "\n";
}

// Test 3: Admin auth service
echo "\n3. Testing AdminAuthService...\n";
try {
    $authService = new App\Services\AdminAuthService();
    echo "   ✅ AdminAuthService loaded\n";
} catch (Exception $e) {
    echo "   ❌ AdminAuthService failed: " . $e->getMessage() . "\n";
}

// Test 4: Template engine
echo "\n4. Testing TemplateEngine...\n";
try {
    $templateEngine = new App\Helpers\TemplateEngine();
    echo "   ✅ TemplateEngine loaded\n";
} catch (Exception $e) {
    echo "   ❌ TemplateEngine failed: " . $e->getMessage() . "\n";
}

// Test 5: Check admin files
echo "\n5. Checking admin files...\n";
$adminFiles = [
    'public/admin/index.php',
    'src/Templates/admin/dashboard.twig',
    'src/Services/AdminAuthService.php',
    'src/Helpers/TemplateEngine.php'
];

foreach ($adminFiles as $file) {
    if (file_exists($file)) {
        echo "   ✅ $file exists\n";
        
        // Check syntax for PHP files
        if (pathinfo($file, PATHINFO_EXTENSION) === 'php') {
            $output = [];
            $return_var = 0;
            exec("php -l \"$file\" 2>&1", $output, $return_var);
            if ($return_var === 0) {
                echo "      ✅ Syntax OK\n";
            } else {
                echo "      ❌ Syntax error: " . implode("\n", $output) . "\n";
            }
        }
    } else {
        echo "   ❌ $file missing\n";
    }
}

// Test 6: Check permissions
echo "\n6. Checking permissions...\n";
$dirs = ['logs', 'src/Templates', 'public/admin'];
foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        if (is_readable($dir)) {
            echo "   ✅ $dir is readable\n";
        } else {
            echo "   ❌ $dir is not readable\n";
        }
        if (is_writable($dir)) {
            echo "   ✅ $dir is writable\n";
        } else {
            echo "   ⚠️ $dir is not writable\n";
        }
    } else {
        echo "   ❌ $dir does not exist\n";
    }
}

// Test 7: Check constants
echo "\n7. Checking constants...\n";
$constants = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASS', 'SOAP_WSDL'];
foreach ($constants as $const) {
    if (defined($const)) {
        echo "   ✅ $const is defined\n";
    } else {
        echo "   ❌ $const is not defined\n";
    }
}

echo "\n=== DEBUG COMPLETE ===\n";
