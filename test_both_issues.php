<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <title>Test Probleme - Portal Judiciar <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 2px solid #007bff; border-radius: 8px; padding: 20px; margin: 15px 0; background: #f8f9fa; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin: 5px 0; }
        
        /* CSS pentru filtrele avansate - copiat din index.php */
        #advancedFiltersToggle {
            text-decoration: none;
            padding: 0.75rem 1rem;
            border: 1px solid #007bff;
            border-radius: 6px;
            background-color: rgba(0, 123, 255, 0.05);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        #advancedFiltersToggle:hover {
            background-color: rgba(0, 123, 255, 0.1);
            text-decoration: none;
            color: #007bff !important;
            transform: translateY(-1px);
        }

        #advancedFiltersToggle i.fa-chevron-down {
            transition: transform 0.3s ease;
        }

        #advancedFiltersToggle.expanded i.fa-chevron-down {
            transform: rotate(180deg);
        }

        #advancedFilters {
            overflow: hidden;
            transition: all 0.3s ease;
            max-height: 0;
            opacity: 0;
            padding: 0;
            margin-top: 0;
            border: none;
            background-color: transparent;
        }

        #advancedFilters.show {
            max-height: 1000px;
            opacity: 1;
            padding: 1.25rem;
            margin-top: 1.25rem;
            border: 1px solid #e9ecef;
            background-color: #f8f9fa;
        }

        /* CSS pentru rezultate */
        .term-results {
            margin-bottom: 2rem;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            overflow: hidden;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .term-header {
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .term-header:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        .term-content {
            padding: 1.5rem;
            background: white;
        }

        .toggle-icon {
            transition: transform 0.2s ease;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Test Probleme Portal Judiciar</h1>
        
        <div class="test-section">
            <h3>1. Test Filtre Avansate</h3>
            
            <!-- Advanced Filters Toggle Button -->
            <div class="mt-3">
                <a href="#" id="advancedFiltersToggle" class="text-primary d-flex align-items-center justify-content-center">
                    <i class="fas fa-filter me-2"></i>
                    <span>Arată filtrele avansate</span>
                    <i class="fas fa-chevron-down ms-2"></i>
                </a>
            </div>

            <!-- Advanced Filters Section -->
            <div id="advancedFilters" class="advanced-filters-section mt-4">
                <h6 class="mb-3">
                    <i class="fas fa-filter me-2"></i>
                    Filtre avansate
                </h6>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="institutie" class="form-label">Instanța</label>
                        <select class="form-select" id="institutie" name="institutie">
                            <option value="">Toate instanțele</option>
                            <option value="test1">Test Instanță 1</option>
                            <option value="test2">Test Instanță 2</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="categorieCaz" class="form-label">Categoria cazului</label>
                        <select class="form-select" id="categorieCaz" name="categorieCaz">
                            <option value="">Toate categoriile</option>
                            <option value="civil">Civil</option>
                            <option value="penal">Penal</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div id="advancedFiltersResult" class="mt-3"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Expandare Rezultate</h3>
            
            <div class="mb-3">
                <button type="button" class="btn btn-sm btn-outline-primary me-2" id="expandAllBtn">
                    <i class="fas fa-expand-alt me-1"></i>
                    Expandează toate
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" id="collapseAllBtn">
                    <i class="fas fa-compress-alt me-1"></i>
                    Restrânge toate
                </button>
            </div>
            
            <!-- Simulăm structura de rezultate -->
            <div class="term-results">
                <div class="term-header" onclick="toggleTermResults(0)">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">
                                <i class="fas fa-search me-2"></i>
                                Căutare pentru: <strong>TEST</strong>
                                <span class="badge bg-primary ms-2">2 rezultate</span>
                            </h6>
                        </div>
                        <div>
                            <i class="fas fa-chevron-down toggle-icon" id="toggleIcon0"></i>
                        </div>
                    </div>
                </div>

                <div class="term-content" id="termContent0" style="display: none;">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Nr. Dosar</th>
                                    <th>Instanța</th>
                                    <th>Părți</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>123/2024</strong></td>
                                    <td>Test Instanță</td>
                                    <td>TEST MARIA vs TEST GHEORGHE</td>
                                </tr>
                                <tr>
                                    <td><strong>456/2024</strong></td>
                                    <td>Test Tribunal</td>
                                    <td>TEST ADRIAN vs SC TEST SRL</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div id="expandResult" class="mt-3"></div>
        </div>

        <div class="test-section">
            <h3>3. Verificare Console și Erori</h3>
            <button onclick="checkConsole()" class="btn btn-info">Verifică Console</button>
            <div id="consoleResult" class="mt-2"></div>
        </div>
    </div>

    <script>
        // Notification system
        function showNotification(message, type = 'info') {
            console.log(`Notification: ${message} (${type})`);
        }

        // Advanced Filters Toggle
        function initAdvancedFiltersToggle() {
            const toggleButton = document.getElementById('advancedFiltersToggle');
            const filtersSection = document.getElementById('advancedFilters');
            const toggleText = toggleButton?.querySelector('span');
            const chevronIcon = toggleButton?.querySelector('i.fa-chevron-down');

            console.log('Advanced filters elements:', {
                toggleButton: !!toggleButton,
                filtersSection: !!filtersSection,
                toggleText: !!toggleText,
                chevronIcon: !!chevronIcon
            });

            if (!toggleButton || !filtersSection || !toggleText) {
                console.warn('Advanced filters toggle elements not found');
                document.getElementById('advancedFiltersResult').innerHTML = 
                    '<div class="error">❌ Elementele pentru filtrele avansate nu au fost găsite!</div>';
                return;
            }

            // Function to show filters
            function showFilters() {
                filtersSection.classList.add('show');
                toggleText.textContent = 'Ascunde filtrele avansate';
                toggleButton.classList.add('expanded');
                toggleButton.setAttribute('aria-expanded', 'true');
                console.log('Filters shown');
            }

            // Function to hide filters
            function hideFilters() {
                filtersSection.classList.remove('show');
                toggleText.textContent = 'Arată filtrele avansate';
                toggleButton.classList.remove('expanded');
                toggleButton.setAttribute('aria-expanded', 'false');
                console.log('Filters hidden');
            }

            // Toggle click handler
            toggleButton.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Advanced filters toggle clicked');
                
                if (filtersSection.classList.contains('show')) {
                    hideFilters();
                    document.getElementById('advancedFiltersResult').innerHTML = 
                        '<div class="success">✅ Filtrele avansate au fost ascunse</div>';
                } else {
                    showFilters();
                    document.getElementById('advancedFiltersResult').innerHTML = 
                        '<div class="success">✅ Filtrele avansate au fost afișate</div>';
                }
            });

            document.getElementById('advancedFiltersResult').innerHTML = 
                '<div class="success">✅ Toggle-ul pentru filtrele avansate a fost inițializat</div>';
        }

        // Expand/Collapse Functions
        function expandAllResults() {
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                console.log('Expanding all results - found', termContents.length, 'content elements and', toggleIcons.length, 'icon elements');

                if (termContents.length === 0) {
                    document.getElementById('expandResult').innerHTML = 
                        '<div class="warning">⚠️ Nu există secțiuni de rezultate pentru expandare</div>';
                    return;
                }

                termContents.forEach(content => {
                    content.style.display = 'block';
                });

                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-up toggle-icon';
                });

                document.getElementById('expandResult').innerHTML = 
                    '<div class="success">✅ Toate secțiunile au fost expandate</div>';
            } catch (error) {
                console.error('Error in expandAllResults:', error);
                document.getElementById('expandResult').innerHTML = 
                    '<div class="error">❌ Eroare la expandarea secțiunilor: ' + error.message + '</div>';
            }
        }

        function collapseAllResults() {
            try {
                const termContents = document.querySelectorAll('[id^="termContent"]');
                const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');

                console.log('Collapsing all results - found', termContents.length, 'content elements and', toggleIcons.length, 'icon elements');

                if (termContents.length === 0) {
                    document.getElementById('expandResult').innerHTML = 
                        '<div class="warning">⚠️ Nu există secțiuni de rezultate pentru restrângere</div>';
                    return;
                }

                termContents.forEach(content => {
                    content.style.display = 'none';
                });

                toggleIcons.forEach(icon => {
                    icon.className = 'fas fa-chevron-down toggle-icon';
                });

                document.getElementById('expandResult').innerHTML = 
                    '<div class="success">✅ Toate secțiunile au fost restrânse</div>';
            } catch (error) {
                console.error('Error in collapseAllResults:', error);
                document.getElementById('expandResult').innerHTML = 
                    '<div class="error">❌ Eroare la restrângerea secțiunilor: ' + error.message + '</div>';
            }
        }

        function toggleTermResults(index) {
            try {
                const content = document.getElementById('termContent' + index);
                const icon = document.getElementById('toggleIcon' + index);

                console.log('Toggling term results for index:', index);

                if (!content) {
                    console.error('Content element not found for index:', index);
                    document.getElementById('expandResult').innerHTML = 
                        '<div class="error">❌ Secțiunea nu a fost găsită</div>';
                    return;
                }

                if (content.style.display === 'none' || content.style.display === '') {
                    content.style.display = 'block';
                    if (icon) {
                        icon.className = 'fas fa-chevron-up toggle-icon';
                    }
                    document.getElementById('expandResult').innerHTML = 
                        '<div class="success">✅ Secțiunea a fost expandată</div>';
                } else {
                    content.style.display = 'none';
                    if (icon) {
                        icon.className = 'fas fa-chevron-down toggle-icon';
                    }
                    document.getElementById('expandResult').innerHTML = 
                        '<div class="success">✅ Secțiunea a fost restrânsă</div>';
                }
            } catch (error) {
                console.error('Error in toggleTermResults:', error);
                document.getElementById('expandResult').innerHTML = 
                    '<div class="error">❌ Eroare la comutarea secțiunii: ' + error.message + '</div>';
            }
        }

        function checkConsole() {
            const resultDiv = document.getElementById('consoleResult');
            let html = '<div class="mt-2">';
            html += '<strong>Verificare Funcționalitate:</strong><br>';
            
            // Check if functions exist
            const functions = ['expandAllResults', 'collapseAllResults', 'toggleTermResults', 'initAdvancedFiltersToggle'];
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    html += `✅ ${func} - definită<br>`;
                } else {
                    html += `❌ ${func} - lipsește<br>`;
                }
            });
            
            // Check elements
            const elements = [
                'advancedFiltersToggle',
                'advancedFilters', 
                'expandAllBtn',
                'collapseAllBtn',
                'termContent0',
                'toggleIcon0'
            ];
            
            html += '<br><strong>Elemente DOM:</strong><br>';
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    html += `✅ ${id} - găsit<br>`;
                } else {
                    html += `❌ ${id} - lipsește<br>`;
                }
            });
            
            html += '</div>';
            resultDiv.innerHTML = html;
        }

        // Initialize expand/collapse buttons
        function initExpandCollapseButtons() {
            const expandBtn = document.getElementById('expandAllBtn');
            const collapseBtn = document.getElementById('collapseAllBtn');

            if (expandBtn) {
                expandBtn.addEventListener('click', function() {
                    console.log('Expand All button clicked');
                    expandAllResults();
                });
            }

            if (collapseBtn) {
                collapseBtn.addEventListener('click', function() {
                    console.log('Collapse All button clicked');
                    collapseAllResults();
                });
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            initAdvancedFiltersToggle();
            initExpandCollapseButtons();
            checkConsole();

            // Test global function availability
            console.log('Testing global functions:');
            console.log('window.expandAllResults:', typeof window.expandAllResults);
            console.log('window.collapseAllResults:', typeof window.collapseAllResults);
            console.log('window.toggleTermResults:', typeof window.toggleTermResults);
            console.log('window.clearActiveFilters:', typeof window.clearActiveFilters);
        });
    </script>
</body>
</html>
