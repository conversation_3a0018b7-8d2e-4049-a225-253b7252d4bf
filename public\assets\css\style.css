/*
 * Portal Judiciar - Stiluri principale
 */

/* Variabile */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --text-color: #333;
    --border-radius: 0.25rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Stiluri generale */
body {
    font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
    color: var(--text-color);
    background-color: #f8f9fa;
    line-height: 1.6;
}

a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color 0.2s ease-in-out;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

/* Header */
header {
    box-shadow: var(--box-shadow);
}

header h1 {
    font-size: 1.75rem;
    font-weight: 600;
}

/* Card */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
}

.card-header {
    border-bottom: none;
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
}

.card-body {
    padding: 1.25rem;
}

/* Tabele */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: rgba(0, 0, 0, 0.03);
    font-weight: 600;
    border-top: none;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.04);
}

/* Formulare */
.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Alerte */
.alert {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Footer */
footer {
    margin-top: 2rem;
}

footer h5 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

footer a {
    color: rgba(255, 255, 255, 0.8);
    transition: color 0.2s ease-in-out;
}

footer a:hover {
    color: #fff;
    text-decoration: none;
}

/* Breadcrumb */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
}

/* Butoane */
.btn {
    font-weight: 500;
    border-radius: var(--border-radius);
    padding: 0.375rem 0.75rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Tooltips */
.tooltip {
    font-size: 0.875rem;
}

/* Modal */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #f8f9fa;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #f8f9fa;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Stiluri pentru printare */
@media print {
    header, footer, .breadcrumb, .dropdown, .btn, .modal, .no-print {
        display: none !important;
    }

    body {
        background-color: #fff;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        margin-bottom: 1rem;
    }

    .card-header {
        background-color: #f8f9fa !important;
        color: #000 !important;
    }

    .table {
        width: 100% !important;
    }

    .table th {
        background-color: #f8f9fa !important;
    }

    .container {
        max-width: 100%;
        width: 100%;
        padding: 0;
        margin: 0;
    }
}

/* Stiluri pentru notificări */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    width: 350px;
    max-width: 90%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

#notification {
    margin: 0;
    padding: 15px;
    border-radius: 4px;
    font-weight: 500;
}
