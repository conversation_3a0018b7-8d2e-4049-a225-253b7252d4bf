<?php
/**
 * Debug script to find specific party "SARAGEA TUDORIŢA" in case 130/98/2022
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Test parameters
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';
$searchParty = 'SARAGEA TUDORIŢA';
$searchQuality = 'Intervenient în numele altei persoane';

echo "<h1>Debug: Specific Party Search Issue</h1>";
echo "<p><strong>Case:</strong> {$numarDosar} from {$institutie}</p>";
echo "<p><strong>Looking for:</strong> {$searchParty}</p>";
echo "<p><strong>Expected Quality:</strong> {$searchQuality}</p>";
echo "<hr>";

try {
    // Initialize service
    $dosarService = new DosarService();
    
    // Get case details
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if (!$dosar || empty((array)$dosar)) {
        echo "<p style='color: red;'>ERROR: No case data returned</p>";
        exit;
    }
    
    $totalParties = count($dosar->parti);
    echo "<h2>Data Analysis</h2>";
    echo "<p><strong>Total parties found:</strong> {$totalParties}</p>";
    
    // Search for the specific party
    $foundParty = false;
    $foundIndex = -1;
    $exactMatches = [];
    $partialMatches = [];
    $qualityMatches = [];
    
    echo "<h3>Searching for '{$searchParty}'...</h3>";
    
    foreach ($dosar->parti as $index => $parte) {
        $partyName = $parte['nume'] ?? '';
        $partyQuality = $parte['calitate'] ?? '';
        
        // Exact match
        if (strcasecmp($partyName, $searchParty) === 0) {
            $exactMatches[] = [
                'index' => $index + 1,
                'name' => $partyName,
                'quality' => $partyQuality,
                'match_type' => 'exact'
            ];
            $foundParty = true;
            $foundIndex = $index;
        }
        
        // Partial name match (case insensitive)
        if (stripos($partyName, 'SARAGEA') !== false || stripos($partyName, 'TUDORIŢA') !== false) {
            $partialMatches[] = [
                'index' => $index + 1,
                'name' => $partyName,
                'quality' => $partyQuality,
                'match_type' => 'partial'
            ];
        }
        
        // Quality match
        if (stripos($partyQuality, 'Intervenient') !== false) {
            $qualityMatches[] = [
                'index' => $index + 1,
                'name' => $partyName,
                'quality' => $partyQuality,
                'match_type' => 'quality'
            ];
        }
    }
    
    // Display results
    echo "<h3>Search Results:</h3>";
    
    if (!empty($exactMatches)) {
        echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
        echo "<h4 style='color: #155724;'>✓ Exact Matches Found:</h4>";
        foreach ($exactMatches as $match) {
            echo "<p><strong>#{$match['index']}:</strong> {$match['name']} - {$match['quality']}</p>";
        }
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
        echo "<h4 style='color: #721c24;'>✗ No Exact Matches Found</h4>";
        echo "</div>";
    }
    
    if (!empty($partialMatches)) {
        echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
        echo "<h4 style='color: #856404;'>~ Partial Name Matches:</h4>";
        foreach ($partialMatches as $match) {
            echo "<p><strong>#{$match['index']}:</strong> {$match['name']} - {$match['quality']}</p>";
        }
        echo "</div>";
    }
    
    if (!empty($qualityMatches)) {
        echo "<div style='background: #d1ecf1; padding: 10px; border: 1px solid #bee5eb; margin: 10px 0;'>";
        echo "<h4 style='color: #0c5460;'>~ Quality Matches (Intervenient):</h4>";
        foreach ($qualityMatches as $match) {
            echo "<p><strong>#{$match['index']}:</strong> {$match['name']} - {$match['quality']}</p>";
        }
        echo "</div>";
    }
    
    // Character encoding analysis
    echo "<h3>Character Encoding Analysis:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Character</th><th>UTF-8 Bytes</th><th>HTML Entity</th></tr>";
    
    $searchChars = ['ă', 'â', 'î', 'ș', 'ț', 'Ă', 'Â', 'Î', 'Ș', 'Ț'];
    foreach ($searchChars as $char) {
        $bytes = bin2hex($char);
        $entity = htmlentities($char, ENT_QUOTES, 'UTF-8');
        echo "<tr><td>{$char}</td><td>{$bytes}</td><td>{$entity}</td></tr>";
    }
    echo "</table>";
    
    // Test search algorithms
    echo "<h3>Search Algorithm Testing:</h3>";
    
    $testSearches = [
        'SARAGEA TUDORIŢA',
        'saragea tudoriţa',
        'SARAGEA TUDORITA',
        'saragea tudorita',
        'SARAGEA',
        'TUDORIŢA',
        'TUDORITA',
        'Intervenient',
        'intervenient'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Search Term</th><th>Matches Found</th><th>Party Names</th></tr>";
    
    foreach ($testSearches as $searchTerm) {
        $matches = [];
        foreach ($dosar->parti as $index => $parte) {
            $partyName = $parte['nume'] ?? '';
            $partyQuality = $parte['calitate'] ?? '';
            
            // Test different search methods
            $nameMatch = stripos($partyName, $searchTerm) !== false;
            $qualityMatch = stripos($partyQuality, $searchTerm) !== false;
            
            if ($nameMatch || $qualityMatch) {
                $matches[] = ($index + 1) . ': ' . $partyName . ' (' . $partyQuality . ')';
            }
        }
        
        $matchCount = count($matches);
        $matchList = $matchCount > 0 ? implode('<br>', array_slice($matches, 0, 3)) : 'None';
        if ($matchCount > 3) {
            $matchList .= '<br>... and ' . ($matchCount - 3) . ' more';
        }
        
        echo "<tr><td>{$searchTerm}</td><td>{$matchCount}</td><td>{$matchList}</td></tr>";
    }
    echo "</table>";
    
    // Raw data dump for the first few parties
    echo "<h3>Raw Data Sample (First 10 Parties):</h3>";
    echo "<pre>";
    for ($i = 0; $i < min(10, $totalParties); $i++) {
        $parte = $dosar->parti[$i];
        echo "Party " . ($i + 1) . ":\n";
        echo "  Name: '" . $parte['nume'] . "'\n";
        echo "  Quality: '" . ($parte['calitate'] ?? 'N/A') . "'\n";
        echo "  Name bytes: " . bin2hex($parte['nume']) . "\n";
        if (!empty($parte['calitate'])) {
            echo "  Quality bytes: " . bin2hex($parte['calitate']) . "\n";
        }
        echo "\n";
    }
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>EXCEPTION: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><em>Debug completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
