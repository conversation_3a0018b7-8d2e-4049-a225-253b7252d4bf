<?php
/**
 * Monitor Dosare - Versiune Simplificată
 * Funcționează fără dependențe de tabele de monitorizare
 */

// Activez raportarea erorilor
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Încarc bootstrap-ul
require_once dirname(__DIR__) . '/bootstrap.php';

use App\Helpers\TemplateEngine;
use App\Config\Database;
use App\Security\CSRFProtection;

// Pornesc sesiunea dacă nu e pornită
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Verific dacă utilizatorul este autentificat
$isLoggedIn = isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
$userId = $isLoggedIn ? $_SESSION['user_id'] : null;

// Inițializez template engine
$templateEngine = new TemplateEngine();

// Dacă utilizatorul nu este autentificat, afișez formularul de login
if (!$isLoggedIn) {
    echo $templateEngine->render('monitor/login.twig', [
        'page_title' => 'Autentificare - Monitor Dosare',
        'csrf_token' => CSRFProtection::generateToken('login')
    ]);
    exit;
}

// Procesez cererile POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'add_case':
                // Validez CSRF token
                if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '', 'add_case')) {
                    throw new Exception('Token CSRF invalid');
                }
                
                $caseNumber = trim($_POST['case_number'] ?? '');
                $institutionCode = trim($_POST['institution_code'] ?? '');
                $frequency = $_POST['frequency'] ?? 'daily';
                
                if (empty($caseNumber) || empty($institutionCode)) {
                    throw new Exception('Numărul dosarului și instituția sunt obligatorii');
                }
                
                // Încerc să adaug în tabela de monitorizare (dacă există)
                try {
                    Database::insert('monitored_cases', [
                        'user_id' => $userId,
                        'case_number' => $caseNumber,
                        'institution_code' => $institutionCode,
                        'notification_frequency' => $frequency,
                        'is_active' => 1,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'Dosarul a fost adăugat la monitorizare cu succes!'
                    ]);
                } catch (Exception $e) {
                    // Tabela nu există încă
                    echo json_encode([
                        'success' => false,
                        'message' => 'Sistemul de monitorizare nu este încă configurat. Rulați setup_monitoring_database.php'
                    ]);
                }
                break;
                
            case 'remove_case':
                // Validez CSRF token
                if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '', 'remove_case')) {
                    throw new Exception('Token CSRF invalid');
                }
                
                $caseId = intval($_POST['case_id'] ?? 0);
                if ($caseId <= 0) {
                    throw new Exception('ID dosar invalid');
                }
                
                try {
                    Database::update('monitored_cases', 
                        ['is_active' => 0], 
                        ['id' => $caseId, 'user_id' => $userId]
                    );
                    
                    echo json_encode([
                        'success' => true,
                        'message' => 'Dosarul a fost eliminat din monitorizare'
                    ]);
                } catch (Exception $e) {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Eroare la eliminarea dosarului: ' . $e->getMessage()
                    ]);
                }
                break;
                
            default:
                throw new Exception('Acțiune necunoscută');
        }
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    
    exit;
}

// Obțin dosarele monitorizate de utilizator
$monitoredCases = [];
try {
    $monitoredCases = Database::fetchAll("
        SELECT * FROM monitored_cases 
        WHERE user_id = ? AND is_active = 1 
        ORDER BY created_at DESC
    ", [$userId]);
} catch (Exception $e) {
    // Tabela nu există încă
}

// Obțin statistici utilizator
$userStats = [
    'total_cases' => count($monitoredCases),
    'notifications_sent' => 0,
    'last_check' => null
];

try {
    $userStats['notifications_sent'] = Database::fetchOne("
        SELECT COUNT(*) as count 
        FROM notification_queue 
        WHERE user_id = ? AND status = 'sent'
    ", [$userId])['count'] ?? 0;
    
    $lastCheck = Database::fetchOne("
        SELECT MAX(last_checked) as last_check 
        FROM monitored_cases 
        WHERE user_id = ? AND is_active = 1
    ", [$userId]);
    
    $userStats['last_check'] = $lastCheck['last_check'] ?? null;
    
} catch (Exception $e) {
    // Tabelele nu există încă
}

// Obțin informații utilizator
$user = Database::fetchOne("SELECT * FROM users WHERE id = ?", [$userId]);

// Pregătesc datele pentru template
$templateData = [
    'page_title' => 'Monitor Dosare - Portal Judiciar România',
    'user' => $user,
    'user_stats' => $userStats,
    'monitored_cases' => $monitoredCases,
    'csrf_tokens' => [
        'add_case' => CSRFProtection::generateToken('add_case'),
        'remove_case' => CSRFProtection::generateToken('remove_case'),
        'update_preferences' => CSRFProtection::generateToken('update_preferences')
    ],
    'institutions' => [
        'JudecatoriaSECTORUL1BUCURESTI' => 'Judecătoria Sectorului 1 București',
        'JudecatoriaSECTORUL2BUCURESTI' => 'Judecătoria Sectorului 2 București',
        'JudecatoriaSECTORUL3BUCURESTI' => 'Judecătoria Sectorului 3 București',
        'JudecatoriaSECTORUL4BUCURESTI' => 'Judecătoria Sectorului 4 București',
        'JudecatoriaSECTORUL5BUCURESTI' => 'Judecătoria Sectorului 5 București',
        'JudecatoriaSECTORUL6BUCURESTI' => 'Judecătoria Sectorului 6 București',
        'TribunalulBUCURESTI' => 'Tribunalul București',
        'CurteaDeApelBUCURESTI' => 'Curtea de Apel București'
    ],
    'frequencies' => [
        'immediate' => 'Imediat',
        'daily' => 'Zilnic',
        'weekly' => 'Săptămânal'
    ]
];

// Render template
echo $templateEngine->render('monitor/dashboard.twig', $templateData);
?>
