<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Frontend Display - Parties Investigation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-section {
            border: 2px solid #007bff;
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .party-row {
            border-bottom: 1px solid #eee;
            padding: 5px 0;
        }
        .error-highlight {
            background: #ffebee;
            border: 1px solid #f44336;
        }
        .success-highlight {
            background: #e8f5e9;
            border: 1px solid #4caf50;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Frontend Display Debug - Case 130/98/2022</h1>
        
        <?php
        // Include necessary files
        require_once 'bootstrap.php';
        require_once 'includes/config.php';
        require_once 'includes/functions.php';
        require_once 'services/DosarService.php';

        // Test parameters
        $numarDosar = '130/98/2022';
        $institutie = 'TribunalulIALOMITA';

        try {
            // Initialize service and get data
            $dosarService = new DosarService();
            $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
            
            if (!$dosar || empty((array)$dosar)) {
                echo "<div class='alert alert-danger'>ERROR: No case data returned</div>";
                exit;
            }
            
            $totalParties = count($dosar->parti);
            ?>
            
            <div class="debug-section success-highlight">
                <h3><i class="fas fa-check-circle text-success"></i> Backend Data Status</h3>
                <div class="debug-info">
                    <strong>Case Number:</strong> <?php echo htmlspecialchars($dosar->numar); ?><br>
                    <strong>Institution:</strong> <?php echo htmlspecialchars($dosar->institutie); ?><br>
                    <strong>Total Parties Retrieved:</strong> <?php echo $totalParties; ?><br>
                    <strong>Data Status:</strong> <span class="text-success">✓ All data retrieved successfully</span>
                </div>
            </div>

            <div class="debug-section">
                <h3><i class="fas fa-table text-primary"></i> Frontend Table Simulation</h3>
                <div class="debug-info">
                    <p><strong>Testing table rendering with all <?php echo $totalParties; ?> parties...</strong></p>
                </div>
                
                <!-- Exact replica of the parties table structure from detalii_dosar.php -->
                <div class="table-responsive">
                    <table class="table table-striped" id="tabelParti">
                        <thead>
                            <tr>
                                <th class="sortable" data-sort="nume">
                                    <div class="d-flex align-items-center">
                                        <span>Nume</span>
                                        <span class="sort-icon ml-1">
                                            <i class="fas fa-sort"></i>
                                        </span>
                                    </div>
                                </th>
                                <th class="sortable" data-sort="calitate">
                                    <div class="d-flex align-items-center">
                                        <span>Calitate</span>
                                        <span class="sort-icon ml-1">
                                            <i class="fas fa-sort"></i>
                                        </span>
                                    </div>
                                </th>
                                <th class="sortable" data-sort="info">
                                    <div class="d-flex align-items-center">
                                        <span>Informații suplimentare</span>
                                        <span class="sort-icon ml-1">
                                            <i class="fas fa-sort"></i>
                                        </span>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $loop_index = 0;
                            foreach ($dosar->parti as $parte): 
                                $loop_index++;
                            ?>
                                <tr class="parte-row"
                                    data-nume="<?php echo htmlspecialchars($parte['nume']); ?>"
                                    data-calitate="<?php echo !empty($parte['calitate']) ? htmlspecialchars($parte['calitate']) : ''; ?>"
                                    data-info=""
                                    data-index="<?php echo $loop_index; ?>">
                                    <td class="nume-parte"><?php echo htmlspecialchars($parte['nume']); ?></td>
                                    <td class="calitate-parte"><?php echo !empty($parte['calitate']) ? htmlspecialchars($parte['calitate']) : '<span class="text-muted">-</span>'; ?></td>
                                    <td>
                                        <span class="text-muted">-</span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="debug-info mt-3">
                    <strong>Table Rendering Status:</strong> 
                    <span id="tableStatus" class="text-info">Checking...</span><br>
                    <strong>Visible Rows Count:</strong> 
                    <span id="visibleRowsCount" class="text-info">Counting...</span><br>
                    <strong>Hidden Rows Count:</strong> 
                    <span id="hiddenRowsCount" class="text-info">Counting...</span>
                </div>
            </div>

            <div class="debug-section">
                <h3><i class="fas fa-code text-warning"></i> JavaScript Diagnostics</h3>
                <div id="jsDebugInfo" class="debug-info">
                    <p>Running JavaScript diagnostics...</p>
                </div>
            </div>

            <div class="debug-section">
                <h3><i class="fas fa-list text-info"></i> Performance Test</h3>
                <div class="debug-info">
                    <button id="performanceTest" class="btn btn-primary">Run Performance Test</button>
                    <div id="performanceResults" class="mt-2"></div>
                </div>
            </div>

        <?php
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>EXCEPTION: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        ?>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Debug page loaded, starting diagnostics...');
            
            // Check table rendering
            const table = document.getElementById('tabelParti');
            const tbody = table ? table.querySelector('tbody') : null;
            const allRows = tbody ? tbody.querySelectorAll('tr.parte-row') : [];
            
            console.log('Total rows found:', allRows.length);
            
            // Count visible and hidden rows
            let visibleCount = 0;
            let hiddenCount = 0;
            
            allRows.forEach((row, index) => {
                const computedStyle = window.getComputedStyle(row);
                const isVisible = computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden';
                
                if (isVisible) {
                    visibleCount++;
                } else {
                    hiddenCount++;
                    console.log(`Row ${index + 1} is hidden:`, row);
                }
            });
            
            // Update status
            document.getElementById('tableStatus').textContent = 
                allRows.length === <?php echo $totalParties; ?> ? '✓ All rows rendered' : '✗ Missing rows';
            document.getElementById('tableStatus').className = 
                allRows.length === <?php echo $totalParties; ?> ? 'text-success' : 'text-danger';
                
            document.getElementById('visibleRowsCount').textContent = visibleCount;
            document.getElementById('hiddenRowsCount').textContent = hiddenCount;
            
            // JavaScript diagnostics
            const jsDebugInfo = document.getElementById('jsDebugInfo');
            let diagnostics = '<ul>';
            
            // Check for JavaScript errors
            window.addEventListener('error', function(e) {
                diagnostics += `<li class="text-danger">JavaScript Error: ${e.message} at ${e.filename}:${e.lineno}</li>`;
                jsDebugInfo.innerHTML = diagnostics + '</ul>';
            });
            
            // Check DOM state
            diagnostics += `<li>DOM Ready: ✓</li>`;
            diagnostics += `<li>Table Element: ${table ? '✓' : '✗'}</li>`;
            diagnostics += `<li>Table Body: ${tbody ? '✓' : '✗'}</li>`;
            diagnostics += `<li>Total Rows in DOM: ${allRows.length}</li>`;
            diagnostics += `<li>Expected Rows: <?php echo $totalParties; ?></li>`;
            diagnostics += `<li>Rows Match Expected: ${allRows.length === <?php echo $totalParties; ?> ? '✓' : '✗'}</li>`;
            
            // Check for CSS issues
            if (table) {
                const tableStyle = window.getComputedStyle(table);
                diagnostics += `<li>Table Display: ${tableStyle.display}</li>`;
                diagnostics += `<li>Table Visibility: ${tableStyle.visibility}</li>`;
                diagnostics += `<li>Table Max-Height: ${tableStyle.maxHeight}</li>`;
                diagnostics += `<li>Table Overflow: ${tableStyle.overflow}</li>`;
            }
            
            diagnostics += '</ul>';
            jsDebugInfo.innerHTML = diagnostics;
            
            // Performance test
            document.getElementById('performanceTest').addEventListener('click', function() {
                const startTime = performance.now();
                
                // Simulate operations that might be causing issues
                allRows.forEach((row, index) => {
                    // Check if row is in viewport
                    const rect = row.getBoundingClientRect();
                    const inViewport = rect.top >= 0 && rect.bottom <= window.innerHeight;
                    
                    // Force reflow
                    row.offsetHeight;
                });
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                document.getElementById('performanceResults').innerHTML = `
                    <div class="alert alert-info">
                        <strong>Performance Test Results:</strong><br>
                        Time to process ${allRows.length} rows: ${duration.toFixed(2)}ms<br>
                        Average time per row: ${(duration / allRows.length).toFixed(4)}ms<br>
                        Performance: ${duration < 100 ? 'Excellent' : duration < 500 ? 'Good' : 'Needs optimization'}
                    </div>
                `;
            });
        });
    </script>
</body>
</html>
