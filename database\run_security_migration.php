<?php
/**
 * Security and GDPR Migration Runner
 * 
 * Runs the security tables migration for the Portal Judiciar România
 * case monitoring system.
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */

require_once dirname(__DIR__) . '/bootstrap.php';
require_once dirname(__DIR__) . '/includes/config.php';

use App\Config\Database;

echo "Portal Judiciar România - Security Migration\n";
echo "==========================================\n\n";

try {
    // Read the migration file
    $migrationFile = $argv[1] ?? __DIR__ . '/migrations/003_create_security_tables.sql';
    
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: {$migrationFile}");
    }
    
    $sql = file_get_contents($migrationFile);
    
    if (empty($sql)) {
        throw new Exception("Migration file is empty");
    }
    
    echo "Reading migration file: " . basename($migrationFile) . "\n";
    echo "File size: " . number_format(strlen($sql)) . " bytes\n\n";
    
    // Connect to database
    echo "Connecting to database...\n";
    $pdo = Database::getInstance();

    // Create schema_migrations table if it doesn't exist
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS schema_migrations (
            version VARCHAR(10) PRIMARY KEY,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        echo "Schema migrations table ready.\n";
    } catch (PDOException $e) {
        echo "Warning: Could not create schema_migrations table: " . $e->getMessage() . "\n";
    }

    // Extract migration version from filename
    $migrationVersion = pathinfo(basename($migrationFile), PATHINFO_FILENAME);

    // Check if migration was already applied
    try {
        $existingMigration = Database::fetchOne(
            "SELECT * FROM schema_migrations WHERE version = ?",
            [$migrationVersion]
        );
    } catch (Exception $e) {
        echo "Warning: Could not check existing migrations: " . $e->getMessage() . "\n";
        $existingMigration = null;
    }

    if ($existingMigration) {
        echo "⚠️  Migration {$migrationVersion} was already applied on: " . $existingMigration['applied_at'] . "\n";
        echo "Do you want to re-run it? (y/N): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) !== 'y') {
            echo "Migration cancelled.\n";
            exit(0);
        }
        
        echo "\nRe-running migration...\n";
    }
    
    // Split SQL into individual statements, handling multi-line statements properly
    $statements = [];
    $currentStatement = '';
    $lines = explode("\n", $sql);

    foreach ($lines as $line) {
        $line = trim($line);

        // Skip empty lines and comments
        if (empty($line) || preg_match('/^\s*--/', $line)) {
            continue;
        }

        $currentStatement .= $line . ' ';

        // Check if statement ends with semicolon
        if (substr(rtrim($line), -1) === ';') {
            $statements[] = trim($currentStatement);
            $currentStatement = '';
        }
    }

    // Add any remaining statement
    if (!empty(trim($currentStatement))) {
        $statements[] = trim($currentStatement);
    }

    // Filter out empty statements
    $statements = array_filter($statements, function($stmt) {
        return !empty(trim($stmt));
    });
    
    echo "Found " . count($statements) . " SQL statements to execute\n\n";
    
    // Execute each statement
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $index => $statement) {
        $statementNumber = $index + 1;
        
        try {
            // Show progress for longer statements
            if (strlen($statement) > 100) {
                $preview = substr(trim($statement), 0, 60) . '...';
            } else {
                $preview = trim($statement);
            }
            
            echo "[$statementNumber/" . count($statements) . "] Executing: $preview\n";
            
            $result = $pdo->exec($statement);
            
            if ($result !== false) {
                echo "✅ Success";
                if ($result > 0) {
                    echo " (affected rows: $result)";
                }
                echo "\n";
                $successCount++;
            } else {
                echo "⚠️  No rows affected\n";
                $successCount++;
            }
            
        } catch (PDOException $e) {
            echo "❌ Error: " . $e->getMessage() . "\n";
            $errorCount++;
            
            // Continue with other statements unless it's a critical error
            if (strpos($e->getMessage(), 'already exists') === false && 
                strpos($e->getMessage(), 'Duplicate') === false) {
                echo "Critical error encountered. Stopping migration.\n";
                break;
            }
        }
        
        echo "\n";
    }
    
    // Summary
    echo "Migration Summary:\n";
    echo "================\n";
    echo "✅ Successful statements: $successCount\n";
    echo "❌ Failed statements: $errorCount\n";
    echo "📊 Total statements: " . count($statements) . "\n\n";
    
    if ($errorCount === 0) {
        echo "🎉 Migration completed successfully!\n\n";
        
        // Verify tables were created
        echo "Verifying created tables:\n";
        $tables = [
            'rate_limit_attempts',
            'user_consents', 
            'data_processing_logs',
            'security_incidents',
            'user_sessions',
            'user_privacy_settings',
            'data_export_requests'
        ];
        
        foreach ($tables as $table) {
            try {
                $result = Database::fetchOne("SHOW TABLES LIKE '$table'");
                if ($result) {
                    echo "✅ Table '$table' exists\n";
                } else {
                    echo "❌ Table '$table' not found\n";
                }
            } catch (Exception $e) {
                echo "❌ Error checking table '$table': " . $e->getMessage() . "\n";
            }
        }
        
        echo "\nVerifying views:\n";
        $views = ['active_users', 'gdpr_compliant_users'];
        
        foreach ($views as $view) {
            try {
                $result = Database::fetchOne("SHOW TABLES LIKE '$view'");
                if ($result) {
                    echo "✅ View '$view' exists\n";
                } else {
                    echo "❌ View '$view' not found\n";
                }
            } catch (Exception $e) {
                echo "❌ Error checking view '$view': " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n🔒 Security and GDPR compliance features are now available!\n";
        echo "\nNext steps:\n";
        echo "1. Test the monitoring dashboard with CSRF protection\n";
        echo "2. Verify GDPR consent modal appears for new users\n";
        echo "3. Test rate limiting functionality\n";
        echo "4. Review security logs and monitoring\n";
        
    } else {
        echo "⚠️  Migration completed with some errors. Please review the output above.\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    echo "\nStack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
