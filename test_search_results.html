<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Search Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { background: #f8f9fa; padding: 15px; margin: 10px 0; border: 1px solid #dee2e6; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test Search Results for "14096/3/2024*"</h1>
    
    <div class="test-section">
        <h2>Automatic Search Test</h2>
        <button onclick="performSearch()">Perform Search</button>
        <button onclick="checkResults()">Check Current Results</button>
        <button onclick="clearFilters()">Clear All Filters</button>
        <div id="searchResults"></div>
    </div>
    
    <div class="test-section">
        <h2>Manual Search Form</h2>
        <form action="index.php" method="POST" target="_blank">
            <input type="text" name="bulkSearchTerms" value="14096/3/2024*" style="width: 300px; padding: 8px;">
            <button type="submit">Search in New Tab</button>
        </form>
    </div>
    
    <div class="test-section">
        <h2>Debug Information</h2>
        <div id="debugInfo"></div>
    </div>

    <script>
        function performSearch() {
            const resultDiv = document.getElementById('searchResults');
            resultDiv.innerHTML = '<p>Performing search...</p>';
            
            // Create and submit form
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'index.php';
            
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'bulkSearchTerms';
            input.value = '14096/3/2024*';
            
            form.appendChild(input);
            document.body.appendChild(form);
            form.submit();
            
            resultDiv.innerHTML = '<div class="success">Search submitted! Check the main page for results.</div>';
        }
        
        function checkResults() {
            const resultDiv = document.getElementById('searchResults');
            
            // Check if we're on a results page or can access results
            const resultMessages = document.querySelectorAll('[id^="resultMessage"]');
            const tableRows = document.querySelectorAll('table tbody tr');
            const resultCards = document.querySelectorAll('.result-card');
            
            let html = '<h3>Current Page Analysis:</h3>';
            html += `<p>Result messages found: ${resultMessages.length}</p>`;
            html += `<p>Table rows found: ${tableRows.length}</p>`;
            html += `<p>Result cards found: ${resultCards.length}</p>`;
            
            if (resultMessages.length > 0) {
                html += '<h4>Result Messages:</h4>';
                resultMessages.forEach((msg, index) => {
                    html += `<div class="test-section">Message ${index + 1}: "${msg.textContent}"</div>`;
                });
            }
            
            if (tableRows.length > 0) {
                html += '<h4>Table Rows Analysis:</h4>';
                let totalRows = 0;
                let visibleRows = 0;
                let asteriskRows = 0;
                let visibleAsteriskRows = 0;
                
                tableRows.forEach((row, index) => {
                    totalRows++;
                    const isVisible = window.getComputedStyle(row).display !== 'none';
                    if (isVisible) visibleRows++;
                    
                    const caseNumber = row.getAttribute('data-numar');
                    if (caseNumber && caseNumber.includes('*')) {
                        asteriskRows++;
                        if (isVisible) visibleAsteriskRows++;
                        
                        html += `<div class="test-section ${isVisible ? 'success' : 'error'}">`;
                        html += `<strong>Asterisk Row:</strong> ${caseNumber}<br>`;
                        html += `<strong>Visible:</strong> ${isVisible}<br>`;
                        html += `<strong>Search Type:</strong> ${row.getAttribute('data-search-type')}<br>`;
                        html += `</div>`;
                    }
                });
                
                html += `<div class="test-section">`;
                html += `<strong>Summary:</strong><br>`;
                html += `Total rows: ${totalRows}<br>`;
                html += `Visible rows: ${visibleRows}<br>`;
                html += `Rows with asterisks: ${asteriskRows}<br>`;
                html += `Visible asterisk rows: ${visibleAsteriskRows}<br>`;
                html += `</div>`;
                
                if (asteriskRows > 0 && visibleAsteriskRows < asteriskRows) {
                    html += `<div class="test-section error">`;
                    html += `<strong>ISSUE FOUND:</strong> Some rows with asterisks are hidden!<br>`;
                    html += `Hidden asterisk rows: ${asteriskRows - visibleAsteriskRows}`;
                    html += `</div>`;
                }
            }
            
            // Check exact match filter
            const exactMatchFilter = document.querySelector('input[type="checkbox"][id*="exactMatch"]');
            if (exactMatchFilter) {
                html += `<div class="test-section ${exactMatchFilter.checked ? 'warning' : 'success'}">`;
                html += `<strong>Exact Match Filter:</strong> ${exactMatchFilter.checked ? 'ENABLED' : 'DISABLED'}<br>`;
                if (exactMatchFilter.checked) {
                    html += `<button onclick="disableExactMatchFilter()">Disable Filter</button>`;
                }
                html += `</div>`;
            }
            
            resultDiv.innerHTML = html;
        }
        
        function disableExactMatchFilter() {
            const exactMatchFilter = document.querySelector('input[type="checkbox"][id*="exactMatch"]');
            if (exactMatchFilter) {
                exactMatchFilter.checked = false;
                exactMatchFilter.dispatchEvent(new Event('change'));
                
                setTimeout(() => {
                    checkResults();
                }, 500);
            }
        }
        
        function clearFilters() {
            // Clear sessionStorage
            sessionStorage.removeItem('exactMatchFilter');
            
            // Uncheck any filter checkboxes
            const filterCheckboxes = document.querySelectorAll('input[type="checkbox"][id*="Filter"]');
            filterCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
                checkbox.dispatchEvent(new Event('change'));
            });
            
            // Show all rows and cards
            const allRows = document.querySelectorAll('table tbody tr');
            const allCards = document.querySelectorAll('.result-card');
            
            allRows.forEach(row => {
                row.style.display = '';
                row.classList.remove('filtered-exact-match');
            });
            
            allCards.forEach(card => {
                card.style.display = '';
                card.classList.remove('filtered-exact-match');
            });
            
            document.getElementById('searchResults').innerHTML = '<div class="success">All filters cleared and results should be visible.</div>';
            
            setTimeout(() => {
                checkResults();
            }, 500);
        }
        
        function updateDebugInfo() {
            const debugDiv = document.getElementById('debugInfo');
            
            let html = '<h3>Debug Information:</h3>';
            
            // Check sessionStorage
            const filterState = sessionStorage.getItem('exactMatchFilter');
            html += `<p><strong>SessionStorage exactMatchFilter:</strong> ${filterState || 'not set'}</p>`;
            
            // Check for JavaScript errors
            html += `<p><strong>Console errors:</strong> Check browser console for any JavaScript errors</p>`;
            
            // Check current URL
            html += `<p><strong>Current URL:</strong> ${window.location.href}</p>`;
            
            debugDiv.innerHTML = html;
        }
        
        // Auto-run checks when page loads
        document.addEventListener('DOMContentLoaded', function() {
            updateDebugInfo();
            
            // Auto-check results after a delay
            setTimeout(() => {
                checkResults();
            }, 1000);
        });
        
        // Update debug info periodically
        setInterval(updateDebugInfo, 5000);
    </script>
</body>
</html>
