<?php
/**
 * Detailed Parties Structure Analyzer
 * Specifically examines the parties array for pagination/limiting indicators
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Test parameters
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "<h1>Detailed Parties Structure Analysis</h1>";
echo "<p><strong>Case:</strong> {$numarDosar} from {$institutie}</p>";
echo "<p><strong>Focus:</strong> Examining parties array for API limitations</p>";
echo "<hr>";

try {
    // Create SOAP client
    $wsdl = SOAP_WSDL;
    $options = [
        'soap_version' => SOAP_1_2,
        'trace' => true,
        'exceptions' => true,
        'cache_wsdl' => WSDL_CACHE_NONE,
        'connection_timeout' => 10,
        'features' => SOAP_SINGLE_ELEMENT_ARRAYS
    ];
    
    $client = new SoapClient($wsdl, $options);
    
    // Prepare request parameters
    $params = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    // Make the SOAP call
    $response = $client->CautareDosare2($params);
    
    if ($response && isset($response->CautareDosare2Result->Dosar)) {
        $dosare = $response->CautareDosare2Result->Dosar;
        $dosarData = is_array($dosare) ? $dosare[0] : $dosare;
        
        echo "<h2>1. Parties Container Analysis</h2>";
        
        if (isset($dosarData->parti)) {
            $partiContainer = $dosarData->parti;
            
            echo "<h3>Parti Container Properties:</h3>";
            echo "<ul>";
            foreach (get_object_vars($partiContainer) as $property => $value) {
                $type = gettype($value);
                if (is_array($value)) {
                    $count = count($value);
                    echo "<li><strong>{$property}</strong>: Array with {$count} elements</li>";
                } elseif (is_object($value)) {
                    $props = count(get_object_vars($value));
                    echo "<li><strong>{$property}</strong>: Object with {$props} properties</li>";
                } else {
                    $valueStr = is_string($value) ? "'" . substr($value, 0, 50) . "'" : $value;
                    echo "<li><strong>{$property}</strong>: {$type} = {$valueStr}</li>";
                }
            }
            echo "</ul>";
            
            // Check for pagination metadata in parti container
            echo "<h3>Checking for Pagination Metadata in Parti Container:</h3>";
            $paginationFields = [
                'totalCount', 'totalResults', 'hasMore', 'nextPage', 'pageSize', 
                'maxResults', 'limit', 'offset', 'page', 'count', 'total',
                'resultCount', 'pageInfo', 'pagination', 'more', 'hasNext'
            ];
            
            $foundPagination = false;
            foreach ($paginationFields as $field) {
                if (isset($partiContainer->$field)) {
                    echo "<p style='color: green;'>✓ Found pagination field: <strong>{$field}</strong> = " . $partiContainer->$field . "</p>";
                    $foundPagination = true;
                }
            }
            
            if (!$foundPagination) {
                echo "<p style='color: orange;'>⚠️ No pagination metadata found in parti container</p>";
            }
            
            // Analyze the actual parties array
            if (isset($partiContainer->DosarParte)) {
                $parties = $partiContainer->DosarParte;
                $partyCount = is_array($parties) ? count($parties) : 1;
                
                echo "<h2>2. Parties Array Analysis</h2>";
                echo "<p><strong>Total parties found:</strong> {$partyCount}</p>";
                
                if ($partyCount === 100) {
                    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
                    echo "<h4 style='color: #856404;'>🚨 CRITICAL: Exactly 100 parties detected!</h4>";
                    echo "<p>This strongly suggests an API limitation. Most systems use round numbers (50, 100, 200) as default limits.</p>";
                    echo "</div>";
                }
                
                if (is_array($parties)) {
                    // Analyze first few and last few parties
                    echo "<h3>First 3 Parties:</h3>";
                    for ($i = 0; $i < min(3, $partyCount); $i++) {
                        echo "<h4>Party " . ($i + 1) . ":</h4>";
                        echo "<ul>";
                        foreach (get_object_vars($parties[$i]) as $prop => $val) {
                            echo "<li><strong>{$prop}:</strong> " . htmlspecialchars($val) . "</li>";
                        }
                        echo "</ul>";
                    }
                    
                    echo "<h3>Last 3 Parties:</h3>";
                    for ($i = max(0, $partyCount - 3); $i < $partyCount; $i++) {
                        echo "<h4>Party " . ($i + 1) . ":</h4>";
                        echo "<ul>";
                        foreach (get_object_vars($parties[$i]) as $prop => $val) {
                            echo "<li><strong>{$prop}:</strong> " . htmlspecialchars($val) . "</li>";
                        }
                        echo "</ul>";
                    }
                    
                    // Check for any ordering or indexing that might indicate truncation
                    echo "<h3>Ordering Analysis:</h3>";
                    echo "<p>Checking if parties appear to be truncated or if there's a natural ending...</p>";
                    
                    // Look for patterns in the last few parties
                    $lastParties = array_slice($parties, -5);
                    echo "<h4>Last 5 Party Names:</h4>";
                    echo "<ol>";
                    foreach ($lastParties as $party) {
                        echo "<li>" . htmlspecialchars($party->nume) . " (" . htmlspecialchars($party->calitateParte) . ")</li>";
                    }
                    echo "</ol>";
                }
            } else {
                echo "<p style='color: red;'>ERROR: No DosarParte array found in parti container</p>";
            }
        } else {
            echo "<p style='color: red;'>ERROR: No parti property found in dosar data</p>";
        }
        
        // Check the raw XML for any truncation indicators
        echo "<h2>3. Raw XML Analysis</h2>";
        $rawResponse = $client->__getLastResponse();
        
        // Look for truncation indicators in XML
        $truncationIndicators = ['...', 'truncated', 'more', 'hasMore', 'nextPage', 'limit', 'maxResults'];
        $foundTruncation = false;
        
        foreach ($truncationIndicators as $indicator) {
            if (stripos($rawResponse, $indicator) !== false) {
                echo "<p style='color: red;'>⚠️ Found potential truncation indicator: <strong>{$indicator}</strong></p>";
                $foundTruncation = true;
            }
        }
        
        if (!$foundTruncation) {
            echo "<p style='color: green;'>✓ No obvious truncation indicators found in raw XML</p>";
        }
        
        // Check response size
        $responseSize = strlen($rawResponse);
        echo "<p><strong>Raw response size:</strong> {$responseSize} bytes</p>";
        
        if ($responseSize > 25000) {
            echo "<p style='color: orange;'>⚠️ Large response size - might indicate complete data</p>";
        }
        
    } else {
        echo "<p style='color: red;'>ERROR: No valid response received</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
    echo "<h4 style='color: #721c24;'>Exception:</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Analysis completed at " . date('Y-m-d H:i:s') . "</em></p>";
?>
