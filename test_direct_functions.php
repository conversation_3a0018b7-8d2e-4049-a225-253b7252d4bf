<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Direct Funcții - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-bug me-2"></i>
            Test Direct Funcții JavaScript
        </h1>
        
        <div class="test-section">
            <h3>Test Accesibilitate Funcții</h3>
            
            <button type="button" class="btn btn-primary me-2" onclick="testFunctionAccess()">
                <i class="fas fa-search me-1"></i>
                Test Accesibilitate
            </button>
            
            <button type="button" class="btn btn-success me-2" onclick="testExpandDirect()">
                <i class="fas fa-expand-alt me-1"></i>
                Test Expand Direct
            </button>
            
            <button type="button" class="btn btn-warning me-2" onclick="testCollapseDirect()">
                <i class="fas fa-compress-alt me-1"></i>
                Test Collapse Direct
            </button>
            
            <button type="button" class="btn btn-info me-2" onclick="testToggleDirect()">
                <i class="fas fa-exchange-alt me-1"></i>
                Test Toggle Direct
            </button>
            
            <button type="button" class="btn btn-secondary" onclick="clearConsole()">
                <i class="fas fa-trash me-1"></i>
                Clear Console
            </button>
            
            <div class="console-output" id="consoleOutput">
                <div>Test Console - Ready</div>
            </div>
        </div>
        
        <!-- Test Elements -->
        <div class="test-section">
            <h3>Elemente Test</h3>
            
            <div class="term-results">
                <div class="d-flex justify-content-between align-items-center mb-2 p-3 bg-light border rounded" onclick="toggleTermResults(0)">
                    <div>
                        <h6 class="mb-1">Test Section 1</h6>
                        <small class="text-muted">Click to toggle</small>
                    </div>
                    <i class="fas fa-chevron-down toggle-icon" id="toggleIcon0"></i>
                </div>
                
                <div id="termContent0" style="display: none;" class="p-3 border rounded">
                    <p>Test content for section 1</p>
                </div>
            </div>
            
            <div class="term-results mt-3">
                <div class="d-flex justify-content-between align-items-center mb-2 p-3 bg-light border rounded" onclick="toggleTermResults(1)">
                    <div>
                        <h6 class="mb-1">Test Section 2</h6>
                        <small class="text-muted">Click to toggle</small>
                    </div>
                    <i class="fas fa-chevron-down toggle-icon" id="toggleIcon1"></i>
                </div>
                
                <div id="termContent1" style="display: none;" class="p-3 border rounded">
                    <p>Test content for section 2</p>
                </div>
            </div>
        </div>
        
        <!-- Direct Test Buttons -->
        <div class="test-section">
            <h3>Test Butoane Directe (ca în index.php)</h3>
            
            <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="expandAllResults()">
                <i class="fas fa-expand-alt me-1"></i>
                Expandează toate
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAllResults()">
                <i class="fas fa-compress-alt me-1"></i>
                Restrânge toate
            </button>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Console logging function
        function logToConsole(message, type = 'info') {
            const consoleOutput = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const colorMap = {
                'info': '#0f0',
                'warning': '#ff0',
                'error': '#f00',
                'success': '#0f0'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.color = colorMap[type] || '#0f0';
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            consoleOutput.appendChild(logEntry);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('consoleOutput').innerHTML = '<div>Console cleared</div>';
        }
        
        // Test function accessibility
        function testFunctionAccess() {
            logToConsole('=== TESTING FUNCTION ACCESSIBILITY ===', 'info');
            
            const functions = ['expandAllResults', 'collapseAllResults', 'toggleTermResults', 'showNotification'];
            
            functions.forEach(funcName => {
                try {
                    if (typeof window[funcName] === 'function') {
                        logToConsole(`✓ ${funcName} is accessible as window.${funcName}`, 'success');
                    } else if (typeof eval(funcName) === 'function') {
                        logToConsole(`✓ ${funcName} is accessible via eval`, 'success');
                    } else {
                        logToConsole(`✗ ${funcName} is NOT accessible`, 'error');
                    }
                } catch (error) {
                    logToConsole(`✗ Error testing ${funcName}: ${error.message}`, 'error');
                }
            });
            
            // Test direct call
            try {
                logToConsole('Testing direct function call...', 'info');
                if (typeof expandAllResults !== 'undefined') {
                    logToConsole('✓ expandAllResults is defined in current scope', 'success');
                } else {
                    logToConsole('✗ expandAllResults is undefined in current scope', 'error');
                }
            } catch (error) {
                logToConsole(`✗ Error in direct test: ${error.message}`, 'error');
            }
        }
        
        function testExpandDirect() {
            logToConsole('=== TESTING EXPAND FUNCTION DIRECTLY ===', 'info');
            try {
                if (typeof expandAllResults === 'function') {
                    logToConsole('Calling expandAllResults()...', 'info');
                    expandAllResults();
                    logToConsole('✓ expandAllResults() called successfully', 'success');
                } else {
                    logToConsole('✗ expandAllResults is not a function', 'error');
                    
                    // Try to define it manually
                    logToConsole('Attempting to define function manually...', 'warning');
                    window.expandAllResults = function() {
                        logToConsole('Manual expandAllResults called', 'info');
                        const termContents = document.querySelectorAll('[id^="termContent"]');
                        const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
                        
                        logToConsole(`Found ${termContents.length} content elements and ${toggleIcons.length} icon elements`, 'info');
                        
                        termContents.forEach(content => {
                            content.style.display = 'block';
                        });
                        
                        toggleIcons.forEach(icon => {
                            icon.className = 'fas fa-chevron-up toggle-icon';
                        });
                        
                        logToConsole('Manual expand completed', 'success');
                    };
                    
                    expandAllResults();
                }
            } catch (error) {
                logToConsole(`✗ Error in testExpandDirect: ${error.message}`, 'error');
            }
        }
        
        function testCollapseDirect() {
            logToConsole('=== TESTING COLLAPSE FUNCTION DIRECTLY ===', 'info');
            try {
                if (typeof collapseAllResults === 'function') {
                    logToConsole('Calling collapseAllResults()...', 'info');
                    collapseAllResults();
                    logToConsole('✓ collapseAllResults() called successfully', 'success');
                } else {
                    logToConsole('✗ collapseAllResults is not a function', 'error');
                    
                    // Try to define it manually
                    logToConsole('Attempting to define function manually...', 'warning');
                    window.collapseAllResults = function() {
                        logToConsole('Manual collapseAllResults called', 'info');
                        const termContents = document.querySelectorAll('[id^="termContent"]');
                        const toggleIcons = document.querySelectorAll('[id^="toggleIcon"]');
                        
                        logToConsole(`Found ${termContents.length} content elements and ${toggleIcons.length} icon elements`, 'info');
                        
                        termContents.forEach(content => {
                            content.style.display = 'none';
                        });
                        
                        toggleIcons.forEach(icon => {
                            icon.className = 'fas fa-chevron-down toggle-icon';
                        });
                        
                        logToConsole('Manual collapse completed', 'success');
                    };
                    
                    collapseAllResults();
                }
            } catch (error) {
                logToConsole(`✗ Error in testCollapseDirect: ${error.message}`, 'error');
            }
        }
        
        function testToggleDirect() {
            logToConsole('=== TESTING TOGGLE FUNCTION DIRECTLY ===', 'info');
            try {
                if (typeof toggleTermResults === 'function') {
                    logToConsole('Calling toggleTermResults(0)...', 'info');
                    toggleTermResults(0);
                    logToConsole('✓ toggleTermResults(0) called successfully', 'success');
                } else {
                    logToConsole('✗ toggleTermResults is not a function', 'error');
                    
                    // Try to define it manually
                    logToConsole('Attempting to define function manually...', 'warning');
                    window.toggleTermResults = function(index) {
                        logToConsole(`Manual toggleTermResults called with index: ${index}`, 'info');
                        const content = document.getElementById('termContent' + index);
                        const icon = document.getElementById('toggleIcon' + index);
                        
                        if (!content) {
                            logToConsole(`✗ Content element not found for index: ${index}`, 'error');
                            return;
                        }
                        
                        const isVisible = content.style.display !== 'none';
                        
                        if (isVisible) {
                            content.style.display = 'none';
                            if (icon) icon.className = 'fas fa-chevron-down toggle-icon';
                            logToConsole(`Collapsed section ${index}`, 'success');
                        } else {
                            content.style.display = 'block';
                            if (icon) icon.className = 'fas fa-chevron-up toggle-icon';
                            logToConsole(`Expanded section ${index}`, 'success');
                        }
                    };
                    
                    toggleTermResults(0);
                }
            } catch (error) {
                logToConsole(`✗ Error in testToggleDirect: ${error.message}`, 'error');
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logToConsole('DOM loaded, starting tests...', 'info');
            testFunctionAccess();
        });
    </script>
</body>
</html>
