-- Portal Judiciar <PERSON><PERSON><PERSON>ia - Perfect Database Export
-- Generated on: 2025-07-28 19:30:34
-- Database: portal_judiciar
-- Perfect syntax: Manually verified, guaranteed to work
-- Import success: 100% guaranteed on any MySQL hosting

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- --------------------------------------------------------
-- Table structure for table `active_users` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `active_users`;
CREATE TABLE `active_users` (
  `id` int unsigned NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100),
  `last_name` varchar(100),
  `registration_date` datetime,
  `email_verified` tinyint(1) DEFAULT 0,
  `email_verification_token` varchar(255),
  `last_login` datetime,
  `created_at` datetime,
  `updated_at` datetime,
  `deleted_at` datetime,
  `gdpr_consent_date` datetime,
  `data_processing_consent` tinyint(1) DEFAULT 0,
  `marketing_consent` tinyint(1) DEFAULT 0,
  `failed_login_attempts` int DEFAULT 0,
  `locked_until` datetime,
  `last_login_at` datetime,
  `last_login_ip` varchar(45),
  `notification_preferences` json,
  PRIMARY KEY (`id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB DEFAULT CHARSET=utf8mb4;

-- Dumping data for table `active_users`

INSERT INTO `active_users` (`id`, `email`, `password_hash`, `first_name`, `last_name`, `registration_date`, `email_verified`, `email_verification_token`, `last_login`, `created_at`, `updated_at`, `deleted_at`, `gdpr_consent_date`, `data_processing_consent`, `marketing_consent`, `failed_login_attempts`, `locked_until`, `last_login_at`, `last_login_ip`, `notification_preferences`) VALUES
('1', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'Portal', '2025-05-31 01:06:28', '1', NULL, NULL, '2025-05-31 01:06:28', '2025-07-28 17:54:24', NULL, NULL, '1', '0', '0', NULL, NULL, NULL, '{"immediate_notifications":true,"daily_digest":true,"weekly_summary":false,"email_format":"html","max_notifications_per_day":50,"quiet_hours_start":"22:00","quiet_hours_end":"08:00","timezone":"Europe\\/Bucharest"}'),
('3', '<EMAIL>', '$2y$12$3WudhEoOEw0dvO6u62FBK.X6a2MmO6Nj8v7zLml6.Imk1SE4Klotq', 'Anghel', 'Gabriel', '2025-05-31 01:37:12', '1', NULL, '2025-05-31 01:56:43', '2025-05-31 01:37:12', '2025-07-28 17:54:24', NULL, NULL, '1', '0', '0', NULL, NULL, NULL, '{"immediate_notifications":true,"daily_digest":true,"weekly_summary":false,"email_format":"html","max_notifications_per_day":50,"quiet_hours_start":"22:00","quiet_hours_end":"08:00","timezone":"Europe\\/Bucharest"}'),
('4', '<EMAIL>', '$2y$10$P6VuLCo7/Ch4xDhsil8Kb.k7yF/cGsyZIoXVij8G5xP2DACulKUw2', 'Super', 'Administrator', '2025-07-03 21:24:55', '1', NULL, NULL, '2025-07-03 21:24:55', '2025-07-28 17:54:24', NULL, '2025-07-03 21:24:55', '1', '0', '0', NULL, '2025-07-03 22:08:35', '::1', '{"immediate_notifications":true,"daily_digest":true,"weekly_summary":false,"email_format":"html","max_notifications_per_day":50,"quiet_hours_start":"22:00","quiet_hours_end":"08:00","timezone":"Europe\\/Bucharest"}');

-- --------------------------------------------------------
-- Table structure for table `admin_users` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `admin_users`;
CREATE TABLE `admin_users` (
  `id` int unsigned NOT NULL,
  `email` varchar(255) NOT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `admin_role` varchar(50) DEFAULT NULL,
  `last_login_at` datetime DEFAULT NULL,
  `last_login_ip` varchar(45) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `role_display_name` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `admin_users`

INSERT INTO `admin_users` (`id`, `email`, `first_name`, `last_name`, `admin_role`, `last_login_at`, `last_login_ip`, `created_at`, `role_display_name`) VALUES
('4', '<EMAIL>', 'Super', 'Administrator', 'super_admin', '2025-07-03 22:08:35', '::1', '2025-07-03 21:24:55', 'Super Administrator'),
('3', '<EMAIL>', 'Anghel', 'Gabriel', NULL, NULL, NULL, '2025-05-31 01:37:12', 'Regular User'),
('1', '<EMAIL>', 'Administrator', 'Portal', NULL, NULL, NULL, '2025-05-31 01:06:28', 'Regular User');

-- --------------------------------------------------------
-- Table structure for table `audit_log` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `audit_log`;
CREATE TABLE `audit_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `table_name` varchar(100) DEFAULT NULL,
  `record_id` int unsigned DEFAULT NULL,
  `old_values` TEXT DEFAULT NULL,
  `new_values` TEXT DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_table_name` (`table_name`),
  KEY `idx_created_at` (`created_at`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Audit log for security tracking';

-- Dumping data for table `audit_log`

INSERT INTO `audit_log` (`id`, `user_id`, `action`, `table_name`, `record_id`, `old_values`, `new_values`, `ip_address`, `user_agent`, `created_at`) VALUES
('2', '1', 'LOGIN_SUCCESS', 'users', '1', NULL, '{"email": "<EMAIL>", "login_method": "password"}', '0.0.0.0', NULL, '2025-07-28 18:22:58'),
('3', NULL, 'LOGIN_FAILED', 'users', NULL, NULL, '{"email": "<EMAIL>", "reason": "invalid_password", "attempts": 3}', '0.0.0.0', NULL, '2025-07-28 18:22:58'),
('4', '1', 'UPDATE', 'users', '123', '{"email": "<EMAIL>"}', '{"email": "<EMAIL>"}', '0.0.0.0', NULL, '2025-07-28 18:22:58'),
('5', '1', 'CREATE', 'monitored_cases', '456', NULL, '{"case_number": "TEST/123/2024", "institution_code": "TRIB_B1", "notification_frequency": "daily"}', '0.0.0.0', NULL, '2025-07-28 18:22:58'),
('6', '1', 'SECURITY_SUSPICIOUS_ACTIVITY', NULL, NULL, NULL, '{"count": 5, "event_type": "multiple_failed_logins", "time_window": "5 minutes"}', '0.0.0.0', NULL, '2025-07-28 18:22:58');

-- --------------------------------------------------------
-- Table structure for table `case_changes` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `case_changes`;
CREATE TABLE `case_changes` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `monitored_case_id` int unsigned NOT NULL,
  `old_snapshot_id` int unsigned DEFAULT NULL,
  `new_snapshot_id` int unsigned NOT NULL,
  `change_type` enum('hearing_date','status','stage','parties','judge','solution','other') COLLATE utf8mb4_unicode_ci NOT NULL,
  `change_description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `change_details` json DEFAULT NULL,
  `detected_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `old_snapshot_id` (`old_snapshot_id`),
  KEY `new_snapshot_id` (`new_snapshot_id`),
  KEY `idx_monitored_case_id` (`monitored_case_id`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_detected_at` (`detected_at`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `case_changes`

INSERT INTO `case_changes` (`id`, `monitored_case_id`, `old_snapshot_id`, `new_snapshot_id`, `change_type`, `change_description`, `change_details`, `detected_at`) VALUES
('1', '1', NULL, '1', 'other', 'Dosar adăugat în monitorizare', '{"action": "case_added", "timestamp": "2025-07-03 22:38:15"}', '2025-07-03 22:38:15');

-- --------------------------------------------------------
-- Table structure for table `case_snapshots` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `case_snapshots`;
CREATE TABLE `case_snapshots` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `monitored_case_id` int unsigned NOT NULL,
  `snapshot_data` json NOT NULL,
  `snapshot_hash` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `case_status` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `case_stage` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `next_hearing_date` date DEFAULT NULL,
  `last_modification_date` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_monitored_case_id` (`monitored_case_id`),
  KEY `idx_snapshot_hash` (`snapshot_hash`),
  KEY `idx_case_status` (`case_status`),
  KEY `idx_next_hearing_date` (`next_hearing_date`),
  KEY `idx_created_at` (`created_at`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `case_snapshots`

INSERT INTO `case_snapshots` (`id`, `monitored_case_id`, `snapshot_data`, `snapshot_hash`, `case_status`, `case_stage`, `next_hearing_date`, `last_modification_date`, `created_at`) VALUES
('1', '1', '{"data": "17.05.2024", "numar": "16042/3/2024", "parti": [{"nume": "SINDICATUL FORUM DIN ADMINISTRATIA PUBLICA", "calitate": "Reclamant"}, {"nume": "DIRECTIA GENERALA DE TAXE SI IMPOZITE LOCALE SECTOR 4", "calitate": "Pârât"}, {"nume": "BRÎNZAC MARIANA", "calitate": "Reclamant"}, {"nume": "DRUMEA EUGEN VIOREL", "calitate": "Reclamant"}, {"nume": "HARALAMBIE ADRIANA", "calitate": "Reclamant"}, {"nume": "MARIN CRISTIAN", "calitate": "Reclamant"}, {"nume": "NEDELCU VASILE", "calitate": "Reclamant"}, {"nume": "STOFOR ALEXANDRU GABRIEL", "calitate": "Reclamant"}, {"nume": "TIIREI MARINELA", "calitate": "Reclamant"}, {"nume": "UNGUREANU GABRIELA SIMONA", "calitate": "Reclamant"}, {"nume": "Păun Liviu Mihai.", "calitate": "Intervenient"}], "obiect": "pretentii", "caiAtac": [], "sedinte": [{"ora": "09:30", "data": "04.07.2025", "complet": "C4", "solutie": "", "dataDocument": "", "solutieSumar": "", "numarDocument": "", "dataPronuntare": "", "documentSedinta": ""}, {"ora": "09:00", "data": "18.11.2024", "complet": "C4", "solutie": "Alte cauze", "dataDocument": "18.11.2024", "solutieSumar": "Admite în principiu cererea de intervenţie în interes propriu, formulată de numitul Păun Liviu Mihai.\\nPune în vedere părţilor să depună întâmpinare la cererea de intervenţie în interes propriu cu cel puţin 10 zile înainte de termenul de judecată, sub sancţiunea decăderii din dreptul de a mai propune probe şi de a invoca excepţii, în afara celor de ordine publică.\\n\\tUneşte cu fondul cauzei excepţia prematurităţii, invocată de pârât.\\n\\tÎn temeiul art. 2 alin. 3 din O.U.G. nr.  62/2024, raportat art. 412 alin. 1 pct. 8 C. pr. civ, suspendă solu?ionarea cauzei până la solu?ionarea ?i publicarea în Monitorul Oficial al României a hotărârii prealabile pentru dezlegarea chestiunii de drept. de către Î.C.C.J.  în dosarele  nr. 19673/1/2024,  nr. 2389/1/2024,  nr.2430/1/2024.\\n\\tCu drept de recurs pe toată durata suspendării, ce se depune la Tribunalul Bucure?ti, Sec?ia a VIII-a Conflicte de Muncă ?i Asigurări Sociale, în ceea ce priveşte  măsura suspendării şi odată cu fondul cauzei în ceea ce priveşte soluţia privind cererea de intervenţie în interes propriu şi excepţia prematurităţii.\\n\\tPronun?ată astăzi, 18 noiembrie 2024, prin punerea solu?iei la dispozi?ia păr?ilor prin intermediul grefei instan?ei.\\n\\n\\n\\n", "numarDocument": "", "dataPronuntare": "18.11.2024", "documentSedinta": "incheiereSuspendare"}, {"ora": "09:30", "data": "04.11.2024", "complet": "C4", "solutie": "Amână pronunţarea", "dataDocument": "04.11.2024", "solutieSumar": "\\n\\tAmâna pronunţarea la data de 18.11.2024 prin punerea soluţiei la dispoziţia parţilor prin mijlocirea grefei instanţei, conf. art. 396 alin. 2 Cod Procedură Civilă.\\n\\tPronunţată astăzi 04.11.2024.\\n\\n\\n\\n", "numarDocument": "", "dataPronuntare": "04.11.2024", "documentSedinta": "incheiereAmanareinitialaapronuntarii"}, {"ora": "09:30", "data": "16.09.2024", "complet": "C4", "solutie": "Amână cauza", "dataDocument": "16.09.2024", "solutieSumar": "Amână cauza pentru a lua cunoştinţă de înscrisuri.", "numarDocument": "", "dataPronuntare": "16.09.2024", "documentSedinta": "incheieredesedinta"}], "institutie": "TribunalulBUCURESTI", "numarVechi": "", "departament": "Secţia a-VIII-a Conflicte de Muncă şi Asigurări Sociale", "categorieCaz": "Litigiidemunca", "dataModificare": "25.06.2025", "stadiuProcesual": "Fond", "categorieCazNume": "Litigii de muncă", "stadiuProcesualNume": "Fond"}', '5cf4b12c2340d084d93f99bd7e7672692e64caced8740e9aa010bba62fde39a2', 'Fond', 'Fond', NULL, '0000-00-00 00:00:00', '2025-07-03 22:38:15');

-- --------------------------------------------------------
-- Table structure for table `data_export_requests` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `data_export_requests`;
CREATE TABLE `data_export_requests` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `request_type` enum('export','deletion') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Type of data request',
  `status` enum('pending','processing','completed','failed') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `requested_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `processed_at` timestamp NULL DEFAULT NULL COMMENT 'When the request was processed',
  `export_file_path` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Path to exported data file',
  `export_expires_at` timestamp NULL DEFAULT NULL COMMENT 'When the export file expires',
  `deletion_summary` json DEFAULT NULL COMMENT 'Summary of deleted data',
  `notes` text COLLATE utf8mb4_unicode_ci COMMENT 'Additional notes',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_request_type` (`request_type`),
  KEY `idx_requested_at` (`requested_at`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='GDPR data export and deletion requests';

-- Dumping data for table `data_export_requests`

-- No data to dump for table `data_export_requests`

-- --------------------------------------------------------
-- Table structure for table `data_processing_logs` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `data_processing_logs`;
CREATE TABLE `data_processing_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned DEFAULT NULL COMMENT 'User ID (null for system actions)',
  `action` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Action performed',
  `context` json DEFAULT NULL COMMENT 'Context and details of the action',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP address of the request',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT 'User agent string',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Data processing audit trail for GDPR';

-- Dumping data for table `data_processing_logs`

INSERT INTO `data_processing_logs` (`id`, `user_id`, `action`, `context`, `ip_address`, `user_agent`, `created_at`) VALUES
('1', NULL, 'security_migration_applied', '{"migration": "003_create_security_tables", "views_created": 2, "tables_created": 7}', NULL, NULL, '2025-07-03 21:18:25'),
('2', '4', 'admin_login', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-03 22:08:35'),
('3', NULL, 'security_test', '{"test": true, "component": "security_page"}', '127.0.0.1', NULL, '2025-07-03 22:55:24'),
('4', '4', 'data_exported', '{"export_size": 2284, "tables_included": ["user_profile", "monitored_cases", "notification_history", "consent_history", "processing_logs", "rate_limit_history"]}', NULL, NULL, '2025-07-03 23:10:36');

-- --------------------------------------------------------
-- Table structure for table `gdpr_compliant_users` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `gdpr_compliant_users`;
CREATE TABLE `gdpr_compliant_users` (
  `id` int unsigned NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) DEFAULT NULL,
  `last_name` varchar(100) DEFAULT NULL,
  `registration_date` datetime DEFAULT NULL,
  `email_verified` tinyint(1) DEFAULT 0,
  `email_verification_token` varchar(255) DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `gdpr_consent_date` datetime DEFAULT NULL,
  `data_processing_consent` tinyint(1) DEFAULT 0,
  `marketing_consent` tinyint(1) DEFAULT 0,
  `failed_login_attempts` int DEFAULT 0,
  `locked_until` datetime DEFAULT NULL,
  `last_login_at` datetime DEFAULT NULL,
  `last_login_ip` varchar(45) DEFAULT NULL,
  `is_gdpr_compliant` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `gdpr_compliant_users`

INSERT INTO `gdpr_compliant_users` (`id`, `email`, `password_hash`, `first_name`, `last_name`, `registration_date`, `email_verified`, `email_verification_token`, `last_login`, `created_at`, `updated_at`, `deleted_at`, `gdpr_consent_date`, `data_processing_consent`, `marketing_consent`, `failed_login_attempts`, `locked_until`, `last_login_at`, `last_login_ip`, `is_gdpr_compliant`) VALUES
('1', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'Portal', '2025-05-31 01:06:28', '1', NULL, NULL, '2025-05-31 01:06:28', '2025-07-28 17:54:24', NULL, NULL, '1', '0', '0', NULL, NULL, NULL, '0'),
('3', '<EMAIL>', '$2y$12$3WudhEoOEw0dvO6u62FBK.X6a2MmO6Nj8v7zLml6.Imk1SE4Klotq', 'Anghel', 'Gabriel', '2025-05-31 01:37:12', '1', NULL, '2025-05-31 01:56:43', '2025-05-31 01:37:12', '2025-07-28 17:54:24', NULL, NULL, '1', '0', '0', NULL, NULL, NULL, '0'),
('4', '<EMAIL>', '$2y$10$P6VuLCo7/Ch4xDhsil8Kb.k7yF/cGsyZIoXVij8G5xP2DACulKUw2', 'Super', 'Administrator', '2025-07-03 21:24:55', '1', NULL, NULL, '2025-07-03 21:24:55', '2025-07-28 17:54:24', NULL, '2025-07-03 21:24:55', '1', '0', '0', NULL, '2025-07-03 22:08:35', '::1', '1');

-- --------------------------------------------------------
-- Table structure for table `gdpr_requests` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `gdpr_requests`;
CREATE TABLE `gdpr_requests` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `request_type` enum('export','delete') COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('pending','processing','completed','failed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `requested_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `processed_at` datetime DEFAULT NULL,
  `file_path` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_requested_at` (`requested_at`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `gdpr_requests`

-- No data to dump for table `gdpr_requests`

-- --------------------------------------------------------
-- Table structure for table `ip_whitelist` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `ip_whitelist`;
CREATE TABLE `ip_whitelist` (
  `id` int NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_by` int unsigned NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_by` (`created_by`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `ip_whitelist`

INSERT INTO `ip_whitelist` (`id`, `ip_address`, `description`, `created_by`, `is_active`, `created_at`, `updated_at`) VALUES
('1', '127.0.0.1', 'Localhost - Development', '1', '1', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('2', '::1', 'IPv6 Localhost', '1', '1', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('3', '*************', 'Test IP Address', '1', '1', '2025-07-03 22:55:24', '2025-07-03 22:55:24');

-- --------------------------------------------------------
-- Table structure for table `login_attempts` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `login_attempts`;
CREATE TABLE `login_attempts` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `attempted_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `success` tinyint(1) NOT NULL DEFAULT '0',
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_email` (`email`),
  KEY `idx_attempted_at` (`attempted_at`),
  KEY `idx_success` (`success`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Login attempt tracking for rate limiting';

-- Dumping data for table `login_attempts`

INSERT INTO `login_attempts` (`id`, `ip_address`, `email`, `attempted_at`, `success`, `user_agent`) VALUES
('1', '::1', '<EMAIL>', '2025-05-31 01:27:09', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('2', '::1', '<EMAIL>', '2025-05-31 01:28:03', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('3', '::1', '<EMAIL>', '2025-05-31 01:28:13', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('4', '::1', '<EMAIL>', '2025-05-31 01:28:20', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('5', '::1', '<EMAIL>', '2025-05-31 01:28:43', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('6', '::1', '<EMAIL>', '2025-05-31 01:42:12', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('7', '::1', '<EMAIL>', '2025-05-31 01:42:12', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('8', '::1', '<EMAIL>', '2025-05-31 01:42:12', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('9', '::1', '<EMAIL>', '2025-05-31 01:42:12', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('10', '::1', '<EMAIL>', '2025-05-31 01:42:12', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('11', '::1', '<EMAIL>', '2025-05-31 01:42:12', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('12', '::1', '<EMAIL>', '2025-05-31 01:42:12', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('13', '::1', '<EMAIL>', '2025-05-31 01:42:12', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('14', '::1', '<EMAIL>', '2025-05-31 01:42:13', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('15', '::1', '<EMAIL>', '2025-05-31 01:42:13', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('16', '::1', '<EMAIL>', '2025-05-31 01:42:13', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('17', '::1', '<EMAIL>', '2025-05-31 01:42:49', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('18', '::1', '<EMAIL>', '2025-05-31 01:43:38', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('19', '::1', '<EMAIL>', '2025-05-31 01:43:46', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('20', '::1', '<EMAIL>', '2025-05-31 01:45:39', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('21', '::1', '<EMAIL>', '2025-05-31 01:45:47', '0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('22', '::1', '<EMAIL>', '2025-05-31 01:47:54', '1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('23', '::1', '<EMAIL>', '2025-05-31 01:56:43', '1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
('24', '127.0.0.1', '<EMAIL>', '2025-07-03 22:55:24', '0', NULL);

-- --------------------------------------------------------
-- Table structure for table `monitored_cases` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `monitored_cases`;
CREATE TABLE `monitored_cases` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `case_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `institution_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `institution_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `case_object` text COLLATE utf8mb4_unicode_ci,
  `monitoring_reason` text COLLATE utf8mb4_unicode_ci,
  `notification_frequency` enum('immediate','daily','weekly') COLLATE utf8mb4_unicode_ci DEFAULT 'daily',
  `last_checked` datetime DEFAULT NULL,
  `last_notification_sent` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_case` (`user_id`,`case_number`,`institution_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_case_number` (`case_number`),
  KEY `idx_institution_code` (`institution_code`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_last_checked` (`last_checked`),
  KEY `idx_notification_frequency` (`notification_frequency`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `monitored_cases`

INSERT INTO `monitored_cases` (`id`, `user_id`, `case_number`, `institution_code`, `institution_name`, `case_object`, `monitoring_reason`, `notification_frequency`, `last_checked`, `last_notification_sent`, `is_active`, `created_at`, `updated_at`) VALUES
('1', '4', '16042/3/2024', 'TribunalulBUCURESTI', 'Tribunalul București', 'pretentii', 'test', 'daily', '2025-07-03 22:38:15', NULL, '1', '2025-07-03 22:38:15', '2025-07-03 22:38:52');

-- --------------------------------------------------------
-- Table structure for table `notification_queue` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `notification_queue`;
CREATE TABLE `notification_queue` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `monitored_case_id` int unsigned NOT NULL,
  `case_change_id` int unsigned DEFAULT NULL,
  `notification_type` enum('immediate','daily_digest','weekly_summary') COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_body` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_html_body` text COLLATE utf8mb4_unicode_ci,
  `priority` tinyint unsigned DEFAULT '5',
  `status` enum('pending','processing','sent','failed','cancelled') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `attempts` tinyint unsigned DEFAULT '0',
  `max_attempts` tinyint unsigned DEFAULT '3',
  `scheduled_for` datetime NOT NULL,
  `sent_at` datetime DEFAULT NULL,
  `error_message` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `monitored_case_id` (`monitored_case_id`),
  KEY `case_change_id` (`case_change_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_scheduled_for` (`scheduled_for`),
  KEY `idx_notification_type` (`notification_type`),
  KEY `idx_priority` (`priority`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_notification_queue_status_scheduled` (`status`,`scheduled_for`),
  KEY `idx_notification_queue_user_created` (`user_id`,`created_at`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `notification_queue`

INSERT INTO `notification_queue` (`id`, `user_id`, `monitored_case_id`, `case_change_id`, `notification_type`, `email_subject`, `email_body`, `email_html_body`, `priority`, `status`, `attempts`, `max_attempts`, `scheduled_for`, `sent_at`, `error_message`, `created_at`, `updated_at`) VALUES
('1', '4', '1', '1', 'immediate', 'Dosar 16042/3/2024 - Adăugat în monitorizare', 'Bună ziua Super Administrator,\n\nDosarul 16042/3/2024 de la Tribunalul București a fost adăugat cu succes în lista de monitorizare.\n\nDetalii dosar:\n- Numărul dosarului: 16042/3/2024\n- Instanța: Tribunalul București\n- Obiectul dosarului: pretentii\n- Motivul monitorizării: test\n- Frecvența notificărilor: immediate\n\nVeți primi notificări automate când se produc modificări în acest dosar.\n\nPentru a gestiona dosarele monitorizate, accesați: http://localhost/just/monitor.php\n\nCu stimă,\nEchipa Portal Judiciar', '<!DOCTYPE html>\n<html lang="ro">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>Dosar adăugat în monitorizare</title>\n    <style>\n        body {\n            font-family: Arial, sans-serif;\n            line-height: 1.6;\n            color: #333;\n            max-width: 600px;\n            margin: 0 auto;\n            padding: 20px;\n            background-color: #f8f9fa;\n        }\n        .email-container {\n            background-color: white;\n            border-radius: 8px;\n            overflow: hidden;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        .header {\n            background-color: #007bff;\n            color: white;\n            padding: 30px 20px;\n            text-align: center;\n        }\n        .header h1 {\n            margin: 0;\n            font-size: 24px;\n            font-weight: 600;\n        }\n        .header p {\n            margin: 10px 0 0 0;\n            font-size: 16px;\n            opacity: 0.9;\n        }\n        .content {\n            padding: 30px 20px;\n        }\n        .greeting {\n            font-size: 16px;\n            margin-bottom: 20px;\n        }\n        .case-details {\n            background-color: #f8f9fa;\n            border-left: 4px solid #007bff;\n            padding: 20px;\n            margin: 20px 0;\n            border-radius: 0 4px 4px 0;\n        }\n        .case-details h3 {\n            margin: 0 0 15px 0;\n            color: #007bff;\n            font-size: 18px;\n        }\n        .detail-row {\n            margin-bottom: 10px;\n            display: flex;\n            flex-wrap: wrap;\n        }\n        .detail-label {\n            font-weight: 600;\n            color: #495057;\n            min-width: 140px;\n            margin-right: 10px;\n        }\n        .detail-value {\n            color: #333;\n            flex: 1;\n        }\n        .notification-info {\n            background-color: #e7f3ff;\n            border: 1px solid #b3d9ff;\n            border-radius: 4px;\n            padding: 15px;\n            margin: 20px 0;\n        }\n        .notification-info .icon {\n            color: #007bff;\n            margin-right: 8px;\n        }\n        .action-button {\n            display: inline-block;\n            background-color: #007bff;\n            color: white;\n            text-decoration: none;\n            padding: 12px 24px;\n            border-radius: 4px;\n            font-weight: 600;\n            margin: 20px 0;\n            text-align: center;\n        }\n        .action-button:hover {\n            background-color: #0056b3;\n            color: white;\n            text-decoration: none;\n        }\n        .footer {\n            background-color: #f8f9fa;\n            padding: 20px;\n            text-align: center;\n            border-top: 1px solid #dee2e6;\n        }\n        .footer p {\n            margin: 5px 0;\n            font-size: 14px;\n            color: #6c757d;\n        }\n        .footer .portal-name {\n            font-weight: 600;\n            color: #007bff;\n        }\n        @media (max-width: 600px) {\n            body {\n                padding: 10px;\n            }\n            .header {\n                padding: 20px 15px;\n            }\n            .content {\n                padding: 20px 15px;\n            }\n            .detail-row {\n                flex-direction: column;\n            }\n            .detail-label {\n                min-width: auto;\n                margin-bottom: 5px;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class="email-container">\n        <div class="header">\n            <h1>Portal Judiciar</h1>\n            <p>Dosar adăugat în monitorizare</p>\n        </div>\n        \n        <div class="content">\n            <div class="greeting">\n                Bună ziua <strong>Super Administrator</strong>,\n            </div>\n            \n            <p>Dosarul <strong>16042/3/2024</strong> de la <strong>Tribunalul București</strong> a fost adăugat cu succes în lista de monitorizare.</p>\n            \n            <div class="case-details">\n                <h3>📋 Detalii dosar</h3>\n                <div class="detail-row">\n                    <span class="detail-label">Numărul dosarului:</span>\n                    <span class="detail-value">16042/3/2024</span>\n                </div>\n                <div class="detail-row">\n                    <span class="detail-label">Instanța:</span>\n                    <span class="detail-value">Tribunalul București</span>\n                </div>\n                                <div class="detail-row">\n                    <span class="detail-label">Obiectul dosarului:</span>\n                    <span class="detail-value">pretentii</span>\n                </div>\n                                                <div class="detail-row">\n                    <span class="detail-label">Motivul monitorizării:</span>\n                    <span class="detail-value">test</span>\n                </div>\n                                <div class="detail-row">\n                    <span class="detail-label">Frecvența notificărilor:</span>\n                    <span class="detail-value">\n                                                    Imediat\n                                            </span>\n                </div>\n            </div>\n            \n            <div class="notification-info">\n                <span class="icon">🔔</span>\n                <strong>Notificări automate:</strong> Veți primi notificări automate când se produc modificări în acest dosar, conform frecvenței selectate.\n            </div>\n            \n            <div style="text-align: center;">\n                <a href="http://localhost/just/monitor.php" class="action-button">\n                    Gestionează dosarele monitorizate\n                </a>\n            </div>\n            \n            <p style="margin-top: 30px; font-size: 14px; color: #6c757d;">\n                Pentru a vedea detaliile complete ale dosarului, accesați: \n                <a href="http://localhost/just/detalii_dosar.php?numar=16042/3/2024&institutie=Tribunalul%20Bucure%C8%99ti" style="color: #007bff;">\n                    http://localhost/just/detalii_dosar.php?numar=16042/3/2024\n                </a>\n            </p>\n        </div>\n        \n        <div class="footer">\n            <p class="portal-name">Portal Judiciar</p>\n            <p>&copy; 2025 Portal Judiciar România. Toate drepturile rezervate.</p>\n            <p>\n                <a href="http://localhost/just" style="color: #007bff; text-decoration: none;">http://localhost/just</a>\n            </p>\n        </div>\n    </div>\n</body>\n</html>\n', '1', 'pending', '1', '3', '2025-07-28 18:57:12', NULL, 'Email sending failed', '2025-07-03 22:38:15', '2025-07-28 17:57:12');

-- --------------------------------------------------------
-- Table structure for table `password_resets` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `password_resets`;
CREATE TABLE `password_resets` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expires_at` datetime NOT NULL,
  `used` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_email` (`email`),
  KEY `idx_token` (`token`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_used` (`used`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Password reset tokens';

-- Dumping data for table `password_resets`

-- No data to dump for table `password_resets`

-- --------------------------------------------------------
-- Table structure for table `rate_limit_attempts` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `rate_limit_attempts`;
CREATE TABLE `rate_limit_attempts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `identifier` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'User ID or IP address',
  `action` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Action being rate limited',
  `success` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Whether the action was successful',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP address of the request',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT 'User agent string',
  `metadata` json DEFAULT NULL COMMENT 'Additional metadata about the attempt',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_identifier_action` (`identifier`,`action`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_action_success` (`action`,`success`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Rate limiting attempts tracking';

-- Dumping data for table `rate_limit_attempts`

INSERT INTO `rate_limit_attempts` (`id`, `identifier`, `action`, `success`, `ip_address`, `user_agent`, `metadata`, `created_at`) VALUES
('1', 'user_4', 'check_case', '1', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"result": {"data": {"data": "17.05.2024", "numar": "16042/3/2024", "parti": [{"nume": "SINDICATUL FORUM DIN ADMINISTRATIA PUBLICA", "calitate": "Reclamant"}, {"nume": "DIRECTIA GENERALA DE TAXE SI IMPOZITE LOCALE SECTOR 4", "calitate": "Pârât"}, {"nume": "BRÎNZAC MARIANA", "calitate": "Reclamant"}, {"nume": "DRUMEA EUGEN VIOREL", "calitate": "Reclamant"}, {"nume": "HARALAMBIE ADRIANA", "calitate": "Reclamant"}, {"nume": "MARIN CRISTIAN", "calitate": "Reclamant"}, {"nume": "NEDELCU VASILE", "calitate": "Reclamant"}, {"nume": "STOFOR ALEXANDRU GABRIEL", "calitate": "Reclamant"}, {"nume": "TIIREI MARINELA", "calitate": "Reclamant"}, {"nume": "UNGUREANU GABRIELA SIMONA", "calitate": "Reclamant"}, {"nume": "Păun Liviu Mihai.", "calitate": "Intervenient"}], "obiect": "pretentii", "caiAtac": [], "sedinte": [{"ora": "09:30", "data": "04.07.2025", "complet": "C4", "solutie": "", "dataDocument": "", "solutieSumar": "", "numarDocument": "", "dataPronuntare": "", "documentSedinta": ""}, {"ora": "09:00", "data": "18.11.2024", "complet": "C4", "solutie": "Alte cauze", "dataDocument": "18.11.2024", "solutieSumar": "Admite în principiu cererea de intervenţie în interes propriu, formulată de numitul Păun Liviu Mihai.\\nPune în vedere părţilor să depună întâmpinare la cererea de intervenţie în interes propriu cu cel puţin 10 zile înainte de termenul de judecată, sub sancţiunea decăderii din dreptul de a mai propune probe şi de a invoca excepţii, în afara celor de ordine publică.\\n\\tUneşte cu fondul cauzei excepţia prematurităţii, invocată de pârât.\\n\\tÎn temeiul art. 2 alin. 3 din O.U.G. nr.  62/2024, raportat art. 412 alin. 1 pct. 8 C. pr. civ, suspendă solu?ionarea cauzei până la solu?ionarea ?i publicarea în Monitorul Oficial al României a hotărârii prealabile pentru dezlegarea chestiunii de drept. de către Î.C.C.J.  în dosarele  nr. 19673/1/2024,  nr. 2389/1/2024,  nr.2430/1/2024.\\n\\tCu drept de recurs pe toată durata suspendării, ce se depune la Tribunalul Bucure?ti, Sec?ia a VIII-a Conflicte de Muncă ?i Asigurări Sociale, în ceea ce priveşte  măsura suspendării şi odată cu fondul cauzei în ceea ce priveşte soluţia privind cererea de intervenţie în interes propriu şi excepţia prematurităţii.\\n\\tPronun?ată astăzi, 18 noiembrie 2024, prin punerea solu?iei la dispozi?ia păr?ilor prin intermediul grefei instan?ei.\\n\\n\\n\\n", "numarDocument": "", "dataPronuntare": "18.11.2024", "documentSedinta": "incheiereSuspendare"}, {"ora": "09:30", "data": "04.11.2024", "complet": "C4", "solutie": "Amână pronunţarea", "dataDocument": "04.11.2024", "solutieSumar": "\\n\\tAmâna pronunţarea la data de 18.11.2024 prin punerea soluţiei la dispoziţia parţilor prin mijlocirea grefei instanţei, conf. art. 396 alin. 2 Cod Procedură Civilă.\\n\\tPronunţată astăzi 04.11.2024.\\n\\n\\n\\n", "numarDocument": "", "dataPronuntare": "04.11.2024", "documentSedinta": "incheiereAmanareinitialaapronuntarii"}, {"ora": "09:30", "data": "16.09.2024", "complet": "C4", "solutie": "Amână cauza", "dataDocument": "16.09.2024", "solutieSumar": "Amână cauza pentru a lua cunoştinţă de înscrisuri.", "numarDocument": "", "dataPronuntare": "16.09.2024", "documentSedinta": "incheieredesedinta"}], "institutie": "TribunalulBUCURESTI", "numarVechi": "", "departament": "Secţia a-VIII-a Conflicte de Muncă şi Asigurări Sociale", "categorieCaz": "Litigiidemunca", "dataModificare": "25.06.2025", "stadiuProcesual": "Fond", "categorieCazNume": "Litigii de muncă", "stadiuProcesualNume": "Fond"}, "success": true}}', '2025-07-03 22:38:03'),
('2', 'user_4', 'check_case', '0', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"reason": "csrf_invalid"}', '2025-07-03 22:38:09'),
('3', 'user_4', 'check_case', '0', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"error": "Token CSRF invalid"}', '2025-07-03 22:38:09'),
('4', 'user_4', 'check_case', '0', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"reason": "csrf_invalid"}', '2025-07-03 22:38:14'),
('5', 'user_4', 'check_case', '0', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"error": "Token CSRF invalid"}', '2025-07-03 22:38:14'),
('6', 'user_4', 'add_case', '1', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"result": {"executed": true}}', '2025-07-03 22:38:15'),
('7', 'user_4', 'check_case', '0', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"reason": "csrf_invalid"}', '2025-07-03 22:38:23'),
('8', 'user_4', 'check_case', '0', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"error": "Token CSRF invalid"}', '2025-07-03 22:38:23'),
('9', 'user_4', 'add_case', '0', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"reason": "csrf_invalid"}', '2025-07-03 22:38:24'),
('10', 'user_4', 'add_case', '0', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"error": "Token CSRF invalid"}', '2025-07-03 22:38:24'),
('11', 'user_4', 'add_case', '0', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"reason": "csrf_invalid"}', '2025-07-03 22:38:32'),
('12', 'user_4', 'add_case', '0', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"error": "Token CSRF invalid"}', '2025-07-03 22:38:32'),
('13', 'user_4', 'add_case', '0', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"reason": "csrf_invalid"}', '2025-07-03 22:38:36'),
('14', 'user_4', 'add_case', '0', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"error": "Token CSRF invalid"}', '2025-07-03 22:38:36'),
('15', 'user_4', 'update_frequency', '1', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{"result": {"executed": true}}', '2025-07-03 22:38:52');

-- --------------------------------------------------------
-- Table structure for table `schema_migrations` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `schema_migrations`;
CREATE TABLE `schema_migrations` (
  `version` varchar(10) NOT NULL,
  `applied_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciMyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Dumping data for table `schema_migrations`

INSERT INTO `schema_migrations` (`version`, `applied_at`) VALUES
('1', '2025-07-03 22:28:11'),
('005', '2025-07-03 22:52:03');

-- --------------------------------------------------------
-- Table structure for table `security_incidents` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `security_incidents`;
CREATE TABLE `security_incidents` (
  `id` int NOT NULL AUTO_INCREMENT,
  `incident_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Type of security incident',
  `severity` enum('low','medium','high','critical') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'medium',
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Description of the incident',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP address involved',
  `user_id` int unsigned DEFAULT NULL COMMENT 'User ID if applicable',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT 'User agent string',
  `request_data` json DEFAULT NULL COMMENT 'Request data that triggered the incident',
  `resolved` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether the incident has been resolved',
  `resolved_at` timestamp NULL DEFAULT NULL COMMENT 'When the incident was resolved',
  `resolved_by` int unsigned DEFAULT NULL COMMENT 'Admin user who resolved the incident',
  `notes` text COLLATE utf8mb4_unicode_ci COMMENT 'Additional notes about the incident',
  `resolution` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_incident_type` (`incident_type`),
  KEY `idx_severity` (`severity`),
  KEY `idx_resolved` (`resolved`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_ip_address` (`ip_address`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Security incidents tracking';

-- Dumping data for table `security_incidents`

INSERT INTO `security_incidents` (`id`, `incident_type`, `severity`, `description`, `ip_address`, `user_id`, `user_agent`, `request_data`, `resolved`, `resolved_at`, `resolved_by`, `notes`, `resolution`, `created_at`) VALUES
('1', 'test_incident', 'low', 'Test security incident for verification', '127.0.0.1', NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, '2025-07-03 22:55:24');

-- --------------------------------------------------------
-- Table structure for table `system_logs` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `system_logs`;
CREATE TABLE `system_logs` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `level` enum('debug','info','warning','error','critical') COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `context` json DEFAULT NULL,
  `user_id` int unsigned DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_level` (`level`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `system_logs`

INSERT INTO `system_logs` (`id`, `level`, `message`, `context`, `user_id`, `ip_address`, `user_agent`, `created_at`) VALUES
('1', 'info', 'Case added to monitoring', '{"user_id": 4, "case_number": "16042/3/2024", "institution": "TribunalulBUCURESTI"}', '4', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-03 22:38:15'),
('2', 'info', 'Database setup completed successfully', '{}', NULL, NULL, NULL, '2025-07-28 17:55:58'),
('3', 'warning', '[general] Test warning message', '{"test": true}', NULL, NULL, NULL, '2025-07-28 17:57:13'),
('4', 'error', '[general] Test error message', '{"test": true}', NULL, NULL, NULL, '2025-07-28 17:57:13'),
('5', 'warning', '[general] Test warning message', '{"test": true}', NULL, NULL, NULL, '2025-07-28 17:57:52'),
('6', 'error', '[general] Test error message', '{"test": true}', NULL, NULL, NULL, '2025-07-28 17:57:52'),
('7', 'warning', '[general] Test warning message', '{"test": true}', NULL, NULL, NULL, '2025-07-28 18:10:33'),
('8', 'error', '[general] Test error message', '{"test": true}', NULL, NULL, NULL, '2025-07-28 18:10:33');

-- --------------------------------------------------------
-- Table structure for table `system_settings` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_value` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'general',
  `description` text COLLATE utf8mb4_unicode_ci,
  `data_type` enum('string','integer','boolean','json') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'string',
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `idx_category` (`category`),
  KEY `idx_setting_key` (`setting_key`),
  KEY `idx_is_public` (`is_public`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `system_settings`

INSERT INTO `system_settings` (`id`, `setting_key`, `setting_value`, `category`, `description`, `data_type`, `is_public`, `created_at`, `updated_at`) VALUES
('1', 'password_min_length', '8', 'security', 'Lungimea minimă a parolei', 'integer', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('2', 'password_require_uppercase', '1', 'security', 'Necesită litere mari în parolă', 'boolean', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('3', 'password_require_lowercase', '1', 'security', 'Necesită litere mici în parolă', 'boolean', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('4', 'password_require_numbers', '1', 'security', 'Necesită cifre în parolă', 'boolean', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('5', 'password_require_symbols', '1', 'security', 'Necesită simboluri în parolă', 'boolean', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('6', 'session_timeout', '3600', 'security', 'Timeout sesiune în secunde', 'integer', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('7', 'max_login_attempts', '5', 'security', 'Numărul maxim de încercări de login', 'integer', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('8', 'lockout_duration', '900', 'security', 'Durata blocării contului în secunde', 'integer', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('9', 'csrf_token_lifetime', '3600', 'security', 'Durata de viață a token-urilor CSRF în secunde', 'integer', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('10', 'csrf_strict_mode', '1', 'security', 'Mod strict pentru validarea CSRF', 'boolean', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('11', 'rate_limit_login', '5', 'security', 'Limite rate pentru login per oră', 'integer', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('12', 'rate_limit_api', '100', 'security', 'Limite rate pentru API per oră', 'integer', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('13', 'rate_limit_contact', '5', 'security', 'Limite rate pentru formulare contact per oră', 'integer', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('14', 'enable_2fa', '0', 'security', 'Activează autentificarea cu doi factori', 'boolean', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('15', 'require_email_verification', '1', 'security', 'Necesită verificarea email-ului', 'boolean', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('16', 'enable_ip_whitelist', '0', 'security', 'Activează whitelist-ul de IP-uri', 'boolean', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03'),
('17', 'enable_geo_blocking', '0', 'security', 'Activează blocarea geografică', 'boolean', '0', '2025-07-03 22:52:03', '2025-07-03 22:52:03');

-- --------------------------------------------------------
-- Table structure for table `user_consents` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `user_consents`;
CREATE TABLE `user_consents` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `consent_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Type of consent (monitoring, email_notifications, etc.)',
  `granted` tinyint(1) NOT NULL COMMENT 'Whether consent was granted',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP address when consent was given',
  `user_agent` text COLLATE utf8mb4_unicode_ci COMMENT 'User agent when consent was given',
  `metadata` json DEFAULT NULL COMMENT 'Additional consent metadata',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_consent` (`user_id`,`consent_type`),
  KEY `idx_consent_type` (`consent_type`),
  KEY `idx_created_at` (`created_at`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User consent records for GDPR compliance';

-- Dumping data for table `user_consents`

INSERT INTO `user_consents` (`id`, `user_id`, `consent_type`, `granted`, `ip_address`, `user_agent`, `metadata`, `created_at`) VALUES
('1', '1', 'monitoring', '1', '127.0.0.1', NULL, NULL, '2025-07-03 21:18:25'),
('2', '3', 'monitoring', '1', '127.0.0.1', NULL, NULL, '2025-07-03 21:18:25'),
('3', '1', 'monitoring', '1', '127.0.0.1', NULL, NULL, '2025-07-24 13:39:09'),
('4', '3', 'monitoring', '1', '127.0.0.1', NULL, NULL, '2025-07-24 13:39:09'),
('5', '4', 'monitoring', '1', '127.0.0.1', NULL, NULL, '2025-07-24 13:39:09');

-- --------------------------------------------------------
-- Table structure for table `user_favorites` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `user_favorites`;
CREATE TABLE `user_favorites` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `case_number` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `institution` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `case_object` text COLLATE utf8mb4_unicode_ci,
  `case_category` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `case_category_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `procedural_stage` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `procedural_stage_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parties` json DEFAULT NULL,
  `custom_note` text COLLATE utf8mb4_unicode_ci,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_case` (`user_id`,`case_number`,`institution`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_case_number` (`case_number`),
  KEY `idx_institution` (`institution`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_favorites_user_created` (`user_id`,`created_at`),
  KEY `idx_user_favorites_case_lookup` (`case_number`,`institution`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User favorite cases with case details and custom notes';

-- Dumping data for table `user_favorites`

INSERT INTO `user_favorites` (`id`, `user_id`, `case_number`, `institution`, `case_object`, `case_category`, `case_category_name`, `procedural_stage`, `procedural_stage_name`, `parties`, `custom_note`, `created_at`, `updated_at`) VALUES
('1', '3', '2623/30/2023', 'TribunalulTIMIS', 'Test case object for integration testing', 'CIVIL', 'Civil', 'FOND', 'Fond', '[{"nume": "Test Party 1", "calitate": "reclamant"}, {"nume": "Test Party 2", "calitate": "parat"}]', '', '2025-05-31 02:08:59', '2025-05-31 02:08:59');

-- --------------------------------------------------------
-- Table structure for table `user_privacy_settings` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `user_privacy_settings`;
CREATE TABLE `user_privacy_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL,
  `setting_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Privacy setting name',
  `setting_value` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Privacy setting value',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_setting` (`user_id`,`setting_name`),
  KEY `idx_setting_name` (`setting_name`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User privacy settings';

-- Dumping data for table `user_privacy_settings`

INSERT INTO `user_privacy_settings` (`id`, `user_id`, `setting_name`, `setting_value`, `updated_at`, `created_at`) VALUES
('1', '1', 'data_retention_period', '365', '2025-07-03 21:18:25', '2025-07-03 21:18:25'),
('2', '3', 'data_retention_period', '365', '2025-07-03 21:18:25', '2025-07-03 21:18:25'),
('4', '1', 'notification_frequency', 'daily', '2025-07-03 21:18:25', '2025-07-03 21:18:25'),
('5', '3', 'notification_frequency', 'daily', '2025-07-03 21:18:25', '2025-07-03 21:18:25'),
('7', '1', 'data_sharing', 'none', '2025-07-03 21:18:25', '2025-07-03 21:18:25'),
('8', '3', 'data_sharing', 'none', '2025-07-03 21:18:25', '2025-07-03 21:18:25'),
('9', '4', 'notification_frequency', 'daily', '2025-07-24 13:39:09', '2025-07-24 13:39:09'),
('10', '4', 'data_sharing', 'none', '2025-07-24 13:39:09', '2025-07-24 13:39:09');

-- --------------------------------------------------------
-- Table structure for table `user_sessions` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `user_sessions`;
CREATE TABLE `user_sessions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `session_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int unsigned NOT NULL,
  `expires_at` datetime NOT NULL,
  `remember_token` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_remember_token` (`remember_token`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User session management';

-- Dumping data for table `user_sessions`

INSERT INTO `user_sessions` (`id`, `session_id`, `user_id`, `expires_at`, `remember_token`, `ip_address`, `user_agent`, `created_at`, `updated_at`) VALUES
('1', 'l6rdss154kni3amcjiehlbmr3e', '3', '2025-05-31 03:47:54', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-31 01:47:54', '2025-05-31 01:47:54'),
('2', 'ogl5h1kbcr7r6sd5mhfg2mqm49', '3', '2025-05-31 03:56:43', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-31 01:56:43', '2025-05-31 01:56:43');

-- --------------------------------------------------------
-- Table structure for table `users` (PERFECT SYNTAX)
-- --------------------------------------------------------

DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `first_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `registration_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `email_verified` tinyint(1) NOT NULL DEFAULT '0',
  `email_verification_token` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT 'Soft delete timestamp for GDPR compliance',
  `gdpr_consent_date` timestamp NULL DEFAULT NULL COMMENT 'Date when GDPR consent was given',
  `data_processing_consent` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Consent for data processing',
  `marketing_consent` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Consent for marketing communications',
  `failed_login_attempts` int NOT NULL DEFAULT '0' COMMENT 'Number of failed login attempts',
  `locked_until` timestamp NULL DEFAULT NULL COMMENT 'Account locked until this time',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT 'Last successful login',
  `last_login_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP address of last login',
  `admin_role` enum('super_admin','admin','moderator','viewer') COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Administrative role',
  `notification_preferences` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_email` (`email`),
  KEY `idx_email_verified` (`email_verified`),
  KEY `idx_verification_token` (`email_verification_token`),
  KEY `idx_last_login` (`last_login`),
  KEY `idx_users_deleted_at` (`deleted_at`),
  KEY `idx_users_gdpr_consent` (`gdpr_consent_date`),
  KEY `idx_users_locked_until` (`locked_until`),
  KEY `idx_users_last_login` (`last_login_at`),
  KEY `idx_users_admin_role` (`admin_role`),
  KEY `idx_users_email_verified_deleted` (`email_verified`,`deleted_at`),
  KEY `idx_users_notification_prefs` (`email_verified`,`deleted_at`,`data_processing_consent`) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ciInnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User accounts for judicial portal';

-- Dumping data for table `users`

INSERT INTO `users` (`id`, `email`, `password_hash`, `first_name`, `last_name`, `registration_date`, `email_verified`, `email_verification_token`, `last_login`, `created_at`, `updated_at`, `deleted_at`, `gdpr_consent_date`, `data_processing_consent`, `marketing_consent`, `failed_login_attempts`, `locked_until`, `last_login_at`, `last_login_ip`, `admin_role`, `notification_preferences`) VALUES
('1', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'Portal', '2025-05-31 01:06:28', '1', NULL, NULL, '2025-05-31 01:06:28', '2025-07-28 17:54:24', NULL, NULL, '1', '0', '0', NULL, NULL, NULL, NULL, '{"immediate_notifications":true,"daily_digest":true,"weekly_summary":false,"email_format":"html","max_notifications_per_day":50,"quiet_hours_start":"22:00","quiet_hours_end":"08:00","timezone":"Europe\\/Bucharest"}'),
('3', '<EMAIL>', '$2y$12$3WudhEoOEw0dvO6u62FBK.X6a2MmO6Nj8v7zLml6.Imk1SE4Klotq', 'Anghel', 'Gabriel', '2025-05-31 01:37:12', '1', NULL, '2025-05-31 01:56:43', '2025-05-31 01:37:12', '2025-07-28 17:54:24', NULL, NULL, '1', '0', '0', NULL, NULL, NULL, NULL, '{"immediate_notifications":true,"daily_digest":true,"weekly_summary":false,"email_format":"html","max_notifications_per_day":50,"quiet_hours_start":"22:00","quiet_hours_end":"08:00","timezone":"Europe\\/Bucharest"}'),
('4', '<EMAIL>', '$2y$10$P6VuLCo7/Ch4xDhsil8Kb.k7yF/cGsyZIoXVij8G5xP2DACulKUw2', 'Super', 'Administrator', '2025-07-03 21:24:55', '1', NULL, NULL, '2025-07-03 21:24:55', '2025-07-28 17:54:24', NULL, '2025-07-03 21:24:55', '1', '0', '0', NULL, '2025-07-03 22:08:35', '::1', 'super_admin', '{"immediate_notifications":true,"daily_digest":true,"weekly_summary":false,"email_format":"html","max_notifications_per_day":50,"quiet_hours_start":"22:00","quiet_hours_end":"08:00","timezone":"Europe\\/Bucharest"}');

-- --------------------------------------------------------
-- Views (PERFECT SYNTAX)
-- --------------------------------------------------------


