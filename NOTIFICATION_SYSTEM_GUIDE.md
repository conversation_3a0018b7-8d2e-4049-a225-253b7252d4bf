# 📧 Portal Judiciar România - Notification System Guide

## 🎯 Overview

The notification system has been completely reviewed, updated, and enhanced to provide:
- **Reliable email delivery** with retry mechanisms
- **Efficient queue management** with batch processing
- **User preference handling** with granular controls
- **Comprehensive logging** for debugging and monitoring
- **Template-based emails** with professional formatting
- **Performance optimization** for WAMP environments

## 🔍 Audit Results

### Issues Found and Fixed:
1. ❌ **Missing error handling** in email sending → ✅ **Added comprehensive error handling**
2. ❌ **No retry mechanism** for failed notifications → ✅ **Implemented exponential backoff retry**
3. ❌ **Hardcoded SMTP credentials** → ✅ **Moved to configuration constants**
4. ❌ **Missing logging system** → ✅ **Added structured logging with rotation**
5. ❌ **No user preference validation** → ✅ **Added UserPreferencesManager**
6. ❌ **Inefficient queue processing** → ✅ **Added NotificationQueueManager**
7. ❌ **Missing notification templates** → ✅ **Added failure and status templates**
8. ❌ **No cleanup mechanism** → ✅ **Added automated cleanup processes**

## 🏗️ System Architecture

### Core Components:

#### 1. **NotificationManager** (`src/Services/NotificationManager.php`)
- Main notification orchestrator
- Handles email template rendering
- Manages notification queuing
- Improved error handling and logging

#### 2. **NotificationQueueManager** (`src/Services/NotificationQueueManager.php`)
- Optimized batch processing
- Queue health monitoring
- Stuck notification cleanup
- Performance optimization

#### 3. **UserPreferencesManager** (`src/Services/UserPreferencesManager.php`)
- User notification preferences
- Quiet hours management
- Daily notification limits
- Email format preferences

#### 4. **NotificationLogger** (`src/Services/NotificationLogger.php`)
- Structured logging system
- Log rotation and cleanup
- Performance monitoring
- Error tracking and analysis

## 📊 How the Notification Process Works

### 1. **Immediate Notifications**
```
Case Change Detected → Queue Immediate Notification → Process Queue → Send Email
```

### 2. **Daily Digest Process**
```
Daily Cron (8 AM) → Check User Preferences → Gather Changes → Generate Digest → Queue → Send
```

### 3. **Weekly Summary Process**
```
Weekly Cron (Monday 8 AM) → Check User Preferences → Gather Week's Changes → Generate Summary → Queue → Send
```

### 4. **Queue Processing**
```
Cron Job → Get Pending Notifications → Check User Limits → Respect Quiet Hours → Send Email → Update Status
```

## ⚙️ Configuration

### Required Constants (in `src/Config/constants.php`):
```php
// Notification settings
define('MAX_NOTIFICATION_ATTEMPTS', 3);
define('NOTIFICATION_RETRY_DELAY', 3600); // 1 hour
define('NOTIFICATION_BATCH_SIZE', 20);

// Email settings
define('CONTACT_EMAIL', '<EMAIL>');
define('CONTACT_NAME', 'Portal Judiciar România');

// Logging
define('LOG_DIR', __DIR__ . '/../../logs');
```

### User Preferences Structure:
```json
{
  "immediate_notifications": true,
  "daily_digest": true,
  "weekly_summary": false,
  "email_format": "html",
  "max_notifications_per_day": 50,
  "quiet_hours_start": "22:00",
  "quiet_hours_end": "08:00",
  "timezone": "Europe/Bucharest"
}
```

## 🧪 Testing the Notification System

### 1. **Run Comprehensive Tests**
```bash
php test_notification_system.php
```

### 2. **Test Individual Components**
```bash
# Test queue management
php -r "
require_once 'bootstrap.php';
use App\Services\NotificationQueueManager;
\$qm = new NotificationQueueManager();
print_r(\$qm->getQueueStats());
"

# Test user preferences
php -r "
require_once 'bootstrap.php';
use App\Services\UserPreferencesManager;
\$pm = new UserPreferencesManager();
print_r(\$pm->getUserPreferences(1));
"
```

### 3. **Test Email Sending**
```bash
# Queue a test notification
php -r "
require_once 'bootstrap.php';
use App\Services\NotificationManager;
\$nm = new NotificationManager();
\$result = \$nm->queueCaseChangeNotification(1, 'TEST/123/2024', 'TRIB_B1');
echo 'Queued: ' . (\$result ? 'Yes' : 'No') . PHP_EOL;
"
```

## 🔄 Cron Job Setup

### 1. **Main Notification Processing** (Every 5 minutes)
```bash
*/5 * * * * /usr/bin/php /path/to/just/cron/send_scheduled_notifications.php >> /path/to/just/logs/cron.log 2>&1
```

### 2. **Case Monitoring** (Every 30 minutes)
```bash
*/30 * * * * /usr/bin/php /path/to/just/cron/monitor_cases.php >> /path/to/just/logs/monitor.log 2>&1
```

### 3. **Daily Cleanup** (Daily at 2 AM)
```bash
0 2 * * * /usr/bin/php /path/to/just/cron/cleanup_notifications.php >> /path/to/just/logs/cleanup.log 2>&1
```

## 📈 Performance Optimizations

### 1. **Batch Processing**
- Process notifications in configurable batches
- Prevent memory exhaustion
- Optimize database queries

### 2. **Queue Management**
- Automatic cleanup of stuck notifications
- Health monitoring and alerts
- Performance metrics tracking

### 3. **User Preference Caching**
- Efficient preference retrieval
- Minimal database queries
- Smart default handling

### 4. **Email Optimization**
- Template caching
- Efficient HTML/text generation
- Retry with exponential backoff

## 🛠️ Production Configuration

### 1. **Email Settings**
Update `src/Config/constants.php`:
```php
define('CONTACT_EMAIL', '<EMAIL>');
define('CONTACT_NAME', 'Portal Judiciar România');
```

### 2. **Log Directory**
Ensure logs directory is writable:
```bash
mkdir -p logs/notifications
chmod 755 logs/notifications
```

### 3. **Database Optimization**
```sql
-- Add indexes for better performance
CREATE INDEX idx_notification_queue_status_scheduled ON notification_queue(status, scheduled_for);
CREATE INDEX idx_notification_queue_user_created ON notification_queue(user_id, created_at);
CREATE INDEX idx_users_notification_prefs ON users(email_verified, is_active);
```

## 📊 Monitoring and Maintenance

### 1. **Check System Health**
```bash
php -r "
require_once 'bootstrap.php';
use App\Services\NotificationQueueManager;
\$qm = new NotificationQueueManager();
\$health = \$qm->getHealthStatus();
echo 'System Health: ' . \$health['status'] . PHP_EOL;
if (!empty(\$health['issues'])) {
    echo 'Issues: ' . implode(', ', \$health['issues']) . PHP_EOL;
}
"
```

### 2. **View Recent Errors**
```bash
php -r "
require_once 'bootstrap.php';
use App\Services\NotificationLogger;
\$logger = new NotificationLogger();
\$errors = \$logger->getRecentErrors(10);
foreach (\$errors as \$error) {
    echo \$error['created_at'] . ' [' . \$error['level'] . '] ' . \$error['message'] . PHP_EOL;
}
"
```

### 3. **Queue Statistics**
```bash
php -r "
require_once 'bootstrap.php';
use App\Services\NotificationQueueManager;
\$qm = new NotificationQueueManager();
print_r(\$qm->getQueueStats());
"
```

## 🎉 System Status

**✅ NOTIFICATION SYSTEM IS FULLY OPERATIONAL!**

### Features Working:
- ✅ **Email notifications** with retry mechanisms
- ✅ **Queue management** with batch processing  
- ✅ **User preferences** with validation
- ✅ **Template system** with professional formatting
- ✅ **Logging system** with rotation and analysis
- ✅ **Performance optimization** for WAMP environments
- ✅ **Error handling** with comprehensive recovery
- ✅ **Testing framework** for validation

### Ready for Production:
- ✅ **Scalable architecture** handles high volumes
- ✅ **Resource efficient** minimal server impact
- ✅ **Reliable delivery** with automatic retries
- ✅ **User-friendly** respects preferences and limits
- ✅ **Maintainable** comprehensive logging and monitoring

The notification system is now enterprise-ready and provides a robust, efficient, and user-friendly experience for all Portal Judiciar România users.
