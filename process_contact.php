<?php
/**
 * Portal Judiciar - Procesare formular de contact
 * 
 * Procesează trimiterea mesajelor de contact cu validare, 
 * protecție CSRF și rate limiting
 */

// Încărcăm bootstrap-ul aplicației
require_once 'bootstrap.php';

// Importăm clasele necesare
use App\Helpers\SecurityHelper;

// Setăm header-ul pentru JSON response
header('Content-Type: application/json; charset=UTF-8');

// Inițializăm sesiunea dacă nu este deja inițializată
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Funcție pentru returnarea unui răspuns JSON
 */
function jsonResponse($success, $message, $data = []) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * Funcție pentru logarea erorilor
 */
function logError($message, $context = []) {
    $logFile = LOG_DIR . '/contact_errors.log';
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'context' => $context,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    file_put_contents($logFile, json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
}

try {
    // Verificăm dacă cererea este de tip POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        jsonResponse(false, 'Metodă de cerere invalidă.');
    }

    // Verificăm token-ul CSRF
    $csrfToken = $_POST['csrf_token'] ?? '';
    if (!SecurityHelper::validateCSRFToken($csrfToken)) {
        logError('Token CSRF invalid', ['token' => $csrfToken]);
        jsonResponse(false, 'Token de securitate invalid. Vă rugăm să reîncărcați pagina și să încercați din nou.');
    }

    // Verificăm rate limiting (maxim 5 mesaje pe oră per IP)
    $rateLimitCheck = SecurityHelper::checkRateLimit('contact', 5, 3600);
    if (!$rateLimitCheck['allowed']) {
        $message = SecurityHelper::getRateLimitMessage($rateLimitCheck);
        logError('Rate limit depășit', $rateLimitCheck);
        jsonResponse(false, $message);
    }

    // Obținem și curățăm datele din formular
    $nume = SecurityHelper::sanitizeInput($_POST['nume'] ?? '');
    $email = SecurityHelper::sanitizeInput($_POST['email'] ?? '');
    $telefon = SecurityHelper::sanitizeInput($_POST['telefon'] ?? '');
    $mesaj = SecurityHelper::sanitizeInput($_POST['mesaj'] ?? '');

    // Validăm datele obligatorii
    $errors = [];

    if (empty($nume)) {
        $errors[] = 'Numele este obligatoriu.';
    } elseif (strlen($nume) < 2 || strlen($nume) > 100) {
        $errors[] = 'Numele trebuie să aibă între 2 și 100 de caractere.';
    }

    if (empty($email)) {
        $errors[] = 'Adresa de email este obligatorie.';
    } elseif (!SecurityHelper::validateEmail($email)) {
        $errors[] = 'Adresa de email nu este validă.';
    }

    if (empty($mesaj)) {
        $errors[] = 'Mesajul este obligatoriu.';
    } elseif (strlen($mesaj) < 10 || strlen($mesaj) > 2000) {
        $errors[] = 'Mesajul trebuie să aibă între 10 și 2000 de caractere.';
    }

    // Validăm numărul de telefon dacă este completat
    if (!empty($telefon) && !SecurityHelper::validateRomanianPhone($telefon)) {
        $errors[] = 'Numărul de telefon nu este valid.';
    }

    // Dacă avem erori, le returnăm
    if (!empty($errors)) {
        jsonResponse(false, 'Vă rugăm să corectați următoarele erori: ' . implode(' ', $errors));
    }

    // Înregistrăm acțiunea pentru rate limiting
    SecurityHelper::recordAction('contact');

    // Pregătim email-ul cu funcția nativă mail()
    $to = CONTACT_EMAIL;
    $subject = 'Mesaj nou de pe Portal Judiciar - ' . $nume;
    $message = generateTextEmailBody($nume, $email, $telefon, $mesaj);

    // Headers pentru email
    $headers = array();
    $headers[] = 'From: ' . $nume . ' <' . $email . '>';
    $headers[] = 'Reply-To: ' . $email;
    $headers[] = 'X-Mailer: PHP/' . phpversion();
    $headers[] = 'Content-Type: text/plain; charset=UTF-8';
    $headers[] = 'Content-Transfer-Encoding: 8bit';

    // Trimitem email-ul
    $mailSent = mail($to, $subject, $message, implode("\r\n", $headers));

    if (!$mailSent) {
        throw new Exception('Funcția mail() a eșuat la trimiterea email-ului.');
    }

    // Logăm succesul
    $logData = [
        'nume' => $nume,
        'email' => $email,
        'telefon' => $telefon,
        'mesaj_length' => strlen($mesaj)
    ];
    
    $logFile = LOG_DIR . '/contact_success.log';
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'data' => $logData,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];
    
    file_put_contents($logFile, json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);

    // Returnăm succesul
    jsonResponse(true, 'Mesajul dumneavoastră a fost trimis cu succes! Vă vom răspunde în cel mai scurt timp posibil.');

} catch (Exception $e) {
    // Logăm eroarea
    logError('Eroare la trimiterea email-ului', [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);

    // Returnăm eroarea
    jsonResponse(false, 'A apărut o eroare la trimiterea mesajului. Vă rugăm să încercați din nou mai târziu.');
}



/**
 * Generează conținutul text al email-ului
 */
function generateTextEmailBody($nume, $email, $telefon, $mesaj) {
    $telefonText = !empty($telefon) ? "\nTelefon: {$telefon}" : '';

    $separator = str_repeat('=', 60);
    $minorSeparator = str_repeat('-', 40);

    return "{$separator}\n" .
           "PORTAL JUDICIAR - MESAJ NOU DE CONTACT\n" .
           "{$separator}\n\n" .
           "DETALII EXPEDITOR:\n" .
           "{$minorSeparator}\n" .
           "Nume: {$nume}\n" .
           "Email: {$email}{$telefonText}\n" .
           "Data trimiterii: " . date('d.m.Y H:i:s') . "\n" .
           "IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'necunoscut') . "\n\n" .
           "MESAJUL:\n" .
           "{$minorSeparator}\n" .
           "{$mesaj}\n\n" .
           "{$separator}\n" .
           "Acest email a fost trimis automat prin formularul de contact\n" .
           "de pe site-ul Portal Judiciar (http://localhost/just/)\n" .
           "Pentru a răspunde, utilizați adresa de email a expeditorului.\n" .
           "{$separator}";
}
