<?php
// Debug why the asterisk is lost from search term
require_once 'bootstrap.php';

use App\Services\DosarService;

echo "<h1>🔍 Debug Asterisk Loss and Missing Checkbox</h1>";

// Test different input scenarios
$testInputs = [
    "14096/3/2024*",
    "14096/3/2024",
    "14096/3/2024*\n",
    " 14096/3/2024* ",
    "14096/3/2024*,",
];

echo "<h2>Step 1: Test Input Processing</h2>";

function parseBulkSearchTerms($input) {
    echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
    echo "<strong>Input received:</strong> '" . htmlspecialchars($input) . "'<br>";
    echo "<strong>Input length:</strong> " . strlen($input) . "<br>";
    echo "<strong>Input bytes:</strong> " . bin2hex($input) . "<br>";
    
    $input = str_replace(',', "\n", $input);
    echo "<strong>After comma replacement:</strong> '" . htmlspecialchars($input) . "'<br>";
    
    $terms = explode("\n", $input);
    echo "<strong>After explode:</strong> " . count($terms) . " terms<br>";
    
    $cleanTerms = [];
    foreach ($terms as $index => $term) {
        echo "<strong>Term #{$index}:</strong> '" . htmlspecialchars($term) . "' (length: " . strlen($term) . ")<br>";
        
        $term = trim($term);
        echo "<strong>After trim:</strong> '" . htmlspecialchars($term) . "' (length: " . strlen($term) . ")<br>";
        
        if (!empty($term) && strlen($term) >= 2) {
            $type = detectSearchType($term);
            echo "<strong>Detected type:</strong> $type<br>";
            
            $cleanTerms[] = [
                'term' => $term,
                'type' => $type
            ];
        }
    }
    
    echo "<strong>Clean terms:</strong> " . count($cleanTerms) . "<br>";
    foreach ($cleanTerms as $index => $termData) {
        echo "- Clean term #{$index}: '" . htmlspecialchars($termData['term']) . "' (type: {$termData['type']})<br>";
    }
    
    // Eliminăm duplicatele pe baza termenului
    $uniqueTerms = [];
    $seenTerms = [];

    foreach ($cleanTerms as $termData) {
        $termKey = strtolower($termData['term']);
        echo "<strong>Term key for deduplication:</strong> '" . htmlspecialchars($termKey) . "'<br>";
        
        if (!in_array($termKey, $seenTerms)) {
            $uniqueTerms[] = $termData;
            $seenTerms[] = $termKey;
            echo "<strong>Added to unique terms</strong><br>";
        } else {
            echo "<strong>Skipped as duplicate</strong><br>";
        }
    }
    
    echo "<strong>Final unique terms:</strong> " . count($uniqueTerms) . "<br>";
    foreach ($uniqueTerms as $index => $termData) {
        echo "- Final term #{$index}: '" . htmlspecialchars($termData['term']) . "' (type: {$termData['type']})<br>";
    }
    
    echo "</div>";
    
    return $uniqueTerms;
}

function detectSearchType($term) {
    $cleanTerm = trim($term, '"\'');
    
    echo "<div style='background: #e7f3ff; padding: 8px; margin: 3px 0; border: 1px solid #007bff;'>";
    echo "<strong>Detecting type for:</strong> '" . htmlspecialchars($term) . "'<br>";
    echo "<strong>Clean term:</strong> '" . htmlspecialchars($cleanTerm) . "'<br>";
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\*$/', $cleanTerm)) {
        echo "<strong>Matches pattern:</strong> ^\d+\/\d+(?:\/\d+)?\*$ → numarDosar<br>";
        echo "</div>";
        return 'numarDosar';
    }
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\/[a-zA-Z0-9]+$/', $cleanTerm)) {
        echo "<strong>Matches pattern:</strong> ^\d+\/\d+(?:\/\d+)?\/[a-zA-Z0-9]+$ → numarDosar<br>";
        echo "</div>";
        return 'numarDosar';
    }
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
        echo "<strong>Matches pattern:</strong> ^\d+\/\d+(?:\/\d+)?$ → numarDosar<br>";
        echo "</div>";
        return 'numarDosar';
    }
    
    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
        echo "<strong>Matches pattern:</strong> ^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$ → numarDosar<br>";
        echo "</div>";
        return 'numarDosar';
    }
    
    echo "<strong>No pattern matched → numeParte</strong><br>";
    echo "</div>";
    return 'numeParte';
}

foreach ($testInputs as $input) {
    echo "<h3>Testing input: '" . htmlspecialchars($input) . "'</h3>";
    $result = parseBulkSearchTerms($input);
    echo "<hr>";
}

echo "<h2>Step 2: Check POST Data Processing</h2>";

// Simulate POST request
$_POST['bulkSearchTerms'] = "14096/3/2024*";

echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border: 1px solid #ffc107;'>";
echo "<strong>POST data:</strong><br>";
echo "bulkSearchTerms = '" . htmlspecialchars($_POST['bulkSearchTerms'] ?? 'NOT SET') . "'<br>";

if (isset($_POST['bulkSearchTerms'])) {
    $bulkSearchTerms = $_POST['bulkSearchTerms'];
    echo "<strong>Raw POST value:</strong> '" . htmlspecialchars($bulkSearchTerms) . "'<br>";
    echo "<strong>Length:</strong> " . strlen($bulkSearchTerms) . "<br>";
    echo "<strong>Bytes:</strong> " . bin2hex($bulkSearchTerms) . "<br>";
    
    $trimmed = trim($bulkSearchTerms);
    echo "<strong>After trim:</strong> '" . htmlspecialchars($trimmed) . "'<br>";
    
    if (!empty($trimmed)) {
        echo "<strong>Processing with parseBulkSearchTerms...</strong><br>";
        $searchTermsData = parseBulkSearchTerms($trimmed);
        
        echo "<strong>Final result:</strong><br>";
        foreach ($searchTermsData as $termData) {
            echo "- Term: '" . htmlspecialchars($termData['term']) . "', Type: {$termData['type']}<br>";
        }
    }
}
echo "</div>";

echo "<h2>Step 3: Check Checkbox Visibility Logic</h2>";

// Check the logic for showing the exact match filter checkbox
echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
echo "<strong>Checkbox visibility logic:</strong><br>";

// Simulate the condition from index.php
$hasPartyNameSearches = false;
if (isset($searchTermsData)) {
    foreach ($searchTermsData as $termData) {
        if ($termData['type'] === 'numeParte') {
            $hasPartyNameSearches = true;
            break;
        }
    }
}

echo "<strong>Has party name searches:</strong> " . ($hasPartyNameSearches ? "YES" : "NO") . "<br>";
echo "<strong>Checkbox should be visible:</strong> " . ($hasPartyNameSearches ? "YES" : "NO") . "<br>";

if (!$hasPartyNameSearches) {
    echo "<strong style='color: red;'>⚠️ PROBLEM IDENTIFIED:</strong> Checkbox is hidden because all searches are detected as 'numarDosar'<br>";
    echo "This means the exact match filter is not available, which might affect result display.<br>";
}
echo "</div>";

echo "<h2>Step 4: Check Search Execution</h2>";

if (isset($searchTermsData) && !empty($searchTermsData)) {
    try {
        $dosarService = new DosarService();
        
        foreach ($searchTermsData as $termData) {
            $term = $termData['term'];
            $searchType = $termData['type'];
            
            echo "<h3>Executing search for: '" . htmlspecialchars($term) . "' (type: $searchType)</h3>";
            
            $searchParams = [
                'numarDosar' => ($searchType === 'numarDosar') ? $term : '',
                'institutie' => null,
                'numeParte' => ($searchType === 'numeParte') ? $term : '',
                'obiectDosar' => '',
                'dataStart' => '',
                'dataStop' => '',
                'dataUltimaModificareStart' => '',
                'dataUltimaModificareStop' => '',
                'categorieInstanta' => '',
                'categorieCaz' => ''
            ];
            
            echo "<div style='background: #f8f9fa; padding: 8px; margin: 3px 0; border: 1px solid #dee2e6;'>";
            echo "<strong>Search params:</strong><br>";
            echo "numarDosar: '" . htmlspecialchars($searchParams['numarDosar']) . "'<br>";
            echo "numeParte: '" . htmlspecialchars($searchParams['numeParte']) . "'<br>";
            echo "</div>";
            
            $results = $dosarService->cautareAvansata($searchParams);
            
            echo "<div style='background: #d4edda; padding: 8px; margin: 3px 0; border: 1px solid #c3e6cb;'>";
            echo "<strong>Results:</strong> " . count($results) . "<br>";
            
            foreach ($results as $index => $dosar) {
                $hasAsterisk = strpos($dosar->numar, '*') !== false;
                echo "- Result #" . ($index + 1) . ": " . htmlspecialchars($dosar->numar) . 
                     ($hasAsterisk ? " <strong>(HAS ASTERISK!)</strong>" : "") . "<br>";
            }
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border: 1px solid #f5c6cb;'>";
        echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
}

echo "<hr>";
echo "<h2>🎯 Summary of Issues Found</h2>";
echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border: 1px solid #ffc107;'>";
echo "<h3>Issues to investigate:</h3>";
echo "<ol>";
echo "<li><strong>Asterisk preservation:</strong> Check if asterisk is lost during input processing</li>";
echo "<li><strong>Checkbox visibility:</strong> Check why exact match filter checkbox is not visible</li>";
echo "<li><strong>Search term display:</strong> Check why the displayed term loses the asterisk</li>";
echo "<li><strong>Result counting:</strong> Check if the correct search term is used for result messages</li>";
echo "</ol>";
echo "</div>";
?>
