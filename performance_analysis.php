<?php
/**
 * Performance Analysis Tool
 * Detailed analysis of performance bottlenecks in case details page
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Performance Analysis - Romanian Judicial Portal</title>";
echo "<meta charset='UTF-8'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
.container { max-width: 1200px; margin: 0 auto; }
.section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.header { background: linear-gradient(135deg, #007bff, #0056b3); color: white; text-align: center; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
.metric-card { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; }
.performance-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
.performance-table th, .performance-table td { padding: 12px; border: 1px solid #ddd; text-align: left; }
.performance-table th { background-color: #007bff; color: white; font-weight: bold; }
.timing-info { background: #e3f2fd; border: 1px solid #2196f3; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px; white-space: pre-wrap; border-radius: 4px; }
.bottleneck { background: #fff3cd; border-left: 4px solid #ffc107; }
.optimization { background: #d4edda; border-left: 4px solid #28a745; }
.critical { background: #f8d7da; border-left: 4px solid #dc3545; }
.progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 5px 0; }
.progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
</style></head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🔍 Performance Analysis</h1>";
echo "<p>Detailed investigation of case details page performance bottlenecks</p>";
echo "<p><strong>Target Case:</strong> 130/98/2022 from CurteadeApelBUCURESTI</p>";
echo "</div>";

// Test case parameters
$testCase = [
    'number' => '130/98/2022',
    'institution' => 'CurteadeApelBUCURESTI',
    'description' => 'Appeal court case with 161 parties'
];

echo "<div class='section'>";
echo "<h2>📊 Performance Breakdown Analysis</h2>";

$performanceMetrics = [];
$totalStartTime = microtime(true);
$totalStartMemory = memory_get_usage(true);

try {
    // 1. SOAP API Call Performance
    echo "<div class='metric-card'>";
    echo "<h4>🌐 SOAP API Call Performance</h4>";
    
    $soapStartTime = microtime(true);
    $soapStartMemory = memory_get_usage(true);
    
    $dosarService = new DosarService();
    
    // Use reflection to test SOAP call directly
    $reflection = new ReflectionClass($dosarService);
    $soapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $soapMethod->setAccessible(true);
    
    $searchParams = [
        'numarDosar' => $testCase['number'],
        'institutie' => $testCase['institution'],
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $soapResponse = $soapMethod->invoke($dosarService, 'CautareDosare2', $searchParams, "Performance test");
    
    $soapEndTime = microtime(true);
    $soapEndMemory = memory_get_usage(true);
    
    $soapTime = ($soapEndTime - $soapStartTime) * 1000;
    $soapMemory = ($soapEndMemory - $soapStartMemory) / 1024 / 1024;
    
    $performanceMetrics['soap_api'] = [
        'time' => $soapTime,
        'memory' => $soapMemory,
        'status' => $soapTime < 1000 ? 'good' : ($soapTime < 2000 ? 'warning' : 'critical')
    ];
    
    echo "<div class='timing-info'>";
    echo "SOAP API Call Results:\n";
    echo "Time: " . round($soapTime, 2) . " ms\n";
    echo "Memory: " . round($soapMemory, 2) . " MB\n";
    echo "Status: " . ($soapTime < 1000 ? "✅ Good" : ($soapTime < 2000 ? "⚠️ Slow" : "❌ Critical")) . "\n";
    echo "</div>";
    echo "</div>";
    
    // 2. Data Processing Performance
    echo "<div class='metric-card'>";
    echo "<h4>⚙️ Data Processing Performance</h4>";
    
    $processingStartTime = microtime(true);
    $processingStartMemory = memory_get_usage(true);
    
    // Extract dosar from response
    $dosar = null;
    if (isset($soapResponse->CautareDosare2Result->Dosar)) {
        $dosare = $soapResponse->CautareDosare2Result->Dosar;
        $dosar = is_array($dosare) ? $dosare[0] : $dosare;
    }
    
    if ($dosar) {
        // Test mapDosarToObject method
        $mapMethod = $reflection->getMethod('mapDosarToObject');
        $mapMethod->setAccessible(true);
        
        $mappedDosar = $mapMethod->invoke($dosarService, $dosar);
        
        $processingEndTime = microtime(true);
        $processingEndMemory = memory_get_usage(true);
        
        $processingTime = ($processingEndTime - $processingStartTime) * 1000;
        $processingMemory = ($processingEndMemory - $processingStartMemory) / 1024 / 1024;
        
        $performanceMetrics['data_processing'] = [
            'time' => $processingTime,
            'memory' => $processingMemory,
            'parties_count' => count($mappedDosar->parti ?? []),
            'status' => $processingTime < 500 ? 'good' : ($processingTime < 1000 ? 'warning' : 'critical')
        ];
        
        echo "<div class='timing-info'>";
        echo "Data Processing Results:\n";
        echo "Time: " . round($processingTime, 2) . " ms\n";
        echo "Memory: " . round($processingMemory, 2) . " MB\n";
        echo "Parties processed: " . count($mappedDosar->parti ?? []) . "\n";
        echo "Status: " . ($processingTime < 500 ? "✅ Good" : ($processingTime < 1000 ? "⚠️ Slow" : "❌ Critical")) . "\n";
        echo "</div>";
        
        // 3. Decision Text Parsing Performance
        echo "</div>";
        echo "<div class='metric-card'>";
        echo "<h4>📄 Decision Text Parsing Performance</h4>";
        
        $parsingStartTime = microtime(true);
        $parsingStartMemory = memory_get_usage(true);
        
        // Test decision text extraction
        $extractMethod = $reflection->getMethod('extractPartiesFromDecisionText');
        $extractMethod->setAccessible(true);
        
        $decisionParties = $extractMethod->invoke($dosarService, $dosar);
        
        $parsingEndTime = microtime(true);
        $parsingEndMemory = memory_get_usage(true);
        
        $parsingTime = ($parsingEndTime - $parsingStartTime) * 1000;
        $parsingMemory = ($parsingEndMemory - $parsingStartMemory) / 1024 / 1024;
        
        $performanceMetrics['text_parsing'] = [
            'time' => $parsingTime,
            'memory' => $parsingMemory,
            'parties_extracted' => count($decisionParties),
            'status' => $parsingTime < 200 ? 'good' : ($parsingTime < 500 ? 'warning' : 'critical')
        ];
        
        echo "<div class='timing-info'>";
        echo "Decision Text Parsing Results:\n";
        echo "Time: " . round($parsingTime, 2) . " ms\n";
        echo "Memory: " . round($parsingMemory, 2) . " MB\n";
        echo "Parties extracted: " . count($decisionParties) . "\n";
        echo "Status: " . ($parsingTime < 200 ? "✅ Good" : ($parsingTime < 500 ? "⚠️ Slow" : "❌ Critical")) . "\n";
        echo "</div>";
        
        // 4. Party Merging Performance
        echo "</div>";
        echo "<div class='metric-card'>";
        echo "<h4>🔄 Party Merging & Deduplication Performance</h4>";
        
        $mergingStartTime = microtime(true);
        $mergingStartMemory = memory_get_usage(true);
        
        // Extract SOAP parties first
        $soapParties = [];
        if (isset($dosar->parti) && isset($dosar->parti->DosarParte)) {
            $parti = $dosar->parti->DosarParte;
            if (!is_array($parti)) {
                $parti = [$parti];
            }
            
            foreach ($parti as $parte) {
                $soapParties[] = [
                    'nume' => $parte->nume ?? '',
                    'calitate' => $parte->calitateParte ?? '',
                    'source' => 'soap_api'
                ];
            }
        }
        
        // Test merging
        $mergeMethod = $reflection->getMethod('mergeAndDeduplicateParties');
        $mergeMethod->setAccessible(true);
        
        $mergedParties = $mergeMethod->invoke($dosarService, $soapParties, $decisionParties);
        
        $mergingEndTime = microtime(true);
        $mergingEndMemory = memory_get_usage(true);
        
        $mergingTime = ($mergingEndTime - $mergingStartTime) * 1000;
        $mergingMemory = ($mergingEndMemory - $mergingStartMemory) / 1024 / 1024;
        
        $performanceMetrics['party_merging'] = [
            'time' => $mergingTime,
            'memory' => $mergingMemory,
            'soap_parties' => count($soapParties),
            'decision_parties' => count($decisionParties),
            'final_parties' => count($mergedParties),
            'status' => $mergingTime < 100 ? 'good' : ($mergingTime < 300 ? 'warning' : 'critical')
        ];
        
        echo "<div class='timing-info'>";
        echo "Party Merging Results:\n";
        echo "Time: " . round($mergingTime, 2) . " ms\n";
        echo "Memory: " . round($mergingMemory, 2) . " MB\n";
        echo "SOAP parties: " . count($soapParties) . "\n";
        echo "Decision parties: " . count($decisionParties) . "\n";
        echo "Final merged parties: " . count($mergedParties) . "\n";
        echo "Status: " . ($mergingTime < 100 ? "✅ Good" : ($mergingTime < 300 ? "⚠️ Slow" : "❌ Critical")) . "\n";
        echo "</div>";
        echo "</div>";
        
    } else {
        echo "<div class='critical'>";
        echo "<h4>❌ No dosar data found in SOAP response</h4>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='critical'>";
    echo "<h4>❌ Error during performance analysis</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

$totalEndTime = microtime(true);
$totalEndMemory = memory_get_usage(true);

$totalTime = ($totalEndTime - $totalStartTime) * 1000;
$totalMemory = ($totalEndMemory - $totalStartMemory) / 1024 / 1024;

echo "</div>";

// Performance Summary
echo "<div class='section'>";
echo "<h2>📈 Performance Summary</h2>";

echo "<table class='performance-table'>";
echo "<tr><th>Component</th><th>Time (ms)</th><th>Memory (MB)</th><th>Status</th><th>Optimization Priority</th></tr>";

$components = [
    'SOAP API Call' => $performanceMetrics['soap_api'] ?? null,
    'Data Processing' => $performanceMetrics['data_processing'] ?? null,
    'Text Parsing' => $performanceMetrics['text_parsing'] ?? null,
    'Party Merging' => $performanceMetrics['party_merging'] ?? null
];

foreach ($components as $name => $metrics) {
    if ($metrics) {
        $statusIcon = $metrics['status'] === 'good' ? '✅' : ($metrics['status'] === 'warning' ? '⚠️' : '❌');
        $priority = $metrics['status'] === 'critical' ? 'HIGH' : ($metrics['status'] === 'warning' ? 'MEDIUM' : 'LOW');
        $priorityClass = $metrics['status'] === 'critical' ? 'critical' : ($metrics['status'] === 'warning' ? 'bottleneck' : 'optimization');
        
        echo "<tr>";
        echo "<td><strong>$name</strong></td>";
        echo "<td>" . round($metrics['time'], 2) . "</td>";
        echo "<td>" . round($metrics['memory'], 2) . "</td>";
        echo "<td>$statusIcon " . ucfirst($metrics['status']) . "</td>";
        echo "<td><span class='$priorityClass'>$priority</span></td>";
        echo "</tr>";
    }
}

echo "<tr style='background-color: #f8f9fa; font-weight: bold;'>";
echo "<td><strong>TOTAL</strong></td>";
echo "<td><strong>" . round($totalTime, 2) . "</strong></td>";
echo "<td><strong>" . round($totalMemory, 2) . "</strong></td>";
echo "<td><strong>" . ($totalTime < 2000 ? "✅ Good" : ($totalTime < 3000 ? "⚠️ Slow" : "❌ Critical")) . "</strong></td>";
echo "<td><strong>" . ($totalTime > 3000 ? "HIGH" : ($totalTime > 2000 ? "MEDIUM" : "LOW")) . "</strong></td>";
echo "</tr>";
echo "</table>";

echo "</div>";

echo "</div>";
echo "</body></html>";
?>
