<?php
/**
 * Comprehensive Party Investigation
 * Complete analysis of the party display issue
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Test case parameters
$numarDosar = '130/98/2022';
$institutie = 'CurteadeApelBUCURESTI';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Comprehensive Party Investigation</title>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
.warning { background: #fff3cd; border-left-color: #ffc107; }
.error { background: #f8d7da; border-left-color: #dc3545; }
.success { background: #d4edda; border-left-color: #28a745; }
.debug-info { background: #e3f2fd; border: 1px solid #2196f3; padding: 10px; margin: 10px 0; font-family: monospace; }
.test-link { display: inline-block; margin: 5px; padding: 8px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; font-size: 14px; }
.test-link:hover { background: #0056b3; color: white; text-decoration: none; }
.comparison-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
.comparison-table th, .comparison-table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
.comparison-table th { background-color: #f8f9fa; }
.status-ok { color: #28a745; font-weight: bold; }
.status-error { color: #dc3545; font-weight: bold; }
.status-warning { color: #ffc107; font-weight: bold; }
</style></head><body>";

echo "<h1>🔍 Comprehensive Party Investigation</h1>";
echo "<p><strong>Case:</strong> {$numarDosar} from {$institutie}</p>";
echo "<p><strong>Objective:</strong> Complete analysis of the 100-party limitation issue</p>";
echo "<hr>";

// Step 1: Backend Analysis
echo "<div class='section'>";
echo "<h2>📊 Step 1: Backend Analysis</h2>";

try {
    $dosarService = new DosarService();
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if (!$dosar) {
        echo "<div class='error'>❌ Failed to retrieve case data</div>";
        exit;
    }
    
    $totalParties = count($dosar->parti ?? []);
    
    echo "<table class='comparison-table'>";
    echo "<tr><th>Metric</th><th>Value</th><th>Status</th></tr>";
    echo "<tr><td>Case Number</td><td>" . ($dosar->numar ?? 'N/A') . "</td><td>✅</td></tr>";
    echo "<tr><td>Institution</td><td>" . ($dosar->institutie ?? 'N/A') . "</td><td>✅</td></tr>";
    echo "<tr><td>Total Parties</td><td>{$totalParties}</td>";
    
    if ($totalParties >= 340) {
        echo "<td class='status-ok'>✅ Expected (340+)</td>";
    } elseif ($totalParties == 100) {
        echo "<td class='status-error'>❌ SOAP Limit (100)</td>";
    } else {
        echo "<td class='status-warning'>⚠️ Unexpected ({$totalParties})</td>";
    }
    echo "</tr>";
    
    // Source analysis
    $soapCount = 0;
    $decisionCount = 0;
    $unknownCount = 0;
    
    if (!empty($dosar->parti)) {
        foreach ($dosar->parti as $parte) {
            $source = $parte['source'] ?? 'unknown';
            switch ($source) {
                case 'soap_api': $soapCount++; break;
                case 'decision_text': $decisionCount++; break;
                default: $unknownCount++; break;
            }
        }
    }
    
    echo "<tr><td>SOAP API Parties</td><td>{$soapCount}</td>";
    echo "<td>" . ($soapCount == 100 ? "✅ Expected" : "⚠️ Unexpected") . "</td></tr>";
    
    echo "<tr><td>Decision Text Parties</td><td>{$decisionCount}</td>";
    echo "<td>" . ($decisionCount > 0 ? "✅ Hybrid Working" : "❌ No Extraction") . "</td></tr>";
    
    echo "<tr><td>Unknown Source</td><td>{$unknownCount}</td>";
    echo "<td>" . ($unknownCount == 0 ? "✅ All Attributed" : "⚠️ Missing Attribution") . "</td></tr>";
    
    echo "</table>";
    
    $backendStatus = ($totalParties > 100) ? "SUCCESS" : "FAILED";
    echo "<div class='" . ($backendStatus == "SUCCESS" ? "success" : "error") . "'>";
    echo "<h4>" . ($backendStatus == "SUCCESS" ? "✅" : "❌") . " Backend Status: {$backendStatus}</h4>";
    echo "<p>Backend " . ($backendStatus == "SUCCESS" ? "correctly extracts {$totalParties} parties" : "only extracts {$totalParties} parties") . "</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h4>❌ Backend Error</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    exit;
}
echo "</div>";

// Step 2: Test Links
echo "<div class='section'>";
echo "<h2>🔗 Step 2: Test Links</h2>";
echo "<p>Test different versions of the case details page:</p>";

$baseUrl = "detalii_dosar.php?numar=" . urlencode($numarDosar) . "&institutie=" . urlencode($institutie);

echo "<a href='{$baseUrl}' class='test-link' target='_blank'>";
echo "<i class='fas fa-file-alt'></i> Standard Page";
echo "</a>";

echo "<a href='{$baseUrl}&debug=1' class='test-link' target='_blank'>";
echo "<i class='fas fa-bug'></i> Debug Page";
echo "</a>";

echo "<a href='test_specific_case_issue.php' class='test-link' target='_blank'>";
echo "<i class='fas fa-search'></i> Investigation Tool";
echo "</a>";

echo "<a href='debug_case_details_data_flow.php' class='test-link' target='_blank'>";
echo "<i class='fas fa-code'></i> Data Flow Test";
echo "</a>";

echo "</div>";

// Step 3: Frontend Simulation
if (isset($dosar) && $dosar && !empty($dosar->parti)) {
    echo "<div class='section'>";
    echo "<h2>🎨 Step 3: Frontend Simulation</h2>";
    echo "<p>Simulating the exact rendering logic from detalii_dosar.php:</p>";
    
    // Initialize variables exactly like detalii_dosar.php
    $loop_index = 0;
    $totalPartiCount = count($dosar->parti);
    
    echo "<div class='debug-info'>";
    echo "Simulation Variables:\n";
    echo "\$loop_index = {$loop_index}\n";
    echo "\$totalPartiCount = {$totalPartiCount}\n";
    echo "foreach (\$dosar->parti as \$parteIndex => \$parte) will iterate {$totalPartiCount} times\n";
    echo "</div>";
    
    // Render a sample of parties (first 10 and last 10)
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped' id='simulationTable'>";
    echo "<thead>";
    echo "<tr>";
    echo "<th>Index</th>";
    echo "<th>Nume</th>";
    echo "<th>Calitate</th>";
    echo "<th>Source</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    $renderedCount = 0;
    $showFirst = 10;
    $showLast = 10;
    
    foreach ($dosar->parti as $parteIndex => $parte) {
        $loop_index++;
        $renderedCount++;
        
        // Show first 10 and last 10 parties
        if ($loop_index <= $showFirst || $loop_index > ($totalPartiCount - $showLast)) {
            echo "<tr class='parte-row'>";
            echo "<td><span class='badge bg-primary'>{$loop_index}</span></td>";
            echo "<td class='nume-parte'>" . htmlspecialchars($parte['nume'] ?? 'N/A') . "</td>";
            echo "<td class='calitate-parte'>" . htmlspecialchars($parte['calitate'] ?? '-') . "</td>";
            echo "<td><span class='badge bg-info'>" . htmlspecialchars($parte['source'] ?? 'unknown') . "</span></td>";
            echo "</tr>";
        } elseif ($loop_index == $showFirst + 1) {
            echo "<tr><td colspan='4' class='text-center bg-light'>";
            echo "<em>... " . ($totalPartiCount - $showFirst - $showLast) . " parties omitted for display ...</em>";
            echo "</td></tr>";
        }
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    echo "<div class='debug-info'>";
    echo "Simulation Results:\n";
    echo "Expected parties: {$totalPartiCount}\n";
    echo "Processed parties: {$renderedCount}\n";
    echo "Loop completed: " . ($renderedCount == $totalPartiCount ? "✅ YES" : "❌ NO") . "\n";
    echo "Final loop_index: {$loop_index}\n";
    echo "</div>";
    
    $simulationStatus = ($renderedCount == $totalPartiCount && $renderedCount > 100) ? "SUCCESS" : "ISSUE";
    echo "<div class='" . ($simulationStatus == "SUCCESS" ? "success" : "error") . "'>";
    echo "<h4>" . ($simulationStatus == "SUCCESS" ? "✅" : "❌") . " Simulation Status: {$simulationStatus}</h4>";
    echo "<p>Frontend simulation " . ($simulationStatus == "SUCCESS" ? "successfully processes all {$renderedCount} parties" : "shows issues with party processing") . "</p>";
    echo "</div>";
    
    echo "</div>";
}

// Step 4: Diagnosis and Next Steps
echo "<div class='section'>";
echo "<h2>🔬 Step 4: Diagnosis and Next Steps</h2>";

echo "<h4>Current Status Summary:</h4>";
echo "<table class='comparison-table'>";
echo "<tr><th>Component</th><th>Expected</th><th>Actual</th><th>Status</th></tr>";
echo "<tr><td>Backend Extraction</td><td>340+ parties</td><td>{$totalParties} parties</td>";
echo "<td>" . ($totalParties > 100 ? "<span class='status-ok'>✅ Working</span>" : "<span class='status-error'>❌ Failed</span>") . "</td></tr>";
echo "<tr><td>Frontend Simulation</td><td>340+ parties</td><td>" . (isset($renderedCount) ? $renderedCount : 0) . " parties</td>";
echo "<td>" . (isset($renderedCount) && $renderedCount > 100 ? "<span class='status-ok'>✅ Working</span>" : "<span class='status-error'>❌ Failed</span>") . "</td></tr>";
echo "<tr><td>Actual Page Display</td><td>340+ parties</td><td>??? parties</td>";
echo "<td><span class='status-warning'>🔍 Test Required</span></td></tr>";
echo "</table>";

echo "<h4>Next Steps:</h4>";
echo "<ol>";
echo "<li><strong>Test the actual page:</strong> Click the 'Standard Page' link above and count the parties displayed</li>";
echo "<li><strong>Check debug output:</strong> Click the 'Debug Page' link and view the HTML source for debug comments</li>";
echo "<li><strong>Run JavaScript verification:</strong> Use browser console to count DOM elements</li>";
echo "<li><strong>Compare results:</strong> Identify where the discrepancy occurs</li>";
echo "</ol>";

if (isset($totalParties) && $totalParties > 100) {
    echo "<div class='success'>";
    echo "<h4>✅ Good News!</h4>";
    echo "<p>The backend is correctly extracting {$totalParties} parties using the hybrid system. If the actual page shows only 100 parties, the issue is in the frontend rendering or data transfer.</p>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h4>❌ Backend Issue Detected!</h4>";
    echo "<p>The backend is only extracting {$totalParties} parties. The hybrid extraction system is not working correctly.</p>";
    echo "</div>";
}

echo "</div>";

// Step 5: JavaScript verification tools
echo "<div class='section'>";
echo "<h2>🔧 Step 5: JavaScript Verification</h2>";
echo "<p>Copy and paste this JavaScript code in the browser console when viewing the actual case details page:</p>";

echo "<div style='background: #f1f3f4; padding: 10px; font-family: monospace; white-space: pre-wrap; border: 1px solid #ddd;'>";
echo "// Complete party verification for case {$numarDosar}
console.log('🔍 Complete Party Verification');
console.log('Expected backend parties: {$totalParties}');

// Count DOM elements
const table = document.getElementById('tabelParti');
const tbody = table ? table.querySelector('tbody') : null;
const partyRows = tbody ? tbody.querySelectorAll('tr.parte-row') : [];

console.log('DOM Analysis:', {
    tableFound: !!table,
    tbodyFound: !!tbody,
    totalPartyRows: partyRows.length
});

// Check party counter badge
const partiCounter = document.querySelector('.parti-counter');
const counterText = partiCounter ? partiCounter.textContent : 'Not found';
console.log('Party Counter Badge:', counterText);

// Check for hidden rows
const hiddenRows = Array.from(partyRows).filter(row => {
    const style = window.getComputedStyle(row);
    return style.display === 'none' || style.visibility === 'hidden';
});

console.log('Visibility Check:', {
    totalRows: partyRows.length,
    visibleRows: partyRows.length - hiddenRows.length,
    hiddenRows: hiddenRows.length
});

// Final comparison
const expectedParties = {$totalParties};
const actualParties = partyRows.length;

console.log('FINAL COMPARISON:', {
    expectedFromBackend: expectedParties,
    actualInDOM: actualParties,
    difference: expectedParties - actualParties,
    status: actualParties >= expectedParties ? 'SUCCESS' : 'ISSUE CONFIRMED'
});

if (actualParties < expectedParties) {
    console.error('❌ ISSUE CONFIRMED: Frontend shows ' + actualParties + ' parties but backend has ' + expectedParties);
    console.log('🔍 This confirms there is a disconnect between backend and frontend');
} else {
    console.log('✅ SUCCESS: Frontend correctly displays all parties');
}";
echo "</div>";
echo "</div>";

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    console.log('🔍 Comprehensive Party Investigation - Ready');";
echo "    console.log('Backend extracted: {$totalParties} parties');";
echo "    console.log('Simulation processed: ' + " . (isset($renderedCount) ? $renderedCount : 0) . " + ' parties');";
echo "    console.log('Next: Test the actual case details page');";
echo "});";
echo "</script>";

echo "</body></html>";
?>
