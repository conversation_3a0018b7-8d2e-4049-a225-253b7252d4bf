<?php
require_once 'config/config.php';

echo "🔍 TESTING WEB SEARCH INTERFACE\n";
echo "================================\n\n";

// Test the search.php interface directly
$testSearches = [
    [
        'searchType' => 'numeParte',
        'numeParte' => 'BURDUŞELU',
        'description' => 'Party search for BURDUŞELU'
    ],
    [
        'searchType' => 'numeParte',
        'numeParte' => 'TUDORIŢA', 
        'description' => 'Party search for TUDORIŢA'
    ]
];

foreach ($testSearches as $i => $test) {
    echo "🔍 WEB SEARCH TEST " . ($i + 1) . ": {$test['description']}\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    // Simulate GET request parameters
    $_GET = $test;
    $_POST = [];
    $_REQUEST = $test;
    
    echo "Simulated request parameters:\n";
    foreach ($test as $key => $value) {
        echo "- {$key}: '{$value}'\n";
    }
    echo "\n";
    
    // Capture output from search.php
    ob_start();
    
    try {
        // Include search.php to test the actual search logic
        include 'search.php';
        $output = ob_get_contents();
    } catch (Exception $e) {
        $output = "Error: " . $e->getMessage();
    } catch (Error $e) {
        $output = "Fatal Error: " . $e->getMessage();
    }
    
    ob_end_clean();
    
    // Analyze the output
    echo "🔍 ANALYZING SEARCH OUTPUT:\n";
    echo "---------------------------\n";
    
    $outputLength = strlen($output);
    echo "Output length: {$outputLength} characters\n";
    
    // Check for key indicators in the output
    $indicators = [
        'Rezultate căutare' => 'Search results section',
        'table' => 'Results table',
        'result-card' => 'Mobile card view',
        'No results' => 'No results message',
        'error' => 'Error message',
        'parti' => 'Party data',
        'BURDUŞELU' => 'Search term in results',
        'TUDORIŢA' => 'Search term in results'
    ];
    
    foreach ($indicators as $pattern => $description) {
        $count = substr_count(strtolower($output), strtolower($pattern));
        $status = $count > 0 ? '✅' : '❌';
        echo "{$status} {$description}: {$count} occurrences\n";
    }
    
    // Extract result count if possible
    if (preg_match('/Rezultate căutare \((\d+)\)/', $output, $matches)) {
        echo "✅ Results found: {$matches[1]}\n";
    } elseif (preg_match('/(\d+)\s+rezultate/', $output, $matches)) {
        echo "✅ Results found: {$matches[1]}\n";
    } else {
        echo "⚠️ Could not extract result count\n";
    }
    
    // Check for party display issues
    if (preg_match_all('/\+(\d+)\s+alții/', $output, $matches)) {
        echo "✅ Party count indicators found:\n";
        foreach ($matches[1] as $count) {
            echo "  - +{$count} others\n";
        }
    } else {
        echo "⚠️ No party count indicators found\n";
    }
    
    // Save output for manual inspection
    $filename = "search_output_test_" . ($i + 1) . ".html";
    file_put_contents($filename, $output);
    echo "📄 Full output saved to: {$filename}\n";
    
    echo "\n" . str_repeat("=", 70) . "\n\n";
}

// Reset globals
$_GET = [];
$_POST = [];
$_REQUEST = [];

echo "✅ Web search interface testing complete\n";
