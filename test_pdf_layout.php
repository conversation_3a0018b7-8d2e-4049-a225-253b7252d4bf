<?php
/**
 * Test page for PDF layout verification
 * Tests the improved PDF export functionality with various content lengths
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';

// Set content type to HTML with UTF-8 encoding
header('Content-Type: text/html; charset=UTF-8');

// Mock session data with various content lengths to test layout
$mockSessions = [
    (object)[
        'data' => '24.06.2024',
        'ora' => '09:00',
        'departament' => 'Secția Civilă',
        'complet' => 'Judecător: <PERSON><PERSON><PERSON> Ion',
        'dosare' => [
            (object)['numar' => '1234/2024', 'institutie' => 'TribunalulBUCURESTI'],
            (object)['numar' => '5678/2024', 'institutie' => 'TribunalulBUCURESTI']
        ]
    ],
    (object)[
        'data' => '24.06.2024',
        'ora' => '10:30',
        'departament' => 'Secția Penală și pentru Minori',
        'complet' => 'Judecător: <PERSON><PERSON>, Grefier: <PERSON><PERSON><PERSON><PERSON>',
        'dosare' => [
            (object)['numar' => '9999/2024', 'institutie' => 'TribunalulBUCURESTI'],
            (object)['numar' => '8888/2024', 'institutie' => 'JudecatoriaBUCURESTI'],
            (object)['numar' => '7777/2024', 'institutie' => 'TribunalulBUCURESTI'],
            (object)['numar' => '6666/2024', 'institutie' => 'JudecatoriaBUCURESTI'],
            (object)['numar' => '5555/2024', 'institutie' => 'TribunalulBUCURESTI']
        ]
    ],
    (object)[
        'data' => '24.06.2024',
        'ora' => '14:00',
        'departament' => 'Secția Comercială și de Contencios Administrativ și Fiscal',
        'complet' => 'Judecător: Georgescu Andrei Constantin, Judecător: Marinescu Elena Cristina, Grefier: Dumitrescu Ioana Alexandra',
        'dosare' => [
            (object)['numar' => '1111/2024', 'institutie' => 'TribunalulBUCURESTI'],
            (object)['numar' => '2222/2024', 'institutie' => 'JudecatoriaBUCURESTI'],
            (object)['numar' => '3333/2024', 'institutie' => 'TribunalulBUCURESTI'],
            (object)['numar' => '4444/2024', 'institutie' => 'JudecatoriaBUCURESTI'],
            (object)['numar' => '1010/2024', 'institutie' => 'TribunalulBUCURESTI'],
            (object)['numar' => '2020/2024', 'institutie' => 'JudecatoriaBUCURESTI'],
            (object)['numar' => '3030/2024', 'institutie' => 'TribunalulBUCURESTI'],
            (object)['numar' => '4040/2024', 'institutie' => 'JudecatoriaBUCURESTI']
        ]
    ]
];

// Test PDF generation if requested
if (isset($_GET['test_pdf'])) {
    try {
        // Include the PDF generation functions from sedinte.php
        require_once 'sedinte.php';

        // Generate test PDF
        $filename = 'Test_PDF_Layout_' . date('Y-m-d_H-i-s') . '.pdf';

        // Clear any output buffers
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Test the PDF generation with our mock data
        generateSessionPdfFile($mockSessions, $filename, 'TribunalulBUCURESTI', '24.06.2024');
        exit;

    } catch (Exception $e) {
        $error = 'Eroare la generarea PDF: ' . $e->getMessage();
        error_log("PDF Generation Error: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
    }
}
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PDF Layout - Portal Judiciar</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .test-card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .test-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .test-body {
            padding: 2rem;
        }
        .session-preview {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #007bff;
        }
        .dosare-list {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .btn-test {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            color: white;
        }
        .error-alert {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <div class="test-header">
                <h1 class="h3 mb-0">
                    <i class="fas fa-file-pdf me-2"></i>
                    Test PDF Layout - Verificare Îmbunătățiri
                </h1>
                <p class="mb-0 mt-2 opacity-75">
                    Testare funcționalitate PDF cu date mock pentru verificarea layout-ului
                </p>
            </div>
            <div class="test-body">
                <?php if (isset($error)): ?>
                    <div class="error-alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-8">
                        <h4><i class="fas fa-list me-2"></i>Date Mock pentru Test</h4>
                        <p class="text-muted">Următoarele date vor fi folosite pentru testarea PDF-ului:</p>
                        
                        <?php foreach ($mockSessions as $index => $session): ?>
                            <div class="session-preview">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Ședința <?php echo $index + 1; ?></strong><br>
                                        <small class="text-muted"><?php echo $session->data; ?> - <?php echo $session->ora; ?></small>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Departament:</strong><br>
                                        <small><?php echo htmlspecialchars($session->departament); ?></small>
                                    </div>
                                    <div class="col-md-5">
                                        <strong>Complet:</strong><br>
                                        <small><?php echo htmlspecialchars($session->complet); ?></small>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <strong>Dosare programate (<?php echo count($session->dosare); ?>):</strong><br>
                                        <div class="dosare-list">
                                            <?php 
                                            $dosareText = [];
                                            foreach ($session->dosare as $dosar) {
                                                $dosareText[] = $dosar->numar . ' (' . $dosar->institutie . ')';
                                            }
                                            echo htmlspecialchars(implode('; ', $dosareText));
                                            ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="col-md-4">
                        <h4><i class="fas fa-cog me-2"></i>Acțiuni Test</h4>
                        <div class="d-grid gap-3">
                            <a href="?test_pdf=1" class="btn btn-test">
                                <i class="fas fa-file-pdf me-2"></i>
                                Generează PDF Test
                            </a>
                            <a href="sedinte.php" class="btn btn-outline-primary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Înapoi la Ședințe
                            </a>
                        </div>
                        
                        <div class="mt-4">
                            <h5><i class="fas fa-info-circle me-2"></i>Îmbunătățiri implementate:</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Lățimi coloane optimizate [12, 22, 30, 40, 66]mm</li>
                                <li><i class="fas fa-check text-success me-2"></i>Calculare înălțime pentru toate coloanele</li>
                                <li><i class="fas fa-check text-success me-2"></i>Alinierea corectă: centrat pentru Nr/Data, stânga pentru rest</li>
                                <li><i class="fas fa-check text-success me-2"></i>Centrare verticală pentru text scurt</li>
                                <li><i class="fas fa-check text-success me-2"></i>Separare contur + conținut pentru poziționare precisă</li>
                                <li><i class="fas fa-check text-success me-2"></i>Diacritice românești cu font dejavusans</li>
                                <li><i class="fas fa-check text-success me-2"></i>Page breaks optimizate</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
