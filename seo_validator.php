<?php
/**
 * SEO Validator - Testează și validează implementarea SEO
 */

require_once 'bootstrap.php';

use App\Helpers\SEOHelper;
use App\Helpers\BreadcrumbHelper;

// Setăm header-ul pentru HTML
header('Content-Type: text/html; charset=utf-8');

// Lista paginilor de testat
$pages = [
    'index' => [
        'name' => 'Pagina Principală',
        'url' => 'index.php',
        'params' => []
    ],
    'contact' => [
        'name' => 'Contact',
        'url' => 'contact.php',
        'params' => []
    ],
    'sedinte' => [
        'name' => 'Ședințe',
        'url' => 'sedinte.php',
        'params' => []
    ],
    'search' => [
        'name' => 'Căutare',
        'url' => 'search.php',
        'params' => ['search_term' => 'test']
    ],
    'detalii_dosar' => [
        'name' => '<PERSON><PERSON><PERSON>',
        'url' => 'detalii_dosar.php',
        'params' => ['numar_dosar' => '123/2024']
    ]
];

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Validator - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .seo-score {
            font-size: 2rem;
            font-weight: bold;
        }
        .score-excellent { color: #28a745; }
        .score-good { color: #ffc107; }
        .score-poor { color: #dc3545; }
        .json-viewer {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-result {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 0.375rem;
        }
        .test-pass {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-fail {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-search me-2"></i>
                    SEO Validator - Portal Judiciar
                </h1>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Acest tool validează implementarea SEO pentru toate paginile Portal Judiciar.
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Scor SEO General
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <?php
                        $totalScore = 0;
                        $maxScore = 0;
                        
                        foreach ($pages as $pageKey => $pageInfo) {
                            $score = validatePageSEO($pageKey, $pageInfo['params']);
                            $totalScore += $score['score'];
                            $maxScore += $score['max_score'];
                        }
                        
                        $overallScore = $maxScore > 0 ? round(($totalScore / $maxScore) * 100) : 0;
                        $scoreClass = $overallScore >= 80 ? 'score-excellent' : ($overallScore >= 60 ? 'score-good' : 'score-poor');
                        ?>
                        <div class="seo-score <?php echo $scoreClass; ?>">
                            <?php echo $overallScore; ?>%
                        </div>
                        <p class="mb-0">
                            <?php echo $totalScore; ?>/<?php echo $maxScore; ?> puncte
                        </p>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">Acțiuni Rapide</h6>
                    </div>
                    <div class="card-body">
                        <a href="sitemap.xml.php" class="btn btn-outline-primary btn-sm d-block mb-2" target="_blank">
                            <i class="fas fa-sitemap me-1"></i>
                            Vezi Sitemap
                        </a>
                        <a href="robots.txt" class="btn btn-outline-primary btn-sm d-block mb-2" target="_blank">
                            <i class="fas fa-robot me-1"></i>
                            Vezi Robots.txt
                        </a>
                        <button class="btn btn-outline-success btn-sm d-block" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>
                            Printează Raport
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-9">
                <?php foreach ($pages as $pageKey => $pageInfo): ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-file-alt me-2"></i>
                                <?php echo htmlspecialchars($pageInfo['name']); ?>
                                <small class="text-muted">(<?php echo htmlspecialchars($pageInfo['url']); ?>)</small>
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $validation = validatePageSEO($pageKey, $pageInfo['params']);
                            displayValidationResults($validation);
                            ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php

/**
 * Validează SEO pentru o pagină specifică
 */
function validatePageSEO($page, $params = [])
{
    $results = [
        'score' => 0,
        'max_score' => 0,
        'tests' => []
    ];
    
    // Test 1: Meta Tags
    $metaTags = SEOHelper::generateMetaTags($page, $params);
    $results['tests'][] = validateMetaTags($metaTags);
    
    // Test 2: Structured Data
    $structuredData = json_decode(SEOHelper::generateOrganizationSchema(), true);
    $results['tests'][] = validateStructuredData($structuredData, 'GovernmentOrganization');

    $websiteSchema = json_decode(SEOHelper::generateWebSiteSchema(), true);
    $results['tests'][] = validateStructuredData($websiteSchema, 'WebSite');
    
    // Test 3: Breadcrumbs
    $breadcrumbs = BreadcrumbHelper::generateStructuredDataBreadcrumbs($page, $params);
    $results['tests'][] = validateBreadcrumbs($breadcrumbs);
    
    // Calculăm scorul total
    foreach ($results['tests'] as $test) {
        $results['score'] += $test['score'];
        $results['max_score'] += $test['max_score'];
    }
    
    return $results;
}

/**
 * Validează meta tag-urile
 */
function validateMetaTags($metaTags)
{
    $test = [
        'name' => 'Meta Tags',
        'score' => 0,
        'max_score' => 10,
        'status' => 'fail',
        'message' => '',
        'details' => []
    ];
    
    // Verifică title
    if (isset($metaTags['title']) && !empty($metaTags['title'])) {
        $titleLength = strlen($metaTags['title']);
        if ($titleLength >= 50 && $titleLength <= 60) {
            $test['score'] += 3;
            $test['details'][] = "✓ Title optim ($titleLength caractere)";
        } else {
            $test['details'][] = "⚠ Title suboptim ($titleLength caractere, recomandat: 50-60)";
        }
    } else {
        $test['details'][] = "✗ Title lipsește";
    }
    
    // Verifică description
    if (isset($metaTags['description']) && !empty($metaTags['description'])) {
        $descLength = strlen($metaTags['description']);
        if ($descLength >= 150 && $descLength <= 160) {
            $test['score'] += 3;
            $test['details'][] = "✓ Description optimă ($descLength caractere)";
        } else {
            $test['details'][] = "⚠ Description suboptimă ($descLength caractere, recomandat: 150-160)";
        }
    } else {
        $test['details'][] = "✗ Description lipsește";
    }
    
    // Verifică keywords
    if (isset($metaTags['keywords']) && !empty($metaTags['keywords'])) {
        $test['score'] += 2;
        $test['details'][] = "✓ Keywords prezente";
    } else {
        $test['details'][] = "⚠ Keywords lipsesc";
    }
    
    // Verifică Open Graph
    if (isset($metaTags['og:title']) && isset($metaTags['og:description'])) {
        $test['score'] += 2;
        $test['details'][] = "✓ Open Graph tags prezente";
    } else {
        $test['details'][] = "⚠ Open Graph tags incomplete";
    }
    
    $test['status'] = $test['score'] >= 7 ? 'pass' : ($test['score'] >= 4 ? 'warning' : 'fail');
    $test['message'] = "Scor: {$test['score']}/{$test['max_score']}";
    
    return $test;
}

/**
 * Validează structured data
 */
function validateStructuredData($data, $type)
{
    $test = [
        'name' => "Structured Data ($type)",
        'score' => 0,
        'max_score' => 5,
        'status' => 'fail',
        'message' => '',
        'details' => [],
        'json' => json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
    ];
    
    if (isset($data['@type']) && $data['@type'] === $type) {
        $test['score'] += 2;
        $test['details'][] = "✓ Tip corect: {$data['@type']}";
    } else {
        $test['details'][] = "✗ Tip incorect sau lipsă";
    }
    
    if (isset($data['name']) && !empty($data['name'])) {
        $test['score'] += 1;
        $test['details'][] = "✓ Nume prezent";
    } else {
        $test['details'][] = "⚠ Nume lipsește";
    }
    
    if (isset($data['url']) && !empty($data['url'])) {
        $test['score'] += 1;
        $test['details'][] = "✓ URL prezent";
    } else {
        $test['details'][] = "⚠ URL lipsește";
    }
    
    if (json_last_error() === JSON_ERROR_NONE) {
        $test['score'] += 1;
        $test['details'][] = "✓ JSON valid";
    } else {
        $test['details'][] = "✗ JSON invalid";
    }
    
    $test['status'] = $test['score'] >= 4 ? 'pass' : ($test['score'] >= 2 ? 'warning' : 'fail');
    $test['message'] = "Scor: {$test['score']}/{$test['max_score']}";
    
    return $test;
}

/**
 * Validează breadcrumbs
 */
function validateBreadcrumbs($breadcrumbs)
{
    $test = [
        'name' => 'Breadcrumbs',
        'score' => 0,
        'max_score' => 3,
        'status' => 'fail',
        'message' => '',
        'details' => []
    ];
    
    if (is_array($breadcrumbs) && count($breadcrumbs) > 0) {
        $test['score'] += 1;
        $test['details'][] = "✓ Breadcrumbs prezente (" . count($breadcrumbs) . " elemente)";
        
        $validItems = 0;
        foreach ($breadcrumbs as $item) {
            if (isset($item['name']) && isset($item['url'])) {
                $validItems++;
            }
        }
        
        if ($validItems === count($breadcrumbs)) {
            $test['score'] += 2;
            $test['details'][] = "✓ Toate elementele sunt valide";
        } else {
            $test['details'][] = "⚠ Unele elemente sunt invalide";
        }
    } else {
        $test['details'][] = "⚠ Breadcrumbs lipsesc";
    }
    
    $test['status'] = $test['score'] >= 2 ? 'pass' : ($test['score'] >= 1 ? 'warning' : 'fail');
    $test['message'] = "Scor: {$test['score']}/{$test['max_score']}";
    
    return $test;
}

/**
 * Afișează rezultatele validării
 */
function displayValidationResults($validation)
{
    foreach ($validation['tests'] as $test) {
        $statusClass = 'test-' . $test['status'];
        echo "<div class=\"test-result $statusClass\">";
        echo "<h6><i class=\"fas fa-" . ($test['status'] === 'pass' ? 'check' : ($test['status'] === 'warning' ? 'exclamation-triangle' : 'times')) . " me-2\"></i>";
        echo htmlspecialchars($test['name']) . " - " . htmlspecialchars($test['message']) . "</h6>";
        
        if (!empty($test['details'])) {
            echo "<ul class=\"mb-0\">";
            foreach ($test['details'] as $detail) {
                echo "<li>" . htmlspecialchars($detail) . "</li>";
            }
            echo "</ul>";
        }
        
        if (isset($test['json'])) {
            echo "<details class=\"mt-2\">";
            echo "<summary>Vezi JSON</summary>";
            echo "<div class=\"json-viewer mt-2\">" . htmlspecialchars($test['json']) . "</div>";
            echo "</details>";
        }
        
        echo "</div>";
    }
}
?>
