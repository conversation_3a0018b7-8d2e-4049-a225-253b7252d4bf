<?php
/**
 * Test Specific Case Issue
 * Investigation of case 130/98/2022 from Curtea de Apel București
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Specific case parameters from the user's request
$numarDosar = '130/98/2022';
$institutie = 'CurteadeApelBUCURESTI';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Specific Case Issue Investigation</title>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
.warning { background: #fff3cd; border-left-color: #ffc107; }
.error { background: #f8d7da; border-left-color: #dc3545; }
.success { background: #d4edda; border-left-color: #28a745; }
.debug-info { background: #e3f2fd; border: 1px solid #2196f3; padding: 10px; margin: 10px 0; font-family: monospace; }
.test-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
.test-link:hover { background: #0056b3; color: white; text-decoration: none; }
.comparison-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
.comparison-table th, .comparison-table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
.comparison-table th { background-color: #f8f9fa; }
</style></head><body>";

echo "<h1>🔍 Specific Case Issue Investigation</h1>";
echo "<p><strong>Case:</strong> {$numarDosar}</p>";
echo "<p><strong>Institution:</strong> {$institutie}</p>";
echo "<p><strong>Problem:</strong> Frontend shows only 100 parties instead of 340+</p>";
echo "<hr>";

// Step 1: Test the exact URL mentioned by the user
echo "<div class='section'>";
echo "<h2>🎯 Step 1: Test Actual Case Details Page</h2>";
echo "<p>Click the link below to test the exact URL mentioned in your request:</p>";

$actualUrl = "detalii_dosar.php?numar=" . urlencode($numarDosar) . "&institutie=" . urlencode($institutie);
echo "<a href='{$actualUrl}' class='test-link' target='_blank'>";
echo "<i class='fas fa-external-link-alt'></i> Open Actual Case Details Page";
echo "</a>";

echo "<p><strong>Instructions:</strong></p>";
echo "<ol>";
echo "<li>Click the link above to open the actual case details page</li>";
echo "<li>Look for the 'Părți implicate' section</li>";
echo "<li>Count how many parties are displayed (look for the counter badge)</li>";
echo "<li>Note if you see exactly 100 parties or more</li>";
echo "</ol>";
echo "</div>";

// Step 2: Backend verification
echo "<div class='section'>";
echo "<h2>📊 Step 2: Backend Data Verification</h2>";

try {
    $dosarService = new DosarService();
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    if (!$dosar) {
        echo "<div class='error'>❌ Failed to retrieve case data from backend</div>";
    } else {
        $totalParties = count($dosar->parti ?? []);
        
        echo "<div class='debug-info'>";
        echo "Backend Results:\n";
        echo "Case Number: " . ($dosar->numar ?? 'N/A') . "\n";
        echo "Institution: " . ($dosar->institutie ?? 'N/A') . "\n";
        echo "Total Parties: {$totalParties}\n";
        echo "</div>";
        
        if ($totalParties >= 340) {
            echo "<div class='success'>✅ Backend correctly extracts {$totalParties} parties (expected 340+)</div>";
        } elseif ($totalParties == 100) {
            echo "<div class='error'>❌ Backend only shows 100 parties - hybrid extraction not working!</div>";
        } else {
            echo "<div class='warning'>⚠️ Unexpected party count: {$totalParties}</div>";
        }
        
        // Source analysis
        if (!empty($dosar->parti)) {
            $soapCount = 0;
            $decisionCount = 0;
            $unknownCount = 0;
            
            foreach ($dosar->parti as $parte) {
                $source = $parte['source'] ?? 'unknown';
                switch ($source) {
                    case 'soap_api':
                        $soapCount++;
                        break;
                    case 'decision_text':
                        $decisionCount++;
                        break;
                    default:
                        $unknownCount++;
                        break;
                }
            }
            
            echo "<table class='comparison-table'>";
            echo "<tr><th>Source</th><th>Count</th><th>Status</th></tr>";
            echo "<tr><td>SOAP API</td><td>{$soapCount}</td><td>" . ($soapCount == 100 ? "✅ Expected limit" : "⚠️ Unexpected") . "</td></tr>";
            echo "<tr><td>Decision Text</td><td>{$decisionCount}</td><td>" . ($decisionCount > 0 ? "✅ Hybrid working" : "❌ No extraction") . "</td></tr>";
            echo "<tr><td>Unknown Source</td><td>{$unknownCount}</td><td>" . ($unknownCount == 0 ? "✅ All attributed" : "⚠️ Missing attribution") . "</td></tr>";
            echo "<tr><td><strong>Total</strong></td><td><strong>{$totalParties}</strong></td><td><strong>" . ($totalParties > 100 ? "✅ Hybrid success" : "❌ Hybrid failed") . "</strong></td></tr>";
            echo "</table>";
        }
    }
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h4>❌ Backend Error</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
echo "</div>";

// Step 3: Frontend simulation
if (isset($dosar) && $dosar && !empty($dosar->parti)) {
    echo "<div class='section'>";
    echo "<h2>🎨 Step 3: Frontend Rendering Simulation</h2>";
    echo "<p>Simulating the exact rendering logic that should happen in detalii_dosar.php:</p>";
    
    $loop_index = 0;
    $renderedCount = 0;
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped' id='simulatedTable'>";
    echo "<thead>";
    echo "<tr>";
    echo "<th>Index</th>";
    echo "<th>Nume</th>";
    echo "<th>Calitate</th>";
    echo "<th>Source</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    // Simulate the exact foreach loop from detalii_dosar.php
    foreach ($dosar->parti as $parteIndex => $parte) {
        $loop_index++;
        $renderedCount++;
        
        echo "<tr class='parte-row'>";
        echo "<td><span class='badge bg-primary'>{$loop_index}</span></td>";
        echo "<td class='nume-parte'>" . htmlspecialchars($parte['nume'] ?? 'N/A') . "</td>";
        echo "<td class='calitate-parte'>" . htmlspecialchars($parte['calitate'] ?? '-') . "</td>";
        echo "<td><span class='badge bg-info'>" . htmlspecialchars($parte['source'] ?? 'unknown') . "</span></td>";
        echo "</tr>";
        
        // Show progress every 50 parties
        if ($renderedCount % 50 == 0) {
            echo "<tr><td colspan='4' class='text-center bg-light'>";
            echo "<em>--- {$renderedCount} parties rendered so far ---</em>";
            echo "</td></tr>";
        }
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    echo "<div class='debug-info'>";
    echo "Frontend Simulation Results:\n";
    echo "Expected parties: " . count($dosar->parti) . "\n";
    echo "Rendered parties: {$renderedCount}\n";
    echo "Rendering complete: " . ($renderedCount == count($dosar->parti) ? "✅ YES" : "❌ NO") . "\n";
    echo "</div>";
    
    if ($renderedCount == count($dosar->parti) && $renderedCount > 100) {
        echo "<div class='success'>✅ Frontend simulation successfully renders all {$renderedCount} parties</div>";
    } elseif ($renderedCount == 100) {
        echo "<div class='error'>❌ Frontend simulation only renders 100 parties - there's a limitation somewhere!</div>";
    }
    echo "</div>";
}

// Step 4: Comparison and diagnosis
echo "<div class='section'>";
echo "<h2>🔬 Step 4: Issue Diagnosis</h2>";

echo "<p><strong>Comparison Test:</strong></p>";
echo "<ol>";
echo "<li>Open the actual case details page using the link in Step 1</li>";
echo "<li>Compare the party count shown there with the backend results above</li>";
echo "<li>If they don't match, we've found the issue location</li>";
echo "</ol>";

echo "<table class='comparison-table'>";
echo "<tr><th>Component</th><th>Expected Result</th><th>Action</th></tr>";
echo "<tr><td>Backend Extraction</td><td>340+ parties</td><td>✅ Verified above</td></tr>";
echo "<tr><td>Frontend Simulation</td><td>340+ parties</td><td>✅ Verified above</td></tr>";
echo "<tr><td>Actual Page Display</td><td>340+ parties</td><td>🔍 Test with link in Step 1</td></tr>";
echo "</table>";

echo "<div class='warning'>";
echo "<h4>⚠️ Possible Causes if Actual Page Shows Only 100 Parties:</h4>";
echo "<ul>";
echo "<li><strong>Data not reaching template:</strong> Issue in detalii_dosar.php before the foreach loop</li>";
echo "<li><strong>Template variable issue:</strong> \$dosar->parti not properly populated</li>";
echo "<li><strong>JavaScript interference:</strong> JS code hiding parties after page load</li>";
echo "<li><strong>Server timeout:</strong> Page loading interrupted before all parties render</li>";
echo "<li><strong>Browser limitation:</strong> Browser stops rendering large tables</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

// Step 5: JavaScript verification
echo "<div class='section'>";
echo "<h2>🔧 Step 5: JavaScript Verification</h2>";
echo "<p>After opening the actual case details page, run this JavaScript in the browser console:</p>";

echo "<div style='background: #f1f3f4; padding: 10px; font-family: monospace; white-space: pre-wrap; border: 1px solid #ddd;'>";
echo "// Comprehensive party count verification
console.log('🔍 Comprehensive Party Analysis for Case {$numarDosar}');

// Count all party rows
const table = document.getElementById('tabelParti');
const tbody = table ? table.querySelector('tbody') : null;
const partyRows = tbody ? tbody.querySelectorAll('tr.parte-row') : [];

console.log('Basic Counts:', {
    tableFound: !!table,
    tbodyFound: !!tbody,
    totalPartyRows: partyRows.length
});

// Check for hidden rows
const hiddenRows = Array.from(partyRows).filter(row => {
    const style = window.getComputedStyle(row);
    return style.display === 'none' || style.visibility === 'hidden';
});

console.log('Visibility Analysis:', {
    visibleParties: partyRows.length - hiddenRows.length,
    hiddenParties: hiddenRows.length
});

// Check party counter badge
const partiCounter = document.querySelector('.parti-counter');
const counterText = partiCounter ? partiCounter.textContent : 'Not found';
console.log('Party Counter Badge:', counterText);

// Sample first and last parties
if (partyRows.length > 0) {
    const firstName = partyRows[0].querySelector('.nume-parte')?.textContent || 'N/A';
    const lastName = partyRows[partyRows.length-1].querySelector('.nume-parte')?.textContent || 'N/A';
    console.log('First Party:', firstName);
    console.log('Last Party:', lastName);
}

// Final diagnosis
if (partyRows.length >= 340) {
    console.log('✅ SUCCESS: Found ' + partyRows.length + ' parties (expected 340+)');
} else if (partyRows.length === 100) {
    console.log('❌ CONFIRMED ISSUE: Only 100 parties displayed (SOAP API limit)');
    console.log('🔍 This confirms the frontend is not receiving all backend data');
} else {
    console.log('⚠️ UNEXPECTED: Found ' + partyRows.length + ' parties');
}

// Check if page is still loading
if (document.readyState !== 'complete') {
    console.log('⚠️ Page still loading, counts may be incomplete');
}";
echo "</div>";
echo "</div>";

echo "<script>";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "    console.log('🔍 Specific Case Issue Investigation - Ready');";
echo "    console.log('Case: {$numarDosar} from {$institutie}');";
echo "    ";
echo "    // Verify our simulation table";
echo "    const simTable = document.getElementById('simulatedTable');";
echo "    const simRows = simTable ? simTable.querySelectorAll('tr.parte-row') : [];";
echo "    console.log('Simulation Table Parties:', simRows.length);";
echo "    ";
echo "    if (simRows.length > 100) {";
echo "        console.log('✅ Simulation shows ' + simRows.length + ' parties - backend data is correct');";
echo "        console.log('🔍 Now test the actual page to see if it matches');";
echo "    } else {";
echo "        console.log('❌ Simulation only shows ' + simRows.length + ' parties - backend issue');";
echo "    }";
echo "});";
echo "</script>";

echo "</body></html>";
?>
