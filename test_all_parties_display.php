<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test All Parties Display - Case 130/98/2022</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-section {
            border: 2px solid #007bff;
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
        }
        .success { background: #d4edda; border-color: #28a745; }
        .warning { background: #fff3cd; border-color: #ffc107; }
        .danger { background: #f8d7da; border-color: #dc3545; }
        .party-item {
            padding: 5px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        .party-item:nth-child(even) {
            background: #f8f9fa;
        }
        .counter-display {
            font-size: 18px;
            font-weight: bold;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-users"></i> Complete Parties Display Test</h1>
        <p class="lead">Testing case 130/98/2022 from Tribunalul IALOMIȚA</p>
        
        <?php
        // Include necessary files
        require_once 'bootstrap.php';
        require_once 'includes/config.php';
        require_once 'includes/functions.php';
        require_once 'services/DosarService.php';

        // Test parameters
        $numarDosar = '130/98/2022';
        $institutie = 'TribunalulIALOMITA';

        try {
            // Initialize service and get data
            $dosarService = new DosarService();
            $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
            
            if (!$dosar || empty((array)$dosar)) {
                echo "<div class='alert alert-danger'>ERROR: No case data returned</div>";
                exit;
            }
            
            $totalParties = count($dosar->parti);
            $expectedParties = 100; // We know this case should have 100 parties
            ?>
            
            <div class="test-section <?php echo $totalParties === $expectedParties ? 'success' : 'danger'; ?>">
                <h3><i class="fas fa-database"></i> Backend Data Verification</h3>
                <div class="counter-display bg-primary text-white">
                    Total Parties Retrieved: <?php echo $totalParties; ?> / <?php echo $expectedParties; ?> expected
                </div>
                <p><strong>Status:</strong> 
                    <?php if ($totalParties === $expectedParties): ?>
                        <span class="text-success">✓ All parties retrieved successfully</span>
                    <?php else: ?>
                        <span class="text-danger">✗ Missing parties detected</span>
                    <?php endif; ?>
                </p>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-table"></i> Frontend Table Test</h3>
                <p>This table simulates the exact structure from detalii_dosar.php:</p>
                
                <div class="table-responsive">
                    <table class="table table-striped" id="testTable">
                        <thead>
                            <tr>
                                <th>Index</th>
                                <th>Nume</th>
                                <th>Calitate</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $loop_index = 0;
                            foreach ($dosar->parti as $parteIndex => $parte): 
                                $loop_index++;
                            ?>
                                <tr class="parte-row" 
                                    data-nume="<?php echo htmlspecialchars($parte['nume']); ?>"
                                    data-calitate="<?php echo !empty($parte['calitate']) ? htmlspecialchars($parte['calitate']) : ''; ?>"
                                    data-index="<?php echo $loop_index; ?>"
                                    data-party-id="<?php echo $parteIndex; ?>">
                                    <td><span class="badge bg-secondary"><?php echo $loop_index; ?></span></td>
                                    <td class="nume-parte"><?php echo htmlspecialchars($parte['nume']); ?></td>
                                    <td class="calitate-parte">
                                        <?php echo !empty($parte['calitate']) ? htmlspecialchars($parte['calitate']) : '<span class="text-muted">-</span>'; ?>
                                    </td>
                                    <td><span class="badge bg-success">Rendered</span></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="counter-display bg-info text-white" id="tableCounter">
                    Table Rows: <span id="tableRowCount">Counting...</span>
                </div>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-search"></i> Search Functionality Test</h3>
                <div class="mb-3">
                    <label for="testSearch" class="form-label">Test Search:</label>
                    <input type="text" class="form-control" id="testSearch" placeholder="Search for a party name...">
                </div>
                <div id="searchResults" class="alert alert-info" style="display: none;"></div>
                <div class="counter-display bg-warning text-dark" id="searchCounter">
                    Visible Parties: <span id="visibleCount">All</span>
                </div>
            </div>

            <div class="test-section">
                <h3><i class="fas fa-list-ol"></i> Complete Parties List</h3>
                <p>All <?php echo $totalParties; ?> parties in order:</p>
                <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                    <?php foreach ($dosar->parti as $index => $parte): ?>
                        <div class="party-item">
                            <strong><?php echo $index + 1; ?>.</strong> 
                            <?php echo htmlspecialchars($parte['nume']); ?>
                            <?php if (!empty($parte['calitate'])): ?>
                                - <em><?php echo htmlspecialchars($parte['calitate']); ?></em>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

        <?php
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>EXCEPTION: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
        ?>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Count table rows
            const table = document.getElementById('testTable');
            const tbody = table.querySelector('tbody');
            const allRows = tbody.querySelectorAll('tr.parte-row');
            
            document.getElementById('tableRowCount').textContent = allRows.length;
            
            // Test search functionality
            const searchInput = document.getElementById('testSearch');
            const searchResults = document.getElementById('searchResults');
            const visibleCountSpan = document.getElementById('visibleCount');
            
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    const searchTerm = this.value.trim().toLowerCase();
                    let visibleCount = 0;
                    
                    if (searchTerm === '') {
                        // Show all rows
                        allRows.forEach(row => {
                            row.style.display = '';
                            visibleCount++;
                        });
                        searchResults.style.display = 'none';
                    } else {
                        // Filter rows
                        allRows.forEach(row => {
                            const numeCell = row.querySelector('.nume-parte');
                            const calitateCell = row.querySelector('.calitate-parte');
                            
                            const nume = numeCell ? numeCell.textContent.toLowerCase() : '';
                            const calitate = calitateCell ? calitateCell.textContent.toLowerCase() : '';
                            
                            if (nume.includes(searchTerm) || calitate.includes(searchTerm)) {
                                row.style.display = '';
                                visibleCount++;
                            } else {
                                row.style.display = 'none';
                            }
                        });
                        
                        // Show search results
                        searchResults.textContent = `Found ${visibleCount} parties matching "${searchTerm}"`;
                        searchResults.style.display = 'block';
                        searchResults.className = visibleCount > 0 ? 'alert alert-success' : 'alert alert-warning';
                    }
                    
                    visibleCountSpan.textContent = visibleCount === allRows.length ? 'All' : `${visibleCount} of ${allRows.length}`;
                }, 300);
            });
            
            // Performance test
            console.log('Performance Test Results:');
            console.log('Total parties in DOM:', allRows.length);
            console.log('Expected parties:', <?php echo $expectedParties; ?>);
            console.log('Match expected:', allRows.length === <?php echo $expectedParties; ?>);
            
            // Test table rendering performance
            const startTime = performance.now();
            allRows.forEach(row => {
                row.offsetHeight; // Force reflow
            });
            const endTime = performance.now();
            console.log(`Table rendering time: ${(endTime - startTime).toFixed(2)}ms`);
        });
    </script>
</body>
</html>
