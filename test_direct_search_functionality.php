<?php
/**
 * Direct Test of Enhanced Search Functionality
 * Tests the search by directly calling the enhanced functions
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

// Define the enhanced normalization function directly
function normalizeForSearchEnhanced($text) {
    if (empty($text)) return '';
    
    $text = trim((string) $text);
    
    // Detectăm encoding-ul și convertim la UTF-8 dacă este necesar
    if (!mb_check_encoding($text, 'UTF-8')) {
        $detected = mb_detect_encoding($text, ['UTF-8', 'ISO-8859-1', 'ISO-8859-2', 'Windows-1252'], true);
        if ($detected && $detected !== 'UTF-8') {
            $text = mb_convert_encoding($text, 'UTF-8', $detected);
        }
    }
    
    // ENHANCED: Comprehensive Romanian diacritics mapping
    $diacritics = [
        // Standard Romanian diacritics (highest priority)
        'ă' => 'a', 'â' => 'a', 'î' => 'i', 'ș' => 's', 'ț' => 't',
        'Ă' => 'A', 'Â' => 'A', 'Î' => 'I', 'Ș' => 'S', 'Ț' => 'T',
        
        // Legacy/alternative variants (very important for compatibility)
        'ţ' => 't', 'ş' => 's', 'Ţ' => 'T', 'Ş' => 'S',
        
        // Other Romanian variants
        'ã' => 'a', 'Ã' => 'A', 'ì' => 'i', 'Ì' => 'I',
        
        // Similar characters from other languages (for typing mistakes)
        'á' => 'a', 'à' => 'a', 'ä' => 'a', 'å' => 'a', 'ā' => 'a',
        'Á' => 'A', 'À' => 'A', 'Ä' => 'A', 'Å' => 'A', 'Ā' => 'A',
        'é' => 'e', 'è' => 'e', 'ë' => 'e', 'ê' => 'e', 'ē' => 'e',
        'É' => 'E', 'È' => 'E', 'Ë' => 'E', 'Ê' => 'E', 'Ē' => 'E',
        'í' => 'i', 'ì' => 'i', 'ï' => 'i', 'ī' => 'i',
        'Í' => 'I', 'Ì' => 'I', 'Ï' => 'I', 'Ī' => 'I',
        'ó' => 'o', 'ò' => 'o', 'ö' => 'o', 'ô' => 'o', 'ō' => 'o',
        'Ó' => 'O', 'Ò' => 'O', 'Ö' => 'O', 'Ô' => 'O', 'Ō' => 'O',
        'ú' => 'u', 'ù' => 'u', 'ü' => 'u', 'û' => 'u', 'ū' => 'u',
        'Ú' => 'U', 'Ù' => 'U', 'Ü' => 'U', 'Û' => 'U', 'Ū' => 'U',
        
        // Special characters that may appear in names
        'ç' => 'c', 'Ç' => 'C', 'ñ' => 'n', 'Ñ' => 'N'
    ];
    
    // Apply diacritics mapping
    $normalized = strtr($text, $diacritics);
    
    // Convert to lowercase and normalize spaces
    $normalized = mb_strtolower($normalized, 'UTF-8');
    $normalized = preg_replace('/\s+/', ' ', $normalized);
    $normalized = trim($normalized);
    
    return $normalized;
}

// Extract party name helper
function extractPartyName($party) {
    if (is_array($party) && isset($party['nume'])) {
        return $party['nume'];
    } elseif (is_object($party) && isset($party->nume)) {
        return $party->nume;
    }
    return '';
}

// Enhanced party search function
function findMatchingPartyEnhanced($parti, $searchTerm) {
    if (empty($parti) || empty($searchTerm)) {
        return null;
    }

    $normalizedSearchTerm = normalizeForSearchEnhanced($searchTerm);
    $lowerSearchTerm = mb_strtolower(trim($searchTerm), 'UTF-8');

    // PHASE 1: Exact match with normalized diacritics
    foreach ($parti as $party) {
        $partyName = extractPartyName($party);
        if (empty($partyName)) continue;

        $normalizedPartyName = normalizeForSearchEnhanced($partyName);
        
        if (strcasecmp($normalizedPartyName, $normalizedSearchTerm) === 0) {
            return $party;
        }
        
        if (strcasecmp(trim($partyName), trim($searchTerm)) === 0) {
            return $party;
        }
    }

    // PHASE 2: Partial match
    foreach ($parti as $party) {
        $partyName = extractPartyName($party);
        if (empty($partyName)) continue;

        $normalizedPartyName = normalizeForSearchEnhanced($partyName);
        $lowerPartyName = mb_strtolower(trim($partyName), 'UTF-8');

        if (mb_strpos($normalizedPartyName, $normalizedSearchTerm, 0, 'UTF-8') !== false) {
            return $party;
        }
        
        if (mb_strpos($lowerPartyName, $lowerSearchTerm, 0, 'UTF-8') !== false) {
            return $party;
        }
    }

    // PHASE 3: Fuzzy matching
    if (strlen($normalizedSearchTerm) >= 4) {
        $bestMatch = null;
        $bestScore = 0;
        
        foreach ($parti as $party) {
            $partyName = extractPartyName($party);
            if (empty($partyName)) continue;
            
            $normalizedPartyName = normalizeForSearchEnhanced($partyName);
            
            $similarity = 0;
            similar_text($normalizedSearchTerm, $normalizedPartyName, $similarity);
            
            if ($similarity > 85 && $similarity > $bestScore) {
                $bestScore = $similarity;
                $bestMatch = $party;
            }
        }
        
        if ($bestMatch && $bestScore >= 85) {
            return $bestMatch;
        }
    }

    return null;
}

echo "<!DOCTYPE html>";
echo "<html><head><title>Direct Search Functionality Test</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    .highlight { background: yellow; font-weight: bold; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style></head><body>";

echo "<h1>🔍 Direct Search Functionality Test</h1>";
echo "<p>Testing the enhanced search functionality with direct function calls</p>";
echo "<hr>";

try {
    // Test normalization
    echo "<div class='section'>";
    echo "<h2>📋 Step 1: Testing Diacritics Normalization</h2>";
    
    $testCases = [
        'Saragea Tudorita' => 'saragea tudorita',
        'SARAGEA TUDORIŢA' => 'saragea tudorita',
        'Şerbănescu Elena' => 'serbanescu elena',
        'ŞERBĂNESCU ELENA' => 'serbanescu elena'
    ];
    
    echo "<table>";
    echo "<tr><th>Input</th><th>Expected</th><th>Actual</th><th>Status</th></tr>";
    
    $allPassed = true;
    foreach ($testCases as $input => $expected) {
        $result = normalizeForSearchEnhanced($input);
        $passed = ($result === $expected);
        $allPassed = $allPassed && $passed;
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($input) . "</td>";
        echo "<td>" . htmlspecialchars($expected) . "</td>";
        echo "<td>" . htmlspecialchars($result) . "</td>";
        echo "<td>" . ($passed ? '✅ Pass' : '❌ Fail') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    if ($allPassed) {
        echo "<p class='success'>✅ All normalization tests passed!</p>";
    } else {
        echo "<p class='error'>❌ Some normalization tests failed</p>";
    }
    
    echo "</div>";
    
    // Test with real data
    echo "<div class='section'>";
    echo "<h2>🔍 Step 2: Testing with Real Case Data</h2>";
    
    $dosarService = new \App\Services\DosarService();
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');
    
    if ($dosar && !empty($dosar->parti)) {
        echo "<p class='info'>📄 Testing with case 130/98/2022 (" . count($dosar->parti) . " parties)</p>";
        
        $searchTests = [
            'Saragea Tudorita',      // Without diacritics - should find SARAGEA TUDORIŢA
            'SARAGEA TUDORIŢA',      // With diacritics - should find exact match
            'Saragea',               // Partial name - should find SARAGEA
            'ŞERBĂNESCU ELENA',      // Another party - should find exact match
            'Serbanescu Elena'       // Without diacritics - should find ŞERBĂNESCU ELENA
        ];
        
        echo "<table>";
        echo "<tr><th>Search Term</th><th>Found Party</th><th>Match Quality</th><th>Status</th></tr>";
        
        $successCount = 0;
        foreach ($searchTests as $searchTerm) {
            $foundParty = findMatchingPartyEnhanced($dosar->parti, $searchTerm);
            
            if ($foundParty) {
                $partyName = extractPartyName($foundParty);
                
                // Determine match quality
                $normalizedSearch = normalizeForSearchEnhanced($searchTerm);
                $normalizedParty = normalizeForSearchEnhanced($partyName);
                
                $matchQuality = 'Unknown';
                if (strcasecmp($normalizedSearch, $normalizedParty) === 0) {
                    $matchQuality = 'Exact (normalized)';
                } elseif (stripos($normalizedParty, $normalizedSearch) !== false) {
                    $matchQuality = 'Partial match';
                } else {
                    $matchQuality = 'Fuzzy match';
                }
                
                echo "<tr>";
                echo "<td>" . htmlspecialchars($searchTerm) . "</td>";
                echo "<td class='highlight'>" . htmlspecialchars($partyName) . "</td>";
                echo "<td>{$matchQuality}</td>";
                echo "<td class='success'>✅ Found</td>";
                echo "</tr>";
                
                $successCount++;
            } else {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($searchTerm) . "</td>";
                echo "<td>-</td>";
                echo "<td>-</td>";
                echo "<td class='error'>❌ Not Found</td>";
                echo "</tr>";
            }
        }
        
        echo "</table>";
        
        echo "<p><strong>Success Rate:</strong> {$successCount}/" . count($searchTests) . " (" . round(($successCount / count($searchTests)) * 100, 1) . "%)</p>";
        
        if ($successCount === count($searchTests)) {
            echo "<p class='success'>🎉 All search tests passed! Enhanced search is working perfectly!</p>";
        } elseif ($successCount > 0) {
            echo "<p class='warning'>⚠️ Partial success - some searches working</p>";
        } else {
            echo "<p class='error'>❌ No searches successful</p>";
        }
        
    } else {
        echo "<p class='error'>❌ Could not retrieve test case data</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2 class='error'>❌ Error Occurred</h2>";
    echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Direct test completed at " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
