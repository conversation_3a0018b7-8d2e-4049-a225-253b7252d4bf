<?php

namespace App\Services;

use App\Helpers\TemplateEngine;
use Exception;

/**
 * Email Template Engine Service
 * 
 * Handles rendering of email templates for case monitoring notifications
 * with support for both HTML and plain text formats.
 */
class EmailTemplateEngine
{
    private $templateEngine;

    public function __construct()
    {
        $this->templateEngine = new TemplateEngine();
    }

    /**
     * Render case added notification - text version
     * 
     * @param array $data Email data
     * @return string Rendered text content
     */
    public function renderCaseAddedText(array $data): string
    {
        $template = "Bună ziua {user_name},

Dosarul {case_number} de la {institution_name} a fost adăugat cu succes în lista de monitorizare.

Detalii dosar:
- Numărul dosarului: {case_number}
- Instanța: {institution_name}
- Obiectul dosarului: {case_object}
- Motivul monitorizării: {monitoring_reason}
- Frecvența notificărilor: {notification_frequency}

Veți primi notificări automate când se produc modificări în acest dosar.

Pentru a gestiona dosarele monitorizate, accesați: " . $this->getBaseUrl() . "/monitor.php

Cu stimă,
Echipa Portal Judiciar";

        return $this->replacePlaceholders($template, $data);
    }

    /**
     * Render case added notification - HTML version
     * 
     * @param array $data Email data
     * @return string Rendered HTML content
     */
    public function renderCaseAddedHtml(array $data): string
    {
        try {
            return $this->templateEngine->render('emails/case_added.twig', $data + [
                'base_url' => $this->getBaseUrl(),
                'portal_name' => 'Portal Judiciar'
            ]);
        } catch (Exception $e) {
            // Fallback to simple HTML if template fails
            return $this->generateSimpleHtml(
                "Dosar adăugat în monitorizare",
                $this->renderCaseAddedText($data)
            );
        }
    }

    /**
     * Render change notification - text version
     * 
     * @param array $data Email data
     * @return string Rendered text content
     */
    public function renderChangeNotificationText(array $data): string
    {
        $template = "Bună ziua {user_name},

S-a detectat o modificare în dosarul {case_number} de la {institution_name}.

Detalii modificare:
- Tipul modificării: {change_type}
- Descrierea: {change_description}
- Data detectării: " . date('d.m.Y H:i') . "

Detalii dosar:
- Numărul dosarului: {case_number}
- Instanța: {institution_name}
- Obiectul dosarului: {case_object}

Pentru mai multe detalii, accesați: " . $this->getBaseUrl() . "/detalii_dosar.php?numar={case_number}&institutie=" . urlencode($data['institution_name'] ?? '') . "

Cu stimă,
Echipa Portal Judiciar";

        return $this->replacePlaceholders($template, $data);
    }

    /**
     * Render change notification - HTML version
     * 
     * @param array $data Email data
     * @return string Rendered HTML content
     */
    public function renderChangeNotificationHtml(array $data): string
    {
        try {
            return $this->templateEngine->render('emails/change_notification.twig', $data + [
                'base_url' => $this->getBaseUrl(),
                'portal_name' => 'Portal Judiciar',
                'detection_date' => date('d.m.Y H:i')
            ]);
        } catch (Exception $e) {
            // Fallback to simple HTML if template fails
            return $this->generateSimpleHtml(
                "Modificare detectată în dosar",
                $this->renderChangeNotificationText($data)
            );
        }
    }

    /**
     * Render daily digest - text version
     * 
     * @param array $data Email data
     * @return string Rendered text content
     */
    public function renderDailyDigestText(array $data): string
    {
        $template = "Bună ziua {user_name},

Raportul zilnic pentru dosarele monitorizate - {date}

Aveți {total_cases} dosare cu modificări în ultimele 24 de ore:

";

        foreach ($data['cases'] as $case) {
            $template .= "• Dosarul {$case['case_number']} ({$case['institution_name']})
  Modificări: {$case['recent_changes']}
  
";
        }

        $template .= "
Pentru mai multe detalii, accesați: " . $this->getBaseUrl() . "/monitor.php

Cu stimă,
Echipa Portal Judiciar";

        return $this->replacePlaceholders($template, $data);
    }

    /**
     * Render daily digest - HTML version
     * 
     * @param array $data Email data
     * @return string Rendered HTML content
     */
    public function renderDailyDigestHtml(array $data): string
    {
        try {
            return $this->templateEngine->render('emails/daily_digest.twig', $data + [
                'base_url' => $this->getBaseUrl(),
                'portal_name' => 'Portal Judiciar'
            ]);
        } catch (Exception $e) {
            // Fallback to simple HTML if template fails
            return $this->generateSimpleHtml(
                "Raport zilnic - Dosarele monitorizate",
                $this->renderDailyDigestText($data)
            );
        }
    }

    /**
     * Render weekly summary - text version
     * 
     * @param array $data Email data
     * @return string Rendered text content
     */
    public function renderWeeklySummaryText(array $data): string
    {
        $template = "Bună ziua {user_name},

Raportul săptămânal pentru dosarele monitorizate
Perioada: {week_start} - {week_end}

Aveți {total_cases} dosare cu modificări în ultima săptămână:

";

        foreach ($data['cases'] as $case) {
            $template .= "• Dosarul {$case['case_number']} ({$case['institution_name']})
  Numărul de modificări: {$case['changes_count']}
  Modificări: {$case['recent_changes']}
  
";
        }

        $template .= "
Pentru mai multe detalii, accesați: " . $this->getBaseUrl() . "/monitor.php

Cu stimă,
Echipa Portal Judiciar";

        return $this->replacePlaceholders($template, $data);
    }

    /**
     * Render weekly summary - HTML version
     * 
     * @param array $data Email data
     * @return string Rendered HTML content
     */
    public function renderWeeklySummaryHtml(array $data): string
    {
        try {
            return $this->templateEngine->render('emails/weekly_summary.twig', $data + [
                'base_url' => $this->getBaseUrl(),
                'portal_name' => 'Portal Judiciar'
            ]);
        } catch (Exception $e) {
            // Fallback to simple HTML if template fails
            return $this->generateSimpleHtml(
                "Raport săptămânal - Dosarele monitorizate",
                $this->renderWeeklySummaryText($data)
            );
        }
    }

    /**
     * Replace placeholders in template
     * 
     * @param string $template Template string
     * @param array $data Data for replacement
     * @return string Processed template
     */
    private function replacePlaceholders(string $template, array $data): string
    {
        foreach ($data as $key => $value) {
            if (is_string($value) || is_numeric($value)) {
                $template = str_replace('{' . $key . '}', $value, $template);
            }
        }
        return $template;
    }

    /**
     * Generate simple HTML wrapper
     * 
     * @param string $title Email title
     * @param string $content Email content
     * @return string HTML content
     */
    private function generateSimpleHtml(string $title, string $content): string
    {
        return "<!DOCTYPE html>
<html lang=\"ro\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>{$title}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #007bff; color: white; padding: 20px; text-align: center; margin-bottom: 20px; }
        .content { background-color: #f8f9fa; padding: 20px; border-radius: 5px; }
        .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class=\"header\">
        <h1>Portal Judiciar</h1>
        <p>{$title}</p>
    </div>
    <div class=\"content\">
        " . nl2br(htmlspecialchars($content)) . "
    </div>
    <div class=\"footer\">
        <p>© " . date('Y') . " Portal Judiciar. Toate drepturile rezervate.</p>
    </div>
</body>
</html>";
    }

    /**
     * Get base URL for links
     * 
     * @return string Base URL
     */
    private function getBaseUrl(): string
    {
        // Check if we're in production
        if (isset($_SERVER['HTTP_HOST']) && $_SERVER['HTTP_HOST'] === 'just.gabrielanghel.ro') {
            return 'http://just.gabrielanghel.ro';
        }
        
        // Development environment
        return 'http://localhost/just';
    }

    /**
     * Format change type for display
     * 
     * @param string $changeType Change type
     * @return string Formatted change type
     */
    private function formatChangeType(string $changeType): string
    {
        $types = [
            'hearing_date' => 'Dată ședință',
            'status' => 'Status dosar',
            'stage' => 'Stadiu procesual',
            'parties' => 'Părți',
            'judge' => 'Judecător',
            'solution' => 'Soluție',
            'other' => 'Altă modificare'
        ];

        return $types[$changeType] ?? $changeType;
    }

    /**
     * Format notification frequency for display
     * 
     * @param string $frequency Frequency
     * @return string Formatted frequency
     */
    private function formatNotificationFrequency(string $frequency): string
    {
        $frequencies = [
            'immediate' => 'Imediat',
            'daily' => 'Zilnic',
            'weekly' => 'Săptămânal'
        ];

        return $frequencies[$frequency] ?? $frequency;
    }
}
