<?php
// Test direct search simulation
require_once 'bootstrap.php';

use App\Services\DosarService;

// Simulate the exact POST request that the web interface makes
$_POST['bulkSearchTerms'] = "14096/3/2024*";

echo "<h1>Direct Search Simulation for '14096/3/2024*'</h1>";

try {
    // Include the search processing logic from index.php
    $bulkSearchTerms = isset($_POST['bulkSearchTerms']) ? trim($_POST['bulkSearchTerms']) : '';
    
    if (!empty($bulkSearchTerms)) {
        echo "<h2>1. Processing Search Terms</h2>";
        echo "<p><strong>Search input:</strong> '$bulkSearchTerms'</p>";
        
        // Parse search terms (copy from index.php)
        function parseBulkSearchTerms($input) {
            $input = str_replace(',', "\n", $input);
            $terms = explode("\n", $input);
            $cleanTerms = [];

            foreach ($terms as $term) {
                $term = trim($term);
                if (!empty($term) && strlen($term) >= 2) {
                    $cleanTerms[] = [
                        'term' => $term,
                        'type' => detectSearchType($term)
                    ];
                }
            }

            $uniqueTerms = [];
            $seenTerms = [];

            foreach ($cleanTerms as $termData) {
                $termKey = strtolower($termData['term']);
                if (!in_array($termKey, $seenTerms)) {
                    $uniqueTerms[] = $termData;
                    $seenTerms[] = $termKey;
                }
            }

            return $uniqueTerms;
        }

        function detectSearchType($term) {
            $cleanTerm = trim($term, '"\'');
            
            if (preg_match('/^\d+\/\d+(?:\/\d+)?\*$/', $cleanTerm)) {
                return 'numarDosar';
            }
            
            if (preg_match('/^\d+\/\d+(?:\/\d+)?\/[a-zA-Z0-9]+$/', $cleanTerm)) {
                return 'numarDosar';
            }
            
            if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
                return 'numarDosar';
            }
            
            if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
                return 'numarDosar';
            }
            
            return 'numeParte';
        }
        
        $searchTermsData = parseBulkSearchTerms($bulkSearchTerms);
        echo "<p><strong>Parsed terms:</strong></p>";
        echo "<pre>" . print_r($searchTermsData, true) . "</pre>";
        
        // Perform search for each term
        echo "<h2>2. Performing Searches</h2>";
        
        $dosarService = new DosarService();
        $searchResults = [];
        
        foreach ($searchTermsData as $termData) {
            $term = $termData['term'];
            $searchType = $termData['type'];
            
            echo "<h3>Searching for: '$term' (type: $searchType)</h3>";
            
            $searchParams = [
                'numarDosar' => ($searchType === 'numarDosar') ? $term : '',
                'institutie' => null,
                'numeParte' => ($searchType === 'numeParte') ? $term : '',
                'obiectDosar' => '',
                'dataStart' => '',
                'dataStop' => '',
                'dataUltimaModificareStart' => '',
                'dataUltimaModificareStop' => '',
                'categorieInstanta' => '',
                'categorieCaz' => ''
            ];
            
            $termResults = $dosarService->cautareAvansata($searchParams);
            
            echo "<p><strong>Results found: " . count($termResults) . "</strong></p>";
            
            // Add search metadata to each result
            foreach ($termResults as $dosar) {
                $dosar->searchTerm = $term;
                $dosar->searchType = $searchType;
            }
            
            $searchResults[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => $termResults,
                'count' => count($termResults),
                'error' => null
            ];
            
            // Show each result
            foreach ($termResults as $index => $dosar) {
                $hasAsterisk = strpos($dosar->numar, '*') !== false;
                echo "<div style='background: " . ($hasAsterisk ? "#fff3cd" : "#e7f3ff") . "; padding: 8px; margin: 3px 0; border: 1px solid " . ($hasAsterisk ? "#ffc107" : "#007bff") . ";'>";
                echo "<strong>Result #" . ($index + 1) . ":</strong> " . ($dosar->numar ?? 'N/A') . "<br>";
                echo "<strong>Institution:</strong> " . ($dosar->instanta ?? 'N/A') . "<br>";
                echo "<strong>Search Type:</strong> " . ($dosar->searchType ?? 'N/A') . "<br>";
                echo "<strong>Has Asterisk:</strong> " . ($hasAsterisk ? "YES" : "NO") . "<br>";
                echo "</div>";
            }
        }
        
        echo "<h2>3. Final Results Summary</h2>";
        
        $totalResults = 0;
        $literalAsteriskFound = false;
        
        foreach ($searchResults as $termResult) {
            $totalResults += count($termResult['results']);
            
            foreach ($termResult['results'] as $dosar) {
                if (strpos($dosar->numar, '*') !== false) {
                    $literalAsteriskFound = true;
                }
            }
        }
        
        echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border: 1px solid #c3e6cb; border-radius: 5px;'>";
        echo "<h3>Summary:</h3>";
        echo "<p><strong>Total results:</strong> $totalResults</p>";
        echo "<p><strong>Literal asterisk case found:</strong> " . ($literalAsteriskFound ? "YES" : "NO") . "</p>";
        echo "<p><strong>Expected message:</strong> \"$totalResults rezultate găsite pentru termenul '14096/3/2024*'\"</p>";
        echo "</div>";
        
        if ($totalResults == 3 && $literalAsteriskFound) {
            echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border: 1px solid #c3e6cb; border-radius: 5px;'>";
            echo "<h3>✅ Backend is working correctly!</h3>";
            echo "<p>The issue must be in the frontend display or JavaScript filtering.</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb; border-radius: 5px;'>";
            echo "<h3>❌ Backend issue detected!</h3>";
            echo "<p>Expected 3 results with literal asterisk case, but got different results.</p>";
            echo "</div>";
        }
        
        // Test the HTML generation
        echo "<h2>4. HTML Generation Test</h2>";
        echo "<p>Testing how the results would be displayed in HTML...</p>";
        
        foreach ($searchResults as $termIndex => $termResult) {
            echo "<h3>Term: {$termResult['term']} ({$termResult['count']} results)</h3>";
            
            if (!empty($termResult['results'])) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>Case Number</th><th>Institution</th><th>Search Type</th><th>Data Attributes</th></tr>";
                
                foreach ($termResult['results'] as $dosar) {
                    $searchType = $dosar->searchType ?? '';
                    $caseNumber = $dosar->numar ?? '';
                    $institution = $dosar->instanta ?? '';
                    
                    echo "<tr data-numar='" . htmlspecialchars($caseNumber) . "' data-search-type='" . htmlspecialchars($searchType) . "'>";
                    echo "<td>" . htmlspecialchars($caseNumber) . "</td>";
                    echo "<td>" . htmlspecialchars($institution) . "</td>";
                    echo "<td>" . htmlspecialchars($searchType) . "</td>";
                    echo "<td>data-numar=\"" . htmlspecialchars($caseNumber) . "\" data-search-type=\"" . htmlspecialchars($searchType) . "\"</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            }
        }
        
    } else {
        echo "<p>No search terms provided.</p>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h2>Next Steps</h2>";
echo "<p>If the backend shows 3 results correctly, then the issue is in:</p>";
echo "<ul>";
echo "<li>JavaScript exact match filter being auto-enabled</li>";
echo "<li>CSS hiding elements</li>";
echo "<li>JavaScript errors preventing proper display</li>";
echo "<li>Result counting logic in the frontend</li>";
echo "</ul>";
echo "<p><strong>To test the actual web interface:</strong></p>";
echo "<ol>";
echo "<li>Go to <a href='index.php' target='_blank'>index.php</a></li>";
echo "<li>Search for '14096/3/2024*'</li>";
echo "<li>Open browser console and look for debug messages</li>";
echo "<li>Check if exact match filter is enabled</li>";
echo "</ol>";
?>
