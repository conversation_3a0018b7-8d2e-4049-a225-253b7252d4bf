<?php
/**
 * Debug Party Display
 * Verifică exact ce se întâmplă cu afișarea părților în detalii_dosar.php
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Debug Party Display</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .code { background: #f5f5f5; padding: 10px; border-radius: 4px; font-family: monospace; }
</style></head><body>";

echo "<h1>🔍 Debug Party Display</h1>";
echo "<p>Verifică exact ce se întâmplă cu afișarea părților în detalii_dosar.php</p>";
echo "<hr>";

try {
    $dosarService = new \App\Services\DosarService();
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');
    
    if (!$dosar) {
        echo "<p class='error'>❌ Nu s-au putut obține detaliile dosarului</p>";
        exit;
    }
    
    echo "<div class='section'>";
    echo "<h2>📊 Analiza Structurii Datelor</h2>";
    
    echo "<table>";
    echo "<tr><th>Proprietate</th><th>Tip</th><th>Valoare</th></tr>";
    echo "<tr><td>dosar</td><td>" . gettype($dosar) . "</td><td>" . (is_object($dosar) ? get_class($dosar) : 'N/A') . "</td></tr>";
    echo "<tr><td>dosar->parti</td><td>" . gettype($dosar->parti ?? null) . "</td><td>" . (isset($dosar->parti) ? 'Set' : 'Not set') . "</td></tr>";
    
    if (isset($dosar->parti)) {
        echo "<tr><td>count(dosar->parti)</td><td>integer</td><td>" . count($dosar->parti) . "</td></tr>";
        echo "<tr><td>is_array(dosar->parti)</td><td>boolean</td><td>" . (is_array($dosar->parti) ? 'true' : 'false') . "</td></tr>";
        echo "<tr><td>is_object(dosar->parti)</td><td>boolean</td><td>" . (is_object($dosar->parti) ? 'true' : 'false') . "</td></tr>";
        
        if (is_array($dosar->parti) && !empty($dosar->parti)) {
            $firstParty = $dosar->parti[0];
            echo "<tr><td>First party type</td><td>" . gettype($firstParty) . "</td><td>" . (is_object($firstParty) ? get_class($firstParty) : 'N/A') . "</td></tr>";
            
            if (is_object($firstParty)) {
                $properties = get_object_vars($firstParty);
                echo "<tr><td>First party properties</td><td>array</td><td>" . implode(', ', array_keys($properties)) . "</td></tr>";
            }
        }
    }
    echo "</table>";
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🔄 Simulare Bucla foreach din detalii_dosar.php</h2>";
    
    if (!empty($dosar->parti)) {
        $loop_index = 0;
        $totalPartiCount = count($dosar->parti);
        $rendered_count = 0;
        $skipped_count = 0;
        $errors = [];
        
        echo "<p><strong>Total părți de procesat:</strong> {$totalPartiCount}</p>";
        
        echo "<table>";
        echo "<tr><th>#</th><th>Index</th><th>Nume</th><th>Calitate</th><th>Status</th><th>Erori</th></tr>";
        
        $displayLimit = 20; // Afișăm doar primele 20 pentru debug
        
        foreach ($dosar->parti as $parteIndex => $parte) {
            $loop_index++;
            $rowErrors = [];
            
            // Verificăm structura părții
            if (!is_object($parte)) {
                $rowErrors[] = "Partea nu este obiect: " . gettype($parte);
                $skipped_count++;
            } else {
                // Verificăm proprietățile necesare
                $nume = isset($parte->nume) ? trim($parte->nume) : '';
                $calitate = isset($parte->calitate) ? trim($parte->calitate) : '';
                
                if (empty($nume)) {
                    $rowErrors[] = "Nume lipsă sau gol";
                }
                
                if (strlen($nume) < 2) {
                    $rowErrors[] = "Nume prea scurt";
                }
                
                if (empty($rowErrors)) {
                    $rendered_count++;
                }
            }
            
            // Afișăm doar primele părți pentru debug
            if ($loop_index <= $displayLimit) {
                $statusClass = empty($rowErrors) ? 'success' : 'error';
                $statusText = empty($rowErrors) ? '✅ OK' : '❌ Skip';
                
                echo "<tr>";
                echo "<td>{$loop_index}</td>";
                echo "<td>{$parteIndex}</td>";
                echo "<td>" . htmlspecialchars($nume ?? 'N/A') . "</td>";
                echo "<td>" . htmlspecialchars($calitate ?? 'N/A') . "</td>";
                echo "<td class='{$statusClass}'>{$statusText}</td>";
                echo "<td>" . htmlspecialchars(implode('; ', $rowErrors)) . "</td>";
                echo "</tr>";
            }
            
            if (!empty($rowErrors)) {
                $errors = array_merge($errors, $rowErrors);
            }
        }
        
        if ($totalPartiCount > $displayLimit) {
            echo "<tr><td colspan='6' class='info'>... și încă " . ($totalPartiCount - $displayLimit) . " părți</td></tr>";
        }
        
        echo "</table>";
        
        echo "<h3>📊 Sumar Procesare</h3>";
        echo "<table>";
        echo "<tr><th>Metric</th><th>Valoare</th></tr>";
        echo "<tr><td>Total părți procesate</td><td>{$loop_index}</td></tr>";
        echo "<tr><td>Părți care ar fi afișate</td><td class='success'>{$rendered_count}</td></tr>";
        echo "<tr><td>Părți care ar fi omise</td><td class='error'>{$skipped_count}</td></tr>";
        echo "<tr><td>Rata de succes</td><td>" . round(($rendered_count / max($totalPartiCount, 1)) * 100, 1) . "%</td></tr>";
        echo "</table>";
        
        if (!empty($errors)) {
            echo "<h3>⚠️ Erori Detectate</h3>";
            $errorCounts = array_count_values($errors);
            echo "<ul>";
            foreach ($errorCounts as $error => $count) {
                echo "<li>{$error}: {$count} cazuri</li>";
            }
            echo "</ul>";
        }
        
    } else {
        echo "<p class='error'>❌ dosar->parti este gol sau nu există</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🔍 Verificare Specific pentru SARAGEA TUDORIŢA</h2>";
    
    $targetFound = false;
    $targetIndex = -1;
    
    if (!empty($dosar->parti)) {
        foreach ($dosar->parti as $index => $party) {
            if (isset($party->nume) && 
                stripos($party->nume, 'SARAGEA') !== false && 
                stripos($party->nume, 'TUDORI') !== false) {
                $targetFound = true;
                $targetIndex = $index;
                
                echo "<p class='success'>✅ SARAGEA TUDORIŢA găsită la index {$index}</p>";
                echo "<table>";
                echo "<tr><th>Proprietate</th><th>Valoare</th></tr>";
                echo "<tr><td>Nume</td><td>" . htmlspecialchars($party->nume) . "</td></tr>";
                echo "<tr><td>Calitate</td><td>" . htmlspecialchars($party->calitate ?? 'N/A') . "</td></tr>";
                echo "<tr><td>Sursă</td><td>" . htmlspecialchars($party->source ?? 'N/A') . "</td></tr>";
                echo "<tr><td>Tip obiect</td><td>" . get_class($party) . "</td></tr>";
                echo "</table>";
                break;
            }
        }
    }
    
    if (!$targetFound) {
        echo "<p class='error'>❌ SARAGEA TUDORIŢA nu a fost găsită în datele backend</p>";
    }
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🌐 Test Simulare HTML Output</h2>";
    
    echo "<p>Simulăm exact codul din detalii_dosar.php:</p>";
    
    echo "<div class='code'>";
    echo "foreach (\$dosar->parti as \$parteIndex => \$parte):<br>";
    echo "&nbsp;&nbsp;\$loop_index++;<br>";
    echo "&nbsp;&nbsp;// render HTML row<br>";
    echo "endforeach;<br>";
    echo "</div>";
    
    if (!empty($dosar->parti)) {
        $simulated_output = "";
        $simulated_count = 0;
        
        foreach ($dosar->parti as $parteIndex => $parte) {
            $simulated_count++;
            
            if (is_object($parte) && isset($parte->nume) && !empty(trim($parte->nume))) {
                $simulated_output .= "<tr data-index='{$simulated_count}'>";
                $simulated_output .= "<td>" . htmlspecialchars($parte->nume) . "</td>";
                $simulated_output .= "<td>" . htmlspecialchars($parte->calitate ?? '-') . "</td>";
                $simulated_output .= "</tr>\n";
            }
        }
        
        echo "<p><strong>Simulare completă:</strong></p>";
        echo "<p>Rânduri HTML generate: <span class='success'>{$simulated_count}</span></p>";
        
        if ($simulated_count !== $totalPartiCount) {
            echo "<p class='error'>❌ PROBLEMĂ: Simularea generează {$simulated_count} rânduri din {$totalPartiCount} părți!</p>";
        } else {
            echo "<p class='success'>✅ Simularea generează toate {$simulated_count} rândurile</p>";
        }
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2 class='error'>❌ Error Occurred</h2>";
    echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p class='info'>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Debug completed at " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
