<?php
/**
 * Debug the filtering process to understand why cases are being filtered out
 */

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';

use App\Services\DosarService;

echo "=== DEBUGGING FILTERING PROCESS ===" . PHP_EOL;
echo "Investigating why cases with 'Saragea Tudorita' are being filtered out" . PHP_EOL;
echo PHP_EOL;

$searchTerm = 'Saragea Tudorita';

try {
    $dosarService = new DosarService();
    
    // Step 1: Get raw SOAP response
    echo "=== STEP 1: Getting Raw SOAP Response ===" . PHP_EOL;
    
    $reflection = new ReflectionClass($dosarService);
    $executeSoapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $executeSoapMethod->setAccessible(true);
    
    $processResponseMethod = $reflection->getMethod('processResponse');
    $processResponseMethod->setAccessible(true);
    
    $filterMethod = $reflection->getMethod('filterPartySearchResults');
    $filterMethod->setAccessible(true);
    
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => $searchTerm,
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    $rawResponse = $executeSoapMethod->invoke($dosarService, 'CautareDosare2', $searchParams, "Debug filtering");
    echo "Raw SOAP response received" . PHP_EOL;
    
    // Step 2: Process response (with hybrid extraction)
    echo PHP_EOL;
    echo "=== STEP 2: Processing Response (with hybrid extraction) ===" . PHP_EOL;
    
    $processedResults = $processResponseMethod->invoke($dosarService, $rawResponse, 1000);
    echo "Processed " . count($processedResults) . " cases" . PHP_EOL;
    
    // Step 3: Analyze each processed case before filtering
    echo PHP_EOL;
    echo "=== STEP 3: Analyzing Cases Before Filtering ===" . PHP_EOL;
    
    foreach ($processedResults as $index => $dosar) {
        echo "Case " . ($index + 1) . ": " . ($dosar->institutie ?? 'Unknown') . " - " . ($dosar->numar ?? 'Unknown') . PHP_EOL;
        
        $parti = $dosar->parti ?? [];
        echo "  Total parties: " . count($parti) . PHP_EOL;
        
        $foundTarget = false;
        $soapParties = 0;
        $decisionParties = 0;
        
        foreach ($parti as $party) {
            $partyName = is_object($party) ? ($party->nume ?? '') : ($party['nume'] ?? '');
            $partySource = is_object($party) ? ($party->source ?? 'soap_api') : ($party['source'] ?? 'soap_api');
            
            if ($partySource === 'soap_api') {
                $soapParties++;
            } else {
                $decisionParties++;
            }
            
            // Check for target party with same logic as filter
            if (!empty($partyName)) {
                // Exact match
                if (strcasecmp($partyName, $searchTerm) === 0) {
                    echo "  ✅ EXACT MATCH: $partyName [Source: $partySource]" . PHP_EOL;
                    $foundTarget = true;
                }
                // Partial match
                elseif (stripos($partyName, $searchTerm) !== false) {
                    echo "  ✅ PARTIAL MATCH: $partyName [Source: $partySource]" . PHP_EOL;
                    $foundTarget = true;
                }
                // Check for individual parts
                elseif (stripos($partyName, 'SARAGEA') !== false || stripos($partyName, 'TUDORITA') !== false) {
                    echo "  ✅ COMPONENT MATCH: $partyName [Source: $partySource]" . PHP_EOL;
                    $foundTarget = true;
                }
            }
        }
        
        echo "  SOAP parties: $soapParties, Decision text parties: $decisionParties" . PHP_EOL;
        echo "  Target found: " . ($foundTarget ? 'YES' : 'NO') . PHP_EOL;
        echo PHP_EOL;
    }
    
    // Step 4: Apply filtering and see what gets filtered out
    echo "=== STEP 4: Applying Filtering ===" . PHP_EOL;
    
    $filteredResults = $filterMethod->invoke($dosarService, $processedResults, $searchTerm);
    echo "After filtering: " . count($filteredResults) . " cases remain" . PHP_EOL;
    echo "Filtered out: " . (count($processedResults) - count($filteredResults)) . " cases" . PHP_EOL;
    echo PHP_EOL;
    
    // Step 5: Show which cases were filtered out
    echo "=== STEP 5: Cases That Were Filtered Out ===" . PHP_EOL;
    
    $filteredOutCases = [];
    foreach ($processedResults as $dosar) {
        $found = false;
        foreach ($filteredResults as $filteredDosar) {
            if (($dosar->numar ?? '') === ($filteredDosar->numar ?? '') && 
                ($dosar->institutie ?? '') === ($filteredDosar->institutie ?? '')) {
                $found = true;
                break;
            }
        }
        if (!$found) {
            $filteredOutCases[] = $dosar;
        }
    }
    
    foreach ($filteredOutCases as $dosar) {
        echo "FILTERED OUT: " . ($dosar->institutie ?? 'Unknown') . " - " . ($dosar->numar ?? 'Unknown') . PHP_EOL;
        
        // Analyze why this case was filtered out
        $parti = $dosar->parti ?? [];
        echo "  Parties: " . count($parti) . PHP_EOL;
        
        $hasTargetParty = false;
        foreach ($parti as $party) {
            $partyName = is_object($party) ? ($party->nume ?? '') : ($party['nume'] ?? '');
            
            if (!empty($partyName)) {
                if (strcasecmp($partyName, $searchTerm) === 0 || 
                    stripos($partyName, $searchTerm) !== false ||
                    stripos($partyName, 'SARAGEA') !== false || 
                    stripos($partyName, 'TUDORITA') !== false) {
                    echo "  Has target party: $partyName" . PHP_EOL;
                    $hasTargetParty = true;
                }
            }
        }
        
        if (!$hasTargetParty) {
            echo "  ❌ No target party found - correctly filtered out" . PHP_EOL;
        } else {
            echo "  ⚠️ Has target party but was filtered out - FILTERING ERROR!" . PHP_EOL;
        }
        echo PHP_EOL;
    }
    
    echo "=== ANALYSIS COMPLETE ===" . PHP_EOL;
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
}
