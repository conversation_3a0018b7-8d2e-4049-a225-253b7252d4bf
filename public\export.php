<?php

/**
 * Portal Judiciar - Pagina de export
 */

// Asigurăm-ne că nu există output înainte de a trimite headerele
ob_start();

// Încărcăm bootstrap-ul aplicației
require_once dirname(__DIR__) . '/bootstrap.php';

// Importăm clasele necesare
use App\Helpers\TemplateEngine;
use App\Services\DosarService;

// Configurăm raportarea erorilor pentru această pagină
error_reporting(E_ALL);
ini_set('display_errors', 0); // Dezactivăm afișarea erorilor pentru a nu strica PDF-ul
ini_set('log_errors', 1);
ini_set('error_log', LOG_DIR . '/pdf_errors.log');

// Formatul de export
$format = $_GET['format'] ?? 'html';

// Verificăm dacă este o descărcare directă
$direct = isset($_GET['direct']) && $_GET['direct'] === 'true';

// Verificăm dacă avem un număr de dosar și o instituție (export detalii dosar)
if (isset($_GET['numar']) && isset($_GET['institutie'])) {
    $numarDosar = $_GET['numar'];
    $institutie = $_GET['institutie'];

    try {
        // Inițializăm serviciul de dosare
        $dosarService = new DosarService();

        // Obținem detaliile dosarului
        $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);

        // Verificăm dacă am găsit dosarul
        if (empty((array)$dosar)) {
            throw new Exception('Nu au fost găsite detalii pentru dosarul specificat.');
        }

        // Obținem lista instanțelor din funcția centralizată
        require_once '../includes/functions.php';
        $instante = getInstanteList();

        // Datele pentru șablon
        $data = [
            'instante' => $instante,
            'dosar' => $dosar,
            'export' => true
        ];

        // Inițializăm motorul de șabloane
        $templateEngine = new TemplateEngine();

        // Generăm conținutul
        $content = $templateEngine->render('detalii_dosar.twig', $data);

        // Exportăm în funcție de format
        if ($format === 'pdf') {
            exportToPdf($content, "Dosar_{$numarDosar}_" . date('Y-m-d'), $direct);
        } else {
            exportToHtml($content, "Dosar_{$numarDosar}_" . date('Y-m-d'));
        }
    } catch (Exception $e) {
        // Afișăm eroarea
        echo 'Eroare la exportul dosarului: ' . $e->getMessage();
        exit;
    }
}
// Verificăm dacă avem un query de căutare (export rezultate căutare)
elseif (isset($_GET['query'])) {
    // Parsăm query-ul
    parse_str($_GET['query'], $searchParams);

    try {
        // Inițializăm serviciul de dosare
        $dosarService = new DosarService();

        // Efectuăm căutarea avansată
        $results = $dosarService->cautareAvansata($searchParams);

        // Verificăm dacă am găsit rezultate
        if (empty($results)) {
            throw new Exception('Nu au fost găsite rezultate pentru criteriile de căutare specificate.');
        }

        // Obținem lista instanțelor din funcția centralizată (deja încărcată mai sus)
        $instante = getInstanteList();

        // Datele pentru șablon
        $data = [
            'instante' => $instante,
            'searchParams' => $searchParams,
            'results' => $results,
            'searchQuery' => $_GET['query'],
            'export' => true
        ];

        // Inițializăm motorul de șabloane
        $templateEngine = new TemplateEngine();

        // Generăm conținutul
        $content = $templateEngine->render('search.twig', $data);

        // Exportăm în funcție de format
        if ($format === 'pdf') {
            exportToPdf($content, "Rezultate_cautare_" . date('Y-m-d'), $direct);
        } else {
            exportToHtml($content, "Rezultate_cautare_" . date('Y-m-d'));
        }
    } catch (Exception $e) {
        // Afișăm eroarea
        echo 'Eroare la exportul rezultatelor căutării: ' . $e->getMessage();
        exit;
    }
} else {
    // Afișăm eroarea
    echo 'Parametri insuficienți pentru export.';
    exit;
}

/**
 * Exportă conținutul în format PDF
 *
 * @param string $content Conținutul HTML
 * @param string $filename Numele fișierului (fără extensie)
 * @param bool $direct Indică dacă este o descărcare directă
 */
function exportToPdf($content, $filename, $direct = false)
{
    // Logăm începerea procesului
    $logMessage = date('Y-m-d H:i:s') . " - Începerea procesului de export PDF pentru {$filename}\n";
    file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);

    // Verificăm dacă directorul de export există și are permisiuni de scriere
    if (!is_dir(EXPORT_DIR)) {
        mkdir(EXPORT_DIR, 0755, true);
        $logMessage = date('Y-m-d H:i:s') . " - Directorul de export a fost creat: " . EXPORT_DIR . "\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);
    } elseif (!is_writable(EXPORT_DIR)) {
        $errorMessage = date('Y-m-d H:i:s') . " - Directorul de export nu are permisiuni de scriere: " . EXPORT_DIR . "\n";
        file_put_contents(LOG_DIR . '/pdf_errors.log', $errorMessage, FILE_APPEND);

        // Afișăm un mesaj de eroare detaliat
        echo '<div style="color: red; font-weight: bold; margin: 20px; padding: 20px; border: 1px solid red;">';
        echo '<h2>Eroare la generarea PDF-ului</h2>';
        echo '<p>Directorul de export nu are permisiuni de scriere: ' . htmlspecialchars(EXPORT_DIR) . '</p>';
        echo '<p>Detalii tehnice au fost înregistrate pentru depanare.</p>';
        echo '<p><a href="javascript:history.back()">Înapoi</a></p>';
        echo '</div>';
        exit;
    }

    // Verificăm dacă avem wkhtmltopdf instalat
    $wkhtmltopdfPaths = [
        'C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe',
        'C:\wkhtmltopdf\bin\wkhtmltopdf.exe',
        '/usr/bin/wkhtmltopdf',
        '/usr/local/bin/wkhtmltopdf'
    ];

    $wkhtmltopdf = null;
    foreach ($wkhtmltopdfPaths as $path) {
        if (file_exists($path)) {
            $wkhtmltopdf = $path;
            $logMessage = date('Y-m-d H:i:s') . " - wkhtmltopdf găsit la calea: {$path}\n";
            file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);
            break;
        }
    }

    if ($wkhtmltopdf === null) {
        // Dacă nu avem wkhtmltopdf, folosim TCPDF
        $logMessage = date('Y-m-d H:i:s') . " - wkhtmltopdf nu a fost găsit, se folosește TCPDF\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);
        exportToPdfWithTCPDF($content, $filename, $direct);
    } else {
        // Dacă avem wkhtmltopdf, îl folosim
        exportToPdfWithWkhtmltopdf($content, $filename, $wkhtmltopdf, $direct);
    }
}

/**
 * Exportă conținutul în format PDF folosind TCPDF
 *
 * @param string $content Conținutul HTML
 * @param string $filename Numele fișierului (fără extensie)
 * @param bool $direct Indică dacă este o descărcare directă
 */
function exportToPdfWithTCPDF($content, $filename, $direct = false)
{
    try {
        // Curățăm orice output anterior
        if (ob_get_length()) {
            ob_clean();
        }

        // Logăm începerea procesului
        $logMessage = date('Y-m-d H:i:s') . " - Începerea generării PDF cu TCPDF pentru {$filename}\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);

        // Verificăm dacă directorul de export există și are permisiuni de scriere
        if (!is_dir(EXPORT_DIR)) {
            mkdir(EXPORT_DIR, 0755, true);
            $logMessage = date('Y-m-d H:i:s') . " - Directorul de export a fost creat: " . EXPORT_DIR . "\n";
            file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);
        } elseif (!is_writable(EXPORT_DIR)) {
            throw new Exception("Directorul de export nu are permisiuni de scriere: " . EXPORT_DIR);
        }

        // Verificăm dacă TCPDF este instalat
        $tcpdfPath = dirname(__DIR__) . '/vendor/tecnickcom/tcpdf/tcpdf.php';
        if (!file_exists($tcpdfPath)) {
            throw new Exception("TCPDF nu este instalat sau calea este incorectă: {$tcpdfPath}");
        }

        // Includem TCPDF
        require_once $tcpdfPath;

        // Definim constantele TCPDF dacă nu sunt deja definite
        if (!defined('K_TCPDF_EXTERNAL_CONFIG') || !K_TCPDF_EXTERNAL_CONFIG) {
            define('K_TCPDF_EXTERNAL_CONFIG', true);
            define('K_PATH_MAIN', dirname(__DIR__) . '/vendor/tecnickcom/tcpdf/');
            define('K_PATH_URL', dirname(__DIR__) . '/vendor/tecnickcom/tcpdf/');
            define('K_PATH_FONTS', K_PATH_MAIN . 'fonts/');
            define('K_PATH_CACHE', CACHE_DIR . '/tcpdf/');
            define('K_PATH_URL_CACHE', CACHE_DIR . '/tcpdf/');
            define('K_PATH_IMAGES', K_PATH_MAIN . 'images/');
            define('K_BLANK_IMAGE', K_PATH_IMAGES . '_blank.png');
            define('PDF_PAGE_FORMAT', 'A4');
            define('PDF_PAGE_ORIENTATION', 'P');
            define('PDF_CREATOR', 'Portal Judiciar');
            define('PDF_AUTHOR', 'Portal Judiciar');
            define('PDF_UNIT', 'mm');
            define('PDF_MARGIN_HEADER', 5);
            define('PDF_MARGIN_FOOTER', 10);
            define('PDF_MARGIN_TOP', 15);
            define('PDF_MARGIN_BOTTOM', 15);
            define('PDF_MARGIN_LEFT', 15);
            define('PDF_MARGIN_RIGHT', 15);
            define('PDF_FONT_NAME_MAIN', 'dejavusans');
            define('PDF_FONT_SIZE_MAIN', 10);
            define('PDF_FONT_NAME_DATA', 'dejavusans');
            define('PDF_FONT_SIZE_DATA', 8);
            define('PDF_FONT_MONOSPACED', 'courier');
            define('PDF_IMAGE_SCALE_RATIO', 1.25);
            define('HEAD_MAGNIFICATION', 1.1);
            define('K_CELL_HEIGHT_RATIO', 1.25);
            define('K_TITLE_MAGNIFICATION', 1.3);
            define('K_SMALL_RATIO', 2/3);
            define('K_THAI_TOPCHARS', true);
            define('K_TCPDF_CALLS_IN_HTML', false);
            define('K_TCPDF_THROW_EXCEPTION_ERROR', true);
        }

        // Creăm directorul de cache pentru TCPDF dacă nu există
        if (!is_dir(K_PATH_CACHE)) {
            mkdir(K_PATH_CACHE, 0755, true);
            $logMessage = date('Y-m-d H:i:s') . " - Directorul de cache TCPDF a fost creat: " . K_PATH_CACHE . "\n";
            file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);
        }

        // Configurăm TCPDF
        class MYPDF extends TCPDF {
            // Adăugăm header și footer personalizate
            public function Header() {
                // Adăugăm logo sau text în header
                $this->SetFont('dejavusans', 'B', 10);
                $this->Cell(0, 10, 'Portal Judiciar - ' . date('d.m.Y'), 0, false, 'R', 0, '', 0, false, 'T', 'M');
            }

            public function Footer() {
                // Poziționăm la 15 mm de jos
                $this->SetY(-15);
                // Setăm fontul
                $this->SetFont('dejavusans', 'I', 8);
                // Adăugăm numărul paginii
                $this->Cell(0, 10, 'Pagina '.$this->getAliasNumPage().'/'.$this->getAliasNbPages(), 0, false, 'C', 0, '', 0, false, 'T', 'M');
            }
        }

        // Creăm o instanță PDF
        $pdf = new MYPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

        // Setăm informațiile documentului
        $pdf->SetCreator(PDF_CREATOR);
        $pdf->SetAuthor(PDF_AUTHOR);
        $pdf->SetTitle($filename);
        $pdf->SetSubject('Export PDF din Portal Judiciar');
        $pdf->SetKeywords('Portal, Judiciar, PDF, Export');

        // Setăm marginile
        $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
        $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
        $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);

        // Setăm auto page breaks
        $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

        // Setăm font-ul implicit
        $pdf->SetFont(PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN);

        // Adăugăm o pagină
        $pdf->AddPage();

        // Eliminăm scripturile și stilurile care pot cauza probleme
        $content = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $content);

        // Înlocuim căile relative cu cele absolute pentru imagini
        $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $content = preg_replace('/(src|href)="(?!http|https|ftp|mailto|data:)([^"]+)"/i', '$1="' . $baseUrl . '/$2"', $content);

        // Adăugăm stiluri pentru a îmbunătăți aspectul PDF-ului
        $styles = '
        <style>
            body {
                font-family: dejavusans, sans-serif;
                font-size: 10pt;
                line-height: 1.5;
            }
            h1, h2, h3, h4, h5, h6 {
                font-weight: bold;
                margin-top: 1em;
                margin-bottom: 0.5em;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 1em;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 0.5em;
                text-align: left;
            }
            th {
                background-color: #f8f9fa;
                font-weight: bold;
            }
            .card {
                border: 1px solid #ddd;
                margin-bottom: 1em;
            }
            .card-header {
                background-color: #f8f9fa;
                padding: 0.5em;
                font-weight: bold;
            }
            .card-body {
                padding: 0.5em;
            }
            .alert {
                padding: 0.5em;
                margin-bottom: 1em;
                border: 1px solid transparent;
                border-radius: 0.25em;
            }
            .alert-info {
                color: #0c5460;
                background-color: #d1ecf1;
                border-color: #bee5eb;
            }
            .btn, .dropdown, .modal, .no-print {
                display: none;
            }
        </style>';

        // Adăugăm stilurile la conținut
        $content = preg_replace('/<\/head>/', $styles . '</head>', $content);

        // Scriem HTML în PDF
        $pdf->writeHTML($content, true, false, true, false, '');

        // Generăm PDF-ul
        $pdfData = $pdf->Output('', 'S'); // S = returnează ca string

        // Logăm succesul
        $logMessage = date('Y-m-d H:i:s') . " - PDF generat cu succes pentru {$filename}\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);

        // Trimitem PDF-ul către browser
        header('Content-Type: application/pdf');

        // Setăm header-ul Content-Disposition în funcție de tipul de descărcare
        $disposition = $direct ? 'attachment' : 'inline';
        header("Content-Disposition: {$disposition}; filename=\"{$filename}.pdf\"");

        header('Content-Length: ' . strlen($pdfData));
        header('Cache-Control: max-age=0');
        header('Pragma: public');

        // Afișăm PDF-ul
        echo $pdfData;

        // Terminăm execuția
        exit;
    } catch (Exception $e) {
        // Logăm eroarea
        $errorMessage = date('Y-m-d H:i:s') . " - Eroare la generarea PDF cu TCPDF: " . $e->getMessage() . "\n";
        file_put_contents(LOG_DIR . '/pdf_errors.log', $errorMessage, FILE_APPEND);

        // Afișăm un mesaj de eroare detaliat
        echo '<div style="color: red; font-weight: bold; margin: 20px; padding: 20px; border: 1px solid red;">';
        echo '<h2>Eroare la generarea PDF-ului</h2>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p>Detalii tehnice au fost înregistrate pentru depanare.</p>';
        echo '<p><a href="javascript:history.back()">Înapoi</a></p>';
        echo '</div>';
        exit;
    }
}

/**
 * Exportă conținutul în format PDF folosind wkhtmltopdf
 *
 * @param string $content Conținutul HTML
 * @param string $filename Numele fișierului (fără extensie)
 * @param string $wkhtmltopdf Calea către executabilul wkhtmltopdf
 * @param bool $direct Indică dacă este o descărcare directă
 */
function exportToPdfWithWkhtmltopdf($content, $filename, $wkhtmltopdf, $direct = false)
{
    try {
        // Curățăm orice output anterior
        if (ob_get_length()) {
            ob_clean();
        }

        // Logăm începerea procesului
        $logMessage = date('Y-m-d H:i:s') . " - Începerea generării PDF cu wkhtmltopdf pentru {$filename}\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);

        // Verificăm dacă executabilul wkhtmltopdf există
        if (!file_exists($wkhtmltopdf)) {
            throw new Exception("Executabilul wkhtmltopdf nu a fost găsit la calea: {$wkhtmltopdf}");
        }

        // Verificăm dacă directorul de export există și are permisiuni de scriere
        if (!is_dir(EXPORT_DIR)) {
            mkdir(EXPORT_DIR, 0755, true);
            $logMessage = date('Y-m-d H:i:s') . " - Directorul de export a fost creat: " . EXPORT_DIR . "\n";
            file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);
        } elseif (!is_writable(EXPORT_DIR)) {
            throw new Exception("Directorul de export nu are permisiuni de scriere: " . EXPORT_DIR);
        }

        // Generăm nume unice pentru fișierele temporare
        $tempId = uniqid();
        $tempFile = EXPORT_DIR . '/' . $tempId . '.html';
        $outputFile = EXPORT_DIR . '/' . $tempId . '.pdf';

        // Adăugăm stiluri pentru a îmbunătăți aspectul PDF-ului
        $styles = '
        <style>
            body {
                font-family: Arial, sans-serif;
                font-size: 10pt;
                line-height: 1.5;
            }
            h1, h2, h3, h4, h5, h6 {
                font-weight: bold;
                margin-top: 1em;
                margin-bottom: 0.5em;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 1em;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 0.5em;
                text-align: left;
            }
            th {
                background-color: #f8f9fa;
                font-weight: bold;
            }
            .card {
                border: 1px solid #ddd;
                margin-bottom: 1em;
            }
            .card-header {
                background-color: #f8f9fa;
                padding: 0.5em;
                font-weight: bold;
            }
            .card-body {
                padding: 0.5em;
            }
            .alert {
                padding: 0.5em;
                margin-bottom: 1em;
                border: 1px solid transparent;
                border-radius: 0.25em;
            }
            .alert-info {
                color: #0c5460;
                background-color: #d1ecf1;
                border-color: #bee5eb;
            }
            .btn, .dropdown, .modal, .no-print {
                display: none;
            }
        </style>';

        // Adăugăm stilurile la conținut
        $content = preg_replace('/<\/head>/', "$styles</head>", $content);

        // Înlocuim căile relative cu cele absolute pentru imagini
        $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";
        $content = preg_replace('/(src|href)="(?!http|https|ftp|mailto|data:)([^"]+)"/i', "$1=\"$baseUrl/$2\"", $content);

        // Salvăm conținutul într-un fișier temporar
        if (file_put_contents($tempFile, $content) === false) {
            throw new Exception("Nu s-a putut scrie în fișierul temporar: {$tempFile}");
        }

        $logMessage = date('Y-m-d H:i:s') . " - Fișier HTML temporar creat: {$tempFile}\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);

        // Comanda pentru generarea PDF-ului cu opțiuni îmbunătățite
        $command = "\"$wkhtmltopdf\" "
                . "--encoding utf-8 "
                . "--page-size A4 "
                . "--margin-top 20 "
                . "--margin-right 20 "
                . "--margin-bottom 20 "
                . "--margin-left 20 "
                . "--dpi 300 "
                . "--image-dpi 300 "
                . "--image-quality 100 "
                . "--enable-local-file-access "
                . "--disable-smart-shrinking "
                . "--print-media-type "
                . "--no-background "
                . "\"$tempFile\" "
                . "\"$outputFile\"";

        $logMessage = date('Y-m-d H:i:s') . " - Executare comandă: {$command}\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);

        // Executăm comanda și capturăm output-ul
        exec("$command 2>&1", $output, $returnVar);

        // Logăm output-ul comenzii
        $outputLog = date('Y-m-d H:i:s') . " - Output comandă wkhtmltopdf:\n" . implode("\n", $output) . "\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $outputLog, FILE_APPEND);

        // Verificăm dacă generarea a reușit
        if ($returnVar !== 0) {
            throw new Exception("Eroare la executarea comenzii wkhtmltopdf (cod: {$returnVar}): " . implode("\n", $output));
        }

        if (!file_exists($outputFile)) {
            throw new Exception("Fișierul PDF nu a fost generat: {$outputFile}");
        }

        // Verificăm dimensiunea fișierului PDF
        $fileSize = filesize($outputFile);
        if ($fileSize === false || $fileSize === 0) {
            throw new Exception("Fișierul PDF generat este gol sau nu poate fi accesat: {$outputFile}");
        }

        $logMessage = date('Y-m-d H:i:s') . " - PDF generat cu succes: {$outputFile} ({$fileSize} bytes)\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);

        // Trimitem PDF-ul către browser
        header('Content-Type: application/pdf');

        // Setăm header-ul Content-Disposition în funcție de tipul de descărcare
        $disposition = $direct ? 'attachment' : 'inline';
        header("Content-Disposition: {$disposition}; filename=\"{$filename}.pdf\"");

        header("Content-Length: $fileSize");
        header('Cache-Control: max-age=0');
        header('Pragma: public');

        // Citim și afișăm fișierul
        if (readfile($outputFile) === false) {
            throw new Exception("Nu s-a putut citi fișierul PDF generat: {$outputFile}");
        }

        // Ștergem fișierele temporare
        @unlink($tempFile);
        @unlink($outputFile);

        $logMessage = date('Y-m-d H:i:s') . " - Fișierele temporare au fost șterse\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);

        exit;
    } catch (Exception $e) {
        // Ștergem fișierele temporare dacă există
        if (isset($tempFile) && file_exists($tempFile)) {
            @unlink($tempFile);
        }

        if (isset($outputFile) && file_exists($outputFile)) {
            @unlink($outputFile);
        }

        // Logăm eroarea
        $errorMessage = date('Y-m-d H:i:s') . " - Eroare la generarea PDF cu wkhtmltopdf: " . $e->getMessage() . "\n";
        file_put_contents(LOG_DIR . '/pdf_errors.log', $errorMessage, FILE_APPEND);

        // Încercăm să folosim TCPDF ca alternativă
        $logMessage = date('Y-m-d H:i:s') . " - Se încearcă generarea PDF cu TCPDF ca alternativă\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);

        try {
            exportToPdfWithTCPDF($content, $filename);
        } catch (Exception $tcpdfError) {
            // Logăm eroarea TCPDF
            $errorMessage = date('Y-m-d H:i:s') . " - Eroare și la generarea PDF cu TCPDF: " . $tcpdfError->getMessage() . "\n";
            file_put_contents(LOG_DIR . '/pdf_errors.log', $errorMessage, FILE_APPEND);

            // Afișăm un mesaj de eroare detaliat
            echo '<div style="color: red; font-weight: bold; margin: 20px; padding: 20px; border: 1px solid red;">';
            echo '<h2>Eroare la generarea PDF-ului</h2>';
            echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '<p>S-a încercat și generarea cu TCPDF, dar a eșuat: ' . htmlspecialchars($tcpdfError->getMessage()) . '</p>';
            echo '<p>Detalii tehnice au fost înregistrate pentru depanare.</p>';
            echo '<p><a href="javascript:history.back()">Înapoi</a></p>';
            echo '</div>';
            exit;
        }
    }
}

/**
 * Exportă conținutul în format HTML
 *
 * @param string $content Conținutul HTML
 * @param string $filename Numele fișierului (fără extensie)
 */
function exportToHtml($content, $filename)
{
    try {
        // Curățăm orice output anterior
        if (ob_get_length()) {
            ob_clean();
        }

        // Logăm începerea procesului
        $logMessage = date('Y-m-d H:i:s') . " - Începerea exportului HTML pentru {$filename}\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);

        // Îmbunătățim conținutul HTML pentru export
        $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]";

        // Înlocuim căile relative cu cele absolute pentru imagini și CSS
        $content = preg_replace('/(src|href)="(?!http|https|ftp|mailto|data:)([^"]+)"/i', "$1=\"$baseUrl/$2\"", $content);

        // Adăugăm metadate și stiluri pentru printare
        $printStyles = '
        <style>
            @media print {
                body {
                    font-family: Arial, sans-serif;
                    font-size: 12pt;
                    line-height: 1.5;
                    color: #000;
                    background: #fff;
                }

                a {
                    color: #000;
                    text-decoration: none;
                }

                .no-print, .dropdown, .btn, .modal {
                    display: none !important;
                }

                .card {
                    border: 1px solid #ddd;
                    margin-bottom: 20px;
                    page-break-inside: avoid;
                }

                .card-header {
                    background-color: #f8f9fa !important;
                    color: #000 !important;
                    font-weight: bold;
                    padding: 10px;
                }

                .card-body {
                    padding: 10px;
                }

                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }

                th, td {
                    padding: 8px;
                    border: 1px solid #ddd;
                    text-align: left;
                }

                th {
                    background-color: #f8f9fa;
                    font-weight: bold;
                }

                @page {
                    margin: 2cm;
                }

                h1, h2, h3, h4, h5, h6 {
                    page-break-after: avoid;
                }

                table, figure {
                    page-break-inside: avoid;
                }

                img {
                    max-width: 100% !important;
                }
            }
        </style>';

        // Adăugăm stilurile de printare în head
        $content = preg_replace('/<\/head>/', "$printStyles</head>", $content);

        // Adăugăm metadate pentru export
        $metadata = '
        <meta name="generator" content="Portal Judiciar">
        <meta name="date" content="' . date('Y-m-d') . '">
        <meta name="author" content="Portal Judiciar">
        ';

        $content = preg_replace('/<\/head>/', "$metadata</head>", $content);

        // Trimitem HTML-ul către browser
        header('Content-Type: text/html; charset=utf-8');
        header("Content-Disposition: attachment; filename=\"$filename.html\"");
        header('Cache-Control: max-age=0');
        header('Pragma: public');

        // Afișăm conținutul
        echo $content;

        // Logăm succesul
        $logMessage = date('Y-m-d H:i:s') . " - Export HTML finalizat cu succes pentru {$filename}\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);

        exit;
    } catch (Exception $e) {
        // Logăm eroarea
        $errorMessage = date('Y-m-d H:i:s') . " - Eroare la exportul HTML: " . $e->getMessage() . "\n";
        file_put_contents(LOG_DIR . '/pdf_errors.log', $errorMessage, FILE_APPEND);

        // Afișăm un mesaj de eroare detaliat
        echo '<div style="color: red; font-weight: bold; margin: 20px; padding: 20px; border: 1px solid red;">';
        echo '<h2>Eroare la exportul HTML</h2>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p>Detalii tehnice au fost înregistrate pentru depanare.</p>';
        echo '<p><a href="javascript:history.back()">Înapoi</a></p>';
        echo '</div>';
        exit;
    }
}
