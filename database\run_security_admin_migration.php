<?php

/**
 * Portal Judiciar România - Security Admin Migration Runner
 * 
 * This script runs migration 005 to create the security administration tables
 * required for the admin security page functionality.
 * 
 * Tables created:
 * - system_settings: Configuration settings for security policies
 * - login_attempts: Login attempt tracking for security monitoring
 * - ip_whitelist: IP whitelist for access control
 * 
 * <AUTHOR> Judiciar Team
 * @version 1.0.0
 */

// Include the bootstrap and configuration
require_once dirname(__DIR__) . '/bootstrap.php';
require_once dirname(__DIR__) . '/includes/config.php';

use App\Config\Database;

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔧 Portal Judiciar România - Security Admin Migration Runner\n";
echo "============================================================\n\n";

try {
    // Check if we're running from command line
    if (php_sapi_name() !== 'cli') {
        echo "⚠️  This script should be run from the command line for security reasons.\n";
        echo "However, proceeding with web execution for development...\n\n";
    }

    // Test database connection
    echo "📡 Testing database connection...\n";
    $testQuery = Database::fetchOne("SELECT 1 as test");
    if ($testQuery && $testQuery['test'] == 1) {
        echo "✅ Database connection successful\n\n";
    } else {
        throw new Exception("Database connection test failed");
    }

    // Check current migration status
    echo "📋 Checking migration status...\n";
    try {
        $currentMigrations = Database::fetchAll("SELECT version, applied_at FROM schema_migrations ORDER BY version");
        echo "Current migrations:\n";
        foreach ($currentMigrations as $migration) {
            echo "  ✅ Migration {$migration['version']} - Applied: {$migration['applied_at']}\n";
        }
    } catch (Exception $e) {
        echo "⚠️  Could not read migration status: " . $e->getMessage() . "\n";
    }
    echo "\n";

    // Check if migration 005 already exists
    $migration005 = Database::fetchOne("SELECT version FROM schema_migrations WHERE version = '005'");
    if ($migration005) {
        echo "ℹ️  Migration 005 already applied. Re-running for safety...\n\n";
    }

    // Read and execute the migration file
    echo "🚀 Running migration 005: Security Admin Tables...\n";
    $migrationFile = __DIR__ . '/migrations/005_create_security_admin_tables.sql';
    
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }

    $migrationSQL = file_get_contents($migrationFile);
    if ($migrationSQL === false) {
        throw new Exception("Could not read migration file: $migrationFile");
    }

    // Split the SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $migrationSQL)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );

    echo "📝 Found " . count($statements) . " SQL statements to execute\n\n";

    // Execute each statement
    $successCount = 0;
    $errorCount = 0;

    foreach ($statements as $index => $statement) {
        try {
            // Skip empty statements and comments
            if (empty(trim($statement)) || preg_match('/^\s*--/', $statement)) {
                continue;
            }

            echo "Executing statement " . ($index + 1) . "... ";
            
            // Execute the statement
            Database::execute($statement);
            
            echo "✅ Success\n";
            $successCount++;
            
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "\n";
            $errorCount++;
            
            // Log the error but continue with other statements
            error_log("Migration 005 error in statement " . ($index + 1) . ": " . $e->getMessage());
        }
    }

    echo "\n📊 Migration Results:\n";
    echo "  ✅ Successful statements: $successCount\n";
    echo "  ❌ Failed statements: $errorCount\n\n";

    if ($errorCount === 0) {
        echo "🎉 Migration 005 completed successfully!\n\n";
        
        // Verify tables were created
        echo "🔍 Verifying created tables:\n";
        $tables = [
            'system_settings',
            'login_attempts', 
            'ip_whitelist'
        ];
        
        foreach ($tables as $table) {
            try {
                $result = Database::fetchOne("SHOW TABLES LIKE '$table'");
                if ($result) {
                    echo "  ✅ Table '$table' exists\n";
                    
                    // Show row count
                    $count = Database::fetchOne("SELECT COUNT(*) as count FROM $table");
                    echo "     📊 Contains {$count['count']} rows\n";
                } else {
                    echo "  ❌ Table '$table' not found\n";
                }
            } catch (Exception $e) {
                echo "  ❌ Error checking table '$table': " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n🔍 Verifying created views:\n";
        $views = [
            'active_security_settings',
            'recent_failed_logins',
            'security_incident_summary'
        ];
        
        foreach ($views as $view) {
            try {
                $result = Database::fetchOne("SHOW TABLES LIKE '$view'");
                if ($result) {
                    echo "  ✅ View '$view' exists\n";
                } else {
                    echo "  ❌ View '$view' not found\n";
                }
            } catch (Exception $e) {
                echo "  ❌ Error checking view '$view': " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n🔍 Verifying security settings:\n";
        try {
            $securitySettings = Database::fetchAll("SELECT setting_key, setting_value FROM system_settings WHERE category = 'security' LIMIT 5");
            foreach ($securitySettings as $setting) {
                echo "  ✅ {$setting['setting_key']}: {$setting['setting_value']}\n";
            }
            echo "  ... and more\n";
        } catch (Exception $e) {
            echo "  ❌ Error reading security settings: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "⚠️  Migration completed with errors. Please check the logs.\n";
    }

} catch (Exception $e) {
    echo "💥 Fatal error during migration: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}

echo "\n✨ Migration process completed!\n";
echo "You can now access the admin security page at: http://localhost/just/public/admin/security.php\n";
?>
