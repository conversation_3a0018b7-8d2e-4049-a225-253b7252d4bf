<?php

/**
 * Portal Judiciar - Pagina principală
 */

// Încărcăm bootstrap-ul aplicației
require_once dirname(__DIR__) . '/bootstrap.php';

// Importăm clasele necesare
use App\Helpers\TemplateEngine;
use App\Services\DosarService;

// Inițializăm motorul de șabloane
$templateEngine = new TemplateEngine();

// Obținem lista instanțelor din funcția centralizată
require_once '../includes/functions.php';
$instante = getInstanteList();

// Datele pentru șablon
$data = [
    'instante' => $instante
];

// Afișăm șablonul
echo $templateEngine->render('index.twig', $data);
