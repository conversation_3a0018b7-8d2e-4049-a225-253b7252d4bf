-- Security Administration Tables
-- Portal Judiciar <PERSON>ânia - Case Monitoring System
-- Migration 005: Security Administration Features

-- System settings table for security configuration
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE COMMENT 'Setting identifier',
    setting_value TEXT NOT NULL COMMENT 'Setting value',
    category VARCHAR(50) NOT NULL DEFAULT 'general' COMMENT 'Setting category (security, general, etc.)',
    description TEXT NULL COMMENT 'Human-readable description',
    data_type ENUM('string', 'integer', 'boolean', 'json') NOT NULL DEFAULT 'string' COMMENT 'Data type for validation',
    is_public TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'Whether setting can be read by non-admin users',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category (category),
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='System configuration settings';

-- Login attempts table for security monitoring
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NULL COMMENT 'Email address used for login attempt',
    ip_address VARCHAR(45) NOT NULL COMMENT 'IP address of the attempt',
    user_agent TEXT NULL COMMENT 'User agent string',
    success TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'Whether the login was successful',
    failure_reason VARCHAR(100) NULL COMMENT 'Reason for failure (invalid_password, account_locked, etc.)',
    user_id INT UNSIGNED NULL COMMENT 'User ID if login was successful',
    session_id VARCHAR(128) NULL COMMENT 'Session ID if login was successful',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_email (email),
    INDEX idx_ip_address (ip_address),
    INDEX idx_success (success),
    INDEX idx_created_at (created_at),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Login attempts tracking for security monitoring';

-- IP whitelist table for access control
CREATE TABLE IF NOT EXISTS ip_whitelist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL COMMENT 'IP address or CIDR range',
    description TEXT NULL COMMENT 'Description of the IP address',
    created_by INT UNSIGNED NOT NULL COMMENT 'Admin user who added this IP',
    is_active TINYINT(1) NOT NULL DEFAULT 1 COMMENT 'Whether this whitelist entry is active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    UNIQUE KEY unique_active_ip (ip_address, is_active),
    INDEX idx_ip_address (ip_address),
    INDEX idx_is_active (is_active),
    INDEX idx_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='IP whitelist for access control';

-- Add resolution field to security_incidents table if it doesn't exist
ALTER TABLE security_incidents 
ADD COLUMN resolution TEXT NULL COMMENT 'Resolution details for the incident' AFTER notes;

-- Insert default security settings
INSERT IGNORE INTO system_settings (setting_key, setting_value, category, description, data_type) VALUES
('password_min_length', '8', 'security', 'Lungimea minimă a parolei', 'integer'),
('password_require_uppercase', '1', 'security', 'Necesită litere mari în parolă', 'boolean'),
('password_require_lowercase', '1', 'security', 'Necesită litere mici în parolă', 'boolean'),
('password_require_numbers', '1', 'security', 'Necesită cifre în parolă', 'boolean'),
('password_require_symbols', '1', 'security', 'Necesită simboluri în parolă', 'boolean'),
('session_timeout', '3600', 'security', 'Timeout sesiune în secunde', 'integer'),
('max_login_attempts', '5', 'security', 'Numărul maxim de încercări de login', 'integer'),
('lockout_duration', '900', 'security', 'Durata blocării contului în secunde', 'integer'),
('csrf_token_lifetime', '3600', 'security', 'Durata de viață a token-urilor CSRF în secunde', 'integer'),
('csrf_strict_mode', '1', 'security', 'Mod strict pentru validarea CSRF', 'boolean'),
('rate_limit_login', '5', 'security', 'Limite rate pentru login per oră', 'integer'),
('rate_limit_api', '100', 'security', 'Limite rate pentru API per oră', 'integer'),
('rate_limit_contact', '5', 'security', 'Limite rate pentru formulare contact per oră', 'integer'),
('enable_2fa', '0', 'security', 'Activează autentificarea cu doi factori', 'boolean'),
('require_email_verification', '1', 'security', 'Necesită verificarea email-ului', 'boolean'),
('enable_ip_whitelist', '0', 'security', 'Activează whitelist-ul de IP-uri', 'boolean'),
('enable_geo_blocking', '0', 'security', 'Activează blocarea geografică', 'boolean'),
('security_log_retention_days', '90', 'security', 'Numărul de zile pentru păstrarea log-urilor de securitate', 'integer'),
('failed_login_notification_threshold', '10', 'security', 'Pragul pentru notificări de login eșuat', 'integer'),
('auto_ban_threshold', '20', 'security', 'Pragul pentru ban automat pe IP', 'integer'),
('maintenance_mode', '0', 'general', 'Mod mentenanță activat', 'boolean'),
('site_name', 'Portal Judiciar România', 'general', 'Numele site-ului', 'string'),
('admin_email', '<EMAIL>', 'general', 'Email-ul administratorului', 'string'),
('backup_retention_days', '30', 'general', 'Numărul de zile pentru păstrarea backup-urilor', 'integer');

-- Insert sample IP whitelist entries (localhost and common development IPs)
INSERT IGNORE INTO ip_whitelist (ip_address, description, created_by, is_active) VALUES
('127.0.0.1', 'Localhost - Development', 1, 1),
('::1', 'IPv6 Localhost', 1, 1),
('***********/24', 'Local Network Range', 1, 1);

-- Create indexes for performance optimization
CREATE INDEX idx_system_settings_category_key ON system_settings(category, setting_key);
CREATE INDEX idx_login_attempts_ip_created ON login_attempts(ip_address, created_at);
CREATE INDEX idx_login_attempts_email_created ON login_attempts(email, created_at);

-- Create a view for active security settings
CREATE OR REPLACE VIEW active_security_settings AS
SELECT setting_key, setting_value, description, updated_at
FROM system_settings 
WHERE category = 'security'
ORDER BY setting_key;

-- Create a view for recent failed login attempts
CREATE OR REPLACE VIEW recent_failed_logins AS
SELECT 
    email,
    ip_address,
    user_agent,
    failure_reason,
    created_at,
    COUNT(*) OVER (PARTITION BY ip_address ORDER BY created_at RANGE BETWEEN INTERVAL 1 HOUR PRECEDING AND CURRENT ROW) as attempts_last_hour,
    COUNT(*) OVER (PARTITION BY email ORDER BY created_at RANGE BETWEEN INTERVAL 1 HOUR PRECEDING AND CURRENT ROW) as email_attempts_last_hour
FROM login_attempts 
WHERE success = 0 
  AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY created_at DESC;

-- Create a view for security incident summary
CREATE OR REPLACE VIEW security_incident_summary AS
SELECT 
    DATE(created_at) as incident_date,
    incident_type,
    severity,
    COUNT(*) as incident_count,
    COUNT(CASE WHEN resolved = 1 THEN 1 END) as resolved_count,
    COUNT(CASE WHEN resolved = 0 THEN 1 END) as unresolved_count
FROM security_incidents
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at), incident_type, severity
ORDER BY incident_date DESC, severity DESC;

-- Log the migration
INSERT INTO data_processing_logs (user_id, action, context, created_at)
VALUES (NULL, 'security_admin_migration_applied', 
        JSON_OBJECT(
            'migration', '005_create_security_admin_tables', 
            'tables_created', 3, 
            'views_created', 3,
            'settings_inserted', 22,
            'whitelist_entries', 3
        ),
        NOW());

-- Update migration tracking
INSERT INTO schema_migrations (version, applied_at) 
VALUES ('005', NOW()) 
ON DUPLICATE KEY UPDATE applied_at = NOW();
