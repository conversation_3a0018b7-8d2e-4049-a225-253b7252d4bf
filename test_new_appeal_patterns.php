<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 TESTING NEW APPEAL PATTERNS\n";
echo "==============================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    // Get the solutieSumar content
    $solutieSumarText = '';
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $i => $sedinta) {
            if (!empty($sedinta['solutieSumar'])) {
                $solutieSumarText = $sedinta['solutieSumar'];
                break;
            }
        }
    }
    
    if (empty($solutieSumarText)) {
        echo "❌ No solutieSumar text found\n";
        exit(1);
    }
    
    echo "📄 SolutieSumar text length: " . strlen($solutieSumarText) . " characters\n\n";
    
    // Test Pattern 1: "Anulează" section
    echo "🔍 TESTING ANULEAZĂ PATTERN:\n";
    echo "============================\n\n";
    
    if (preg_match('/Anulează\s+apelurile\s+formulate\s+de\s+apelanţii\s+([^.]+?)(?:\s*ca\s+(?:netimbrate|nefondate))?\./', $solutieSumarText, $anuleazaMatch)) {
        $apellantsText = $anuleazaMatch[1];
        echo "Found Anulează section:\n";
        echo "Length: " . strlen($apellantsText) . " characters\n";
        echo "Preview: \"" . substr($apellantsText, 0, 200) . "...\"\n\n";
        
        $apellantNames = explode(',', $apellantsText);
        $validNames = 0;
        
        foreach ($apellantNames as $apellantName) {
            $apellantName = trim($apellantName);
            $apellantName = preg_replace('/\s*şi\s*$/', '', $apellantName);
            $apellantName = preg_replace('/\s*\?.*$/', '', $apellantName);
            $apellantName = preg_replace('/\s*\(.*?\)/', '', $apellantName);
            $apellantName = trim($apellantName);
            
            if (strlen($apellantName) >= 3 && preg_match('/^[A-Za-zĂÂÎȘȚăâîșțţ0-9][A-Za-zĂÂÎȘȚăâîșțţ0-9\s\-\.\(\)\/]+$/u', $apellantName)) {
                $validNames++;
            }
        }
        
        echo "Comma-separated parts: " . count($apellantNames) . "\n";
        echo "Valid names: {$validNames}\n\n";
    } else {
        echo "❌ Anulează pattern not found\n\n";
    }
    
    // Test Pattern 2: "Respinge" section with typo tolerance
    echo "🔍 TESTING RESPINGE PATTERN:\n";
    echo "============================\n\n";
    
    if (preg_match('/Respinge\s+apelurile\s+formulate\s+de\s+apelan[ţ?]ii\s+(.+?)(?:\s*ca\s+(?:netimbrate|nefondate))?(?:\.\s*Admite|$)/s', $solutieSumarText, $respingeMatch)) {
        $apellantsText = $respingeMatch[1];
        echo "Found Respinge section:\n";
        echo "Raw length: " . strlen($apellantsText) . " characters\n";
        echo "Raw preview: \"" . substr($apellantsText, 0, 300) . "...\"\n\n";
        
        // Apply cleanup
        $cleanedText = $apellantsText;
        $cleanedText = preg_replace('/\.\s*[A-Z][^,]*(?:Georgeta|Elisabeta|Marioara|Anişoara|Florica|Steliana|Florenţa|Sorin)[^,]*/', '', $cleanedText);
        $cleanedText = preg_replace('/\s*ca\s+(?:netimbrate|nefondate)\s*/', '', $cleanedText);
        
        echo "After cleanup length: " . strlen($cleanedText) . " characters\n";
        echo "After cleanup preview: \"" . substr($cleanedText, 0, 300) . "...\"\n\n";
        
        $apellantNames = explode(',', $cleanedText);
        $validNames = 0;
        
        foreach ($apellantNames as $apellantName) {
            $apellantName = trim($apellantName);
            $apellantName = preg_replace('/\s*şi\s*$/', '', $apellantName);
            $apellantName = preg_replace('/\s*\?.*$/', '', $apellantName);
            $apellantName = preg_replace('/\s*\(.*?\)/', '', $apellantName);
            $apellantName = trim($apellantName);
            
            if (strlen($apellantName) >= 3 && preg_match('/^[A-Za-zĂÂÎȘȚăâîșțţ0-9][A-Za-zĂÂÎȘȚăâîșțţ0-9\s\-\.\(\)\/]+$/u', $apellantName)) {
                $validNames++;
            }
        }
        
        echo "Comma-separated parts: " . count($apellantNames) . "\n";
        echo "Valid names: {$validNames}\n\n";
        
        // Show first 10 names for debugging
        echo "First 10 valid names:\n";
        $count = 0;
        foreach ($apellantNames as $apellantName) {
            $apellantName = trim($apellantName);
            $apellantName = preg_replace('/\s*şi\s*$/', '', $apellantName);
            $apellantName = preg_replace('/\s*\?.*$/', '', $apellantName);
            $apellantName = preg_replace('/\s*\(.*?\)/', '', $apellantName);
            $apellantName = trim($apellantName);
            
            if (strlen($apellantName) >= 3 && preg_match('/^[A-Za-zĂÂÎȘȚăâîșțţ0-9][A-Za-zĂÂÎȘȚăâîșțţ0-9\s\-\.\(\)\/]+$/u', $apellantName)) {
                $count++;
                if ($count <= 10) {
                    echo "  {$count}. {$apellantName}\n";
                }
            }
        }
        
    } else {
        echo "❌ Respinge pattern not found\n\n";
    }
    
    echo "✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
