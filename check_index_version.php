<?php
/**
 * Script pentru verificarea versiunii și modificărilor din index.php
 * Portal Judiciar România - Debug Tool
 */

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificare Versiune index.php - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .warning { color: #ffc107; font-weight: bold; }
        .info { color: #17a2b8; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-file-code me-2"></i>
            Verificare Versiune index.php
        </h1>
        
        <?php
        $indexFile = 'index.php';
        
        if (!file_exists($indexFile)) {
            echo '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>Fișierul index.php nu a fost găsit!</div>';
            exit;
        }
        
        // Informații despre fișier
        $fileInfo = [
            'size' => filesize($indexFile),
            'modified' => date('Y-m-d H:i:s', filemtime($indexFile)),
            'permissions' => substr(sprintf('%o', fileperms($indexFile)), -4)
        ];
        
        echo '<div class="alert alert-info">';
        echo '<h5><i class="fas fa-info-circle me-2"></i>Informații Fișier</h5>';
        echo '<ul class="mb-0">';
        echo '<li><strong>Mărime:</strong> ' . number_format($fileInfo['size']) . ' bytes</li>';
        echo '<li><strong>Ultima modificare:</strong> ' . $fileInfo['modified'] . '</li>';
        echo '<li><strong>Permisiuni:</strong> ' . $fileInfo['permissions'] . '</li>';
        echo '</ul>';
        echo '</div>';
        
        // Citește conținutul fișierului
        $content = file_get_contents($indexFile);
        $lines = explode("\n", $content);
        $totalLines = count($lines);
        
        echo '<div class="alert alert-primary">';
        echo '<h5><i class="fas fa-file-alt me-2"></i>Statistici Conținut</h5>';
        echo '<ul class="mb-0">';
        echo '<li><strong>Total linii:</strong> ' . number_format($totalLines) . '</li>';
        echo '<li><strong>Mărime conținut:</strong> ' . number_format(strlen($content)) . ' caractere</li>';
        echo '</ul>';
        echo '</div>';
        
        // Verifică funcțiile JavaScript
        echo '<div class="row">';
        echo '<div class="col-md-6">';
        echo '<h5><i class="fas fa-search me-2"></i>Verificare Funcții JavaScript</h5>';
        
        $functions = [
            'showNotification' => 'function showNotification(',
            'expandAllResults' => 'function expandAllResults(',
            'collapseAllResults' => 'function collapseAllResults(',
            'toggleTermResults' => 'function toggleTermResults('
        ];
        
        foreach ($functions as $funcName => $searchPattern) {
            $found = strpos($content, $searchPattern) !== false;
            $class = $found ? 'success' : 'error';
            $icon = $found ? 'check' : 'times';
            echo '<div class="' . $class . '"><i class="fas fa-' . $icon . ' me-2"></i>' . $funcName . ': ' . ($found ? 'GĂSITĂ' : 'LIPSEȘTE') . '</div>';
            
            if ($found) {
                // Găsește linia
                $lineNumber = 0;
                foreach ($lines as $index => $line) {
                    if (strpos($line, $searchPattern) !== false) {
                        $lineNumber = $index + 1;
                        break;
                    }
                }
                echo '<div class="text-muted ms-4">Linia: ' . $lineNumber . '</div>';
            }
        }
        echo '</div>';
        
        echo '<div class="col-md-6">';
        echo '<h5><i class="fas fa-code me-2"></i>Verificare Elemente HTML</h5>';
        
        $elements = [
            'notificationContainer' => 'id="notificationContainer"',
            'Expandează toate button' => 'onclick="expandAllResults()"',
            'Restrânge toate button' => 'onclick="collapseAllResults()"'
        ];
        
        foreach ($elements as $elemName => $searchPattern) {
            $found = strpos($content, $searchPattern) !== false;
            $class = $found ? 'success' : 'error';
            $icon = $found ? 'check' : 'times';
            echo '<div class="' . $class . '"><i class="fas fa-' . $icon . ' me-2"></i>' . $elemName . ': ' . ($found ? 'GĂSIT' : 'LIPSEȘTE') . '</div>';
        }
        echo '</div>';
        echo '</div>';
        
        // Verifică zona cu funcțiile JavaScript
        echo '<h5 class="mt-4"><i class="fas fa-code me-2"></i>Conținut Funcții JavaScript (liniile 4980-5120)</h5>';
        
        $startLine = 4980;
        $endLine = 5120;
        
        if ($totalLines >= $endLine) {
            echo '<div class="code-block">';
            for ($i = $startLine - 1; $i < min($endLine, $totalLines); $i++) {
                $lineNum = $i + 1;
                $line = htmlspecialchars($lines[$i]);
                
                // Evidențiază liniile importante
                $highlight = '';
                if (strpos($line, 'function ') !== false) {
                    $highlight = ' style="background-color: #fff3cd;"';
                } elseif (strpos($line, 'onclick=') !== false) {
                    $highlight = ' style="background-color: #d1ecf1;"';
                }
                
                echo '<span' . $highlight . '>' . str_pad($lineNum, 4, '0', STR_PAD_LEFT) . ': ' . $line . '</span>' . "\n";
            }
            echo '</div>';
        } else {
            echo '<div class="alert alert-warning">Fișierul are doar ' . $totalLines . ' linii, nu poate afișa liniile ' . $startLine . '-' . $endLine . '</div>';
        }
        
        // Test rapid de funcționalitate
        echo '<div class="mt-4">';
        echo '<h5><i class="fas fa-play me-2"></i>Test Rapid</h5>';
        echo '<div class="alert alert-warning">';
        echo '<h6>Instrucțiuni pentru testare:</h6>';
        echo '<ol>';
        echo '<li>Deschide <a href="index.php" target="_blank" class="btn btn-sm btn-primary">index.php</a> într-o fereastră nouă</li>';
        echo '<li>Apasă F12 pentru a deschide Developer Tools</li>';
        echo '<li>Mergi la tab-ul Console</li>';
        echo '<li>Efectuează o căutare cu mai mulți termeni</li>';
        echo '<li>Testează butoanele "Expandează toate" și "Restrânge toate"</li>';
        echo '<li>Verifică dacă apar erori în consolă</li>';
        echo '</ol>';
        echo '</div>';
        echo '</div>';
        
        // Verifică cache-ul browser
        echo '<div class="mt-4">';
        echo '<h5><i class="fas fa-sync me-2"></i>Verificare Cache Browser</h5>';
        echo '<div class="alert alert-info">';
        echo '<h6>Pentru a elimina cache-ul browser:</h6>';
        echo '<ul>';
        echo '<li><strong>Chrome/Edge:</strong> Ctrl+Shift+R sau F12 → Network → Disable cache</li>';
        echo '<li><strong>Firefox:</strong> Ctrl+Shift+R sau F12 → Network → Settings → Disable cache</li>';
        echo '<li><strong>Safari:</strong> Cmd+Option+R sau Develop → Empty Caches</li>';
        echo '</ul>';
        echo '</div>';
        echo '</div>';
        ?>
        
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-primary me-2">
                <i class="fas fa-external-link-alt me-1"></i>
                Deschide index.php
            </a>
            <a href="test_simple_buttons.html" class="btn btn-success me-2">
                <i class="fas fa-vial me-1"></i>
                Test Funcții Izolate
            </a>
            <button onclick="location.reload()" class="btn btn-secondary">
                <i class="fas fa-sync me-1"></i>
                Reîmprospătează
            </button>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
