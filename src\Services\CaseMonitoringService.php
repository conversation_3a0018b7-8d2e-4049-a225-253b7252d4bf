<?php

namespace App\Services;

use App\Config\Database;
use App\Services\DosarService;
use App\Services\NotificationManager;
use Exception;

/**
 * Case Monitoring Service
 * 
 * Handles case monitoring operations including adding/removing cases,
 * checking for changes, and managing monitoring preferences.
 */
class CaseMonitoringService
{
    private $dosarService;
    private $notificationManager;

    public function __construct()
    {
        $this->dosarService = new DosarService();
        $this->notificationManager = new NotificationManager();
    }

    /**
     * Add a case to monitoring for a user
     * 
     * @param int $userId User ID
     * @param string $caseNumber Case number
     * @param string $institutionCode Institution code
     * @param string $institutionName Institution name
     * @param string $caseObject Case object/description
     * @param string $monitoringReason Reason for monitoring
     * @param string $notificationFrequency Notification frequency
     * @return int Monitored case ID
     * @throws Exception If case cannot be added
     */
    public function addCaseToMonitoring(
        int $userId,
        string $caseNumber,
        string $institutionCode,
        string $institutionName,
        string $caseObject = '',
        string $monitoringReason = '',
        string $notificationFrequency = 'daily'
    ): int {
        try {
            // Check if user has reached the maximum number of monitored cases
            $currentCount = $this->getUserMonitoredCasesCount($userId);
            if ($currentCount >= MAX_MONITORED_CASES_PER_USER) {
                throw new Exception("Ați atins numărul maxim de dosare monitorizate (" . MAX_MONITORED_CASES_PER_USER . ").");
            }

            // Check if case is already being monitored by this user
            if ($this->isCaseMonitored($userId, $caseNumber, $institutionCode)) {
                throw new Exception("Acest dosar este deja monitorizat.");
            }

            // Verify case exists in judicial system
            $caseData = $this->dosarService->getDetaliiDosar($caseNumber, $institutionCode);
            if (!$caseData) {
                throw new Exception("Dosarul nu a fost găsit în sistemul judiciar.");
            }

            Database::beginTransaction();

            // Insert monitored case
            $monitoredCaseId = Database::insert('monitored_cases', [
                'user_id' => $userId,
                'case_number' => $caseNumber,
                'institution_code' => $institutionCode,
                'institution_name' => $institutionName,
                'case_object' => $caseObject ?: $caseData->obiect,
                'monitoring_reason' => $monitoringReason,
                'notification_frequency' => $notificationFrequency,
                'last_checked' => date('Y-m-d H:i:s'),
                'is_active' => 1
            ]);

            // Create initial snapshot
            $snapshotId = $this->createCaseSnapshot($monitoredCaseId, $caseData);

            // Log the change
            $changeId = Database::insert('case_changes', [
                'monitored_case_id' => $monitoredCaseId,
                'old_snapshot_id' => null,
                'new_snapshot_id' => $snapshotId,
                'change_type' => 'other',
                'change_description' => 'Dosar adăugat în monitorizare',
                'change_details' => json_encode([
                    'action' => 'case_added',
                    'timestamp' => date('Y-m-d H:i:s')
                ])
            ]);

            // Queue notification for immediate types
            if ($notificationFrequency === 'immediate') {
                $this->notificationManager->queueCaseAddedNotification($userId, $monitoredCaseId, $changeId);
            }

            Database::commit();

            // Log system event
            $this->logSystemEvent('info', 'Case added to monitoring', [
                'user_id' => $userId,
                'case_number' => $caseNumber,
                'institution' => $institutionCode
            ], $userId);

            return $monitoredCaseId;

        } catch (Exception $e) {
            if (Database::inTransaction()) {
                Database::rollback();
            }
            throw $e;
        }
    }

    /**
     * Remove a case from monitoring
     * 
     * @param int $userId User ID
     * @param int $monitoredCaseId Monitored case ID
     * @return bool Success status
     */
    public function removeCaseFromMonitoring(int $userId, int $monitoredCaseId): bool
    {
        try {
            // Verify ownership
            $case = Database::fetchOne(
                "SELECT * FROM monitored_cases WHERE id = ? AND user_id = ?",
                [$monitoredCaseId, $userId]
            );

            if (!$case) {
                throw new Exception("Dosarul nu a fost găsit sau nu aveți permisiunea să îl ștergeți.");
            }

            Database::beginTransaction();

            // Soft delete - mark as inactive
            Database::update('monitored_cases', 
                ['is_active' => 0, 'updated_at' => date('Y-m-d H:i:s')],
                ['id' => $monitoredCaseId, 'user_id' => $userId]
            );

            // Cancel pending notifications
            Database::update('notification_queue',
                ['status' => 'cancelled'],
                ['monitored_case_id' => $monitoredCaseId, 'status' => 'pending']
            );

            Database::commit();

            // Log system event
            $this->logSystemEvent('info', 'Case removed from monitoring', [
                'user_id' => $userId,
                'case_number' => $case['case_number'],
                'institution' => $case['institution_code']
            ], $userId);

            return true;

        } catch (Exception $e) {
            if (Database::inTransaction()) {
                Database::rollback();
            }
            throw $e;
        }
    }

    /**
     * Check for changes in monitored cases
     * 
     * @param int|null $userId Optional user ID to check specific user's cases
     * @param int|null $limit Maximum number of cases to check
     * @return array Summary of changes detected
     */
    public function checkForCaseChanges(?int $userId = null, ?int $limit = null): array
    {
        $sql = "SELECT * FROM monitored_cases WHERE is_active = 1";
        $params = [];

        if ($userId) {
            $sql .= " AND user_id = ?";
            $params[] = $userId;
        }

        // Prioritize cases that haven't been checked recently
        $sql .= " ORDER BY last_checked ASC";

        if ($limit) {
            $sql .= " LIMIT ?";
            $params[] = $limit;
        }

        $cases = Database::fetchAll($sql, $params);
        $summary = [
            'checked' => 0,
            'changes_detected' => 0,
            'errors' => 0,
            'notifications_queued' => 0
        ];

        foreach ($cases as $case) {
            try {
                $summary['checked']++;
                
                // Get current case data from SOAP API
                $currentData = $this->dosarService->getDetaliiDosar(
                    $case['case_number'], 
                    $case['institution_code']
                );

                if (!$currentData) {
                    $this->logSystemEvent('warning', 'Case not found in judicial system', [
                        'case_number' => $case['case_number'],
                        'institution' => $case['institution_code']
                    ]);
                    continue;
                }

                // Get latest snapshot
                $latestSnapshot = Database::fetchOne(
                    "SELECT * FROM case_snapshots WHERE monitored_case_id = ? ORDER BY created_at DESC LIMIT 1",
                    [$case['id']]
                );

                // Create new snapshot
                $newSnapshotId = $this->createCaseSnapshot($case['id'], $currentData);

                // Compare with previous snapshot
                if ($latestSnapshot) {
                    $changes = $this->detectChanges($latestSnapshot, $currentData);
                    
                    if (!empty($changes)) {
                        $summary['changes_detected']++;
                        
                        // Record changes and queue notifications
                        foreach ($changes as $change) {
                            $changeId = Database::insert('case_changes', [
                                'monitored_case_id' => $case['id'],
                                'old_snapshot_id' => $latestSnapshot['id'],
                                'new_snapshot_id' => $newSnapshotId,
                                'change_type' => $change['type'],
                                'change_description' => $change['description'],
                                'change_details' => json_encode($change['details'])
                            ]);

                            // Queue notification based on user preferences
                            if ($case['notification_frequency'] === 'immediate') {
                                $this->notificationManager->queueChangeNotification(
                                    $case['user_id'], 
                                    $case['id'], 
                                    $changeId
                                );
                                $summary['notifications_queued']++;
                            }
                        }
                    }
                }

                // Update last checked timestamp
                Database::update('monitored_cases',
                    ['last_checked' => date('Y-m-d H:i:s')],
                    ['id' => $case['id']]
                );

            } catch (Exception $e) {
                $summary['errors']++;
                $this->logSystemEvent('error', 'Error checking case for changes', [
                    'case_number' => $case['case_number'],
                    'institution' => $case['institution_code'],
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $summary;
    }

    /**
     * Get user's monitored cases
     *
     * @param int $userId User ID
     * @param bool $activeOnly Whether to return only active cases
     * @return array List of monitored cases
     */
    public function getUserMonitoredCases(int $userId, bool $activeOnly = true): array
    {
        try {
            $sql = "SELECT mc.*,
                           (SELECT COUNT(*) FROM case_changes cc WHERE cc.monitored_case_id = mc.id) as changes_count,
                           (SELECT MAX(cc.detected_at) FROM case_changes cc WHERE cc.monitored_case_id = mc.id) as last_change
                    FROM monitored_cases mc
                    WHERE mc.user_id = ?";

            $params = [$userId];

            if ($activeOnly) {
                $sql .= " AND mc.is_active = 1";
            }

            $sql .= " ORDER BY mc.created_at DESC";

            return Database::fetchAll($sql, $params);

        } catch (Exception $e) {
            // Log error and return empty array if monitoring tables don't exist
            error_log("Failed to get user monitored cases: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get monitored cases count for a user
     * 
     * @param int $userId User ID
     * @return int Number of monitored cases
     */
    public function getUserMonitoredCasesCount(int $userId): int
    {
        $result = Database::fetchOne(
            "SELECT COUNT(*) as count FROM monitored_cases WHERE user_id = ? AND is_active = 1",
            [$userId]
        );
        return (int) $result['count'];
    }

    /**
     * Check if a case is already being monitored by a user
     * 
     * @param int $userId User ID
     * @param string $caseNumber Case number
     * @param string $institutionCode Institution code
     * @return bool True if case is monitored
     */
    public function isCaseMonitored(int $userId, string $caseNumber, string $institutionCode): bool
    {
        $result = Database::fetchOne(
            "SELECT id FROM monitored_cases WHERE user_id = ? AND case_number = ? AND institution_code = ? AND is_active = 1",
            [$userId, $caseNumber, $institutionCode]
        );
        return $result !== false;
    }

    /**
     * Create a case snapshot
     * 
     * @param int $monitoredCaseId Monitored case ID
     * @param object $caseData Case data from SOAP API
     * @return int Snapshot ID
     */
    private function createCaseSnapshot(int $monitoredCaseId, $caseData): int
    {
        $snapshotData = json_encode($caseData, JSON_UNESCAPED_UNICODE);
        $snapshotHash = hash('sha256', $snapshotData);

        return Database::insert('case_snapshots', [
            'monitored_case_id' => $monitoredCaseId,
            'snapshot_data' => $snapshotData,
            'snapshot_hash' => $snapshotHash,
            'case_status' => $caseData->stadiuProcesualNume ?? null,
            'case_stage' => $caseData->stadiuProcesual ?? null,
            'next_hearing_date' => $this->extractNextHearingDate($caseData),
            'last_modification_date' => $caseData->dataModificare ?? null
        ]);
    }

    /**
     * Detect changes between snapshots
     * 
     * @param array $oldSnapshot Previous snapshot
     * @param object $newData New case data
     * @return array List of detected changes
     */
    private function detectChanges(array $oldSnapshot, $newData): array
    {
        $changes = [];
        $oldData = json_decode($oldSnapshot['snapshot_data']);

        // Check status changes
        if (($oldData->stadiuProcesualNume ?? '') !== ($newData->stadiuProcesualNume ?? '')) {
            $changes[] = [
                'type' => 'status',
                'description' => 'Schimbare status: ' . ($newData->stadiuProcesualNume ?? 'Necunoscut'),
                'details' => [
                    'old_status' => $oldData->stadiuProcesualNume ?? '',
                    'new_status' => $newData->stadiuProcesualNume ?? ''
                ]
            ];
        }

        // Check hearing date changes
        $oldHearingDate = $this->extractNextHearingDate($oldData);
        $newHearingDate = $this->extractNextHearingDate($newData);
        
        if ($oldHearingDate !== $newHearingDate) {
            $changes[] = [
                'type' => 'hearing_date',
                'description' => 'Schimbare dată ședință: ' . ($newHearingDate ?: 'Nicio ședință programată'),
                'details' => [
                    'old_date' => $oldHearingDate,
                    'new_date' => $newHearingDate
                ]
            ];
        }

        // Check for new hearings with solutions
        if (isset($newData->sedinte) && isset($oldData->sedinte)) {
            $newSolutions = $this->extractNewSolutions($oldData->sedinte, $newData->sedinte);
            foreach ($newSolutions as $solution) {
                $changes[] = [
                    'type' => 'solution',
                    'description' => 'Soluție nouă: ' . $solution['solutie'],
                    'details' => $solution
                ];
            }
        }

        return $changes;
    }

    /**
     * Extract next hearing date from case data
     * 
     * @param object $caseData Case data
     * @return string|null Next hearing date
     */
    private function extractNextHearingDate($caseData): ?string
    {
        if (!isset($caseData->sedinte) || empty($caseData->sedinte)) {
            return null;
        }

        $today = date('Y-m-d');
        $nextDate = null;

        foreach ($caseData->sedinte as $sedinta) {
            if (isset($sedinta['data']) && $sedinta['data'] > $today) {
                if (!$nextDate || $sedinta['data'] < $nextDate) {
                    $nextDate = $sedinta['data'];
                }
            }
        }

        return $nextDate;
    }

    /**
     * Extract new solutions from hearings
     * 
     * @param array $oldHearings Old hearings
     * @param array $newHearings New hearings
     * @return array New solutions
     */
    private function extractNewSolutions(array $oldHearings, array $newHearings): array
    {
        $newSolutions = [];
        
        foreach ($newHearings as $newHearing) {
            if (empty($newHearing['solutie'])) continue;
            
            $found = false;
            foreach ($oldHearings as $oldHearing) {
                if ($oldHearing['data'] === $newHearing['data'] && 
                    $oldHearing['solutie'] === $newHearing['solutie']) {
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $newSolutions[] = $newHearing;
            }
        }
        
        return $newSolutions;
    }

    /**
     * Log system event
     *
     * @param string $level Log level
     * @param string $message Log message
     * @param array $context Additional context
     * @param int|null $userId User ID
     */
    private function logSystemEvent(string $level, string $message, array $context = [], ?int $userId = null): void
    {
        Database::insert('system_logs', [
            'level' => $level,
            'message' => $message,
            'context' => json_encode($context, JSON_UNESCAPED_UNICODE),
            'user_id' => $userId,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    }

    /**
     * Get all active monitored cases for cron job processing
     */
    public function getAllActiveCases()
    {
        return Database::fetchAll("
            SELECT DISTINCT case_number, institution_code, institution_name
            FROM monitored_cases
            WHERE is_active = 1
            ORDER BY case_number, institution_code
        ");
    }

    /**
     * Get users monitoring a specific case
     */
    public function getUsersMonitoringCase($caseNumber, $institutionCode)
    {
        return Database::fetchAll("
            SELECT mc.user_id, mc.notification_frequency, u.email, u.name
            FROM monitored_cases mc
            LEFT JOIN users u ON mc.user_id = u.id
            WHERE mc.case_number = ? AND mc.institution_code = ? AND mc.is_active = 1
        ", [$caseNumber, $institutionCode]);
    }

    /**
     * Clean up old case snapshots
     */
    public function cleanupOldSnapshots($daysToKeep = 30)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));

        // First, get the IDs of snapshots to keep (latest for each case)
        $keepIds = Database::fetchAll("
            SELECT MAX(id) as id
            FROM case_snapshots cs
            INNER JOIN monitored_cases mc ON cs.monitored_case_id = mc.id
            GROUP BY mc.case_number, mc.institution_code
        ");

        $keepIdsList = array_column($keepIds, 'id');
        $placeholders = str_repeat('?,', count($keepIdsList) - 1) . '?';

        // Delete old snapshots, but keep the latest one for each case
        $params = array_merge([$cutoffDate], $keepIdsList);

        return Database::execute("
            DELETE FROM case_snapshots
            WHERE created_at < ?
            AND id NOT IN ({$placeholders})
        ", $params);
    }
}
