<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 ANALYZING SOLUTIE SUMAR CONTENT FOR MISSING PARTIES\n";
echo "======================================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    echo "Current parties: " . count($dosar->parti) . "\n\n";
    
    // Get the solutieSumar content
    $solutieSumarText = '';
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $i => $sedinta) {
            if (!empty($sedinta['solutieSumar'])) {
                echo "📄 Session " . ($i + 1) . " SolutieSumar content:\n";
                echo "Length: " . strlen($sedinta['solutieSumar']) . " characters\n\n";
                
                $solutieSumarText = $sedinta['solutieSumar'];
                
                // Show the full content
                echo "Full content:\n";
                echo "\"" . $solutieSumarText . "\"\n\n";
                
                // Count potential party names manually
                echo "🔍 MANUAL PARTY ANALYSIS:\n";
                echo "========================\n\n";
                
                // Look for the specific pattern in this text
                if (preg_match('/apelanţii\s+(.+)/i', $solutieSumarText, $matches)) {
                    $apelantiText = $matches[1];
                    echo "Found 'apelanţii' section:\n";
                    echo "\"" . substr($apelantiText, 0, 500) . "...\"\n\n";
                    
                    // Count comma-separated names
                    $commaNames = explode(',', $apelantiText);
                    $validNames = [];
                    
                    foreach ($commaNames as $name) {
                        $name = trim($name);
                        // Clean up the name
                        $name = preg_replace('/\s*împotriva.*$/i', '', $name);
                        $name = preg_replace('/\s*contra.*$/i', '', $name);
                        $name = preg_replace('/\s*în.*$/i', '', $name);
                        $name = preg_replace('/\s*pentru.*$/i', '', $name);
                        $name = preg_replace('/\s*de.*$/i', '', $name);
                        $name = preg_replace('/\s*cu.*$/i', '', $name);
                        $name = preg_replace('/\s*la.*$/i', '', $name);
                        $name = preg_replace('/\s*din.*$/i', '', $name);
                        $name = preg_replace('/\s*prin.*$/i', '', $name);
                        $name = preg_replace('/\s*reprezentat.*$/i', '', $name);
                        $name = preg_replace('/\s*având.*$/i', '', $name);
                        $name = preg_replace('/\s*domiciliat.*$/i', '', $name);
                        $name = preg_replace('/\s*\(.*\).*$/i', '', $name);
                        $name = trim($name);
                        
                        // Check if it looks like a valid name
                        if (strlen($name) >= 3 && preg_match('/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+$/u', $name)) {
                            $validNames[] = $name;
                        }
                    }
                    
                    echo "Comma-separated names found: " . count($commaNames) . "\n";
                    echo "Valid names after cleaning: " . count($validNames) . "\n\n";
                    
                    echo "First 20 valid names:\n";
                    for ($i = 0; $i < min(20, count($validNames)); $i++) {
                        echo "  " . ($i + 1) . ". " . $validNames[$i] . "\n";
                    }
                    
                    if (count($validNames) > 20) {
                        echo "  ... and " . (count($validNames) - 20) . " more\n";
                    }
                    
                    echo "\nLast 10 valid names:\n";
                    for ($i = max(0, count($validNames) - 10); $i < count($validNames); $i++) {
                        echo "  " . ($i + 1) . ". " . $validNames[$i] . "\n";
                    }
                    
                    echo "\nTotal potential parties in solutieSumar: " . count($validNames) . "\n";
                    echo "Current extracted parties from decision text: 250\n";
                    echo "Difference: " . (count($validNames) - 250) . "\n\n";
                }
                
                break; // Only analyze the first session with content
            }
        }
    }
    
    echo "✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
