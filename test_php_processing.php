<?php
/**
 * Test PHP Processing
 * Simple test to verify <PERSON><PERSON> is working and POST data is received
 */

echo "🔧 PHP PROCESSING TEST\n";
echo "======================\n\n";

echo "📋 SERVER INFORMATION:\n";
echo "- PHP Version: " . PHP_VERSION . "\n";
echo "- Request Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'NOT SET') . "\n";
echo "- Script Name: " . ($_SERVER['SCRIPT_NAME'] ?? 'NOT SET') . "\n";
echo "- Request URI: " . ($_SERVER['REQUEST_URI'] ?? 'NOT SET') . "\n";

echo "\n📨 POST DATA:\n";
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "POST method detected\n";
    echo "POST data count: " . count($_POST) . "\n";
    
    if (!empty($_POST)) {
        foreach ($_POST as $key => $value) {
            echo "- $key: " . (is_string($value) ? "'$value'" : json_encode($value)) . "\n";
        }
    } else {
        echo "No POST data received\n";
    }
} else {
    echo "Not a POST request\n";
}

echo "\n📨 GET DATA:\n";
if (!empty($_GET)) {
    foreach ($_GET as $key => $value) {
        echo "- $key: " . (is_string($value) ? "'$value'" : json_encode($value)) . "\n";
    }
} else {
    echo "No GET data\n";
}

echo "\n🔍 RAW INPUT:\n";
$rawInput = file_get_contents('php://input');
echo "Raw input length: " . strlen($rawInput) . "\n";
if (!empty($rawInput)) {
    echo "Raw input: " . substr($rawInput, 0, 200) . (strlen($rawInput) > 200 ? '...' : '') . "\n";
} else {
    echo "No raw input\n";
}

echo "\n🌐 HEADERS:\n";
$headers = getallheaders();
if ($headers) {
    foreach ($headers as $name => $value) {
        echo "- $name: $value\n";
    }
} else {
    echo "No headers available\n";
}

echo "\n✅ PHP processing test completed.\n";
?>
