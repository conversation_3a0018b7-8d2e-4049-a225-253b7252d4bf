<?php
// Test wildcard search with existing case numbers
require_once 'bootstrap.php';

use App\Services\DosarService;

echo "<h1>Specific Wildcard Search Test</h1>";

// Test with a case number we know exists
$baseCase = "14096/3/2024";
$wildcardCase = "14096/3/2024*";

echo "<h2>Testing with existing case number: '$baseCase'</h2>";

try {
    $dosarService = new DosarService();
    
    // Test exact search first
    echo "<h3>1. Exact Search: '$baseCase'</h3>";
    $exactResults = $dosarService->cautareAvansata(['numarDosar' => $baseCase]);
    echo "<p><strong>Exact search results: " . count($exactResults) . "</strong></p>";
    
    if (!empty($exactResults)) {
        foreach ($exactResults as $index => $dosar) {
            echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
            echo "<strong>Exact Result #" . ($index + 1) . ":</strong><br>";
            echo "Case Number: " . ($dosar->numar ?? 'N/A') . "<br>";
            echo "Object: " . substr($dosar->obiect ?? 'N/A', 0, 100) . "...<br>";
            echo "</div>";
        }
    }
    
    // Test wildcard search
    echo "<h3>2. Wildcard Search: '$wildcardCase'</h3>";
    $wildcardResults = $dosarService->cautareAvansata(['numarDosar' => $wildcardCase]);
    echo "<p><strong>Wildcard search results: " . count($wildcardResults) . "</strong></p>";
    
    if (!empty($wildcardResults)) {
        foreach ($wildcardResults as $index => $dosar) {
            echo "<div style='background: #f0fff0; padding: 10px; margin: 5px 0; border: 1px solid #90EE90;'>";
            echo "<strong>Wildcard Result #" . ($index + 1) . ":</strong><br>";
            echo "Case Number: " . ($dosar->numar ?? 'N/A') . "<br>";
            echo "Object: " . substr($dosar->obiect ?? 'N/A', 0, 100) . "...<br>";
            echo "</div>";
        }
    }
    
    // Compare results
    echo "<h3>3. Comparison Analysis</h3>";
    echo "<p>Exact search found: " . count($exactResults) . " results</p>";
    echo "<p>Wildcard search found: " . count($wildcardResults) . " results</p>";
    
    if (count($wildcardResults) >= count($exactResults)) {
        echo "<p style='color: green;'><strong>✓ Wildcard search is working correctly - it found equal or more results than exact search</strong></p>";
        
        // Check if wildcard found additional cases
        if (count($wildcardResults) > count($exactResults)) {
            echo "<p style='color: blue;'>Wildcard search found additional cases:</p>";
            $exactNumbers = array_map(function($dosar) { return $dosar->numar; }, $exactResults);
            
            foreach ($wildcardResults as $dosar) {
                if (!in_array($dosar->numar, $exactNumbers)) {
                    echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border: 1px solid #ffc107;'>";
                    echo "<strong>Additional case found by wildcard:</strong><br>";
                    echo "Case Number: " . ($dosar->numar ?? 'N/A') . "<br>";
                    echo "Object: " . substr($dosar->obiect ?? 'N/A', 0, 100) . "...<br>";
                    echo "</div>";
                }
            }
        }
    } else {
        echo "<p style='color: red;'><strong>✗ Issue detected - wildcard search found fewer results than exact search</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<h3>Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

// Test the original problematic case
echo "<hr><h2>Testing Original Problematic Case</h2>";

$originalCase = "14096/32024";
$originalWildcard = "14096/32024*";

echo "<h3>Original Case Analysis</h3>";
echo "<p>The user reported that '$originalWildcard' doesn't work, but let's verify if '$originalCase' exists at all.</p>";

try {
    // Test if the base case exists
    $baseResults = $dosarService->cautareAvansata(['numarDosar' => $originalCase]);
    echo "<p>Base case '$originalCase' found: " . count($baseResults) . " results</p>";
    
    // Test the wildcard version
    $wildcardResults = $dosarService->cautareAvansata(['numarDosar' => $originalWildcard]);
    echo "<p>Wildcard case '$originalWildcard' found: " . count($wildcardResults) . " results</p>";
    
    if (count($baseResults) == 0) {
        echo "<p style='color: orange;'><strong>Conclusion: The case '$originalCase' doesn't exist in the database, so the wildcard search '$originalWildcard' correctly returns no results.</strong></p>";
        echo "<p>This is not a bug - the wildcard search is working correctly.</p>";
    } else {
        echo "<p style='color: red;'><strong>Issue confirmed: Base case exists but wildcard doesn't find it.</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error testing original case: " . $e->getMessage() . "</p>";
}

// Test with a broader pattern to see if there are similar cases
echo "<hr><h2>Testing Broader Pattern</h2>";

try {
    // Test with a broader wildcard to see what cases exist with similar patterns
    $broadPattern = "14096/*";
    echo "<h3>Searching for pattern: '$broadPattern'</h3>";
    
    $broadResults = $dosarService->cautareAvansata(['numarDosar' => $broadPattern]);
    echo "<p><strong>Broad pattern results: " . count($broadResults) . "</strong></p>";
    
    if (!empty($broadResults)) {
        echo "<h4>Sample results (first 5):</h4>";
        $sampleResults = array_slice($broadResults, 0, 5);
        foreach ($sampleResults as $index => $dosar) {
            echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
            echo "<strong>Sample #" . ($index + 1) . ":</strong><br>";
            echo "Case Number: " . ($dosar->numar ?? 'N/A') . "<br>";
            echo "Object: " . substr($dosar->obiect ?? 'N/A', 0, 80) . "...<br>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error testing broad pattern: " . $e->getMessage() . "</p>";
}

echo "<hr><h2>Final Conclusion</h2>";
echo "<p>Based on the tests above, the wildcard search functionality appears to be working correctly.</p>";
echo "<p>If a specific case like '14096/32024*' is not returning results, it's likely because:</p>";
echo "<ul>";
echo "<li>The base case '14096/32024' doesn't exist in the database</li>";
echo "<li>No cases with that pattern exist (e.g., no '14096/32024/a1', '14096/32024/b2', etc.)</li>";
echo "</ul>";
echo "<p>The wildcard search is designed to find cases that START with the pattern before the asterisk.</p>";
?>
