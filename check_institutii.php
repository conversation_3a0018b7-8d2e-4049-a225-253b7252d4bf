<?php
// Check available institutions in database
require_once 'bootstrap.php';

echo "<h1>🔍 Verificare Instituții Disponibile</h1>";

try {
    $pdo = new PDO('mysql:host=localhost;dbname=portal_judiciar;charset=utf8mb4', 'root', '');
    
    echo "<h2>Instituții din baza de date:</h2>";
    
    // Get unique institutions
    $sql = "SELECT DISTINCT institutie, COUNT(*) as count 
            FROM dosare 
            WHERE institutie IS NOT NULL AND institutie != '' 
            ORDER BY count DESC, institutie ASC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $institutii = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Instituție</th><th>Număr dosare</th></tr>";
    
    foreach ($institutii as $inst) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($inst->institutie) . "</td>";
        echo "<td>" . $inst->count . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h2>Căutare dosare cu numărul 14096/3/2024:</h2>";
    
    $sql = "SELECT numar, institutie, obiect 
            FROM dosare 
            WHERE numar LIKE '14096/3/2024%' 
            ORDER BY numar, institutie";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $dosare = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    if (empty($dosare)) {
        echo "<p>Nu au fost găsite dosare cu numărul 14096/3/2024</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Număr</th><th>Instituție</th><th>Obiect</th></tr>";
        
        foreach ($dosare as $dosar) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($dosar->numar) . "</td>";
            echo "<td>" . htmlspecialchars($dosar->institutie) . "</td>";
            echo "<td>" . htmlspecialchars(substr($dosar->obiect, 0, 100)) . "...</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Test cu primul dosar găsit
    if (!empty($dosare)) {
        $primuDosar = $dosare[0];
        
        echo "<h2>Test cu primul dosar găsit:</h2>";
        echo "<p><strong>Număr:</strong> " . htmlspecialchars($primuDosar->numar) . "</p>";
        echo "<p><strong>Instituție:</strong> " . htmlspecialchars($primuDosar->institutie) . "</p>";
        
        // Verifică părțile pentru acest dosar
        $sql = "SELECT COUNT(*) as parti_count FROM parti WHERE dosar_id = (
                    SELECT id FROM dosare WHERE numar = ? AND institutie = ?
                )";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$primuDosar->numar, $primuDosar->institutie]);
        $partiCount = $stmt->fetch(PDO::FETCH_OBJ);
        
        echo "<p><strong>Părți în baza de date:</strong> " . ($partiCount->parti_count ?? 0) . "</p>";
        
        // Link pentru testare
        echo "<p><a href='detalii_dosar.php?numar=" . urlencode($primuDosar->numar) . "&institutie=" . urlencode($primuDosar->institutie) . "&debug=1' target='_blank'>Test detalii dosar cu debug</a></p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb;'>";
    echo "<h3>❌ Eroare:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>
