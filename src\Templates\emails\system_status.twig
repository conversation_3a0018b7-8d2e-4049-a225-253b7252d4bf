<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Raport status sistem - Portal Ju<PERSON>iar</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 700px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background-color: #28a745;
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 20px;
            border-radius: 4px;
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin: 0;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin: 5px 0 0 0;
        }
        .status-ok { border-left-color: #28a745; }
        .status-ok .stat-number { color: #28a745; }
        .status-warning { border-left-color: #ffc107; }
        .status-warning .stat-number { color: #ffc107; }
        .status-error { border-left-color: #dc3545; }
        .status-error .stat-number { color: #dc3545; }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>📊 Raport Status Sistem</h1>
            <p>Portal Judiciar România - {{ report_date }}</p>
        </div>
        
        <div class="content">
            <h3>Statistici Notificări (ultimele 24 ore)</h3>
            
            <div class="stats-grid">
                <div class="stat-card status-ok">
                    <div class="stat-number">{{ stats.sent }}</div>
                    <div class="stat-label">Notificări trimise</div>
                </div>
                
                <div class="stat-card {% if stats.pending > 0 %}status-warning{% else %}status-ok{% endif %}">
                    <div class="stat-number">{{ stats.pending }}</div>
                    <div class="stat-label">În așteptare</div>
                </div>
                
                <div class="stat-card {% if stats.failed > 0 %}status-error{% else %}status-ok{% endif %}">
                    <div class="stat-number">{{ stats.failed }}</div>
                    <div class="stat-label">Eșuate</div>
                </div>
                
                <div class="stat-card status-ok">
                    <div class="stat-number">{{ stats.total }}</div>
                    <div class="stat-label">Total procesate</div>
                </div>
            </div>
            
            <h3>Utilizatori Activi</h3>
            <div class="stats-grid">
                <div class="stat-card status-ok">
                    <div class="stat-number">{{ user_stats.total_users }}</div>
                    <div class="stat-label">Utilizatori înregistrați</div>
                </div>
                
                <div class="stat-card status-ok">
                    <div class="stat-number">{{ user_stats.active_monitors }}</div>
                    <div class="stat-label">Cu dosare monitorizate</div>
                </div>
                
                <div class="stat-card status-ok">
                    <div class="stat-number">{{ user_stats.total_cases }}</div>
                    <div class="stat-label">Dosare monitorizate</div>
                </div>
            </div>
            
            {% if recent_errors|length > 0 %}
            <h3>Erori Recente</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Timp</th>
                        <th>Tip</th>
                        <th>Mesaj</th>
                    </tr>
                </thead>
                <tbody>
                    {% for error in recent_errors %}
                    <tr>
                        <td>{{ error.timestamp }}</td>
                        <td>{{ error.type }}</td>
                        <td>{{ error.message }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% endif %}
            
            <h3>Performanță Sistem</h3>
            <ul>
                <li><strong>Rata de succes:</strong> {{ success_rate }}%</li>
                <li><strong>Timp mediu procesare:</strong> {{ avg_processing_time }}s</li>
                <li><strong>Memorie utilizată:</strong> {{ memory_usage }}</li>
                <li><strong>Spațiu disc:</strong> {{ disk_usage }}</li>
            </ul>
            
            <h3>Următoarele Acțiuni Programate</h3>
            <ul>
                <li>Curățare notificări vechi: {{ next_cleanup }}</li>
                <li>Backup bază de date: {{ next_backup }}</li>
                <li>Verificare integritate: {{ next_integrity_check }}</li>
            </ul>
        </div>
        
        <div class="footer">
            <p>Raport generat automat la {{ current_time }}</p>
            <p>Pentru suport tehnic: {{ contact_email }}</p>
        </div>
    </div>
</body>
</html>
