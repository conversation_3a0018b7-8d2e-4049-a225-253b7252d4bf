<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

    <title>Detalii Dosar 130/98/2022 - Portal Judiciar România</title>
<meta name="description" content="Detalii complete dosar 130/98/2022 portal judiciar românia. P<PERSON>r<PERSON><PERSON>, termene, ședințe și evoluție dosar în instanțe judecătorești cu verificare actualizată.">
<meta name="keywords" content="detalii dosar, portal judiciar românia, informații dosar, verificare dosare tribunal, părți dosar">
<meta name="robots" content="index, follow">
<meta name="author" content="Portal Judiciar România">
<meta name="language" content="ro">
<link rel="canonical" href="http://localhost/just/just/detalii_dosar.php">
<meta property="og:title" content="Detalii Dosar 130/98/2022 - Portal Judiciar România">
<meta property="og:description" content="Detalii complete dosar 130/98/2022 portal judiciar românia. Părți, termene, ședințe și evoluție dosar în instanțe judecătorești cu verificare actualizată.">
<meta property="og:type" content="article">
<meta property="og:url" content="http://localhost/just/just/detalii_dosar.php">
<meta property="og:site_name" content="Portal Judiciar România">
<meta property="og:locale" content="ro_RO">
<meta property="og:image" content="http://localhost/just/images/logo.jpg">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:image:alt" content="Portal Judiciar România - Logo">
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="Detalii Dosar 130/98/2022 - Portal Judiciar România">
<meta name="twitter:description" content="Detalii complete dosar 130/98/2022 portal judiciar românia. Părți, termene, ședințe și evoluție dosar în instanțe judecătorești cu verificare actualizată.">
<meta name="twitter:image" content="http://localhost/just/images/logo.jpg">
<meta name="twitter:image:alt" content="Portal Judiciar România - Logo">
<meta name="geo.region" content="RO">
<meta name="geo.country" content="Romania">
<script type="application/ld+json">
{"@context":"https://schema.org","@type":"GovernmentOrganization","name":"Portal Judiciar România","alternateName":"DosareJust.ro","description":"Portal oficial pentru căutarea dosarelor judecătorești din România","url":"http://localhost/just/","logo":"http://localhost/just/images/logo.jpg","contactPoint":{"@type":"ContactPoint","contactType":"customer service","availableLanguage":"Romanian","url":"http://localhost/just/contact.php"},"areaServed":{"@type":"Country","name":"Romania"},"serviceType":"Servicii judiciare online","governmentType":"Portal informativ"}
</script>
<script type="application/ld+json">
{"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Acasă","item":"http://localhost/just/"},{"@type":"ListItem","position":2,"name":"Căutare","item":"http://localhost/just/search.php"},{"@type":"ListItem","position":3,"name":"Dosar 130/98/2022","item":""}]}
</script>

<style>
.breadcrumb-nav {
    margin-bottom: 1.5rem;
    padding: 0;
}

.breadcrumb {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0;
    font-size: 0.875rem;
}

.breadcrumb-item {
    color: #6c757d;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
    font-weight: bold;
    padding: 0 0.5rem;
}

.breadcrumb-item a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumb-item a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #2c3e50;
    font-weight: 500;
}

/* Responsive design */
@media (max-width: 767.98px) {
    .breadcrumb {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        padding: 0 0.25rem;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .breadcrumb {
        border: 2px solid #000;
        background-color: #fff;
    }
    
    .breadcrumb-item a {
        color: #000;
        font-weight: bold;
    }
    
    .breadcrumb-item.active {
        color: #000;
        font-weight: bold;
    }
}

/* Print styles */
@media print {
    .breadcrumb-nav {
        display: none;
    }
}
</style><meta property="og:title" content="Dosar 130/98/2022 - Portal Judiciar România">
<meta property="og:description" content="Detalii complete pentru dosarul 130/98/2022 de la Curtea de Apel București">
<meta property="og:type" content="article">
<meta property="og:url" content="http://localhost/just/detalii_dosar.php?numar=130%2F98%2F2022&institutie=CurteadeApelBUCURESTI">
<meta property="og:site_name" content="Portal Judiciar România">
<meta property="og:locale" content="ro_RO">
<meta property="og:image" content="http://localhost/just/images/logo.jpg">
<meta name="twitter:card" content="summary">
<meta name="twitter:title" content="Dosar 130/98/2022 - Portal Judiciar România">
<meta name="twitter:description" content="Detalii complete pentru dosarul 130/98/2022 de la Curtea de Apel București">
<meta name="twitter:image" content="http://localhost/just/images/logo.jpg">

    <!-- CSS Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.min.css?v=1.0">
    <link rel="stylesheet" href="assets/css/responsive.min.css?v=1.0">
    <link rel="stylesheet" href="assets/css/buttons.min.css?v=1.0">
    <link rel="stylesheet" href="assets/css/footer.min.css?v=1.0">

    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>

<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<!-- Stiluri pentru loading overlay -->
<style>
/* Loading overlay pentru detalii dosar */
.page-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(44, 62, 80, 0.9);
    z-index: 9998;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 1;
    transition: opacity 0.5s ease-out;
}

.page-loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

.page-loading-content {
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 350px;
    width: 90%;
    border-top: 4px solid #007bff;
}

.page-loading-spinner {
    width: 50px;
    height: 50px;
    margin: 0 auto 1rem;
    border: 4px solid #e9ecef;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: pageLoadingSpin 1s linear infinite;
}

.page-loading-message {
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
    line-height: 1.4;
}

.page-loading-submessage {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    margin-bottom: 0;
}

@keyframes pageLoadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design pentru mobile */
@media (max-width: 767.98px) {
    .page-loading-content {
        padding: 1.5rem;
        max-width: 300px;
    }

    .page-loading-spinner {
        width: 40px;
        height: 40px;
    }

    .page-loading-message {
        font-size: 0.9rem;
    }

    .page-loading-submessage {
        font-size: 0.8rem;
    }
}

/* Asigură că conținutul principal este ascuns inițial */
.main-content {
    opacity: 0;
    transition: opacity 0.5s ease-in;
}

.main-content.loaded {
    opacity: 1;
}

/* Text Size Adjustment Feature - Discrete Design */
.text-size-controls {
    display: flex;
    align-items: center;
    gap: 0.125rem;
    margin-left: 0.375rem;
    padding-left: 0.375rem;
    border-left: 1px solid rgba(255, 255, 255, 0.15);
    flex-shrink: 0;
}

.text-size-controls .control-label {
    color: rgba(255, 255, 255, 0.75);
    font-weight: 300;
    font-size: 0.65rem;
    margin-right: 0.125rem;
    white-space: nowrap;
    opacity: 0.8;
}

.text-size-controls .btn {
    padding: 0.125rem 0.25rem;
    font-size: 0.6rem;
    line-height: 1;
    border-radius: 2px;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.15s ease;
}

.text-size-controls .btn:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.4);
    color: rgba(255, 255, 255, 0.95);
    transform: translateY(-0.5px);
}

.text-size-controls .btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: none;
}

.text-size-controls .btn:disabled:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.2);
    transform: none;
}

.text-size-indicator {
    color: rgba(255, 255, 255, 0.75);
    font-weight: 300;
    font-size: 0.6rem;
    min-width: 35px;
    text-align: center;
    padding: 0 0.125rem;
    opacity: 0.9;
}

/* Font size classes for content scaling */
.font-size-xs { font-size: 0.75rem !important; }
.font-size-sm { font-size: 0.875rem !important; }
.font-size-base { font-size: 1rem !important; }
.font-size-lg { font-size: 1.125rem !important; }
.font-size-xl { font-size: 1.25rem !important; }
.font-size-xxl { font-size: 1.375rem !important; }

/* Apply font size scaling to all readable content */
.text-size-xs .main-content,
.text-size-xs .main-content p,
.text-size-xs .main-content td,
.text-size-xs .main-content th,
.text-size-xs .main-content .form-control,
.text-size-xs .main-content .btn,
.text-size-xs .main-content .alert,
.text-size-xs .main-content .badge,
.text-size-xs .main-content .card-body,
.text-size-xs .main-content label,
.text-size-xs .main-content span,
.text-size-xs .main-content div {
    font-size: 0.75rem !important;
}

.text-size-sm .main-content,
.text-size-sm .main-content p,
.text-size-sm .main-content td,
.text-size-sm .main-content th,
.text-size-sm .main-content .form-control,
.text-size-sm .main-content .btn,
.text-size-sm .main-content .alert,
.text-size-sm .main-content .badge,
.text-size-sm .main-content .card-body,
.text-size-sm .main-content label,
.text-size-sm .main-content span,
.text-size-sm .main-content div {
    font-size: 0.875rem !important;
}

.text-size-lg .main-content,
.text-size-lg .main-content p,
.text-size-lg .main-content td,
.text-size-lg .main-content th,
.text-size-lg .main-content .form-control,
.text-size-lg .main-content .btn,
.text-size-lg .main-content .alert,
.text-size-lg .main-content .badge,
.text-size-lg .main-content .card-body,
.text-size-lg .main-content label,
.text-size-lg .main-content span,
.text-size-lg .main-content div {
    font-size: 1.125rem !important;
}

.text-size-xl .main-content,
.text-size-xl .main-content p,
.text-size-xl .main-content td,
.text-size-xl .main-content th,
.text-size-xl .main-content .form-control,
.text-size-xl .main-content .btn,
.text-size-xl .main-content .alert,
.text-size-xl .main-content .badge,
.text-size-xl .main-content .card-body,
.text-size-xl .main-content label,
.text-size-xl .main-content span,
.text-size-xl .main-content div {
    font-size: 1.25rem !important;
}

.text-size-xxl .main-content,
.text-size-xxl .main-content p,
.text-size-xxl .main-content td,
.text-size-xxl .main-content th,
.text-size-xxl .main-content .form-control,
.text-size-xxl .main-content .btn,
.text-size-xxl .main-content .alert,
.text-size-xxl .main-content .badge,
.text-size-xxl .main-content .card-body,
.text-size-xxl .main-content label,
.text-size-xxl .main-content span,
.text-size-xxl .main-content div {
    font-size: 1.375rem !important;
}

/* Adjust headings proportionally */
.text-size-xs .main-content h1 { font-size: 1.5rem !important; }
.text-size-xs .main-content h2 { font-size: 1.25rem !important; }
.text-size-xs .main-content h3 { font-size: 1.125rem !important; }
.text-size-xs .main-content h4 { font-size: 1rem !important; }

.text-size-sm .main-content h1 { font-size: 1.75rem !important; }
.text-size-sm .main-content h2 { font-size: 1.5rem !important; }
.text-size-sm .main-content h3 { font-size: 1.25rem !important; }
.text-size-sm .main-content h4 { font-size: 1.125rem !important; }

.text-size-lg .main-content h1 { font-size: 2.25rem !important; }
.text-size-lg .main-content h2 { font-size: 1.875rem !important; }
.text-size-lg .main-content h3 { font-size: 1.5rem !important; }
.text-size-lg .main-content h4 { font-size: 1.375rem !important; }

.text-size-xl .main-content h1 { font-size: 2.5rem !important; }
.text-size-xl .main-content h2 { font-size: 2.125rem !important; }
.text-size-xl .main-content h3 { font-size: 1.75rem !important; }
.text-size-xl .main-content h4 { font-size: 1.5rem !important; }

.text-size-xxl .main-content h1 { font-size: 2.75rem !important; }
.text-size-xxl .main-content h2 { font-size: 2.375rem !important; }
.text-size-xxl .main-content h3 { font-size: 2rem !important; }
.text-size-xxl .main-content h4 { font-size: 1.75rem !important; }

/* Mobile responsiveness for text size controls - Ultra-Discrete Design */
@media (max-width: 767.98px) {
    .text-size-controls {
        position: absolute;
        top: 0.25rem;
        right: 0.25rem;
        margin: 0;
        padding: 0.125rem 0.25rem;
        border: none;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        backdrop-filter: blur(4px);
        width: auto;
        justify-content: center;
        gap: 0.125rem;
        flex-wrap: nowrap;
        opacity: 0.4;
        transition: opacity 0.3s ease;
        z-index: 10;
    }

    .text-size-controls:hover,
    .text-size-controls:focus-within {
        opacity: 0.9;
    }

    .text-size-controls .control-label {
        display: none;
    }

    .text-size-controls .btn {
        min-width: 44px;
        height: 44px;
        font-size: 0.6rem;
        padding: 0;
        flex: 0 0 auto;
        border: 1px solid rgba(255, 255, 255, 0.15);
        background: rgba(255, 255, 255, 0.05);
        border-radius: 50%;
        color: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .text-size-controls .btn:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
    }

    .text-size-indicator {
        display: none;
    }

    /* Ensure mobile header has relative positioning for absolute controls */
    .card-header {
        position: relative;
    }
}

/* Tablet responsiveness for text size controls */
@media (min-width: 768px) and (max-width: 991.98px) {
    .text-size-controls {
        gap: 0.125rem;
        margin-left: 0.25rem;
        padding-left: 0.25rem;
    }

    .text-size-controls .control-label {
        font-size: 0.6rem;
        opacity: 0.85;
    }

    .text-size-controls .btn {
        min-width: 22px;
        height: 22px;
        font-size: 0.55rem;
        padding: 0.125rem 0.25rem;
    }

    .text-size-indicator {
        font-size: 0.55rem;
        min-width: 32px;
        opacity: 0.85;
    }
}

/* Desktop responsiveness for text size controls */
@media (min-width: 992px) {
    .text-size-controls {
        gap: 0.125rem;
    }

    .text-size-controls .btn {
        min-width: 20px;
        height: 20px;
    }
}

/* Extra small devices - ultra-compact floating design */
@media (max-width: 575.98px) {
    .text-size-controls {
        top: 0.125rem;
        right: 0.125rem;
        padding: 0.0625rem 0.125rem;
        border-radius: 10px;
        gap: 0.0625rem;
        opacity: 0.3;
    }

    .text-size-controls .btn {
        min-width: 40px;
        height: 40px;
        font-size: 0.55rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(255, 255, 255, 0.03);
    }

    .text-size-controls .btn:hover {
        transform: scale(1.1);
    }
}

/* Social Sharing Section Styles */
.social-sharing-section .card {
    border: 1px solid #e9ecef;
    transition: box-shadow 0.2s ease;
}

.social-sharing-section .card:hover {
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.social-buttons .btn {
    transition: all 0.2s ease;
    border-width: 1px;
    font-weight: 500;
}

.social-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.social-buttons .btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
}

.social-buttons .btn-outline-success:hover {
    background-color: #25d366;
    border-color: #25d366;
}

.social-buttons .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* Responsive adjustments for social sharing */
@media (max-width: 767.98px) {
    .social-sharing-section .row {
        text-align: center;
    }

    .social-sharing-section .col-md-3,
    .social-sharing-section .col-md-9 {
        margin-bottom: 1rem;
    }

    .social-buttons {
        justify-content: center !important;
    }

    .social-buttons .btn {
        flex: 1;
        min-width: 120px;
        max-width: 140px;
    }
}
</style>

<!-- Loading overlay -->
<div id="pageLoadingOverlay" class="page-loading-overlay" role="status" aria-live="polite" aria-label="Se încarcă detaliile dosarului">
    <div class="page-loading-content">
        <div class="page-loading-spinner" aria-hidden="true"></div>
        <p class="page-loading-message">Se încarcă detaliile dosarului...</p>
        <p class="page-loading-submessage">Vă rugăm să așteptați</p>
    </div>
</div>



<style>
    .dosar-section {
        margin-bottom: 2rem;
    }
    .dosar-section h4 {
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #e9ecef;
    }
    .table-responsive {
        overflow-x: auto;
    }
    .table th {
        background-color: #f8f9fa;
    }
    .table td {
        vertical-align: middle;
    }
    @media (max-width: 767px) {
        .table th, .table td {
            font-size: 0.9rem;
        }
    }
    .solution-detail {
        background-color: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        margin-top: 5px;
    }
    .badge {
        display: inline-block;
        padding: 0.25em 0.6em;
        font-size: 75%;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }
    .bg-info {
        background-color: #17a2b8 !important;
    }
    .text-white {
        color: #fff !important;
    }
    .p-2 {
        padding: 0.5rem !important;
    }
    .mt-1 {
        margin-top: 0.25rem !important;
    }
    .mb-0 {
        margin-bottom: 0 !important;
    }
    .mb-2 {
        margin-bottom: 0.5rem !important;
    }
    .mr-2 {
        margin-right: 0.5rem !important;
    }
    .d-flex {
        display: flex !important;
    }
    .align-items-center {
        align-items: center !important;
    }
    .border-light {
        border-color: #f8f9fa !important;
    }
    .card {
        position: relative;
        display: flex;
        flex-direction: column;
        min-width: 0;
        word-wrap: break-word;
        background-color: #fff;
        background-clip: border-box;
        border: 1px solid rgba(0,0,0,.125);
        border-radius: 0.25rem;
    }
    .card-body {
        flex: 1 1 auto;
        min-height: 1px;
        padding: 1.25rem;
    }
    .p-2 {
        padding: 0.5rem !important;
    }
    .font-weight-bold {
        font-weight: 700 !important;
    }
    .table-info {
        background-color: #d1ecf1 !important;
    }
    .bg-primary {
        background-color: #007bff !important;
    }
    .alert {
        position: relative;
        padding: 0.75rem 1.25rem;
        margin-bottom: 1rem;
        border: 1px solid transparent;
        border-radius: 0.25rem;
    }
    .alert-info {
        color: #0c5460;
        background-color: #d1ecf1;
        border-color: #bee5eb;
    }

    /* Stiluri pentru sortare */
    .sortable {
        cursor: pointer;
        user-select: none;
    }
    .sortable:hover {
        background-color: #e9ecef;
    }
    .sort-icon {
        display: inline-block;
        width: 16px;
        text-align: center;
    }
    .sort-asc .fa-sort {
        display: none;
    }
    .sort-asc::after {
        font-family: "Font Awesome 5 Free";
        content: "\f0de";
        font-weight: 900;
        color: #007bff;
    }
    .sort-desc .fa-sort {
        display: none;
    }
    .sort-desc::after {
        font-family: "Font Awesome 5 Free";
        content: "\f0dd";
        font-weight: 900;
        color: #007bff;
    }
    .sort-active {
        background-color: #f0f7ff;
    }
    .ml-1 {
        margin-left: 0.25rem !important;
    }

    .border-bottom {
        border-bottom: 1px solid #dee2e6 !important;
    }
    .pb-0 {
        padding-bottom: 0 !important;
    }

    /* Stiluri pentru modal */
    .modal {
        display: none;
        position: fixed;
        z-index: 1050;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.4);
    }
    .modal-dialog {
        position: relative;
        width: auto;
        margin: 1.75rem auto;
        max-width: 500px;
    }
    .modal-content {
        position: relative;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, 0.2);
        border-radius: 0.3rem;
        outline: 0;
    }
    .modal-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
        border-top-left-radius: 0.3rem;
        border-top-right-radius: 0.3rem;
    }
    .modal-title {
        margin-bottom: 0;
        line-height: 1.5;
    }
    .modal-body {
        position: relative;
        flex: 1 1 auto;
        padding: 1rem;
    }
    .modal-footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 1rem;
        border-top: 1px solid #dee2e6;
    }
    .close {
        float: right;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity: 0.5;
        background: none;
        border: none;
        padding: 0;
        cursor: pointer;
    }
    .close:hover {
        opacity: 0.75;
    }

    /* Stiluri pentru printare */
    @media print {
        /* Ascunde tot conținutul principal și afișează doar versiunea pentru printare */
        body > *:not(#printVersion) {
            display: none !important;
        }



        /* Afișează doar versiunea pentru printare */
        #printVersion {
            display: block !important;
            position: static !important;
            width: 100% !important;
            height: auto !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Stiluri pentru conținutul de printare */
        #printVersion .print-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }

        #printVersion .print-header h1 {
            color: #007bff;
            font-size: 24pt;
            margin-bottom: 10px;
        }

        #printVersion .print-header p {
            color: #6c757d;
            font-size: 12pt;
            margin: 0;
        }

        /* Stiluri pentru conținutul principal */
        #printContent {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #000;
        }

        #printContent h2 {
            color: #007bff;
            font-size: 16pt;
            margin-top: 25px;
            margin-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 5px;
        }

        #printContent h3 {
            color: #495057;
            font-size: 14pt;
            margin-top: 20px;
            margin-bottom: 10px;
        }

        #printContent p {
            margin-bottom: 8px;
        }

        #printContent strong {
            font-weight: bold;
            color: #495057;
        }

        /* Stiluri pentru tabele */
        #printContent table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            page-break-inside: avoid;
            table-layout: fixed; /* Forțează respectarea lățimilor de coloane */
        }

        #printContent th {
            background-color: #007bff;
            color: white;
            padding: 6px;
            text-align: left;
            font-weight: bold;
            border: 1px solid #007bff;
            font-size: 10pt;
            word-wrap: break-word;
        }

        #printContent td {
            padding: 6px;
            border: 1px solid #dee2e6;
            vertical-align: top;
            font-size: 9pt;
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            white-space: normal;
            max-width: 0; /* Forțează respectarea lățimilor de coloane */
        }

        #printContent tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        /* Optimizare specifică pentru tabelul de ședințe de judecată */
        #printContent .sedinte table {
            width: 100%;
            table-layout: fixed;
        }

        /* Lățimi optimizate pentru coloanele tabelului de ședințe - Soluție 52% cu text justificat */
        #printContent .sedinte th:nth-child(1), /* Data ședință */
        #printContent .sedinte td:nth-child(1) {
            width: 8%;
        }

        #printContent .sedinte th:nth-child(2), /* Ora */
        #printContent .sedinte td:nth-child(2) {
            width: 5%;
        }

        #printContent .sedinte th:nth-child(3), /* Complet */
        #printContent .sedinte td:nth-child(3) {
            width: 10%;
        }

        #printContent .sedinte th:nth-child(4), /* Soluție */
        #printContent .sedinte td:nth-child(4) {
            width: 52%;
        }

        #printContent .sedinte th:nth-child(5), /* Data pronunțare */
        #printContent .sedinte td:nth-child(5) {
            width: 8%;
        }

        #printContent .sedinte th:nth-child(6), /* Document */
        #printContent .sedinte td:nth-child(6) {
            width: 17%;
        }

        /* Text justificat pentru toate celulele din tabelul de ședințe */
        #printContent .sedinte td {
            text-align: justify;
            text-justify: inter-word;
        }

        /* Optimizare pentru tabelul de părți implicate */
        #printContent .parti-implicate table {
            width: 100%;
            table-layout: fixed;
        }

        #printContent .parti-implicate th:nth-child(1), /* Nume */
        #printContent .parti-implicate td:nth-child(1) {
            width: 42%;
        }

        #printContent .parti-implicate th:nth-child(2), /* Calitate */
        #printContent .parti-implicate td:nth-child(2) {
            width: 28%;
        }

        #printContent .parti-implicate th:nth-child(3), /* Informații suplimentare */
        #printContent .parti-implicate td:nth-child(3) {
            width: 30%;
        }

        /* Optimizare pentru tabelul de căi de atac */
        #printContent .cai-atac table {
            width: 100%;
            table-layout: fixed;
        }

        #printContent .cai-atac th:nth-child(1), /* Data declarare */
        #printContent .cai-atac td:nth-child(1) {
            width: 12%;
        }

        #printContent .cai-atac th:nth-child(2), /* Parte declaratoare */
        #printContent .cai-atac td:nth-child(2) {
            width: 38%;
        }

        #printContent .cai-atac th:nth-child(3), /* Tip cale atac */
        #printContent .cai-atac td:nth-child(3) {
            width: 18%;
        }

        #printContent .cai-atac th:nth-child(4), /* Dosar instanță superioară */
        #printContent .cai-atac td:nth-child(4) {
            width: 32%;
        }

        /* Stiluri pentru secțiuni */
        #printContent .dosar-info,
        #printContent .parti-implicate,
        #printContent .sedinte,
        #printContent .cai-atac {
            margin-bottom: 25px;
            page-break-inside: auto;
            page-break-after: auto;
        }

        /* Stiluri specifice pentru tabele mari */
        #printContent .sedinte,
        #printContent .cai-atac {
            page-break-before: auto;
            page-break-after: auto;
        }

        /* Stiluri pentru tabele */
        #printContent table {
            page-break-inside: auto;
            page-break-before: auto;
            page-break-after: auto;
        }

        /* Stiluri pentru rândurile de tabel */
        #printContent tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }

        /* Footer */
        #printContent .footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            font-size: 9pt;
            color: #6c757d;
            page-break-inside: avoid;
        }

        /* Configurare pagină */
        @page {
            margin: 2cm;
            size: A4;
        }

        /* Previne duplicarea conținutului */
        #printContent > * {
            page-break-after: auto;
        }

        /* Asigură că headerul nu se repetă */
        #printVersion .print-header {
            page-break-after: auto;
        }
    }



    /* Header Actions Styles */
    .header-actions {
        gap: 0.5rem;
        flex-wrap: wrap;
        align-items: center;
    }

    .header-actions .btn {
        min-height: 44px;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .header-actions .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .header-actions .btn i {
        font-size: 1em;
    }

    /* Mobile responsive adjustments for header actions */
    @media (max-width: 767.98px) {
        .card-header {
            flex-direction: column;
            align-items: stretch !important;
            gap: 1rem;
        }

        .card-header h2 {
            text-align: center;
            margin-bottom: 0;
        }

        .header-actions {
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .header-actions .btn:not(.text-size-controls .btn) {
            flex: 1;
            max-width: 150px;
            font-size: 0.9rem;
        }

        /* Text size controls are now floating, no need for order management */
        .header-actions .text-size-controls {
            position: absolute;
            order: unset;
        }
    }

    /* Desktop responsive adjustments for header actions */
    @media (min-width: 768px) {
        .header-actions .btn {
            min-width: 120px;
        }
    }

    /* Compact Results Information Styles */
    .results-info-compact {
        padding: 0.5rem 0.75rem;
        background-color: rgba(108, 117, 125, 0.05);
        border-left: 2px solid rgba(108, 117, 125, 0.2);
        border-radius: 0 3px 3px 0;
        margin: 0.75rem 0;
    }

    .results-info-compact .text-muted {
        color: #6c757d !important;
        font-size: 0.8rem;
        line-height: 1.4;
        margin: 0;
    }

    .results-info-compact i {
        font-size: 0.75rem;
        opacity: 0.6;
    }

    /* Mobile optimization for compact info */
    @media (max-width: 767.98px) {
        .results-info-compact {
            padding: 0.375rem 0.5rem;
            margin: 0.5rem 0;
        }

        .results-info-compact .text-muted {
            font-size: 0.75rem;
        }

        .results-info-compact i {
            font-size: 0.7rem;
        }
    }

    /* ===== MOBILE RESPONSIVE STYLES FOR COURT SESSIONS ===== */

    /* Card view pentru ședințe de judecată pe mobile */
    .sedinte-card-view {
        display: none;
    }

    /* Media Queries pentru Responsive Design - Court Sessions */

    /* Extra small devices (portrait phones, less than 576px) */
    @media (max-width: 575.98px) {
        /* Ascunde tabelul și afișează card view pe ecrane foarte mici */
        .sedinte-table-container {
            display: none;
        }

        .sedinte-card-view {
            display: block;
        }

        .sedinta-card {
            border: 1px solid rgba(0,0,0,.125);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
        }

        .sedinta-card-header {
            font-weight: bold;
            margin-bottom: 0.75rem;
            font-size: 1.1rem;
            color: #007bff;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 0.5rem;
        }

        .sedinta-card-body {
            margin-bottom: 0.5rem;
        }

        .sedinta-card-item {
            display: flex;
            margin-bottom: 0.5rem;
            align-items: flex-start;
        }

        .sedinta-card-label {
            font-weight: 600;
            min-width: 120px;
            color: #2c3e50;
            font-size: 0.9rem;
        }

        .sedinta-card-value {
            flex: 1;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        /* Touch-optimized spacing */
        .sedinta-card-item:last-child {
            margin-bottom: 0;
        }
    }

    /* Small devices (landscape phones, 576px and up) */
    @media (min-width: 576px) and (max-width: 767.98px) {
        /* Ascunde tabelul și afișează card view pe ecrane mici */
        .sedinte-table-container {
            display: none;
        }

        .sedinte-card-view {
            display: block;
        }

        .sedinta-card {
            border: 1px solid rgba(0,0,0,.125);
            border-radius: 8px;
            padding: 1.25rem;
            margin-bottom: 1rem;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
        }

        .sedinta-card-header {
            font-weight: bold;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            color: #007bff;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 0.5rem;
        }

        .sedinta-card-body {
            margin-bottom: 0.5rem;
        }

        .sedinta-card-item {
            display: flex;
            margin-bottom: 0.75rem;
            align-items: flex-start;
        }

        .sedinta-card-label {
            font-weight: 600;
            min-width: 140px;
            color: #2c3e50;
            font-size: 0.95rem;
        }

        .sedinta-card-value {
            flex: 1;
            font-size: 0.95rem;
            line-height: 1.4;
        }

        /* Touch-optimized spacing */
        .sedinta-card-item:last-child {
            margin-bottom: 0;
        }
    }

    /* Ensure desktop layout remains unchanged (768px and above) */
    @media (min-width: 768px) {
        .sedinte-table-container {
            display: block;
        }

        .sedinte-card-view {
            display: none;
        }
    }

    /* Accessibility and visual enhancements for mobile cards */
    @media (max-width: 767.98px) {
        .sedinta-card:focus-within {
            outline: 2px solid #007bff;
            outline-offset: 2px;
        }

        .sedinta-card-header i {
            color: #007bff;
        }

        .sedinta-card-value .text-muted {
            color: #6c757d !important;
        }

        .sedinta-card-value .small {
            font-size: 0.8rem;
            color: #6c757d;
        }

        /* Ensure proper text wrapping for long content */
        .sedinta-card-value {
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
    }
</style>


<div class="container mt-4 main-content" id="mainContent">


    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h2>Detalii dosar</h2>
                    <div class="header-actions d-flex gap-2">
                        <button type="button" class="btn btn-light" id="printBtnHeader" title="Salvează documentul ca PDF cu toate informațiile dosarului">
                            <i class="fas fa-file-pdf mr-2"></i>Salvează PDF
                        </button>
                        <a href="index.php" class="btn btn-light">
                            <i class="fas fa-search"></i> Căutare nouă
                        </a>
                        <!-- Text Size Adjustment Controls - Discrete -->
                        <div class="text-size-controls" id="textSizeControls" role="toolbar" aria-label="Controlul mărimii textului">
                            <span class="control-label">A</span>
                            <button type="button" class="btn" id="decreaseTextSize" title="Micșorează textul" aria-label="Micșorează mărimea textului">
                                <i class="fas fa-minus"></i>
                            </button>
                            <span class="text-size-indicator" id="textSizeIndicator" aria-live="polite">Normal</span>
                            <button type="button" class="btn" id="increaseTextSize" title="Mărește textul" aria-label="Mărește mărimea textului">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button type="button" class="btn" id="resetTextSize" title="Resetează la mărimea normală" aria-label="Resetează mărimea textului la normal">
                                <i class="fas fa-undo"></i>
                            </button>
                        </div>
                    </div>
                </div>


                <!-- Container pentru notificări -->
                <div id="notificationContainer" class="card-body p-0" style="display: none;">
                    <div id="notification" class="alert m-3"></div>
                </div>

                <div class="card-body">
                                            <div class="dosar-header">
                            <h3>130/98/2022</h3>
                                                            <p class="text-muted">Număr vechi: 2351/2022</p>
                                                        <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Instanță:</strong>
                                        Curtea de Apel București                                    </p>
                                                                            <p><strong>Departament/Secție:</strong> Secţia a VI-a civilă</p>
                                                                        <p><strong>Data:</strong> 09.11.2022</p>
                                    <p><strong>Obiect:</strong> procedura insolvenţei – societăţi cu răspundere limitată</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Stadiu procesual:</strong> Apel</p>
                                    <p><strong>Categorie caz:</strong> Faliment</p>
                                    <p><strong>Data ultimei modificări:</strong> 04.09.2023</p>
                                </div>
                            </div>
                        </div>

                        <!-- Social Sharing Section -->
                        <div class="social-sharing-section mt-4 mb-4">
                            <div class="card border-0 bg-light">
                                <div class="card-body py-3">
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <h6 class="mb-0 text-muted">
                                                <i class="fas fa-share-alt me-2"></i>Distribuie dosarul
                                            </h6>
                                        </div>
                                        <div class="col-md-9">
                                            <div class="social-buttons d-flex flex-wrap gap-2">
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="copyLinkBtn" title="Copiază link-ul în clipboard">
                                                    <i class="fas fa-copy me-1"></i>Copiază link
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="facebookShareBtn" title="Distribuie pe Facebook">
                                                    <i class="fab fa-facebook-f me-1"></i>Facebook
                                                </button>
                                                <button type="button" class="btn btn-outline-success btn-sm" id="whatsappShareBtn" title="Distribuie pe WhatsApp">
                                                    <i class="fab fa-whatsapp me-1"></i>WhatsApp
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" id="emailShareBtn" title="Trimite prin email">
                                                    <i class="fas fa-envelope me-1"></i>Email
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Părțile implicate -->
                        <div class="dosar-section">
                            <h4 class="d-flex align-items-center justify-content-between">
                                <span><i class="fas fa-users mr-2"></i>Părți implicate</span>
                                <span class="badge bg-secondary text-white parti-counter"></span>
                            </h4>

                                                            <!-- Modul de căutare în timp real -->
                                <div class="search-module mb-3">
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        </div>
                                        <input type="text" id="searchParti" class="form-control" placeholder="Căutați după nume sau calitate..." aria-label="Căutare părți">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary clear-search" type="button" title="Șterge textul">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div id="searchResults" class="search-results mt-2" style="display: none;">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle mr-2"></i>
                                            <span id="resultsMessage"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-striped" id="tabelParti">
                                        <thead>
                                            <tr>
                                                <th class="sortable" data-sort="nume">
                                                    <div class="d-flex align-items-center">
                                                        <span>Nume</span>
                                                        <span class="sort-icon ml-1">
                                                            <i class="fas fa-sort"></i>
                                                        </span>
                                                    </div>
                                                </th>
                                                <th class="sortable" data-sort="calitate">
                                                    <div class="d-flex align-items-center">
                                                        <span>Calitate</span>
                                                        <span class="sort-icon ml-1">
                                                            <i class="fas fa-sort"></i>
                                                        </span>
                                                    </div>
                                                </th>
                                                <th class="sortable" data-sort="info">
                                                    <div class="d-flex align-items-center">
                                                        <span>Informații suplimentare</span>
                                                        <span class="sort-icon ml-1">
                                                            <i class="fas fa-sort"></i>
                                                        </span>
                                                    </div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                                                            <tr class="parte-row"                                                    data-nume="CONSTANTIN VASILICA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="1"
                                                    data-party-id="0">
                                                    <td class="nume-parte" data-original-nume="CONSTANTIN VASILICA">
                                                        CONSTANTIN VASILICA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="FILIPACHE ALEXANDRU"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="2"
                                                    data-party-id="1">
                                                    <td class="nume-parte" data-original-nume="FILIPACHE ALEXANDRU">
                                                        FILIPACHE ALEXANDRU                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="FILIPACHE ELENA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="3"
                                                    data-party-id="2">
                                                    <td class="nume-parte" data-original-nume="FILIPACHE ELENA">
                                                        FILIPACHE ELENA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="PARASCHIV ELENA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="4"
                                                    data-party-id="3">
                                                    <td class="nume-parte" data-original-nume="PARASCHIV ELENA">
                                                        PARASCHIV ELENA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="POPESCU CONSTANTIN"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="5"
                                                    data-party-id="4">
                                                    <td class="nume-parte" data-original-nume="POPESCU CONSTANTIN">
                                                        POPESCU CONSTANTIN                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="POPESCU STANA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="6"
                                                    data-party-id="5">
                                                    <td class="nume-parte" data-original-nume="POPESCU STANA">
                                                        POPESCU STANA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="POPIAG DORINA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="7"
                                                    data-party-id="6">
                                                    <td class="nume-parte" data-original-nume="POPIAG DORINA">
                                                        POPIAG DORINA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="POPOVICI ŞTEFANIA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="8"
                                                    data-party-id="7">
                                                    <td class="nume-parte" data-original-nume="POPOVICI ŞTEFANIA">
                                                        POPOVICI ŞTEFANIA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="PUŞCOI TIŢA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="9"
                                                    data-party-id="8">
                                                    <td class="nume-parte" data-original-nume="PUŞCOI TIŢA">
                                                        PUŞCOI TIŢA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="RADU SORICA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="10"
                                                    data-party-id="9">
                                                    <td class="nume-parte" data-original-nume="RADU SORICA">
                                                        RADU SORICA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="RĂCEANU NELA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="11"
                                                    data-party-id="10">
                                                    <td class="nume-parte" data-original-nume="RĂCEANU NELA">
                                                        RĂCEANU NELA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="SAMIT STERE"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="12"
                                                    data-party-id="11">
                                                    <td class="nume-parte" data-original-nume="SAMIT STERE">
                                                        SAMIT STERE                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ŞERBAN MIHAELA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="13"
                                                    data-party-id="12">
                                                    <td class="nume-parte" data-original-nume="ŞERBAN MIHAELA">
                                                        ŞERBAN MIHAELA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="VOINEA VASILICA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="14"
                                                    data-party-id="13">
                                                    <td class="nume-parte" data-original-nume="VOINEA VASILICA">
                                                        VOINEA VASILICA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ZAHARIA MIRELA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="15"
                                                    data-party-id="14">
                                                    <td class="nume-parte" data-original-nume="ZAHARIA MIRELA">
                                                        ZAHARIA MIRELA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ZAINEA COSTIN"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="16"
                                                    data-party-id="15">
                                                    <td class="nume-parte" data-original-nume="ZAINEA COSTIN">
                                                        ZAINEA COSTIN                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ZAINA IULIA GABRIELA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="17"
                                                    data-party-id="16">
                                                    <td class="nume-parte" data-original-nume="ZAINA IULIA GABRIELA">
                                                        ZAINA IULIA GABRIELA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ZAINEA MANUELA CORNELIA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="18"
                                                    data-party-id="17">
                                                    <td class="nume-parte" data-original-nume="ZAINEA MANUELA CORNELIA">
                                                        ZAINEA MANUELA CORNELIA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ZAINEA NICOLAE"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="19"
                                                    data-party-id="18">
                                                    <td class="nume-parte" data-original-nume="ZAINEA NICOLAE">
                                                        ZAINEA NICOLAE                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ZAMFIRACHE IOANA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="20"
                                                    data-party-id="19">
                                                    <td class="nume-parte" data-original-nume="ZAMFIRACHE IOANA">
                                                        ZAMFIRACHE IOANA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ZAINESCU EMILIA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="21"
                                                    data-party-id="20">
                                                    <td class="nume-parte" data-original-nume="ZAINESCU EMILIA">
                                                        ZAINESCU EMILIA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ZIDPRESCU MIRELA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="22"
                                                    data-party-id="21">
                                                    <td class="nume-parte" data-original-nume="ZIDPRESCU MIRELA">
                                                        ZIDPRESCU MIRELA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="MĂCELARU VANGHELITA NICOLAETA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="23"
                                                    data-party-id="22">
                                                    <td class="nume-parte" data-original-nume="MĂCELARU VANGHELITA NICOLAETA">
                                                        MĂCELARU VANGHELITA NICOLAETA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="MĂCELARU ALEXANDRU"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="24"
                                                    data-party-id="23">
                                                    <td class="nume-parte" data-original-nume="MĂCELARU ALEXANDRU">
                                                        MĂCELARU ALEXANDRU                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="SIMION DUMITRU"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="25"
                                                    data-party-id="24">
                                                    <td class="nume-parte" data-original-nume="SIMION DUMITRU">
                                                        SIMION DUMITRU                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ARON VIORICA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="26"
                                                    data-party-id="25">
                                                    <td class="nume-parte" data-original-nume="ARON VIORICA">
                                                        ARON VIORICA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="MĂCEAŞA NICULINA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="27"
                                                    data-party-id="26">
                                                    <td class="nume-parte" data-original-nume="MĂCEAŞA NICULINA">
                                                        MĂCEAŞA NICULINA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="MĂCEAŞA PETRE"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="28"
                                                    data-party-id="27">
                                                    <td class="nume-parte" data-original-nume="MĂCEAŞA PETRE">
                                                        MĂCEAŞA PETRE                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="VULCAN GEORGETA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="29"
                                                    data-party-id="28">
                                                    <td class="nume-parte" data-original-nume="VULCAN GEORGETA">
                                                        VULCAN GEORGETA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="STOICA ION"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="30"
                                                    data-party-id="29">
                                                    <td class="nume-parte" data-original-nume="STOICA ION">
                                                        STOICA ION                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="COSTACHE PAULA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="31"
                                                    data-party-id="30">
                                                    <td class="nume-parte" data-original-nume="COSTACHE PAULA">
                                                        COSTACHE PAULA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CRÂNGAŞU DOMNICA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="32"
                                                    data-party-id="31">
                                                    <td class="nume-parte" data-original-nume="CRÂNGAŞU DOMNICA">
                                                        CRÂNGAŞU DOMNICA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="NAUM DUMITRU"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="33"
                                                    data-party-id="32">
                                                    <td class="nume-parte" data-original-nume="NAUM DUMITRU">
                                                        NAUM DUMITRU                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="TEODORESCU ECATERINA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="34"
                                                    data-party-id="33">
                                                    <td class="nume-parte" data-original-nume="TEODORESCU ECATERINA">
                                                        TEODORESCU ECATERINA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="VULCAN GEORGETA, STOICA ION, MODOLEA (FOSTĂ STOICA) OANA, MODOLEA ANDREI OCTAVIAN, COSTACHE PAULA, CRÂNGAŞU DOMNICA, NAUM DUMITRU, TEODORESCU ECATERINA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="35"
                                                    data-party-id="34">
                                                    <td class="nume-parte" data-original-nume="VULCAN GEORGETA, STOICA ION, MODOLEA (FOSTĂ STOICA) OANA, MODOLEA ANDREI OCTAVIAN, COSTACHE PAULA, CRÂNGAŞU DOMNICA, NAUM DUMITRU, TEODORESCU ECATERINA">
                                                        VULCAN GEORGETA, STOICA ION, MODOLEA (FOSTĂ STOICA) OANA, MODOLEA ANDREI OCTAVIAN, COSTACHE PAULA, CRÂNGAŞU DOMNICA, NAUM DUMITRU, TEODORESCU ECATERINA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CRISTEA DANIELA, DUNEL MARIA, DUNEL CONSTANTIN, STANCU BOGDAN"
                                                    data-calitate="Intimat Intervenient în numele altei persoane"
                                                    data-info=""
                                                    data-index="36"
                                                    data-party-id="35">
                                                    <td class="nume-parte" data-original-nume="CRISTEA DANIELA, DUNEL MARIA, DUNEL CONSTANTIN, STANCU BOGDAN">
                                                        CRISTEA DANIELA, DUNEL MARIA, DUNEL CONSTANTIN, STANCU BOGDAN                                                    </td>
                                                    <td class="calitate-parte">
                                                        Intimat Intervenient în numele altei persoane                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ŢÎMBALARIU ELENA, STANCIU ENE, CÂRSTEA ELENA, SAVU ALEXANDRINA, TRIFU IOANA, CEACU MAGDALENA, BOŞCĂ MARIA, PÂRVU TEODORA, CÂRNU VICTORIA"
                                                    data-calitate="Intimat Intervenient în numele altei persoane"
                                                    data-info=""
                                                    data-index="37"
                                                    data-party-id="36">
                                                    <td class="nume-parte" data-original-nume="ŢÎMBALARIU ELENA, STANCIU ENE, CÂRSTEA ELENA, SAVU ALEXANDRINA, TRIFU IOANA, CEACU MAGDALENA, BOŞCĂ MARIA, PÂRVU TEODORA, CÂRNU VICTORIA">
                                                        ŢÎMBALARIU ELENA, STANCIU ENE, CÂRSTEA ELENA, SAVU ALEXANDRINA, TRIFU IOANA, CEACU MAGDALENA, BOŞCĂ MARIA, PÂRVU TEODORA, CÂRNU VICTORIA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Intimat Intervenient în numele altei persoane                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="AGAPIE CRISTINA, AGAPIE ŞTEFAN"
                                                    data-calitate="Petent"
                                                    data-info=""
                                                    data-index="38"
                                                    data-party-id="37">
                                                    <td class="nume-parte" data-original-nume="AGAPIE CRISTINA, AGAPIE ŞTEFAN">
                                                        AGAPIE CRISTINA, AGAPIE ŞTEFAN                                                    </td>
                                                    <td class="calitate-parte">
                                                        Petent                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ŞERBĂNESCU ELENA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="39"
                                                    data-party-id="38">
                                                    <td class="nume-parte" data-original-nume="ŞERBĂNESCU ELENA">
                                                        ŞERBĂNESCU ELENA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="PASCU NICOLETA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="40"
                                                    data-party-id="39">
                                                    <td class="nume-parte" data-original-nume="PASCU NICOLETA">
                                                        PASCU NICOLETA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ASOCIAŢIA CAR ÎNVĂŢĂMÂNT SLOBOZIA"
                                                    data-calitate="Intimat Pârât"
                                                    data-info=""
                                                    data-index="41"
                                                    data-party-id="40">
                                                    <td class="nume-parte" data-original-nume="ASOCIAŢIA CAR ÎNVĂŢĂMÂNT SLOBOZIA">
                                                        ASOCIAŢIA CAR ÎNVĂŢĂMÂNT SLOBOZIA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Intimat Pârât                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ALECU NICULAE"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="42"
                                                    data-party-id="41">
                                                    <td class="nume-parte" data-original-nume="ALECU NICULAE">
                                                        ALECU NICULAE                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ACHIM MARILENA, ALECU NICULAE S.A."
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="43"
                                                    data-party-id="42">
                                                    <td class="nume-parte" data-original-nume="ACHIM MARILENA, ALECU NICULAE S.A.">
                                                        ACHIM MARILENA, ALECU NICULAE S.A.                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ALEXANDRESCU BOGDAN IONUŢ"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="44"
                                                    data-party-id="43">
                                                    <td class="nume-parte" data-original-nume="ALEXANDRESCU BOGDAN IONUŢ">
                                                        ALEXANDRESCU BOGDAN IONUŢ                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ALEXE LIXANDRA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="45"
                                                    data-party-id="44">
                                                    <td class="nume-parte" data-original-nume="ALEXE LIXANDRA">
                                                        ALEXE LIXANDRA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ALEXE MARIA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="46"
                                                    data-party-id="45">
                                                    <td class="nume-parte" data-original-nume="ALEXE MARIA">
                                                        ALEXE MARIA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ANAGNOSTE GHEORGHE"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="47"
                                                    data-party-id="46">
                                                    <td class="nume-parte" data-original-nume="ANAGNOSTE GHEORGHE">
                                                        ANAGNOSTE GHEORGHE                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ARDELEANU ADRIANA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="48"
                                                    data-party-id="47">
                                                    <td class="nume-parte" data-original-nume="ARDELEANU ADRIANA">
                                                        ARDELEANU ADRIANA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ARDELEAN AMALIA LOREDANA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="49"
                                                    data-party-id="48">
                                                    <td class="nume-parte" data-original-nume="ARDELEAN AMALIA LOREDANA">
                                                        ARDELEAN AMALIA LOREDANA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ARITON ZINICA"
                                                    data-calitate="Intimat Reclamant"
                                                    data-info=""
                                                    data-index="50"
                                                    data-party-id="49">
                                                    <td class="nume-parte" data-original-nume="ARITON ZINICA">
                                                        ARITON ZINICA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Intimat Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ARON BOGDAN CRISTIAN"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="51"
                                                    data-party-id="50">
                                                    <td class="nume-parte" data-original-nume="ARON BOGDAN CRISTIAN">
                                                        ARON BOGDAN CRISTIAN                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="AVRAM GEORGETA"
                                                    data-calitate="Intimat Reclamant"
                                                    data-info=""
                                                    data-index="52"
                                                    data-party-id="51">
                                                    <td class="nume-parte" data-original-nume="AVRAM GEORGETA">
                                                        AVRAM GEORGETA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Intimat Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BACIU SONIA ŞTEFANIA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="53"
                                                    data-party-id="52">
                                                    <td class="nume-parte" data-original-nume="BACIU SONIA ŞTEFANIA">
                                                        BACIU SONIA ŞTEFANIA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BADEA EMIL"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="54"
                                                    data-party-id="53">
                                                    <td class="nume-parte" data-original-nume="BADEA EMIL">
                                                        BADEA EMIL                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BADEA MIRELA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="55"
                                                    data-party-id="54">
                                                    <td class="nume-parte" data-original-nume="BADEA MIRELA">
                                                        BADEA MIRELA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BALA CĂTĂLIN DUMITRU"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="56"
                                                    data-party-id="55">
                                                    <td class="nume-parte" data-original-nume="BALA CĂTĂLIN DUMITRU">
                                                        BALA CĂTĂLIN DUMITRU                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BALA NICOLAE"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="57"
                                                    data-party-id="56">
                                                    <td class="nume-parte" data-original-nume="BALA NICOLAE">
                                                        BALA NICOLAE                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BALA ZOIŢA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="58"
                                                    data-party-id="57">
                                                    <td class="nume-parte" data-original-nume="BALA ZOIŢA">
                                                        BALA ZOIŢA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BARBU ADRIAN CRISTIAN"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="59"
                                                    data-party-id="58">
                                                    <td class="nume-parte" data-original-nume="BARBU ADRIAN CRISTIAN">
                                                        BARBU ADRIAN CRISTIAN                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BARBU MIHAIL"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="60"
                                                    data-party-id="59">
                                                    <td class="nume-parte" data-original-nume="BARBU MIHAIL">
                                                        BARBU MIHAIL                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BARBU VASILICA STELUŢA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="61"
                                                    data-party-id="60">
                                                    <td class="nume-parte" data-original-nume="BARBU VASILICA STELUŢA">
                                                        BARBU VASILICA STELUŢA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BĂDIC ANGELA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="62"
                                                    data-party-id="61">
                                                    <td class="nume-parte" data-original-nume="BĂDIC ANGELA">
                                                        BĂDIC ANGELA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BĂILĂ NICOLAE EUGEN"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="63"
                                                    data-party-id="62">
                                                    <td class="nume-parte" data-original-nume="BĂILĂ NICOLAE EUGEN">
                                                        BĂILĂ NICOLAE EUGEN                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BĂLAN ALEXANDRA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="64"
                                                    data-party-id="63">
                                                    <td class="nume-parte" data-original-nume="BĂLAN ALEXANDRA">
                                                        BĂLAN ALEXANDRA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="VULCAN GEORGETA, ş.a.-la av.Turache Ramona"
                                                    data-calitate="Intimat Reclamant"
                                                    data-info=""
                                                    data-index="65"
                                                    data-party-id="64">
                                                    <td class="nume-parte" data-original-nume="VULCAN GEORGETA, ş.a.-la av.Turache Ramona">
                                                        VULCAN GEORGETA, ş.a.-la av.Turache Ramona                                                    </td>
                                                    <td class="calitate-parte">
                                                        Intimat Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BĂLAN DANIEL"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="66"
                                                    data-party-id="65">
                                                    <td class="nume-parte" data-original-nume="BĂLAN DANIEL">
                                                        BĂLAN DANIEL                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BĂNICĂ GABRIELA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="67"
                                                    data-party-id="66">
                                                    <td class="nume-parte" data-original-nume="BĂNICĂ GABRIELA">
                                                        BĂNICĂ GABRIELA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BĂNICĂ GENICA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="68"
                                                    data-party-id="67">
                                                    <td class="nume-parte" data-original-nume="BĂNICĂ GENICA">
                                                        BĂNICĂ GENICA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BĂTRÎNU CONSTANTIN"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="69"
                                                    data-party-id="68">
                                                    <td class="nume-parte" data-original-nume="BĂTRÎNU CONSTANTIN">
                                                        BĂTRÎNU CONSTANTIN                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BĂTRÎNU MARIA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="70"
                                                    data-party-id="69">
                                                    <td class="nume-parte" data-original-nume="BĂTRÎNU MARIA">
                                                        BĂTRÎNU MARIA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BIVOLARU DUMITRU"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="71"
                                                    data-party-id="70">
                                                    <td class="nume-parte" data-original-nume="BIVOLARU DUMITRU">
                                                        BIVOLARU DUMITRU                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BÎRSAN ANDREI VLĂDUŢ"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="72"
                                                    data-party-id="71">
                                                    <td class="nume-parte" data-original-nume="BÎRSAN ANDREI VLĂDUŢ">
                                                        BÎRSAN ANDREI VLĂDUŢ                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BÎRSAN MARIA MAGDALENA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="73"
                                                    data-party-id="72">
                                                    <td class="nume-parte" data-original-nume="BÎRSAN MARIA MAGDALENA">
                                                        BÎRSAN MARIA MAGDALENA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BOERESCU EMILIA ADRIANA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="74"
                                                    data-party-id="73">
                                                    <td class="nume-parte" data-original-nume="BOERESCU EMILIA ADRIANA">
                                                        BOERESCU EMILIA ADRIANA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BOTEA GEORGETA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="75"
                                                    data-party-id="74">
                                                    <td class="nume-parte" data-original-nume="BOTEA GEORGETA">
                                                        BOTEA GEORGETA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BOSNEANU VICTORIA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="76"
                                                    data-party-id="75">
                                                    <td class="nume-parte" data-original-nume="BOSNEANU VICTORIA">
                                                        BOSNEANU VICTORIA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BRATU ELENA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="77"
                                                    data-party-id="76">
                                                    <td class="nume-parte" data-original-nume="BRATU ELENA">
                                                        BRATU ELENA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BRATU IULIANA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="78"
                                                    data-party-id="77">
                                                    <td class="nume-parte" data-original-nume="BRATU IULIANA">
                                                        BRATU IULIANA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BRĂCACI EUGENIA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="79"
                                                    data-party-id="78">
                                                    <td class="nume-parte" data-original-nume="BRĂCACI EUGENIA">
                                                        BRĂCACI EUGENIA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BURDULEA ELENA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="80"
                                                    data-party-id="79">
                                                    <td class="nume-parte" data-original-nume="BURDULEA ELENA">
                                                        BURDULEA ELENA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BURDUŞELU MIHAI CĂTĂLIN"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="81"
                                                    data-party-id="80">
                                                    <td class="nume-parte" data-original-nume="BURDUŞELU MIHAI CĂTĂLIN">
                                                        BURDUŞELU MIHAI CĂTĂLIN                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="BURDUŞELU TUDORIŢA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="82"
                                                    data-party-id="81">
                                                    <td class="nume-parte" data-original-nume="BURDUŞELU TUDORIŢA">
                                                        BURDUŞELU TUDORIŢA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CARNICIU ELENA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="83"
                                                    data-party-id="82">
                                                    <td class="nume-parte" data-original-nume="CARNICIU ELENA">
                                                        CARNICIU ELENA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CÂLŢEA LICĂ"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="84"
                                                    data-party-id="83">
                                                    <td class="nume-parte" data-original-nume="CÂLŢEA LICĂ">
                                                        CÂLŢEA LICĂ                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CHECIU IONELA LUCIANA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="85"
                                                    data-party-id="84">
                                                    <td class="nume-parte" data-original-nume="CHECIU IONELA LUCIANA">
                                                        CHECIU IONELA LUCIANA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CHIRIŢĂ VIORICA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="86"
                                                    data-party-id="85">
                                                    <td class="nume-parte" data-original-nume="CHIRIŢĂ VIORICA">
                                                        CHIRIŢĂ VIORICA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CHIŢU EUGENIA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="87"
                                                    data-party-id="86">
                                                    <td class="nume-parte" data-original-nume="CHIŢU EUGENIA">
                                                        CHIŢU EUGENIA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CHIŢU GHEORGHE"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="88"
                                                    data-party-id="87">
                                                    <td class="nume-parte" data-original-nume="CHIŢU GHEORGHE">
                                                        CHIŢU GHEORGHE                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CIOBAN GABRIEL STELIAN"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="89"
                                                    data-party-id="88">
                                                    <td class="nume-parte" data-original-nume="CIOBAN GABRIEL STELIAN">
                                                        CIOBAN GABRIEL STELIAN                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CIOCHINARU ELENA"
                                                    data-calitate="Intimat Reclamant"
                                                    data-info=""
                                                    data-index="90"
                                                    data-party-id="89">
                                                    <td class="nume-parte" data-original-nume="CIOCHINARU ELENA">
                                                        CIOCHINARU ELENA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Intimat Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Anonimizat 1"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="91"
                                                    data-party-id="90">
                                                    <td class="nume-parte" data-original-nume="Anonimizat 1">
                                                        Anonimizat 1                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CÎRCIUMARU DANA ANDREEA"
                                                    data-calitate="Intimat Reclamant"
                                                    data-info=""
                                                    data-index="92"
                                                    data-party-id="91">
                                                    <td class="nume-parte" data-original-nume="CÎRCIUMARU DANA ANDREEA">
                                                        CÎRCIUMARU DANA ANDREEA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Intimat Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="COJOCARU ION"
                                                    data-calitate="Intimat Reclamant"
                                                    data-info=""
                                                    data-index="93"
                                                    data-party-id="92">
                                                    <td class="nume-parte" data-original-nume="COJOCARU ION">
                                                        COJOCARU ION                                                    </td>
                                                    <td class="calitate-parte">
                                                        Intimat Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="COMŞA ILEANA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="94"
                                                    data-party-id="93">
                                                    <td class="nume-parte" data-original-nume="COMŞA ILEANA">
                                                        COMŞA ILEANA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CONSTANTIN ELEONORA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="95"
                                                    data-party-id="94">
                                                    <td class="nume-parte" data-original-nume="CONSTANTIN ELEONORA">
                                                        CONSTANTIN ELEONORA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CONSTANTIN IOANA"
                                                    data-calitate="Intimat Reclamant"
                                                    data-info=""
                                                    data-index="96"
                                                    data-party-id="95">
                                                    <td class="nume-parte" data-original-nume="CONSTANTIN IOANA">
                                                        CONSTANTIN IOANA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Intimat Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CONSTANTIN SĂNDICA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="97"
                                                    data-party-id="96">
                                                    <td class="nume-parte" data-original-nume="CONSTANTIN SĂNDICA">
                                                        CONSTANTIN SĂNDICA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CONSTANTIN TUDOREL"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="98"
                                                    data-party-id="97">
                                                    <td class="nume-parte" data-original-nume="CONSTANTIN TUDOREL">
                                                        CONSTANTIN TUDOREL                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CONSTANTINESCU OCTAVIAN FLORIN"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="99"
                                                    data-party-id="98">
                                                    <td class="nume-parte" data-original-nume="CONSTANTINESCU OCTAVIAN FLORIN">
                                                        CONSTANTINESCU OCTAVIAN FLORIN                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="CORNĂŢEANU ŞTEFANIA"
                                                    data-calitate="Apelant Reclamant"
                                                    data-info=""
                                                    data-index="100"
                                                    data-party-id="99">
                                                    <td class="nume-parte" data-original-nume="CORNĂŢEANU ŞTEFANIA">
                                                        CORNĂŢEANU ŞTEFANIA                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant Reclamant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Aron Bogdan-Cristian"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="101"
                                                    data-party-id="100">
                                                    <td class="nume-parte" data-original-nume="Aron Bogdan-Cristian">
                                                        Aron Bogdan-Cristian                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Barbu Adrian-Cristian"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="102"
                                                    data-party-id="101">
                                                    <td class="nume-parte" data-original-nume="Barbu Adrian-Cristian">
                                                        Barbu Adrian-Cristian                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Barbu Vasilica-Steluţa"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="103"
                                                    data-party-id="102">
                                                    <td class="nume-parte" data-original-nume="Barbu Vasilica-Steluţa">
                                                        Barbu Vasilica-Steluţa                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Boerescu Emilia-Adriana"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="104"
                                                    data-party-id="103">
                                                    <td class="nume-parte" data-original-nume="Boerescu Emilia-Adriana">
                                                        Boerescu Emilia-Adriana                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Chira (fostă Graur) Adina-Tatiana"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="105"
                                                    data-party-id="104">
                                                    <td class="nume-parte" data-original-nume="Chira (fostă Graur) Adina-Tatiana">
                                                        Chira (fostă Graur) Adina-Tatiana                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Cioban Gabriel-Stelian"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="106"
                                                    data-party-id="105">
                                                    <td class="nume-parte" data-original-nume="Cioban Gabriel-Stelian">
                                                        Cioban Gabriel-Stelian                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Copilău Gheorghe"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="107"
                                                    data-party-id="106">
                                                    <td class="nume-parte" data-original-nume="Copilău Gheorghe">
                                                        Copilău Gheorghe                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Creţu Aurica-Lili"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="108"
                                                    data-party-id="107">
                                                    <td class="nume-parte" data-original-nume="Creţu Aurica-Lili">
                                                        Creţu Aurica-Lili                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Creţu Neagu-Stelian"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="109"
                                                    data-party-id="108">
                                                    <td class="nume-parte" data-original-nume="Creţu Neagu-Stelian">
                                                        Creţu Neagu-Stelian                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Danciu Elena"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="110"
                                                    data-party-id="109">
                                                    <td class="nume-parte" data-original-nume="Danciu Elena">
                                                        Danciu Elena                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Filimon Constantin"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="111"
                                                    data-party-id="110">
                                                    <td class="nume-parte" data-original-nume="Filimon Constantin">
                                                        Filimon Constantin                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Ghimpe Neculai"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="112"
                                                    data-party-id="111">
                                                    <td class="nume-parte" data-original-nume="Ghimpe Neculai">
                                                        Ghimpe Neculai                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Godeanu Frusina"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="113"
                                                    data-party-id="112">
                                                    <td class="nume-parte" data-original-nume="Godeanu Frusina">
                                                        Godeanu Frusina                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Goţea Ionela-Nicoleta"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="114"
                                                    data-party-id="113">
                                                    <td class="nume-parte" data-original-nume="Goţea Ionela-Nicoleta">
                                                        Goţea Ionela-Nicoleta                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Graur Nina"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="115"
                                                    data-party-id="114">
                                                    <td class="nume-parte" data-original-nume="Graur Nina">
                                                        Graur Nina                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Iancu Marioara"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="116"
                                                    data-party-id="115">
                                                    <td class="nume-parte" data-original-nume="Iancu Marioara">
                                                        Iancu Marioara                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Iancu Tănase"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="117"
                                                    data-party-id="116">
                                                    <td class="nume-parte" data-original-nume="Iancu Tănase">
                                                        Iancu Tănase                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Ion Mariana"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="118"
                                                    data-party-id="117">
                                                    <td class="nume-parte" data-original-nume="Ion Mariana">
                                                        Ion Mariana                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Ion Paraschiv"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="119"
                                                    data-party-id="118">
                                                    <td class="nume-parte" data-original-nume="Ion Paraschiv">
                                                        Ion Paraschiv                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Ion Paul-Marius"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="120"
                                                    data-party-id="119">
                                                    <td class="nume-parte" data-original-nume="Ion Paul-Marius">
                                                        Ion Paul-Marius                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Iorga Elena-Chiriaca"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="121"
                                                    data-party-id="120">
                                                    <td class="nume-parte" data-original-nume="Iorga Elena-Chiriaca">
                                                        Iorga Elena-Chiriaca                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Lemnaru Neculai"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="122"
                                                    data-party-id="121">
                                                    <td class="nume-parte" data-original-nume="Lemnaru Neculai">
                                                        Lemnaru Neculai                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Lupa Anghelina"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="123"
                                                    data-party-id="122">
                                                    <td class="nume-parte" data-original-nume="Lupa Anghelina">
                                                        Lupa Anghelina                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Nan Barbu"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="124"
                                                    data-party-id="123">
                                                    <td class="nume-parte" data-original-nume="Nan Barbu">
                                                        Nan Barbu                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Năstase Alexandra-Florina"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="125"
                                                    data-party-id="124">
                                                    <td class="nume-parte" data-original-nume="Năstase Alexandra-Florina">
                                                        Năstase Alexandra-Florina                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Necula Ioana"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="126"
                                                    data-party-id="125">
                                                    <td class="nume-parte" data-original-nume="Necula Ioana">
                                                        Necula Ioana                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Oprea Costica"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="127"
                                                    data-party-id="126">
                                                    <td class="nume-parte" data-original-nume="Oprea Costica">
                                                        Oprea Costica                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Panţuru Ion"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="128"
                                                    data-party-id="127">
                                                    <td class="nume-parte" data-original-nume="Panţuru Ion">
                                                        Panţuru Ion                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Papuc Niculae"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="129"
                                                    data-party-id="128">
                                                    <td class="nume-parte" data-original-nume="Papuc Niculae">
                                                        Papuc Niculae                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Paraschiv Ion"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="130"
                                                    data-party-id="129">
                                                    <td class="nume-parte" data-original-nume="Paraschiv Ion">
                                                        Paraschiv Ion                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Petre Anastasia"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="131"
                                                    data-party-id="130">
                                                    <td class="nume-parte" data-original-nume="Petre Anastasia">
                                                        Petre Anastasia                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Petre Dumitru"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="132"
                                                    data-party-id="131">
                                                    <td class="nume-parte" data-original-nume="Petre Dumitru">
                                                        Petre Dumitru                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Petre Iulian"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="133"
                                                    data-party-id="132">
                                                    <td class="nume-parte" data-original-nume="Petre Iulian">
                                                        Petre Iulian                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Petre Rodica"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="134"
                                                    data-party-id="133">
                                                    <td class="nume-parte" data-original-nume="Petre Rodica">
                                                        Petre Rodica                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Radu Gheorghe"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="135"
                                                    data-party-id="134">
                                                    <td class="nume-parte" data-original-nume="Radu Gheorghe">
                                                        Radu Gheorghe                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Radu-Bută Ana"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="136"
                                                    data-party-id="135">
                                                    <td class="nume-parte" data-original-nume="Radu-Bută Ana">
                                                        Radu-Bută Ana                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Radu-Bută Nicolae"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="137"
                                                    data-party-id="136">
                                                    <td class="nume-parte" data-original-nume="Radu-Bută Nicolae">
                                                        Radu-Bută Nicolae                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Rusenescu Mariana"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="138"
                                                    data-party-id="137">
                                                    <td class="nume-parte" data-original-nume="Rusenescu Mariana">
                                                        Rusenescu Mariana                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Staicu Valentina-Julieta"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="139"
                                                    data-party-id="138">
                                                    <td class="nume-parte" data-original-nume="Staicu Valentina-Julieta">
                                                        Staicu Valentina-Julieta                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Stănilă Ovidiu"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="140"
                                                    data-party-id="139">
                                                    <td class="nume-parte" data-original-nume="Stănilă Ovidiu">
                                                        Stănilă Ovidiu                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Stoica Violeta"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="141"
                                                    data-party-id="140">
                                                    <td class="nume-parte" data-original-nume="Stoica Violeta">
                                                        Stoica Violeta                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Tătaru Petre"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="142"
                                                    data-party-id="141">
                                                    <td class="nume-parte" data-original-nume="Tătaru Petre">
                                                        Tătaru Petre                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Teodorescu Emilia"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="143"
                                                    data-party-id="142">
                                                    <td class="nume-parte" data-original-nume="Teodorescu Emilia">
                                                        Teodorescu Emilia                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Tohăneanu Tudor"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="144"
                                                    data-party-id="143">
                                                    <td class="nume-parte" data-original-nume="Tohăneanu Tudor">
                                                        Tohăneanu Tudor                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Tolce Sultana"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="145"
                                                    data-party-id="144">
                                                    <td class="nume-parte" data-original-nume="Tolce Sultana">
                                                        Tolce Sultana                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Trifan Eugen"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="146"
                                                    data-party-id="145">
                                                    <td class="nume-parte" data-original-nume="Trifan Eugen">
                                                        Trifan Eugen                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Tudose Petruţa-Mirela"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="147"
                                                    data-party-id="146">
                                                    <td class="nume-parte" data-original-nume="Tudose Petruţa-Mirela">
                                                        Tudose Petruţa-Mirela                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Turcu Vasile"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="148"
                                                    data-party-id="147">
                                                    <td class="nume-parte" data-original-nume="Turcu Vasile">
                                                        Turcu Vasile                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Tutelcă Elena"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="149"
                                                    data-party-id="148">
                                                    <td class="nume-parte" data-original-nume="Tutelcă Elena">
                                                        Tutelcă Elena                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Urse Gabriel"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="150"
                                                    data-party-id="149">
                                                    <td class="nume-parte" data-original-nume="Urse Gabriel">
                                                        Urse Gabriel                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Vechiu Cornelia"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="151"
                                                    data-party-id="150">
                                                    <td class="nume-parte" data-original-nume="Vechiu Cornelia">
                                                        Vechiu Cornelia                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Vesman Gianina-Cristina"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="152"
                                                    data-party-id="151">
                                                    <td class="nume-parte" data-original-nume="Vesman Gianina-Cristina">
                                                        Vesman Gianina-Cristina                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Vesman Teodora"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="153"
                                                    data-party-id="152">
                                                    <td class="nume-parte" data-original-nume="Vesman Teodora">
                                                        Vesman Teodora                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Zainea Iulia-Gabriela"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="154"
                                                    data-party-id="153">
                                                    <td class="nume-parte" data-original-nume="Zainea Iulia-Gabriela">
                                                        Zainea Iulia-Gabriela                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Modolea Andrei Octavian"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="155"
                                                    data-party-id="154">
                                                    <td class="nume-parte" data-original-nume="Modolea Andrei Octavian">
                                                        Modolea Andrei Octavian                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Alexandrescu Bogdan-Ionuţ"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="156"
                                                    data-party-id="155">
                                                    <td class="nume-parte" data-original-nume="Alexandrescu Bogdan-Ionuţ">
                                                        Alexandrescu Bogdan-Ionuţ                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Ardelean Amalia-Loredana"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="157"
                                                    data-party-id="156">
                                                    <td class="nume-parte" data-original-nume="Ardelean Amalia-Loredana">
                                                        Ardelean Amalia-Loredana                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Bala Cătălin-Dumitru"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="158"
                                                    data-party-id="157">
                                                    <td class="nume-parte" data-original-nume="Bala Cătălin-Dumitru">
                                                        Bala Cătălin-Dumitru                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Băilă Nicolae-Eugen"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="159"
                                                    data-party-id="158">
                                                    <td class="nume-parte" data-original-nume="Băilă Nicolae-Eugen">
                                                        Băilă Nicolae-Eugen                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="Bătr"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="160"
                                                    data-party-id="159">
                                                    <td class="nume-parte" data-original-nume="Bătr">
                                                        Bătr                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr class="parte-row"                                                    data-nume="ca netimbrate"
                                                    data-calitate="Apelant"
                                                    data-info=""
                                                    data-index="161"
                                                    data-party-id="160">
                                                    <td class="nume-parte" data-original-nume="ca netimbrate">
                                                        ca netimbrate                                                    </td>
                                                    <td class="calitate-parte">
                                                        Apelant                                                    </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                                                                </tbody>
                                    </table>
                                </div>

                                <!-- Stiluri pentru modulul de căutare -->
                                <style>
                                    .search-module {
                                        position: relative;
                                    }

                                    .search-module .form-control {
                                        padding-right: 40px;
                                        min-height: 44px;
                                        transition: background-color 0.3s ease, box-shadow 0.3s ease;
                                    }

                                    .search-module .form-control.search-highlight {
                                        background-color: rgba(0, 123, 255, 0.1);
                                        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
                                    }

                                    .search-module .clear-search {
                                        min-height: 44px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        cursor: pointer;
                                        transition: all 0.3s ease;
                                    }

                                    .search-module .clear-search:hover {
                                        background-color: var(--gray-200, #e9ecef);
                                    }

                                    .search-results {
                                        margin-top: 0.5rem;
                                    }

                                    .highlight {
                                        background-color: rgba(255, 230, 0, 0.5);
                                        padding: 2px;
                                        border-radius: 2px;
                                        font-weight: bold;
                                    }

                                    .no-results {
                                        display: none;
                                        padding: 1rem;
                                        text-align: center;
                                        background-color: var(--gray-100, #f8f9fa);
                                        border-radius: 4px;
                                        margin-top: 1rem;
                                    }

                                    .parti-counter {
                                        font-size: 0.9rem;
                                        padding: 0.35rem 0.65rem;
                                    }

                                    /* Stiluri pentru celulele clickabile */
                                    .clickable-cell {
                                        cursor: pointer;
                                        position: relative;
                                        transition: background-color 0.2s ease;
                                    }

                                    .clickable-cell:hover {
                                        background-color: rgba(0, 123, 255, 0.1);
                                    }

                                    .clickable-cell::after {
                                        content: "";
                                        position: absolute;
                                        top: 0;
                                        right: 0;
                                        width: 0;
                                        height: 0;
                                        border-style: solid;
                                        border-width: 0 8px 8px 0;
                                        border-color: transparent rgba(0, 123, 255, 0.3) transparent transparent;
                                        opacity: 0;
                                        transition: opacity 0.2s ease;
                                    }

                                    .clickable-cell:hover::after {
                                        opacity: 1;
                                    }

                                    /* Stiluri pentru celulele de căutare globală */
                                    .global-search-cell {
                                        position: relative;
                                        cursor: pointer;
                                        transition: all 0.3s ease;
                                    }

                                    .global-search-cell:hover {
                                        background-color: rgba(52, 152, 219, 0.15) !important;
                                        color: #2980b9;
                                        font-weight: 500;
                                    }

                                    .global-search-cell::after {
                                        content: "\f002";
                                        font-family: "Font Awesome 5 Free";
                                        font-weight: 900;
                                        position: absolute;
                                        top: 50%;
                                        right: 10px;
                                        transform: translateY(-50%);
                                        font-size: 0.8rem;
                                        color: #3498db;
                                        opacity: 0;
                                        transition: opacity 0.3s ease;
                                    }

                                    .global-search-cell:hover::after {
                                        opacity: 1;
                                    }

                                    /* Stiluri pentru tooltip-ul de informare */
                                    .search-tooltip {
                                        background-color: rgba(0, 123, 255, 0.1);
                                        border-left: 3px solid rgba(0, 123, 255, 0.7);
                                        padding: 8px 12px;
                                        margin-bottom: 10px;
                                        border-radius: 4px;
                                        font-size: 0.9rem;
                                        color: #495057;
                                        transition: opacity 0.5s ease;
                                        opacity: 1;
                                    }

                                    /* Stiluri pentru sortare */
                                    th.sortable {
                                        cursor: pointer;
                                        user-select: none;
                                        position: relative;
                                        transition: background-color 0.3s ease;
                                    }

                                    th.sortable:hover {
                                        background-color: rgba(0, 0, 0, 0.05);
                                    }

                                    th.sort-active {
                                        background-color: rgba(0, 0, 0, 0.075);
                                    }

                                    .sort-icon {
                                        display: inline-flex;
                                        align-items: center;
                                        justify-content: center;
                                        width: 16px;
                                        height: 16px;
                                        opacity: 0.5;
                                        transition: opacity 0.3s ease;
                                    }

                                    th.sortable:hover .sort-icon {
                                        opacity: 0.8;
                                    }

                                    th.sort-active .sort-icon {
                                        opacity: 1;
                                    }

                                    .sort-asc::after {
                                        content: "↑";
                                        display: inline-block;
                                    }

                                    .sort-desc::after {
                                        content: "↓";
                                        display: inline-block;
                                    }

                                    /* Stiluri pentru sortare pe mobile */
                                    @media (max-width: 767.98px) {
                                        th.sortable {
                                            padding-right: 25px;
                                        }

                                        .sort-icon {
                                            position: absolute;
                                            right: 8px;
                                            top: 50%;
                                            transform: translateY(-50%);
                                        }
                                    }

                                    @media (max-width: 767.98px) {
                                        .search-module .input-group {
                                            flex-wrap: nowrap;
                                        }

                                        .search-module .form-control {
                                            font-size: 0.9rem;
                                        }
                                    }
                                </style>
                                                    </div>

                        <!-- Ședințele de judecată -->
                        <div class="dosar-section">
                            <h4><i class="fas fa-gavel mr-2"></i>Ședințe de judecată</h4>
                                                            <!-- Vizualizare tabel pentru desktop și tablete -->
                                <div class="table-responsive sedinte-table-container">
                                    <table class="table table-striped" id="tabelSedinte">
                                        <thead>
                                            <tr>
                                                <th class="sortable" data-sort="data">
                                                    <div class="d-flex align-items-center">
                                                        <span>Data ședință</span>
                                                        <span class="sort-icon ml-1">
                                                            <i class="fas fa-sort"></i>
                                                        </span>
                                                    </div>
                                                </th>
                                                <th class="sortable" data-sort="ora">
                                                    <div class="d-flex align-items-center">
                                                        <span>Ora</span>
                                                        <span class="sort-icon ml-1">
                                                            <i class="fas fa-sort"></i>
                                                        </span>
                                                    </div>
                                                </th>
                                                <th class="sortable" data-sort="complet">
                                                    <div class="d-flex align-items-center">
                                                        <span>Complet</span>
                                                        <span class="sort-icon ml-1">
                                                            <i class="fas fa-sort"></i>
                                                        </span>
                                                    </div>
                                                </th>
                                                <th class="sortable" data-sort="solutie">
                                                    <div class="d-flex align-items-center">
                                                        <span>Soluție</span>
                                                        <span class="sort-icon ml-1">
                                                            <i class="fas fa-sort"></i>
                                                        </span>
                                                    </div>
                                                </th>
                                                <th class="sortable" data-sort="dataPronuntare">
                                                    <div class="d-flex align-items-center">
                                                        <span>Data pronunțare</span>
                                                        <span class="sort-icon ml-1">
                                                            <i class="fas fa-sort"></i>
                                                        </span>
                                                    </div>
                                                </th>
                                                <th class="sortable" data-sort="document">
                                                    <div class="d-flex align-items-center">
                                                        <span>Document</span>
                                                        <span class="sort-icon ml-1">
                                                            <i class="fas fa-sort"></i>
                                                        </span>
                                                    </div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                                                            <tr data-index="0" class="sedinta-row">
                                                    <td data-value="17.05.2023">
                                                        17.05.2023                                                    </td>
                                                    <td data-value="09:00">
                                                        09:00                                                    </td>
                                                    <td data-value="S6 Completul 19A">
                                                        S6 Completul 19A                                                    </td>
                                                    <td data-value="Anulează apelurile formulate de apelanţii Alecu Niculae, Alexe Lixandra, Alexe Maria, Aron Bogdan-Cristian, Badea Emil, Badea Mirela, Badic Angela, Barbu Adrian-Cristian, Barbu Mihail, Barbu Vasilica-Steluţa, Boerescu Emilia-Adriana, Bosneanu Victoria, Câlţea Lică, Carniciu Elena, Chira (fostă Graur) Adina-Tatiana, Chiţu Gheorghe, Cioban Gabriel-Stelian, Constantin Eleonora, Copilău Gheorghe, Creţu Aurica-Lili, Creţu Neagu-Stelian, Danciu Elena, Ercuş Gheorghe, Ercuş Maria, Ercuş Săftica, Filimon Constantin, Gheorghieş Angelica, Ghimpe Neculai, Godeanu Frusina, Goţea Ionela-Nicoleta, Graur Nina, Iancu Marioara, Iancu Tănase, Ion Mariana, Ion Paraschiv, Ion Paul-Marius, Ioniţă Cristinela-Brânduşa, Iorga Elena-Chiriaca, Lemnaru Neculai, Lupa Anghelina, Muşat Iuliana, Nan Barbu, Năstase Alexandra-Florina, Necula Ioana, Oprea Costica, Oprea Ştefania-Loredana, Panţuru Ion, Papuc Niculae, Paraschiv Ion, Petre Anastasia, Petre Dumitru, Petre Iulian, Petre Rodica, Pleşcău Florin, Popescu Stana, Puşcoi Mihaela-Nicoleta, Puşcoi Tiţa, Radu Gheorghe, Radu-Bută Ana, Radu-Bută Nicolae, Rusenescu Mariana, Simion Dumitru, Staicu Valentina-Julieta, Stănilă Ovidiu, Stoica Violeta, Tătaru Petre, Teodorescu Emilia, Ţîrcă Iulia, Tohăneanu Tudor, Tolce Sultana, Trifan Eugen, Tudose Petruţa-Mirela, Turcu Vasile, Tutelcă Elena, Urse Gabriel, Vechiu Cornelia, Vesman Gianina-Cristina, Vesman Teodora, Vişan Daniel-Cristian, Zainea Iulia-Gabriela, Modolea Andrei Octavian, Costache Paula, Crângaşu Domnica, Naum Dumitru ?i Teodorescu Ecaterina, ca netimbrate.
Respinge apelurile formulate de apelan?ii Achim Marilena, Alexandrescu Bogdan-Ionuţ, Anagnoste Gheorghe, Ardeleanu Adriana, Ardelean Amalia-Loredana, Baciu Sonia-Ştefania, Bala Cătălin-Dumitru, Bala Nicolae, Bala Zoiţa, Băilă Nicolae-Eugen, Bălan Alexandra, Bălan Daniel, Bănică Gabriela, Bănică Genica, Bătrînu Constantin, Bătrînu Maria, Bivolaru Dumitru, Bîrsan Andrei-Vlăduţ, Bîrsan Maria-Magdalena, Botea Georgeta, Bratu Elena, Bratu Iuliana, Brăcaci Eugenia, Burdulea Elena, Burduşelu Mihai-Cătălin, Burduşelu Tudoriţa, Checiu Ionela-Luciana, Chiriţă Viorica, Chiţu Eugenia, Anonimizat 1, Comşa Ileana, Constantin Săndica, Constantin Tudorel, Constantin Vasilica, Constantinescu Octavian-Florin, Cornăţeanu Ştefania, Copăceanu Alina-Viorica, Costea Maria, Coţofană Paul, Cuturela Zoiţa, Damian Ioana, Damian Niculina, David Viorica, Dima Costin-Vlăduţ, Dinu Erica, Dinu Ion, Dinu Ioana, Dinu Ştefan, Doicescu Ionela, Doncea Geta, Dragomir Victoria, Drăguşin Valentina-Luiza, Dumitrache Atena, Dumitrescu Ionuţ-Edi, Dumitrică Cătălin-Daniel, Dumitrică Marioara, Durac Alexandru, Durac Mihaela, Enache Carmen-Tatiana, Enescu Maria, Fâşie Nicoleta, Filipache Alexandru, Filipache Elena, Frangu Alexandra Oana, Frangu Valerica, Frangu Vasile, Frâncu Maria, Găianu Aurica, Georgescu Corneliţa, Georgescu George, Georgescu Norelia, Gheorghe Elena, Gheorghe Ionel, Gheorghe Mariana, Gheorghe Romeo-Dorian, Ghica Antoneta, Ghioca Iulian-Constantin, Grama Dumitru, Grama Rada, Graur Dumitru, Graur Gheorghe, Graur Ion, Grigore Traian, Grozea Luiza-Valentina, Haralambie Constantin, Haralambie Tudoriţa, Hoinaru Elvira, Hoinaru Gheorghe, Ilie Georgeta, Ioniţă Florica, Iordache Mihaela, Ivan Laura-Elena, Ivan Liana-Cristiana, Ivan Nicolae, Jianu Aura, Lică Daniela, Loghin Iulian-Nicuşor, Luca Maria, Lupa Liliana, Lupa Ştefan, Lutotovici Alexandru, Lutotovici Margareta, Magdalin Ana, Mandache Maria, Manea Maria, Manole Iuliana-Dana, Măcianu Daniela-Georgiana, Merăoru Tincuţa, Mihai Alin-Daniel, Mihai Elena, Mihai Maria, Mihai F. Maria, Mihălţeanu Sofia, Mirică Maria, Mirică Natalica, Mirică Zoica, Mişu Tiţa, Moise Elena, Moroianu Marioara, Moroianu Vasilica, Munteanu Elena, Muşat Lenuţa, Naca Pompiliu, Neacşu Păuna, Neagu Adrian-Costin, Neagu Drăguţa, Neagu Georgeta, Neagu T. Georgeta, Nedelcu Elisabeta, Negură Marioara, Nicolae Anişoara, Nicolae Florica, Nilă Steliana, Oltenaşu Florenţa, Oltenaşu Sorin, Oprea Florica, Pamparău Valerica, Panait Andreia-Ionela, Pantilimon Aurel, Pantilimon Carmen-Mihaela, Paraschiv Elena, Păduraru Ioan-Alexandru, Pârlitu Cristi-Gigi, Petcu Valeria, Popa Adrian, Popa Iuliana, Popa Victor, Popescu Constantin, Popoiag Dorina, Popovici Ştefania, Posoceanu Ema, Pruşu Eugenia-Mariana, Radu Adela, Radu Eugenia, Radu Marian-Valentin, Radu Sorica, Răceanu Nela, Răceanu Octavia-Elena, Roibu Maria-Lizica, Romaşcu Angheluşa, Scurtu Anica, Stalidi Alina-Nicoleta, Stanciu Lucica, Stancu Luminiţa, Stancu Georgeta, Stoica Liliana, Stoica Silvia, Stoica Viorica-Adina, Stoian Daniela, Stoian Florin-Silviu, Stoian Giorgian, Samit Stere, Şerban Mihaela, Ştefan Maria, Titirişcă Florica, Toader Maria, Toma Nela, Tudorache Nicolae, Ungureanu Valentin, Urjumă Anişoara, Urjumă Neacşa, Urse Valerica, Varialopol Ciciliea, Vasile Florentina, Vişan Pia, Vlad Olimpiea, Voinea Vasilica, Zaharia Mirela, Zainea Costin, Zainea Manuela-Cornelia, Zainea Nicolae, Zamfirache Ioana, Zăinescu Emilia, Zăinescu Nicolae, Zidărescu Mirela, Măcelaru Vangheli?a-Nicoleta, Măcelaru Alexandru, Aron Viorica, Măceaşă Niculina, Măceaşă Petre, Caloean Virginia-Aurelia, Caloean Frîncu, Vulcan Georgeta, Stoica Ion, Modolea (fostă Stoica) Oana, Şerbănescu Elena şi Pascu Nicoleta, ca nefondate.
Admite cererea de restituire a sumelor de bani achitate cu titlu de taxe judiciare de timbru formulată de petenţii Agapie Cristina şi Agapie Ştefan.
Dispune restituirea către petenta Agapie Cristina a sumei de 100 de lei, achitate cu titlu de taxă judiciară de timbru cu chitanţa seria 92658 nr. 3007140/ 30.12.2022.
Dispune restituirea către petentul Agapie Ştefan a sumei de 100 de lei, achitate cu titlu de taxă judiciară de timbru cu chitanţa seria 92658 nr. 3007141/ 30.12.2022.
Definitivă.
Pronunţată prin punerea soluţiei la dispoziţia părţilor de către grefa instanţei astăzi, 17 mai 2023.
">
                                                                                                                    <div>Alte soluţii</div>
                                                                                                                            <div class="text-muted small mt-1">Anulează apelurile formulate de apelanţii Alecu Niculae, Alexe Lixandra, Alexe Maria, Aron Bogdan-Cristian, Badea Emil, Badea Mirela, Badic Angela, Barbu Adrian-Cristian, Barbu Mihail, Barbu Vasilica-Steluţa, Boerescu Emilia-Adriana, Bosneanu Victoria, Câlţea Lică, Carniciu Elena, Chira (fostă Graur) Adina-Tatiana, Chiţu Gheorghe, Cioban Gabriel-Stelian, Constantin Eleonora, Copilău Gheorghe, Creţu Aurica-Lili, Creţu Neagu-Stelian, Danciu Elena, Ercuş Gheorghe, Ercuş Maria, Ercuş Săftica, Filimon Constantin, Gheorghieş Angelica, Ghimpe Neculai, Godeanu Frusina, Goţea Ionela-Nicoleta, Graur Nina, Iancu Marioara, Iancu Tănase, Ion Mariana, Ion Paraschiv, Ion Paul-Marius, Ioniţă Cristinela-Brânduşa, Iorga Elena-Chiriaca, Lemnaru Neculai, Lupa Anghelina, Muşat Iuliana, Nan Barbu, Năstase Alexandra-Florina, Necula Ioana, Oprea Costica, Oprea Ştefania-Loredana, Panţuru Ion, Papuc Niculae, Paraschiv Ion, Petre Anastasia, Petre Dumitru, Petre Iulian, Petre Rodica, Pleşcău Florin, Popescu Stana, Puşcoi Mihaela-Nicoleta, Puşcoi Tiţa, Radu Gheorghe, Radu-Bută Ana, Radu-Bută Nicolae, Rusenescu Mariana, Simion Dumitru, Staicu Valentina-Julieta, Stănilă Ovidiu, Stoica Violeta, Tătaru Petre, Teodorescu Emilia, Ţîrcă Iulia, Tohăneanu Tudor, Tolce Sultana, Trifan Eugen, Tudose Petruţa-Mirela, Turcu Vasile, Tutelcă Elena, Urse Gabriel, Vechiu Cornelia, Vesman Gianina-Cristina, Vesman Teodora, Vişan Daniel-Cristian, Zainea Iulia-Gabriela, Modolea Andrei Octavian, Costache Paula, Crângaşu Domnica, Naum Dumitru ?i Teodorescu Ecaterina, ca netimbrate.
Respinge apelurile formulate de apelan?ii Achim Marilena, Alexandrescu Bogdan-Ionuţ, Anagnoste Gheorghe, Ardeleanu Adriana, Ardelean Amalia-Loredana, Baciu Sonia-Ştefania, Bala Cătălin-Dumitru, Bala Nicolae, Bala Zoiţa, Băilă Nicolae-Eugen, Bălan Alexandra, Bălan Daniel, Bănică Gabriela, Bănică Genica, Bătrînu Constantin, Bătrînu Maria, Bivolaru Dumitru, Bîrsan Andrei-Vlăduţ, Bîrsan Maria-Magdalena, Botea Georgeta, Bratu Elena, Bratu Iuliana, Brăcaci Eugenia, Burdulea Elena, Burduşelu Mihai-Cătălin, Burduşelu Tudoriţa, Checiu Ionela-Luciana, Chiriţă Viorica, Chiţu Eugenia, Anonimizat 1, Comşa Ileana, Constantin Săndica, Constantin Tudorel, Constantin Vasilica, Constantinescu Octavian-Florin, Cornăţeanu Ştefania, Copăceanu Alina-Viorica, Costea Maria, Coţofană Paul, Cuturela Zoiţa, Damian Ioana, Damian Niculina, David Viorica, Dima Costin-Vlăduţ, Dinu Erica, Dinu Ion, Dinu Ioana, Dinu Ştefan, Doicescu Ionela, Doncea Geta, Dragomir Victoria, Drăguşin Valentina-Luiza, Dumitrache Atena, Dumitrescu Ionuţ-Edi, Dumitrică Cătălin-Daniel, Dumitrică Marioara, Durac Alexandru, Durac Mihaela, Enache Carmen-Tatiana, Enescu Maria, Fâşie Nicoleta, Filipache Alexandru, Filipache Elena, Frangu Alexandra Oana, Frangu Valerica, Frangu Vasile, Frâncu Maria, Găianu Aurica, Georgescu Corneliţa, Georgescu George, Georgescu Norelia, Gheorghe Elena, Gheorghe Ionel, Gheorghe Mariana, Gheorghe Romeo-Dorian, Ghica Antoneta, Ghioca Iulian-Constantin, Grama Dumitru, Grama Rada, Graur Dumitru, Graur Gheorghe, Graur Ion, Grigore Traian, Grozea Luiza-Valentina, Haralambie Constantin, Haralambie Tudoriţa, Hoinaru Elvira, Hoinaru Gheorghe, Ilie Georgeta, Ioniţă Florica, Iordache Mihaela, Ivan Laura-Elena, Ivan Liana-Cristiana, Ivan Nicolae, Jianu Aura, Lică Daniela, Loghin Iulian-Nicuşor, Luca Maria, Lupa Liliana, Lupa Ştefan, Lutotovici Alexandru, Lutotovici Margareta, Magdalin Ana, Mandache Maria, Manea Maria, Manole Iuliana-Dana, Măcianu Daniela-Georgiana, Merăoru Tincuţa, Mihai Alin-Daniel, Mihai Elena, Mihai Maria, Mihai F. Maria, Mihălţeanu Sofia, Mirică Maria, Mirică Natalica, Mirică Zoica, Mişu Tiţa, Moise Elena, Moroianu Marioara, Moroianu Vasilica, Munteanu Elena, Muşat Lenuţa, Naca Pompiliu, Neacşu Păuna, Neagu Adrian-Costin, Neagu Drăguţa, Neagu Georgeta, Neagu T. Georgeta, Nedelcu Elisabeta, Negură Marioara, Nicolae Anişoara, Nicolae Florica, Nilă Steliana, Oltenaşu Florenţa, Oltenaşu Sorin, Oprea Florica, Pamparău Valerica, Panait Andreia-Ionela, Pantilimon Aurel, Pantilimon Carmen-Mihaela, Paraschiv Elena, Păduraru Ioan-Alexandru, Pârlitu Cristi-Gigi, Petcu Valeria, Popa Adrian, Popa Iuliana, Popa Victor, Popescu Constantin, Popoiag Dorina, Popovici Ştefania, Posoceanu Ema, Pruşu Eugenia-Mariana, Radu Adela, Radu Eugenia, Radu Marian-Valentin, Radu Sorica, Răceanu Nela, Răceanu Octavia-Elena, Roibu Maria-Lizica, Romaşcu Angheluşa, Scurtu Anica, Stalidi Alina-Nicoleta, Stanciu Lucica, Stancu Luminiţa, Stancu Georgeta, Stoica Liliana, Stoica Silvia, Stoica Viorica-Adina, Stoian Daniela, Stoian Florin-Silviu, Stoian Giorgian, Samit Stere, Şerban Mihaela, Ştefan Maria, Titirişcă Florica, Toader Maria, Toma Nela, Tudorache Nicolae, Ungureanu Valentin, Urjumă Anişoara, Urjumă Neacşa, Urse Valerica, Varialopol Ciciliea, Vasile Florentina, Vişan Pia, Vlad Olimpiea, Voinea Vasilica, Zaharia Mirela, Zainea Costin, Zainea Manuela-Cornelia, Zainea Nicolae, Zamfirache Ioana, Zăinescu Emilia, Zăinescu Nicolae, Zidărescu Mirela, Măcelaru Vangheli?a-Nicoleta, Măcelaru Alexandru, Aron Viorica, Măceaşă Niculina, Măceaşă Petre, Caloean Virginia-Aurelia, Caloean Frîncu, Vulcan Georgeta, Stoica Ion, Modolea (fostă Stoica) Oana, Şerbănescu Elena şi Pascu Nicoleta, ca nefondate.
Admite cererea de restituire a sumelor de bani achitate cu titlu de taxe judiciare de timbru formulată de petenţii Agapie Cristina şi Agapie Ştefan.
Dispune restituirea către petenta Agapie Cristina a sumei de 100 de lei, achitate cu titlu de taxă judiciară de timbru cu chitanţa seria 92658 nr. 3007140/ 30.12.2022.
Dispune restituirea către petentul Agapie Ştefan a sumei de 100 de lei, achitate cu titlu de taxă judiciară de timbru cu chitanţa seria 92658 nr. 3007141/ 30.12.2022.
Definitivă.
Pronunţată prin punerea soluţiei la dispoziţia părţilor de către grefa instanţei astăzi, 17 mai 2023.
</div>
                                                                                                                                                                        </td>
                                                    <td data-value="17.05.2023">
                                                        17.05.2023                                                    </td>
                                                    <td data-value="807/2023">
                                                                                                                    <div>Nr: 807/2023</div>
                                                                                                                            <div class="small">Data: 17.05.2023</div>
                                                                                                                                                                                        <div class="small">Tip: Hotarare</div>
                                                                                                                                                                        </td>
                                                </tr>
                                                                                                                                            <tr data-index="1" class="sedinta-row">
                                                    <td data-value="03.05.2023">
                                                        03.05.2023                                                    </td>
                                                    <td data-value="10:00">
                                                        10:00                                                    </td>
                                                    <td data-value="S6 Completul 19A">
                                                        S6 Completul 19A                                                    </td>
                                                    <td data-value="Amână pronunţarea în data de 17.05.2023.">
                                                                                                                    <div>Amână pronunţarea</div>
                                                                                                                            <div class="text-muted small mt-1">Amână pronunţarea în data de 17.05.2023.</div>
                                                                                                                                                                        </td>
                                                    <td data-value="03.05.2023">
                                                        03.05.2023                                                    </td>
                                                    <td data-value="">
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                                                                    </tbody>
                                    </table>
                                </div>

                                <!-- Vizualizare card pentru mobile -->
                                <div class="sedinte-card-view">
                                                                        <div class="sedinta-card"
                                        data-index="0"
                                        data-data="17.05.2023"
                                        data-ora="09:00"
                                        data-complet="S6 Completul 19A"
                                        data-solutie="Alte soluţii"
                                        data-data-pronuntare="17.05.2023"
                                        data-document="807/2023">

                                        <div class="sedinta-card-header">
                                            <i class="fas fa-calendar-alt mr-2"></i>17.05.2023                                                                                            <span class="ml-2 text-muted">
                                                    <i class="fas fa-clock mr-1"></i>09:00                                                </span>
                                                                                    </div>

                                        <div class="sedinta-card-body">
                                                                                        <div class="sedinta-card-item">
                                                <div class="sedinta-card-label">Complet:</div>
                                                <div class="sedinta-card-value">
                                                    S6 Completul 19A                                                </div>
                                            </div>
                                            
                                            <div class="sedinta-card-item">
                                                <div class="sedinta-card-label">Soluție:</div>
                                                <div class="sedinta-card-value">
                                                                                                            Alte soluţii                                                                                                                    <div class="text-muted small mt-1">Anulează apelurile formulate de apelanţii Alecu Niculae, Alexe Lixandra, Alexe Maria, Aron Bogdan-Cristian, Badea Emil, Badea Mirela, Badic Angela, Barbu Adrian-Cristian, Barbu Mihail, Barbu Vasilica-Steluţa, Boerescu Emilia-Adriana, Bosneanu Victoria, Câlţea Lică, Carniciu Elena, Chira (fostă Graur) Adina-Tatiana, Chiţu Gheorghe, Cioban Gabriel-Stelian, Constantin Eleonora, Copilău Gheorghe, Creţu Aurica-Lili, Creţu Neagu-Stelian, Danciu Elena, Ercuş Gheorghe, Ercuş Maria, Ercuş Săftica, Filimon Constantin, Gheorghieş Angelica, Ghimpe Neculai, Godeanu Frusina, Goţea Ionela-Nicoleta, Graur Nina, Iancu Marioara, Iancu Tănase, Ion Mariana, Ion Paraschiv, Ion Paul-Marius, Ioniţă Cristinela-Brânduşa, Iorga Elena-Chiriaca, Lemnaru Neculai, Lupa Anghelina, Muşat Iuliana, Nan Barbu, Năstase Alexandra-Florina, Necula Ioana, Oprea Costica, Oprea Ştefania-Loredana, Panţuru Ion, Papuc Niculae, Paraschiv Ion, Petre Anastasia, Petre Dumitru, Petre Iulian, Petre Rodica, Pleşcău Florin, Popescu Stana, Puşcoi Mihaela-Nicoleta, Puşcoi Tiţa, Radu Gheorghe, Radu-Bută Ana, Radu-Bută Nicolae, Rusenescu Mariana, Simion Dumitru, Staicu Valentina-Julieta, Stănilă Ovidiu, Stoica Violeta, Tătaru Petre, Teodorescu Emilia, Ţîrcă Iulia, Tohăneanu Tudor, Tolce Sultana, Trifan Eugen, Tudose Petruţa-Mirela, Turcu Vasile, Tutelcă Elena, Urse Gabriel, Vechiu Cornelia, Vesman Gianina-Cristina, Vesman Teodora, Vişan Daniel-Cristian, Zainea Iulia-Gabriela, Modolea Andrei Octavian, Costache Paula, Crângaşu Domnica, Naum Dumitru ?i Teodorescu Ecaterina, ca netimbrate.
Respinge apelurile formulate de apelan?ii Achim Marilena, Alexandrescu Bogdan-Ionuţ, Anagnoste Gheorghe, Ardeleanu Adriana, Ardelean Amalia-Loredana, Baciu Sonia-Ştefania, Bala Cătălin-Dumitru, Bala Nicolae, Bala Zoiţa, Băilă Nicolae-Eugen, Bălan Alexandra, Bălan Daniel, Bănică Gabriela, Bănică Genica, Bătrînu Constantin, Bătrînu Maria, Bivolaru Dumitru, Bîrsan Andrei-Vlăduţ, Bîrsan Maria-Magdalena, Botea Georgeta, Bratu Elena, Bratu Iuliana, Brăcaci Eugenia, Burdulea Elena, Burduşelu Mihai-Cătălin, Burduşelu Tudoriţa, Checiu Ionela-Luciana, Chiriţă Viorica, Chiţu Eugenia, Anonimizat 1, Comşa Ileana, Constantin Săndica, Constantin Tudorel, Constantin Vasilica, Constantinescu Octavian-Florin, Cornăţeanu Ştefania, Copăceanu Alina-Viorica, Costea Maria, Coţofană Paul, Cuturela Zoiţa, Damian Ioana, Damian Niculina, David Viorica, Dima Costin-Vlăduţ, Dinu Erica, Dinu Ion, Dinu Ioana, Dinu Ştefan, Doicescu Ionela, Doncea Geta, Dragomir Victoria, Drăguşin Valentina-Luiza, Dumitrache Atena, Dumitrescu Ionuţ-Edi, Dumitrică Cătălin-Daniel, Dumitrică Marioara, Durac Alexandru, Durac Mihaela, Enache Carmen-Tatiana, Enescu Maria, Fâşie Nicoleta, Filipache Alexandru, Filipache Elena, Frangu Alexandra Oana, Frangu Valerica, Frangu Vasile, Frâncu Maria, Găianu Aurica, Georgescu Corneliţa, Georgescu George, Georgescu Norelia, Gheorghe Elena, Gheorghe Ionel, Gheorghe Mariana, Gheorghe Romeo-Dorian, Ghica Antoneta, Ghioca Iulian-Constantin, Grama Dumitru, Grama Rada, Graur Dumitru, Graur Gheorghe, Graur Ion, Grigore Traian, Grozea Luiza-Valentina, Haralambie Constantin, Haralambie Tudoriţa, Hoinaru Elvira, Hoinaru Gheorghe, Ilie Georgeta, Ioniţă Florica, Iordache Mihaela, Ivan Laura-Elena, Ivan Liana-Cristiana, Ivan Nicolae, Jianu Aura, Lică Daniela, Loghin Iulian-Nicuşor, Luca Maria, Lupa Liliana, Lupa Ştefan, Lutotovici Alexandru, Lutotovici Margareta, Magdalin Ana, Mandache Maria, Manea Maria, Manole Iuliana-Dana, Măcianu Daniela-Georgiana, Merăoru Tincuţa, Mihai Alin-Daniel, Mihai Elena, Mihai Maria, Mihai F. Maria, Mihălţeanu Sofia, Mirică Maria, Mirică Natalica, Mirică Zoica, Mişu Tiţa, Moise Elena, Moroianu Marioara, Moroianu Vasilica, Munteanu Elena, Muşat Lenuţa, Naca Pompiliu, Neacşu Păuna, Neagu Adrian-Costin, Neagu Drăguţa, Neagu Georgeta, Neagu T. Georgeta, Nedelcu Elisabeta, Negură Marioara, Nicolae Anişoara, Nicolae Florica, Nilă Steliana, Oltenaşu Florenţa, Oltenaşu Sorin, Oprea Florica, Pamparău Valerica, Panait Andreia-Ionela, Pantilimon Aurel, Pantilimon Carmen-Mihaela, Paraschiv Elena, Păduraru Ioan-Alexandru, Pârlitu Cristi-Gigi, Petcu Valeria, Popa Adrian, Popa Iuliana, Popa Victor, Popescu Constantin, Popoiag Dorina, Popovici Ştefania, Posoceanu Ema, Pruşu Eugenia-Mariana, Radu Adela, Radu Eugenia, Radu Marian-Valentin, Radu Sorica, Răceanu Nela, Răceanu Octavia-Elena, Roibu Maria-Lizica, Romaşcu Angheluşa, Scurtu Anica, Stalidi Alina-Nicoleta, Stanciu Lucica, Stancu Luminiţa, Stancu Georgeta, Stoica Liliana, Stoica Silvia, Stoica Viorica-Adina, Stoian Daniela, Stoian Florin-Silviu, Stoian Giorgian, Samit Stere, Şerban Mihaela, Ştefan Maria, Titirişcă Florica, Toader Maria, Toma Nela, Tudorache Nicolae, Ungureanu Valentin, Urjumă Anişoara, Urjumă Neacşa, Urse Valerica, Varialopol Ciciliea, Vasile Florentina, Vişan Pia, Vlad Olimpiea, Voinea Vasilica, Zaharia Mirela, Zainea Costin, Zainea Manuela-Cornelia, Zainea Nicolae, Zamfirache Ioana, Zăinescu Emilia, Zăinescu Nicolae, Zidărescu Mirela, Măcelaru Vangheli?a-Nicoleta, Măcelaru Alexandru, Aron Viorica, Măceaşă Niculina, Măceaşă Petre, Caloean Virginia-Aurelia, Caloean Frîncu, Vulcan Georgeta, Stoica Ion, Modolea (fostă Stoica) Oana, Şerbănescu Elena şi Pascu Nicoleta, ca nefondate.
Admite cererea de restituire a sumelor de bani achitate cu titlu de taxe judiciare de timbru formulată de petenţii Agapie Cristina şi Agapie Ştefan.
Dispune restituirea către petenta Agapie Cristina a sumei de 100 de lei, achitate cu titlu de taxă judiciară de timbru cu chitanţa seria 92658 nr. 3007140/ 30.12.2022.
Dispune restituirea către petentul Agapie Ştefan a sumei de 100 de lei, achitate cu titlu de taxă judiciară de timbru cu chitanţa seria 92658 nr. 3007141/ 30.12.2022.
Definitivă.
Pronunţată prin punerea soluţiei la dispoziţia părţilor de către grefa instanţei astăzi, 17 mai 2023.
</div>
                                                                                                                                                            </div>
                                            </div>

                                                                                        <div class="sedinta-card-item">
                                                <div class="sedinta-card-label">Data pronunțare:</div>
                                                <div class="sedinta-card-value">
                                                    17.05.2023                                                </div>
                                            </div>
                                            
                                                                                        <div class="sedinta-card-item">
                                                <div class="sedinta-card-label">Document:</div>
                                                <div class="sedinta-card-value">
                                                    <div>Nr: 807/2023</div>
                                                                                                            <div class="small">Data: 17.05.2023</div>
                                                                                                                                                                <div class="small">Tip: Hotarare</div>
                                                                                                    </div>
                                            </div>
                                                                                    </div>
                                    </div>
                                                                        <div class="sedinta-card"
                                        data-index="1"
                                        data-data="03.05.2023"
                                        data-ora="10:00"
                                        data-complet="S6 Completul 19A"
                                        data-solutie="Amână pronunţarea"
                                        data-data-pronuntare="03.05.2023"
                                        data-document="">

                                        <div class="sedinta-card-header">
                                            <i class="fas fa-calendar-alt mr-2"></i>03.05.2023                                                                                            <span class="ml-2 text-muted">
                                                    <i class="fas fa-clock mr-1"></i>10:00                                                </span>
                                                                                    </div>

                                        <div class="sedinta-card-body">
                                                                                        <div class="sedinta-card-item">
                                                <div class="sedinta-card-label">Complet:</div>
                                                <div class="sedinta-card-value">
                                                    S6 Completul 19A                                                </div>
                                            </div>
                                            
                                            <div class="sedinta-card-item">
                                                <div class="sedinta-card-label">Soluție:</div>
                                                <div class="sedinta-card-value">
                                                                                                            Amână pronunţarea                                                                                                                    <div class="text-muted small mt-1">Amână pronunţarea în data de 17.05.2023.</div>
                                                                                                                                                            </div>
                                            </div>

                                                                                        <div class="sedinta-card-item">
                                                <div class="sedinta-card-label">Data pronunțare:</div>
                                                <div class="sedinta-card-value">
                                                    03.05.2023                                                </div>
                                            </div>
                                            
                                                                                    </div>
                                    </div>
                                                                    </div>
                                                    </div>

                        <!-- Căile de atac -->
                        <div class="dosar-section">
                            <h4><i class="fas fa-balance-scale mr-2"></i>Căi de atac</h4>
                            <p class="text-muted small">Informații despre căile de atac declarate în acest dosar</p>
                                                            <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Data declarare</th>
                                                <th>Parte declaratoare</th>
                                                <th>Tip cale atac</th>
                                                <th>Dosar instanță superioară</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                                                            <tr>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                            <tr>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                    <td>
                                                                                                                    <span class="text-muted">-</span>
                                                                                                            </td>
                                                </tr>
                                                                                    </tbody>
                                    </table>
                                </div>
                                                    </div>
                                    </div>
            </div>

                            <!-- Compact Results Information -->
                <div class="results-info-compact mt-3 mb-2">
                    <div class="text-muted small">
                        <i class="fas fa-info-circle me-1" style="opacity: 0.6;"></i>
                        <span style="font-size: 0.8rem;">
                            Toate informațiile sunt preluate în timp real de la sistemul oficial al instanțelor de judecată.
                            Ultima actualizare: 05.07.2025 17:06                        </span>
                    </div>
                </div>
                    </div>
    </div>
</div>

<!-- Script pentru sortarea tabelului de ședințe și funcționalitățile de export -->

<script>
// Adăugăm o funcție de logging pentru depanare
function logDebug(message, data) {
    if (window.console && console.debug) {
        if (data) {
            console.debug('[DEBUG] ' + message, data);
        } else {
            console.debug('[DEBUG] ' + message);
        }
    }
}

/**
 * Normalizează textul românesc pentru căutare - convertește diacriticele
 * @param {string} text - Textul de normalizat
 * @returns {string} - Textul normalizat
 */
function normalizeRomanianText(text) {
    if (!text) return '';

    return text.toLowerCase()
        // Convertește diacriticele românești la echivalentele fără diacritice
        .replace(/[ăâ]/g, 'a')
        .replace(/[îi]/g, 'i')
        .replace(/[șş]/g, 's')
        .replace(/[țţ]/g, 't')
        // Elimină spațiile multiple și trim
        .replace(/\s+/g, ' ')
        .trim();
}

/**
 * Verifică dacă un text conține termenul de căutare folosind normalizare românească
 * @param {string} text - Textul în care să caute
 * @param {string} searchTerm - Termenul de căutat
 * @returns {boolean} - True dacă găsește o potrivire
 */
function romanianTextContains(text, searchTerm) {
    const normalizedText = normalizeRomanianText(text);
    const normalizedSearch = normalizeRomanianText(searchTerm);
    return normalizedText.includes(normalizedSearch);
}

document.addEventListener('DOMContentLoaded', function() {
    logDebug('DOM încărcat, inițializare funcționalități...');



    // Inițializare sortare tabel ședințe
    initTableSort();

    // Inițializare modul de căutare pentru părți implicate
    console.log('Initializing search module with enhanced debugging...');
    initSearchModule();

    // Inițializare funcționalități de export și distribuire
    initExportFunctions();
});

/**
 * Inițializează modulul de căutare pentru părțile implicate
 */
function initSearchModule() {
    logDebug('Inițializare modul de căutare pentru părți implicate...');
    console.log('=== SEARCH MODULE INITIALIZATION START ===');

    // Selectăm elementele necesare
    const searchInput = document.getElementById('searchParti');
    const clearButton = document.querySelector('.clear-search');
    const searchResults = document.getElementById('searchResults');
    const resultsMessage = document.getElementById('resultsMessage');
    const tabelParti = document.getElementById('tabelParti');

    console.log('Search elements found:', {
        searchInput: !!searchInput,
        clearButton: !!clearButton,
        searchResults: !!searchResults,
        resultsMessage: !!resultsMessage,
        tabelParti: !!tabelParti
    });

    // Verificăm dacă elementele necesare există
    if (!searchInput) {
        console.error('Elementul de căutare #searchParti nu a fost găsit!');
        return;
    }

    if (!tabelParti) {
        console.error('Tabelul #tabelParti nu a fost găsit!');
        return;
    }

    logDebug('Elemente găsite:', {
        searchInput: searchInput,
        tabelParti: tabelParti,
        clearButton: clearButton,
        searchResults: searchResults
    });

    // Obține toate rândurile din tabel - selectăm toate rândurile din tbody, nu doar cele cu clasa parte-row
    const tbody = tabelParti.querySelector('tbody');
    if (!tbody) {
        console.error('Elementul tbody din tabelul #tabelParti nu a fost găsit!');
        return;
    }

    // Selectăm toate rândurile din tbody
    const rows = tbody.querySelectorAll('tr');
    logDebug(`Număr de rânduri găsite în tabel: ${rows.length}`);

    // Debug: Check each row's properties
    rows.forEach((row, index) => {
        const hasParteRowClass = row.classList.contains('parte-row');
        const hasDataNume = row.hasAttribute('data-nume');
        logDebug(`Initial row ${index + 1}:`, {
            hasParteRowClass: hasParteRowClass,
            hasDataNume: hasDataNume,
            classList: Array.from(row.classList),
            dataNume: row.getAttribute('data-nume'),
            innerHTML: row.innerHTML.substring(0, 100) + '...'
        });
    });

    // Verificăm dacă avem rânduri
    if (rows.length === 0) {
        console.warn('Nu s-au găsit rânduri în tabelul #tabelParti!');
        return;
    }



    // Actualizează contorul de părți
    const partiCounter = document.querySelector('.parti-counter');
    if (partiCounter) {
        logDebug(`Actualizare contor inițial: ${rows.length} părți`);
        updatePartiCounter(rows.length, rows.length);


    } else {
        logDebug('Contorul de părți nu a fost găsit.');
    }

    // Adaugă event listener pentru input cu debounce pentru performanță
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        logDebug('Eveniment input detectat, valoare:', this.value);
        clearTimeout(searchTimeout);

        // Utilizăm debounce pentru a îmbunătăți performanța cu seturi mari de date
        searchTimeout = setTimeout(() => {
            const searchTerm = this.value.trim();
            logDebug('Termen de căutare procesat:', searchTerm);
            let visibleCount = 0;
            const totalRows = rows.length;

            if (searchTerm === '') {
                // Resetează tabelul dacă nu există termen de căutare
                logDebug('Termen de căutare gol, resetare tabel');
                resetTable(rows);
                if (searchResults) searchResults.style.display = 'none';
                if (partiCounter) updatePartiCounter(totalRows, totalRows);
                return;
            }

            // Optimizare pentru seturi mari de date - folosim requestAnimationFrame pentru a evita blocarea UI-ului
            const processRowsBatch = (startIndex, batchSize = 50) => {
                const endIndex = Math.min(startIndex + batchSize, totalRows);

                for (let i = startIndex; i < endIndex; i++) {
                    const row = rows[i];

                    // Debug: Check row properties
                    const hasParteRowClass = row.classList.contains('parte-row');
                    const hasDataNume = row.hasAttribute('data-nume');

                    logDebug(`Row ${i + 1}:`, {
                        hasParteRowClass: hasParteRowClass,
                        hasDataNume: hasDataNume,
                        classList: Array.from(row.classList),
                        dataNume: row.getAttribute('data-nume')
                    });

                    // Verificăm dacă este un rând valid (trebuie să aibă clasa parte-row SAU atributul data-nume)
                    if (!hasParteRowClass && !hasDataNume) {
                        logDebug(`Skipping row ${i + 1} - no parte-row class and no data-nume attribute`);
                        continue;
                    }

                    // Obține valorile din celule direct
                    const numeCell = row.querySelector('.nume-parte');
                    const calitateCell = row.querySelector('.calitate-parte');

                    if (!numeCell) {
                        continue;
                    }

                    // Obținem textul din celule
                    const numeText = numeCell.textContent || '';
                    const calitateText = calitateCell ? calitateCell.textContent || '' : '';

                    // Verifică dacă numele sau calitatea conțin termenul de căutare folosind normalizare românească
                    const matchNume = romanianTextContains(numeText, searchTerm);
                    const matchCalitate = romanianTextContains(calitateText, searchTerm);

                    logDebug(`Verificare rând ${i + 1}:`, {
                        nume: numeText,
                        calitate: calitateText,
                        searchTerm: searchTerm,
                        matchNume: matchNume,
                        matchCalitate: matchCalitate
                    });

                    if (matchNume || matchCalitate) {
                        // Afișează rândul
                        row.style.display = '';
                        visibleCount++;

                        // Salvăm textul original în atribute dacă nu există deja
                        if (!row.hasAttribute('data-original-nume') && numeCell) {
                            row.setAttribute('data-original-nume', numeCell.innerHTML);
                        }

                        if (!row.hasAttribute('data-original-calitate') && calitateCell) {
                            row.setAttribute('data-original-calitate', calitateCell.innerHTML);
                        }

                        // Evidențiază textul care se potrivește
                        if (matchNume && numeCell) {
                            const highlightedNume = highlightText(numeText, searchTerm);
                            numeCell.innerHTML = highlightedNume;
                        }

                        if (matchCalitate && calitateCell) {
                            const highlightedCalitate = highlightText(calitateText, searchTerm);
                            calitateCell.innerHTML = highlightedCalitate;
                        }
                    } else {
                        // Ascunde rândul dacă nu se potrivește
                        row.style.display = 'none';
                    }
                }

                // Continuă cu următorul batch sau finalizează
                if (endIndex < totalRows) {
                    requestAnimationFrame(() => processRowsBatch(endIndex, batchSize));
                } else {
                    // Finalizează procesarea
                    logDebug(`Căutare finalizată: ${visibleCount} din ${totalRows} părți găsite`);

                    // Actualizează contorul
                    if (partiCounter) updatePartiCounter(visibleCount, totalRows);

                    // Afișează rezultatele căutării
                    if (searchResults) {
                        const resultsMessage = document.getElementById('resultsMessage');
                        if (resultsMessage) {
                            if (visibleCount === 0) {
                                resultsMessage.textContent = `Nu au fost găsite părți care să conțină "${searchTerm}"`;
                                searchResults.className = 'search-results mt-2 alert alert-warning';
                            } else {
                                resultsMessage.textContent = `Găsite ${visibleCount} părți din ${totalRows} care conțin "${searchTerm}"`;
                                searchResults.className = 'search-results mt-2 alert alert-success';
                            }
                        }
                        searchResults.style.display = 'block';
                    }

                    // Reaplică sortarea dacă este activă
                    const tabelParti = document.getElementById('tabelParti');
                    if (tabelParti) {
                        const sortBy = tabelParti.getAttribute('data-sort-by');
                        const direction = tabelParti.getAttribute('data-sort-direction');
                        if (sortBy && direction) {
                            logDebug('Reaplică sortarea după filtrare:', { sortBy, direction });
                            const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');
                            if (visibleRows.length > 0) {
                                sortPartiTable(tabelParti, sortBy, direction, 'parte-row');
                            }
                        }
                    }
                }
            };

            // Începe procesarea în batch-uri
            processRowsBatch(0);
        }, 300); // Debounce de 300ms pentru performanță optimă
    });

    // Adaugă event listener pentru butonul de ștergere
    if (clearButton) {
        logDebug('Adăugare event listener pentru butonul de ștergere');
        clearButton.addEventListener('click', function() {
            logDebug('Buton de ștergere apăsat');
            searchInput.value = '';
            resetTable(rows);
            if (searchResults) searchResults.style.display = 'none';
            if (partiCounter) updatePartiCounter(rows.length, rows.length);
            searchInput.focus();
        });
    }

    // Adaugă event listener pentru tastele Escape și Enter
    logDebug('Adăugare event listener pentru tastele Escape și Enter');
    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Resetează căutarea la apăsarea tastei Escape
            logDebug('Tasta Escape apăsată, resetare căutare');
            searchInput.value = '';
            resetTable(rows);
            if (searchResults) searchResults.style.display = 'none';
            if (partiCounter) updatePartiCounter(rows.length, rows.length);
        }
    });

    // Inițializare funcționalitate de căutare rapidă
    initQuickSearch(rows, searchInput);

    // Inițializare funcționalitate de căutare globală pentru numele părților
    initGlobalPartySearch();

    // Verificăm dacă funcționalitatea de căutare a fost inițializată corect
    logDebug('Modul de căutare inițializat cu succes');
}

/**
 * Inițializează funcționalitatea de căutare globală pentru numele părților
 * Această funcție adaugă event listeners pentru numele părților care redirecționează către index.php
 */
function initGlobalPartySearch() {
    logDebug('Inițializare funcționalitate de căutare globală pentru numele părților...');

    // Verificăm dacă tabelul de părți există
    const tabelParti = document.getElementById('tabelParti');
    if (!tabelParti) {
        logDebug('Tabelul de părți nu a fost găsit, se ignoră inițializarea căutării globale');
        return;
    }

    // Adăugăm un tooltip pentru a informa utilizatorul despre funcționalitatea de căutare globală
    const tableContainer = tabelParti.closest('.table-responsive');
    if (tableContainer) {
        const tooltip = document.createElement('div');
        tooltip.className = 'search-tooltip';
        tooltip.innerHTML = '<i class="fas fa-info-circle mr-1"></i> Puteți face click pe numele unei părți pentru a căuta toate dosarele asociate';

        tableContainer.insertBefore(tooltip, tabelParti);

        // Ascundem tooltip-ul după 5 secunde
        setTimeout(() => {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                tooltip.remove();
            }, 500);
        }, 5000);
    }

    // Obținem toate celulele cu numele părților
    const numeCells = tabelParti.querySelectorAll('td.nume-parte');

    // Adăugăm event listeners pentru fiecare celulă cu numele părții
    numeCells.forEach((cell, index) => {
        // Adăugăm clasa pentru stilizare și tooltip
        cell.classList.add('clickable-cell', 'global-search-cell');
        cell.setAttribute('title', 'Click pentru a căuta toate dosarele cu această parte');

        // Adăugăm event listener pentru click
        cell.addEventListener('click', function() {
            const text = this.textContent.trim();
            if (text && text !== '-') {
                // Afișăm notificare de informare
                showNotification(`Se caută toate dosarele pentru "${text}"...`, 'info');
                // Redirecționăm către index.php cu parametrul numeParte
                quickSearch(text, null, true);
            }
        });

        logDebug(`Celula nume ${index}: Adăugat event listener pentru căutare globală`);
    });

    logDebug('Funcționalitate de căutare globală inițializată cu succes');
}

/**
 * Inițializează funcționalitatea de căutare rapidă pentru celulele din tabel
 * @param {NodeList} rows - Lista de rânduri din tabel
 * @param {HTMLElement} searchInput - Elementul input pentru căutare
 */
function initQuickSearch(rows, searchInput) {
    logDebug('Inițializare funcționalitate de căutare rapidă...');

    // Parcurgem fiecare rând și adăugăm event listeners pentru celulele clickabile
    rows.forEach((row, index) => {
        // Verificăm dacă este un rând valid (nu un rând de detalii sau alt tip de rând)
        if (!row.classList.contains('parte-row') && !row.hasAttribute('data-nume')) {
            return; // Ignorăm rândurile care nu sunt părți
        }

        // Obținem celulele pentru nume și calitate
        const numeCell = row.querySelector('.nume-parte');
        const calitateCell = row.querySelector('.calitate-parte');

        // Adăugăm clasa clickable-cell și event listener pentru celula nume
        if (numeCell) {
            numeCell.classList.add('clickable-cell');
            numeCell.setAttribute('title', 'Click pentru a căuta acest nume');
            numeCell.addEventListener('click', function() {
                const text = this.textContent.trim();
                if (text && text !== '-') {
                    showNotification(`Căutare rapidă pentru: "${text}"`, 'info');
                    quickSearch(text, searchInput);
                }
            });
            logDebug(`Rândul ${index}: Adăugat event listener pentru celula nume`);
        }

        // Adăugăm clasa clickable-cell și event listener pentru celula calitate
        if (calitateCell) {
            calitateCell.classList.add('clickable-cell');
            calitateCell.setAttribute('title', 'Click pentru a căuta această calitate');
            calitateCell.addEventListener('click', function() {
                const text = this.textContent.trim();
                if (text && text !== '-') {
                    quickSearch(text, searchInput);
                }
            });
            logDebug(`Rândul ${index}: Adăugat event listener pentru celula calitate`);
        }

        // Adăugăm clasa clickable-cell și event listener pentru celula informații suplimentare
        const infoCell = row.cells[2]; // A treia celulă (index 2) este pentru informații suplimentare
        if (infoCell) {
            // Verificăm dacă celula conține text valid pentru căutare
            const badges = infoCell.querySelectorAll('.badge');
            const hasContent = badges.length > 0 || (infoCell.textContent.trim() !== '' && infoCell.textContent.trim() !== '-');

            if (hasContent) {
                infoCell.classList.add('clickable-cell');
                infoCell.setAttribute('title', 'Click pentru a căuta aceste informații');
                infoCell.addEventListener('click', function() {
                    let searchText = '';

                    // Dacă avem badge-uri, extragem textul din ele
                    if (badges.length > 0) {
                        const badgeTexts = Array.from(badges).map(badge => badge.textContent.trim());
                        searchText = badgeTexts.join(' ');
                    } else {
                        // Altfel, folosim textul direct din celulă
                        searchText = this.textContent.trim();
                    }

                    // Verificăm dacă textul nu este doar un placeholder
                    if (searchText && searchText !== '-') {
                        quickSearch(searchText, searchInput);
                    }
                });
                logDebug(`Rândul ${index}: Adăugat event listener pentru celula informații`);
            }
        }

        // Adăugăm un tooltip pentru a informa utilizatorul despre funcționalitatea de căutare rapidă
        if (index === 0) {
            const tooltip = document.createElement('div');
            tooltip.className = 'search-tooltip';
            tooltip.innerHTML = '<i class="fas fa-info-circle mr-1"></i> Puteți face click pe orice celulă pentru a căuta rapid acel text';

            const tabelParti = row.closest('table');
            if (tabelParti && tabelParti.parentNode) {
                tabelParti.parentNode.insertBefore(tooltip, tabelParti);

                // Ascundem tooltip-ul după 5 secunde
                setTimeout(() => {
                    tooltip.style.opacity = '0';
                    setTimeout(() => {
                        tooltip.remove();
                    }, 500);
                }, 5000);
            }
        }
    });

    logDebug('Funcționalitate de căutare rapidă inițializată cu succes');
}

/**
 * Efectuează o căutare rapidă în tabel
 * @param {string} text - Textul care va fi căutat
 * @param {HTMLElement} searchInput - Elementul input pentru căutare (opțional)
 * @param {boolean} redirectToIndex - Dacă true, redirecționează către index.php pentru căutare globală
 */
function quickSearch(text, searchInput = null, redirectToIndex = false) {
    // Curățăm textul de HTML și spații suplimentare
    const cleanText = cleanSearchText(text);

    if (!cleanText) {
        logDebug('Textul pentru căutare rapidă este gol după curățare');
        return;
    }

    logDebug('Căutare rapidă pentru textul:', cleanText);

    // Dacă redirectToIndex este true, redirecționăm către index.php cu parametrul numeParte
    if (redirectToIndex) {
        try {
            logDebug('Redirecționare către index.php cu numeParte:', cleanText);

            // Adăugăm parametrul autoSubmit pentru a trimite automat formularul
            window.location.href = 'index.php?numeParte=' + encodeURIComponent(cleanText) + '&autoSubmit=true';

            // Afișăm un mesaj de încărcare înainte de redirecționare
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'loading-overlay';
            loadingDiv.innerHTML = `
                <div class="loading-content">
                    <div class="spinner"></div>
                    <p>Se caută dosare pentru "${cleanText}"...</p>
                </div>
            `;

            // Adăugăm stiluri pentru overlay
            const style = document.createElement('style');
            style.textContent = `
                .loading-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    z-index: 9999;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
                .loading-content {
                    background-color: white;
                    padding: 20px;
                    border-radius: 5px;
                    text-align: center;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
                }
                .spinner {
                    width: 40px;
                    height: 40px;
                    margin: 0 auto 10px;
                    border: 4px solid #f3f3f3;
                    border-top: 4px solid #3498db;
                    border-radius: 50%;
                    animation: spin 2s linear infinite;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;

            document.head.appendChild(style);
            document.body.appendChild(loadingDiv);
        } catch (error) {
            console.error('Eroare la redirecționare:', error);
            alert('A apărut o eroare la redirecționare. Vă rugăm să încercați din nou.');
        }
        return;
    }

    // Dacă nu redirecționăm, continuăm cu căutarea locală
    if (!searchInput) {
        logDebug('Nu s-a furnizat un element input pentru căutare');
        return;
    }

    // Populăm câmpul de căutare cu textul curățat
    searchInput.value = cleanText;

    // Declanșăm evenimentul de input pentru a activa căutarea
    const inputEvent = new Event('input', {
        bubbles: true,
        cancelable: true
    });

    searchInput.dispatchEvent(inputEvent);

    // Focusăm câmpul de căutare pentru a permite utilizatorului să continue căutarea
    searchInput.focus();

    // Adăugăm o animație subtilă pentru a evidenția că s-a efectuat o căutare
    searchInput.classList.add('search-highlight');
    setTimeout(() => {
        searchInput.classList.remove('search-highlight');
    }, 500);
}

/**
 * Curăță textul pentru căutare de HTML și spații suplimentare
 * @param {string} text - Textul care trebuie curățat
 * @returns {string} - Textul curățat
 */
function cleanSearchText(text) {
    if (!text) return '';

    // Creăm un element temporar pentru a curăța HTML-ul
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = text;

    // Obținem textul fără HTML
    let cleanText = tempDiv.textContent || tempDiv.innerText || '';

    // Eliminăm spațiile suplimentare și caracterele speciale
    cleanText = cleanText.replace(/\s+/g, ' ').trim();

    // Eliminăm caracterele speciale care ar putea cauza probleme în căutare
    cleanText = cleanText.replace(/[^\w\s\-.,]/g, '');

    return cleanText;
}

// Închiderea evenimentului DOMContentLoaded

/**
 * Evidențiază textul care se potrivește cu termenul de căutare folosind normalizare românească
 * @param {string} text - Textul în care se caută
 * @param {string} term - Termenul de căutare
 * @returns {string} - Textul cu evidențierea termenului de căutare
 */
function highlightText(text, term) {
    // Verifică dacă textul sau termenul de căutare sunt goale
    if (!term || !text) return text;

    try {
        // Pentru căutarea cu diacritice românești, folosim o abordare mai complexă
        const normalizedTerm = normalizeRomanianText(term);
        const normalizedText = normalizeRomanianText(text);

        // Dacă nu găsim potrivire cu textul normalizat, returnăm textul original
        if (!normalizedText.includes(normalizedTerm)) {
            return text;
        }

        // Creează un regex pentru a găsi toate aparițiile termenului (case insensitive)
        // Înlocuim diacriticele cu pattern-uri care să potrivească ambele variante
        let regexPattern = escapeRegExp(term)
            .replace(/[aăâ]/gi, '[aăâ]')
            .replace(/[iî]/gi, '[iî]')
            .replace(/[sș]/gi, '[sș]')
            .replace(/[tț]/gi, '[tț]');

        const regex = new RegExp(`(${regexPattern})`, 'gi');

        // Înlocuiește toate aparițiile cu versiunea evidențiată
        return text.replace(regex, '<span class="highlight">$1</span>');
    } catch (error) {
        // În caz de eroare (regex invalid), returnăm textul original
        console.error('Eroare la evidențierea textului:', error);
        return text;
    }
}

/**
 * Resetează tabelul la starea inițială
 * @param {NodeList} rows - Lista de rânduri din tabel
 */
function resetTable(rows) {
    logDebug('Resetare tabel, număr de rânduri:', rows.length);

    let processedCount = 0;
    let skippedCount = 0;

    rows.forEach((row, index) => {
        // Debug: Check each row
        const hasParteRowClass = row.classList.contains('parte-row');
        const hasDataNume = row.hasAttribute('data-nume');

        logDebug(`Reset row ${index + 1}:`, {
            hasParteRowClass: hasParteRowClass,
            hasDataNume: hasDataNume,
            classList: Array.from(row.classList)
        });

        // Verificăm dacă este un rând valid (nu un rând de detalii sau alt tip de rând)
        if (!hasParteRowClass && !hasDataNume) {
            logDebug(`Rândul ${index + 1} nu este un rând de parte, se ignoră la resetare`);
            skippedCount++;
            return; // Ignorăm rândurile care nu sunt părți
        }

        processedCount++;

        // Afișează toate rândurile
        row.style.display = '';
        logDebug(`Rândul ${index + 1} resetat și afișat`);

        // Resetează textul evidențiat în coloana Nume
        const numeCell = row.querySelector('.nume-parte');
        if (numeCell) {
            // Verificăm dacă avem textul original salvat
            if (row.hasAttribute('data-original-nume')) {
                numeCell.innerHTML = row.getAttribute('data-original-nume');
                logDebug(`Rândul ${index}: Resetare nume din data-original-nume`);
            } else {
                // Dacă nu avem textul original salvat, folosim atributul data-nume
                const numeAttr = row.getAttribute('data-nume') || '';
                numeCell.textContent = numeAttr;
                logDebug(`Rândul ${index}: Resetare nume din data-nume`);
            }
        }

        // Resetează textul evidențiat în coloana Calitate
        const calitateCell = row.querySelector('.calitate-parte');
        if (calitateCell) {
            // Verificăm dacă avem textul original salvat
            if (row.hasAttribute('data-original-calitate')) {
                calitateCell.innerHTML = row.getAttribute('data-original-calitate');
                logDebug(`Rândul ${index}: Resetare calitate din data-original-calitate`);
            } else {
                // Dacă nu avem textul original salvat, folosim atributul data-calitate
                const calitateAttr = row.getAttribute('data-calitate') || '';
                if (calitateAttr) {
                    calitateCell.textContent = calitateAttr;
                } else {
                    calitateCell.innerHTML = '<span class="text-muted">-</span>';
                }
                logDebug(`Rândul ${index}: Resetare calitate din data-calitate`);
            }
        }
    });

    // Verifică dacă există o sortare activă și o aplică din nou
    const tabelParti = document.getElementById('tabelParti');
    if (tabelParti) {
        const activeHeader = tabelParti.querySelector('th.sort-active');
        if (activeHeader) {
            const sortBy = activeHeader.getAttribute('data-sort');
            const direction = activeHeader.getAttribute('data-direction');
            if (sortBy && direction) {
                sortPartiTable(tabelParti, sortBy, direction, 'parte-row');
            }
        }
    }

    logDebug(`Reset table summary: ${processedCount} rows processed, ${skippedCount} rows skipped`);
    console.log(`RESET TABLE: Processed ${processedCount} rows, Skipped ${skippedCount} rows`);
}

/**
 * Actualizează contorul de părți
 * @param {number} visible - Numărul de părți vizibile
 * @param {number} total - Numărul total de părți
 */
function updatePartiCounter(visible, total) {
    const partiCounter = document.querySelector('.parti-counter');
    if (partiCounter) {
        logDebug(`Actualizare contor părți: ${visible} din ${total}`);

        // Actualizează textul contorului
        if (visible === total) {
            partiCounter.textContent = `${visible} părți`;
        } else {
            partiCounter.textContent = `${visible} din ${total} părți`;
        }

        // Schimbă culoarea contorului în funcție de rezultate
        if (visible === 0) {
            partiCounter.classList.remove('bg-secondary', 'bg-success');
            partiCounter.classList.add('bg-danger');
            logDebug('Contor părți: stil pentru niciun rezultat (roșu)');
        } else if (visible < total) {
            partiCounter.classList.remove('bg-secondary', 'bg-danger');
            partiCounter.classList.add('bg-success');
            logDebug('Contor părți: stil pentru rezultate parțiale (verde)');
        } else {
            partiCounter.classList.remove('bg-success', 'bg-danger');
            partiCounter.classList.add('bg-secondary');
            logDebug('Contor părți: stil pentru toate rezultatele (gri)');
        }
    } else {
        logDebug('Contorul de părți nu a fost găsit în DOM');
    }
}

/**
 * Escapează caracterele speciale din regex
 * @param {string} string - Stringul care trebuie escapat
 * @returns {string} - Stringul escapat
 */
function escapeRegExp(string) {
    // Verifică dacă stringul este valid
    if (typeof string !== 'string') {
        console.error('escapeRegExp: Argumentul nu este un string valid', string);
        return '';
    }

    // Escapează toate caracterele speciale din regex
    // Caracterele care trebuie escapate: . * + ? ^ $ { } ( ) | [ ] \
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Inițializează funcționalitatea de sortare pentru tabele
 */
function initTableSort() {
    // Inițializează sortarea pentru tabelul de ședințe
    initTableSortForTable('tabelSedinte', 'sedinta-row', 'detail-row');

    // Inițializează sortarea pentru tabelul de părți implicate
    initTableSortForTable('tabelParti', 'parte-row');
}

/**
 * Inițializează funcționalitatea de sortare pentru un tabel specific
 * @param {string} tableId - ID-ul tabelului
 * @param {string} rowClass - Clasa rândurilor principale
 * @param {string} detailRowClass - Clasa rândurilor de detalii (opțional)
 */
function initTableSortForTable(tableId, rowClass, detailRowClass = null) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const headers = table.querySelectorAll('th.sortable');

    headers.forEach(header => {
        header.addEventListener('click', function() {
            const sortBy = this.getAttribute('data-sort');
            const currentDirection = this.getAttribute('data-direction') || 'none';

            // Resetează toate antetele
            headers.forEach(h => {
                h.querySelector('.sort-icon').classList.remove('sort-asc', 'sort-desc');
                h.classList.remove('sort-active');
                h.removeAttribute('data-direction');
            });

            // Setează direcția de sortare
            let direction = 'asc';
            if (currentDirection === 'asc') {
                direction = 'desc';
            }

            // Actualizează antetul curent
            this.setAttribute('data-direction', direction);
            this.querySelector('.sort-icon').classList.add('sort-' + direction);
            this.classList.add('sort-active');

            // Sortează tabelul
            if (tableId === 'tabelSedinte') {
                sortTable(table, sortBy, direction, rowClass, detailRowClass);
            } else if (tableId === 'tabelParti') {
                sortPartiTable(table, sortBy, direction, rowClass);
            }
        });
    });
}

/**
 * Sortează tabelul în funcție de coloana și direcția specificate
 * @param {HTMLElement} table - Tabelul care trebuie sortat
 * @param {string} sortBy - Coloana după care se sortează
 * @param {string} direction - Direcția de sortare ('asc' sau 'desc')
 * @param {string} rowClass - Clasa rândurilor principale
 * @param {string} detailRowClass - Clasa rândurilor de detalii (opțional)
 */
function sortTable(table, sortBy, direction, rowClass, detailRowClass = null) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll(`tr.${rowClass}`));

    // Sortează rândurile
    rows.sort((a, b) => {
        const aValue = getRowValue(a, sortBy);
        const bValue = getRowValue(b, sortBy);

        return compareValues(aValue, bValue, direction);
    });

    // Reordonează rândurile în tabel
    rows.forEach(row => {
        tbody.appendChild(row);

        // Mută și rândul de detalii dacă există
        if (detailRowClass) {
            const rowIndex = row.getAttribute('data-index');
            const detailRow = tbody.querySelector(`tr.${detailRowClass}[data-parent="${rowIndex}"]`);
            if (detailRow) {
                tbody.appendChild(detailRow);
            }
        }
    });
}

/**
 * Sortează tabelul de părți implicate în funcție de coloana și direcția specificate
 * @param {HTMLElement} table - Tabelul care trebuie sortat
 * @param {string} sortBy - Coloana după care se sortează
 * @param {string} direction - Direcția de sortare ('asc' sau 'desc')
 * @param {string} rowClass - Clasa rândurilor
 */
function sortPartiTable(table, sortBy, direction, rowClass) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll(`tr.${rowClass}`));

    // Sortează rândurile
    rows.sort((a, b) => {
        const aValue = getPartiRowValue(a, sortBy);
        const bValue = getPartiRowValue(b, sortBy);

        return compareValues(aValue, bValue, direction);
    });

    // Reordonează rândurile în tabel
    rows.forEach(row => {
        tbody.appendChild(row);
    });

    // Actualizează contorul de părți vizibile
    const visibleRows = rows.filter(row => row.style.display !== 'none');
    updatePartiCounter(visibleRows.length, rows.length);
}

/**
 * Obține valoarea unei celule din rând pentru sortare (tabel ședințe)
 * @param {HTMLElement} row - Rândul din care se extrage valoarea
 * @param {string} sortBy - Coloana pentru care se extrage valoarea
 * @returns {string} - Valoarea pentru sortare
 */
function getRowValue(row, sortBy) {
    // Obține celula corespunzătoare coloanei de sortare
    const cellIndex = {
        'data': 0,
        'ora': 1,
        'complet': 2,
        'solutie': 3,
        'dataPronuntare': 4,
        'document': 5
    };

    const index = cellIndex[sortBy] || 0;
    const cell = row.cells[index];

    if (!cell) return '';

    // Obține valoarea din atributul data-value
    let value = cell.getAttribute('data-value') || '';

    // Obține valoarea în funcție de tipul coloanei
    switch(sortBy) {
        case 'data':
        case 'dataPronuntare':
            // Pentru coloanele de dată, convertim în format sortabil
            if (value) {
                // Convertim data din format DD.MM.YYYY în YYYY-MM-DD pentru sortare corectă
                const parts = value.split('.');
                if (parts.length === 3) {
                    value = `${parts[2]}-${parts[1]}-${parts[0]}`;
                }
            }
            break;
        case 'ora':
            // Pentru coloana de oră, convertim în format sortabil
            if (value) {
                // Asigurăm-ne că ora este în format HH:MM pentru sortare corectă
                const timeParts = value.split(':');
                if (timeParts.length === 2) {
                    const hours = timeParts[0].padStart(2, '0');
                    const minutes = timeParts[1].padStart(2, '0');
                    value = `${hours}:${minutes}`;
                }
            }
            break;
        default:
            // Pentru celelalte coloane, folosim valoarea directă
            break;
    }

    return value.toLowerCase();
}

/**
 * Obține valoarea unei celule din rând pentru sortare (tabel părți implicate)
 * @param {HTMLElement} row - Rândul din care se extrage valoarea
 * @param {string} sortBy - Coloana pentru care se extrage valoarea
 * @returns {string} - Valoarea pentru sortare
 */
function getPartiRowValue(row, sortBy) {
    let value = '';

    // Obține valoarea în funcție de coloana de sortare
    switch(sortBy) {
        case 'nume':
            // Obține valoarea din atributul data-nume
            value = row.getAttribute('data-nume') || '';
            break;
        case 'calitate':
            // Obține valoarea din atributul data-calitate
            value = row.getAttribute('data-calitate') || '';
            break;
        case 'info':
            // Obține valoarea din atributul data-info
            value = row.getAttribute('data-info') || '';
            // Prioritizăm părțile declaratoare pentru sortare
            if (value === 'parte_declaratoare') {
                value = 'a'; // Pentru a le pune primele în sortarea ascendentă
            } else {
                value = 'z'; // Pentru a le pune ultimele în sortarea ascendentă
            }
            break;
        default:
            // Pentru alte coloane, folosim indexul pentru a păstra ordinea inițială
            value = row.getAttribute('data-index') || '0';
            break;
    }

    return value.toLowerCase();
}

/**
 * Compară două valori pentru sortare
 * @param {string} a - Prima valoare
 * @param {string} b - A doua valoare
 * @param {string} direction - Direcția de sortare ('asc' sau 'desc')
 * @returns {number} - Rezultatul comparației (-1, 0, 1)
 */
function compareValues(a, b, direction) {
    // Tratăm valorile goale special - le punem la sfârșit indiferent de direcția de sortare
    if (a === '' && b !== '') return 1;
    if (a !== '' && b === '') return -1;
    if (a === '' && b === '') return 0;

    // Verificăm dacă valorile sunt date
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    const isDate = dateRegex.test(a) && dateRegex.test(b);

    // Verificăm dacă valorile sunt ore
    const timeRegex = /^\d{2}:\d{2}$/;
    const isTime = timeRegex.test(a) && timeRegex.test(b);

    let result;

    if (isDate) {
        // Comparăm datele
        const dateA = new Date(a);
        const dateB = new Date(b);
        result = dateA - dateB;
    } else if (isTime) {
        // Comparăm orele
        result = a.localeCompare(b);
    } else {
        // Comparăm textul
        result = a.localeCompare(b, 'ro', { sensitivity: 'base' });
    }

    // Inversăm rezultatul pentru sortare descendentă
    return direction === 'asc' ? result : -result;
}

/**
 * Inițializează funcționalitățile de export și distribuire
 */
function initExportFunctions() {
    // Buton pentru salvare PDF (header)
    const printBtnHeader = document.getElementById('printBtnHeader');
    if (printBtnHeader) {
        printBtnHeader.addEventListener('click', printDosar);
    }
}

/**
 * Generează un document PDF cu informațiile dosarului
 *
 * @param {string} disposition - Tipul de afișare: 'inline' pentru vizualizare în browser, 'attachment' pentru descărcare
 */
function generatePDF(disposition = 'inline') {
    // Obținem parametrii din URL
    const urlParams = new URLSearchParams(window.location.search);
    const numarDosar = urlParams.get('numar');
    const institutie = urlParams.get('institutie');

    if (!numarDosar || !institutie) {
        showNotification('Parametrii necesari pentru generarea PDF lipsesc.', 'danger');
        return;
    }

    // Afișăm un mesaj de așteptare
    const actionText = disposition === 'attachment' ? 'descărcare' : 'vizualizare';
    showNotification(`Se pregătește documentul PDF pentru ${actionText}...`, 'info');
    showLoading(`Se pregătește documentul PDF pentru ${actionText}...`);

    // Adăugăm un timestamp pentru a evita cache-ul
    const timestamp = new Date().getTime();

    // Construim URL-ul pentru generarea PDF
    const pdfUrl = `generate_pdf.php?numar=${encodeURIComponent(numarDosar)}&institutie=${encodeURIComponent(institutie)}&disposition=${disposition}&t=${timestamp}`;

    if (disposition === 'inline') {
        // Pentru vizualizare, deschidem într-o fereastră nouă
        const newWindow = window.open(pdfUrl, '_blank');

        // Verificăm dacă fereastra a fost deschisă cu succes
        if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
            hideLoading();
            showNotification('Browserul a blocat deschiderea unei ferestre noi. Vă rugăm să permiteți pop-up-urile pentru acest site.', 'warning');
        } else {
            // Ascundem indicatorul de încărcare după un scurt delay
            setTimeout(function() {
                hideLoading();
                showNotification('Documentul PDF a fost deschis într-o fereastră nouă.', 'success');
            }, 1000);
        }
    } else {
        // Pentru descărcare, folosim un link direct
        try {
            // Creăm un link ascuns
            const link = document.createElement('a');
            link.href = pdfUrl;
            link.download = `Dosar_${numarDosar.replace(/[\/\\:*?"<>|]/g, '_')}_${new Date().toISOString().slice(0, 10)}.pdf`;
            link.style.display = 'none';
            document.body.appendChild(link);

            // Simulăm click pe link
            link.click();

            // Ascundem indicatorul de încărcare după un scurt delay
            setTimeout(function() {
                hideLoading();
                showNotification('Documentul PDF a fost descărcat cu succes. Verificați folderul de descărcări al browserului.', 'success');
                document.body.removeChild(link);
            }, 1000);
        } catch (e) {
            console.error('Eroare la descărcarea PDF-ului:', e);

            // Dacă metoda directă eșuează, folosim iframe ca backup
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            document.body.appendChild(iframe);

            // Adăugăm un handler pentru a detecta când iframe-ul a terminat de încărcat
            iframe.onload = function() {
                // Ascundem indicatorul de încărcare
                hideLoading();

                // Verificăm dacă iframe-ul conține un PDF sau o pagină de eroare
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                    // Dacă avem acces la document, probabil este o pagină de eroare HTML
                    if (iframeDoc && iframeDoc.body.innerHTML) {
                        // Verificăm dacă conține un mesaj de eroare
                        const errorContainer = iframeDoc.querySelector('.error-container');
                        if (errorContainer) {
                            const errorDescription = errorContainer.querySelector('.error-description');
                            const errorMessage = errorDescription ? errorDescription.textContent : 'Eroare la generarea PDF-ului.';
                            showNotification(errorMessage, 'danger');
                        } else {
                            showNotification('A apărut o eroare la generarea PDF-ului.', 'danger');
                        }
                    } else {
                        // Dacă nu putem accesa documentul, probabil este un PDF descărcat cu succes
                        showNotification('Documentul PDF a fost descărcat cu succes. Verificați folderul de descărcări al browserului.', 'success');
                    }
                } catch (e) {
                    console.error('Eroare la verificarea iframe:', e);
                    // Dacă avem o excepție de securitate, probabil PDF-ul a fost descărcat cu succes
                    showNotification('Documentul PDF a fost descărcat cu succes. Verificați folderul de descărcări al browserului.', 'success');
                }

                // Ștergem iframe-ul după un scurt delay
                setTimeout(function() {
                    document.body.removeChild(iframe);
                }, 1000);
            };

            // Adăugăm un handler pentru erori
            iframe.onerror = function() {
                console.error('Eroare la încărcarea iframe-ului');
                hideLoading();
                showNotification('A apărut o eroare la generarea PDF-ului. Vă rugăm să încercați din nou.', 'danger');
                document.body.removeChild(iframe);
            };

            // Inițiem descărcarea
            iframe.src = pdfUrl;
        }
    }
}


/**
 * Deschide modalul pentru trimitere email
 */
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
    }
}

/**
 * Închide modalul
 */
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Trimite informațiile dosarului pe email
 */
function sendEmail() {
    // Obținem valorile din formular
    const emailAddress = document.getElementById('emailAddress').value;
    const emailFormat = document.querySelector('input[name="emailFormat"]:checked').value;
    const emailSubject = document.getElementById('emailSubject').value;
    const emailMessage = document.getElementById('emailMessage').value;

    // Validăm adresa de email
    if (!emailAddress || !validateEmail(emailAddress)) {
        showNotification('Vă rugăm să introduceți o adresă de email validă.', 'warning');
        return;
    }

    // Afișăm un mesaj de așteptare
    showNotification('Se trimite email-ul...', 'info');
    showLoading('Se trimite email-ul...');

    // Simulăm trimiterea email-ului (în implementarea reală, aici ar fi un apel AJAX către server)
    setTimeout(function() {
        // Închide modalul
        closeModal('emailModal');
        hideLoading();

        // Afișăm mesaj de confirmare
        showNotification(`Email-ul a fost trimis cu succes la adresa ${emailAddress}.`, 'success');

        // Resetăm formularul
        document.getElementById('emailForm').reset();
        document.getElementById('emailSubject').value = 'Informații dosar ' + getDosarInfo().numar;
    }, 2000);
}

/**
 * Validează o adresă de email
 */
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
}

// Variabilă globală pentru a preveni apelurile multiple simultane
let isPrinting = false;

// Variabilă pentru a stoca titlul original al paginii
let originalPageTitle = '';

/**
 * Generează un nume de fișier valid pentru PDF pe baza numărului dosarului
 * @param {string} numarDosar - Numărul dosarului (ex: "2623/30/2023")
 * @returns {string} - Numele de fișier formatat (ex: "Dosar nr2623_30_2023")
 */
function generatePdfFilename(numarDosar) {
    try {
        if (!numarDosar || typeof numarDosar !== 'string') {
            console.warn('Numărul dosarului nu este valid pentru generarea numelui de fișier');
            return 'Dosar_detalii';
        }

        // Curățăm numărul dosarului de caractere speciale și spații
        let cleanNumber = numarDosar.trim();

        // Înlocuim caracterele problematice pentru sistemul de fișiere
        cleanNumber = cleanNumber
            .replace(/\//g, '_')        // Înlocuim slash-urile cu underscore
            .replace(/\\/g, '_')        // Înlocuim backslash-urile cu underscore
            .replace(/:/g, '_')         // Înlocuim două puncte cu underscore
            .replace(/\*/g, '_')        // Înlocuim asteriscurile cu underscore
            .replace(/\?/g, '_')        // Înlocuim semnele întrebării cu underscore
            .replace(/"/g, '_')         // Înlocuim ghilimelele cu underscore
            .replace(/</g, '_')         // Înlocuim < cu underscore
            .replace(/>/g, '_')         // Înlocuim > cu underscore
            .replace(/\|/g, '_')        // Înlocuim pipe cu underscore
            .replace(/\s+/g, '_')       // Înlocuim spațiile cu underscore
            .replace(/_+/g, '_')        // Înlocuim underscore-urile multiple cu unul singur
            .replace(/^_|_$/g, '');     // Eliminăm underscore-urile de la început și sfârșit

        // Construim numele de fișier final
        const filename = `Dosar nr${cleanNumber}`;

        console.log(`Nume fișier generat: "${filename}" din numărul dosarului: "${numarDosar}"`);
        return filename;

    } catch (error) {
        console.error('Eroare la generarea numelui de fișier:', error);
        return 'Dosar_detalii';
    }
}

/**
 * Salvează informațiile dosarului ca PDF
 */
function printDosar() {
    // Verificăm dacă o operație de printare este deja în curs
    if (isPrinting) {
        showNotification('O operație de printare este deja în curs. Vă rugăm să așteptați.', 'warning');
        return;
    }

    // Marcăm că o operație de printare este în curs
    isPrinting = true;

    // Afișăm notificare de informare
    showNotification('Se pregătește documentul pentru printare...', 'info');

    // Pregătim conținutul pentru printare
    const printContent = document.getElementById('printContent');
    const printVersion = document.getElementById('printVersion');

    // Verificăm dacă elementele necesare există
    if (!printContent) {
        console.error('Elementul #printContent nu a fost găsit!');
        showNotification('Eroare: Nu s-a putut pregăti documentul pentru printare.', 'danger');
        isPrinting = false; // Resetăm starea
        return;
    }

    if (!printVersion) {
        console.error('Elementul #printVersion nu a fost găsit!');
        showNotification('Eroare: Nu s-a putut pregăti documentul pentru printare.', 'danger');
        isPrinting = false; // Resetăm starea
        return;
    }

    // IMPORTANT: Curățăm conținutul anterior pentru a preveni duplicarea
    printContent.innerHTML = '';

    // Obținem informațiile dosarului
    const dosarInfo = getDosarInfo();

    // Verificăm dacă avem date valide
    if (!dosarInfo || !dosarInfo.numar) {
        console.error('Nu s-au putut extrage informațiile dosarului!');
        showNotification('Eroare: Nu s-au putut extrage informațiile dosarului pentru printare.', 'danger');
        isPrinting = false; // Resetăm starea
        return;
    }

    // Validăm numărul dosarului pentru generarea numelui de fișier
    if (typeof dosarInfo.numar !== 'string' || dosarInfo.numar.trim() === '') {
        console.warn('Numărul dosarului nu este valid, se va folosi un nume de fișier generic');
        dosarInfo.numar = 'Nedisponibil';
    }

    // Construim conținutul HTML pentru printare
    let html = `
        <div class="dosar-info">
            <h2>Informații generale</h2>
            <p><strong>Număr dosar:</strong> ${dosarInfo.numar}</p>
    `;

    if (dosarInfo.numarVechi) {
        html += `<p><strong>Număr vechi:</strong> ${dosarInfo.numarVechi}</p>`;
    }

    html += `
            <p><strong>Instanță:</strong> ${dosarInfo.instanta}</p>
    `;

    if (dosarInfo.departament) {
        html += `<p><strong>Departament/Secție:</strong> ${dosarInfo.departament}</p>`;
    }

    html += `
            <p><strong>Data:</strong> ${dosarInfo.data || 'Nedisponibilă'}</p>
            <p><strong>Obiect:</strong> ${dosarInfo.obiect || 'Nedisponibil'}</p>
            <p><strong>Stadiu procesual:</strong> ${dosarInfo.stadiuProcesual || 'Nedisponibil'}</p>
            <p><strong>Categorie caz:</strong> ${dosarInfo.categorieCaz || 'Nedisponibilă'}</p>
            <p><strong>Data ultimei modificări:</strong> ${dosarInfo.dataModificare || 'Nedisponibilă'}</p>
        </div>
    `;

    // Adăugăm părțile implicate cu informații complete și optimizare pentru PDF
    if (dosarInfo.parti && dosarInfo.parti.length > 0) {
        html += `
            <div class="parti-implicate">
                <h2>Părți implicate</h2>
                <table border="1" cellpadding="3" cellspacing="0" width="100%" style="table-layout: fixed;">
                    <thead>
                        <tr>
                            <th style="width: 42%;">Nume</th>
                            <th style="width: 28%;">Calitate</th>
                            <th style="width: 30%;">Informații suplimentare</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        dosarInfo.parti.forEach(parte => {
            let informatiiSuplimentare = '';

            // Construim informațiile suplimentare
            if (parte.esteDeclaratoare) {
                informatiiSuplimentare += 'Parte declaratoare';

                if (parte.tipuriCaleAtac && parte.tipuriCaleAtac.length > 0) {
                    informatiiSuplimentare += ' (' + parte.tipuriCaleAtac.join(', ') + ')';
                }
            } else if (parte.tipuriCaleAtac && parte.tipuriCaleAtac.length > 0) {
                informatiiSuplimentare = parte.tipuriCaleAtac.join(', ');
            }

            if (!informatiiSuplimentare) {
                informatiiSuplimentare = '-';
            }

            // Curățăm și formatăm datele pentru afișare completă în PDF (fără truncare)
            const nume = parte.nume || '-';
            const calitate = parte.calitate || '-';

            html += `
                <tr>
                    <td style="width: 42%; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto;">${nume}</td>
                    <td style="width: 28%; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto;">${calitate}</td>
                    <td style="width: 30%; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto;">${informatiiSuplimentare}</td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;
    }

    // Adăugăm ședințele de judecată cu optimizare pentru PDF
    if (dosarInfo.sedinte && dosarInfo.sedinte.length > 0) {
        html += `
            <div class="sedinte">
                <h2>Ședințe de judecată</h2>
                <table border="1" cellpadding="3" cellspacing="0" width="100%" style="table-layout: fixed;">
                    <thead>
                        <tr>
                            <th style="width: 8%;">Data ședință</th>
                            <th style="width: 5%;">Ora</th>
                            <th style="width: 10%;">Complet</th>
                            <th style="width: 52%;">Soluție</th>
                            <th style="width: 8%;">Data pronunțare</th>
                            <th style="width: 17%;">Document</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        dosarInfo.sedinte.forEach(sedinta => {
            // Curățăm și formatăm datele pentru afișare completă în PDF (fără truncare)
            const data = sedinta.data || '-';
            const ora = sedinta.ora || '-';
            const complet = sedinta.complet || '-';
            const solutie = sedinta.solutie || '-';
            const dataPronuntare = sedinta.dataPronuntare || '-';
            const document = sedinta.document || '-';

            html += `
                <tr>
                    <td style="width: 8%; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto; text-align: justify;">${data}</td>
                    <td style="width: 5%; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto; text-align: justify;">${ora}</td>
                    <td style="width: 10%; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto; text-align: justify;">${complet}</td>
                    <td style="width: 52%; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto; text-align: justify;">${solutie}</td>
                    <td style="width: 8%; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto; text-align: justify;">${dataPronuntare}</td>
                    <td style="width: 17%; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto; text-align: justify;">${document}</td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;
    }

    // Adăugăm căile de atac cu optimizare pentru PDF
    if (dosarInfo.caiAtac && dosarInfo.caiAtac.length > 0) {
        html += `
            <div class="cai-atac">
                <h2>Căi de atac</h2>
                <table border="1" cellpadding="3" cellspacing="0" width="100%" style="table-layout: fixed;">
                    <thead>
                        <tr>
                            <th style="width: 12%;">Data declarare</th>
                            <th style="width: 38%;">Parte declaratoare</th>
                            <th style="width: 18%;">Tip cale atac</th>
                            <th style="width: 32%;">Dosar instanță superioară</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        dosarInfo.caiAtac.forEach(caleAtac => {
            // Curățăm și formatăm datele pentru afișare completă în PDF (fără truncare)
            const dataDeclarare = caleAtac.dataDeclarare || '-';
            const parteDeclaratoare = caleAtac.parteDeclaratoare || '-';
            const tipCaleAtac = caleAtac.tipCaleAtac || '-';
            const numarDosarInstantaSuperior = caleAtac.numarDosarInstantaSuperior || '-';

            html += `
                <tr>
                    <td style="width: 12%; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto;">${dataDeclarare}</td>
                    <td style="width: 38%; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto;">${parteDeclaratoare}</td>
                    <td style="width: 18%; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto;">${tipCaleAtac}</td>
                    <td style="width: 32%; word-wrap: break-word; overflow-wrap: break-word; hyphens: auto;">${numarDosarInstantaSuperior}</td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;
    }

    // Adăugăm nota de subsol
    html += `
        <div class="footer" style="margin-top: 20px; font-size: 10px; text-align: center;">
            <p>Document generat de Portalul Dosare Judecătorești. Informațiile sunt preluate direct de la Portalul Instanțelor de Judecată.</p>
        </div>
    `;

    // Actualizăm conținutul pentru printare - folosim o singură operație
    printContent.innerHTML = html;

    // Forțăm o repaintare pentru a asigura că conținutul este complet încărcat
    printContent.offsetHeight;

    // Setăm titlul documentului pentru numele de fișier PDF
    try {
        // Salvăm titlul original al paginii
        originalPageTitle = document.title;

        // Generăm numele de fișier pe baza numărului dosarului
        const pdfFilename = generatePdfFilename(dosarInfo.numar);

        // Setăm titlul documentului pentru a influența numele de fișier PDF
        document.title = pdfFilename;

        console.log(`Titlul documentului setat la: "${pdfFilename}" pentru printare`);

    } catch (error) {
        console.error('Eroare la setarea titlului documentului:', error);
        // Continuăm cu printarea chiar dacă setarea titlului a eșuat
    }

    // Adăugăm un mic delay pentru a asigura că browserul a procesat complet conținutul și titlul
    setTimeout(function() {
        // Deschidem dialogul de printare
        window.print();

        // Afișăm notificare de succes după printare și resetăm starea
        setTimeout(function() {
            // Restaurăm titlul original al paginii
            try {
                if (originalPageTitle) {
                    document.title = originalPageTitle;
                    console.log('Titlul original al paginii a fost restaurat');
                }
            } catch (error) {
                console.error('Eroare la restaurarea titlului original:', error);
            }

            showNotification('Documentul PDF este gata pentru salvare.', 'success');
            isPrinting = false; // Resetăm starea pentru a permite printări ulterioare
        }, 500);
    }, 200); // Mărim delay-ul pentru a permite browserului să proceseze titlul
}

/**
 * Obține informațiile dosarului din pagină
 */
function getDosarInfo() {
    const dosarInfo = {
        numar: '',
        numarVechi: '',
        instanta: '',
        departament: '',
        data: '',
        obiect: '',
        stadiuProcesual: '',
        categorieCaz: '',
        dataModificare: '',
        parti: [],
        sedinte: [],
        caiAtac: []
    };

    // Extragere robustă a informațiilor generale folosind selectori specifici
    try {
        // Numărul dosarului
        const numarElement = document.querySelector('.dosar-header h3');
        if (numarElement) {
            dosarInfo.numar = numarElement.textContent.trim();
        }

        // Numărul vechi (opțional)
        const numarVechiElement = document.querySelector('.dosar-header .text-muted');
        if (numarVechiElement && numarVechiElement.textContent.includes('Număr vechi:')) {
            dosarInfo.numarVechi = numarVechiElement.textContent.replace('Număr vechi:', '').trim();
        }

        // Extragere informații folosind text-ul etichetelor pentru robustețe
        const dosarHeaderPs = document.querySelectorAll('.dosar-header p');
        dosarHeaderPs.forEach(p => {
            const strongElement = p.querySelector('strong');
            if (strongElement) {
                const label = strongElement.textContent.trim();
                const value = p.textContent.replace(strongElement.textContent, '').trim();

                switch (label) {
                    case 'Instanță:':
                        dosarInfo.instanta = value;
                        break;
                    case 'Departament/Secție:':
                        dosarInfo.departament = value;
                        break;
                    case 'Data:':
                        dosarInfo.data = value;
                        break;
                    case 'Obiect:':
                        dosarInfo.obiect = value;
                        break;
                    case 'Stadiu procesual:':
                        dosarInfo.stadiuProcesual = value;
                        break;
                    case 'Categorie caz:':
                        dosarInfo.categorieCaz = value;
                        break;
                    case 'Data ultimei modificări:':
                        dosarInfo.dataModificare = value;
                        break;
                }
            }
        });
    } catch (error) {
        console.error('Eroare la extragerea informațiilor generale:', error);
    }

    // Obținem părțile implicate cu informații complete
    try {
        const partiRows = document.querySelectorAll('#tabelParti tbody tr.parte-row');
        partiRows.forEach(row => {
            if (row.cells && row.cells.length >= 3) {
                const nume = row.cells[0]?.textContent.trim() || '';
                const calitate = row.cells[1]?.textContent.trim() || '';
                const informatiiSuplimentare = row.cells[2] || null;

                // Curățăm numele și calitatea de caractere HTML
                const numeClean = nume.replace(/<[^>]*>/g, '').trim();
                const calitateClean = calitate.replace(/<[^>]*>/g, '').trim();

                if (numeClean && numeClean !== '-') {
                    const parteInfo = {
                        nume: numeClean,
                        calitate: calitateClean === '-' ? '' : calitateClean,
                        esteDeclaratoare: false,
                        tipuriCaleAtac: []
                    };

                    // Verificăm informațiile suplimentare pentru "Parte declaratoare" și tipuri de căi de atac
                    if (informatiiSuplimentare) {
                        const informatiiText = informatiiSuplimentare.textContent.trim();

                        // Verificăm dacă este parte declaratoare
                        if (informatiiText.includes('Parte declaratoare')) {
                            parteInfo.esteDeclaratoare = true;
                        }

                        // Extragem tipurile de căi de atac din badge-uri
                        const badges = informatiiSuplimentare.querySelectorAll('.badge');
                        badges.forEach(badge => {
                            const badgeText = badge.textContent.trim();
                            if (badgeText && badgeText !== 'Parte declaratoare') {
                                parteInfo.tipuriCaleAtac.push(badgeText);
                            }
                        });
                    }

                    dosarInfo.parti.push(parteInfo);
                }
            }
        });
    } catch (error) {
        console.error('Eroare la extragerea părților implicate:', error);
    }

    // Obținem ședințele de judecată - selectăm doar rândurile principale, nu cele de detalii
    const sedinteRows = document.querySelectorAll('#tabelSedinte tbody tr.sedinta-row');
    sedinteRows.forEach(row => {
        // Verificăm că este un rând principal de ședință, nu un rând de detalii
        if (!row.classList.contains('detail-row')) {
            const data = row.cells[0]?.textContent.trim() || '';
            const ora = row.cells[1]?.textContent.trim() || '';
            const complet = row.cells[2]?.textContent.trim() || '';
            // Pentru soluție, extragem doar textul principal, nu și detaliile suplimentare
            let solutie = row.cells[3]?.textContent.trim() || '';
            // Curățăm textul de caractere speciale și spații multiple
            solutie = solutie.replace(/\s+/g, ' ').trim();

            const dataPronuntare = row.cells[4]?.textContent.trim() || '';
            const document = row.cells[5]?.textContent.trim() || '';

            if (data) {
                dosarInfo.sedinte.push({
                    data: data,
                    ora: ora === '-' ? '' : ora,
                    complet: complet === '-' ? '' : complet,
                    solutie: solutie === 'Nedisponibilă' ? '' : solutie,
                    dataPronuntare: dataPronuntare === '-' ? '' : dataPronuntare,
                    document: document === '-' ? '' : document
                });
            }
        }
    });

    // Obținem căile de atac - folosim selector mai specific pentru a evita duplicarea
    const caiAtacSection = document.querySelector('.dosar-section:last-of-type');
    if (caiAtacSection) {
        const caiAtacRows = caiAtacSection.querySelectorAll('table tbody tr');
        caiAtacRows.forEach(row => {
            const dataDeclarare = row.cells[0]?.textContent.trim() || '';
            const parteDeclaratoare = row.cells[1]?.textContent.trim() || '';
            const tipCaleAtac = row.cells[2]?.textContent.trim() || '';
            const numarDosarInstantaSuperior = row.cells[3]?.textContent.trim() || '';

            // Verificăm că avem date valide înainte de a adăuga
            if (dataDeclarare && dataDeclarare !== '-' ||
                parteDeclaratoare && parteDeclaratoare !== '-' ||
                tipCaleAtac && tipCaleAtac !== '-') {
                dosarInfo.caiAtac.push({
                    dataDeclarare: dataDeclarare === '-' ? '' : dataDeclarare,
                    parteDeclaratoare: parteDeclaratoare === '-' ? '' : parteDeclaratoare,
                    tipCaleAtac: tipCaleAtac === '-' ? '' : tipCaleAtac,
                    numarDosarInstantaSuperior: numarDosarInstantaSuperior === '-' ? '' : numarDosarInstantaSuperior
                });
            }
        });
    }

    // Validare și curățare finală a datelor
    try {
        // Validăm și curățăm toate câmpurile text pentru caractere românești
        Object.keys(dosarInfo).forEach(key => {
            if (typeof dosarInfo[key] === 'string') {
                // Curățăm spațiile multiple și caracterele speciale
                dosarInfo[key] = dosarInfo[key].replace(/\s+/g, ' ').trim();

                // Înlocuim entitățile HTML cu caracterele corespunzătoare
                dosarInfo[key] = dosarInfo[key]
                    .replace(/&aacute;/g, 'á')
                    .replace(/&acirc;/g, 'â')
                    .replace(/&icirc;/g, 'î')
                    .replace(/&scedil;/g, 'ș')
                    .replace(/&tcedil;/g, 'ț')
                    .replace(/&Aacute;/g, 'Á')
                    .replace(/&Acirc;/g, 'Â')
                    .replace(/&Icirc;/g, 'Î')
                    .replace(/&Scedil;/g, 'Ș')
                    .replace(/&Tcedil;/g, 'Ț')
                    .replace(/&amp;/g, '&')
                    .replace(/&lt;/g, '<')
                    .replace(/&gt;/g, '>')
                    .replace(/&quot;/g, '"')
                    .replace(/&#39;/g, "'");
            }
        });

        // Validăm că avem cel puțin numărul dosarului
        if (!dosarInfo.numar) {
            console.warn('Numărul dosarului nu a fost găsit!');
        }

        console.log('Informații dosar extrase cu succes:', {
            numar: dosarInfo.numar,
            parti: dosarInfo.parti.length,
            sedinte: dosarInfo.sedinte.length,
            caiAtac: dosarInfo.caiAtac.length
        });

    } catch (error) {
        console.error('Eroare la validarea finală a datelor:', error);
    }

    return dosarInfo;
}

/**
 * Afișează un mesaj de confirmare
 */
function showConfirmation(message, isError = false) {
    const confirmModal = document.getElementById('confirmModal');
    const confirmMessage = document.getElementById('confirmMessage');

    if (confirmModal && confirmMessage) {
        confirmMessage.textContent = message;

        if (isError) {
            confirmMessage.style.color = '#dc3545';
        } else {
            confirmMessage.style.color = '#28a745';
        }

        openModal('confirmModal');
    }
}

/**
 * Afișează un indicator de încărcare
 */
function showLoading(message) {
    // Verificăm dacă există deja un indicator de încărcare
    let loadingElement = document.getElementById('loadingIndicator');

    if (!loadingElement) {
        // Creăm un nou indicator de încărcare
        loadingElement = document.createElement('div');
        loadingElement.id = 'loadingIndicator';
        loadingElement.className = 'loading-indicator';
        loadingElement.innerHTML = `
            <div class="loading-overlay"></div>
            <div class="loading-content">
                <div class="spinner"></div>
                <p id="loadingMessage"></p>
            </div>
        `;

        // Adăugăm stilurile necesare
        const style = document.createElement('style');
        style.textContent = `
            .loading-indicator {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 9999;
            }
            .loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
            }
            .loading-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: white;
                padding: 20px;
                border-radius: 5px;
                text-align: center;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
            }
            .spinner {
                width: 40px;
                height: 40px;
                margin: 0 auto 10px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #3498db;
                border-radius: 50%;
                animation: spin 2s linear infinite;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(loadingElement);
    }

    // Actualizăm mesajul
    const loadingMessage = document.getElementById('loadingMessage');
    if (loadingMessage) {
        loadingMessage.textContent = message;
    }

    // Afișăm indicatorul
    loadingElement.style.display = 'block';
}

/**
 * Ascunde indicatorul de încărcare
 */
function hideLoading() {
    const loadingElement = document.getElementById('loadingIndicator');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}
</script>

<!-- Modal pentru trimitere email -->
<div class="modal" id="emailModal" tabindex="-1" role="dialog" aria-labelledby="emailModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailModalLabel">Trimite informațiile dosarului pe email</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal('emailModal')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="emailForm">
                    <div class="form-group">
                        <label for="emailAddress">Adresă email:</label>
                        <input type="email" class="form-control" id="emailAddress" placeholder="Introduceți adresa de email" required>
                    </div>
                    <div class="form-group">
                        <label>Format:</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="emailFormat" id="formatPdf" value="pdf" checked>
                            <label class="form-check-label" for="formatPdf">
                                PDF
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="emailFormat" id="formatHtml" value="html">
                            <label class="form-check-label" for="formatHtml">
                                HTML
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="emailSubject">Subiect:</label>
                        <input type="text" class="form-control" id="emailSubject" value="Informații dosar 130/98/2022">
                    </div>
                    <div class="form-group">
                        <label for="emailMessage">Mesaj (opțional):</label>
                        <textarea class="form-control" id="emailMessage" rows="3" placeholder="Introduceți un mesaj opțional"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="closeModal('emailModal')">Anulează</button>
                <button type="button" class="btn btn-primary" id="sendEmailBtn">Trimite</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal pentru confirmare -->
<div class="modal" id="confirmModal" tabindex="-1" role="dialog" aria-labelledby="confirmModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmModalLabel">Confirmare</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="closeModal('confirmModal')">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal" onclick="closeModal('confirmModal')">OK</button>
            </div>
        </div>
    </div>
</div>

<!-- Versiune pentru printare (inițial ascunsă) -->
<div id="printVersion" class="print-only" style="display: none;">
    <div class="print-header">
        <h1>Detalii dosar: 130/98/2022</h1>
        <p>Generat la data: 05.07.2025 17:06</p>
    </div>
    <div id="printContent"></div>
</div>

<script>
/**
 * Afișează o notificare temporară pentru utilizator
 * Funcție integrată cu sistemul de notificări al portalului judiciar
 *
 * @param {string} message - Textul notificării care va fi afișat utilizatorului
 * @param {string} type - Tipul notificării care determină stilul (success, danger, warning, info)
 *
 * Exemplu de utilizare:
 * showNotification("Operațiune realizată cu succes!", "success");
 * showNotification("A apărut o eroare!", "danger");
 * showNotification("Atenție, verificați datele introduse!", "warning");
 * showNotification("Procesul este în curs de desfășurare...", "info");
 */
function showNotification(message, type) {
    // Verifică dacă containerul de notificări există
    const notificationContainer = $('#notificationContainer');
    const notification = $('#notification');

    if (notificationContainer.length === 0 || notification.length === 0) {
        console.error('Containerul de notificări nu a fost găsit în pagină!');
        // Fallback: afișează o alertă simplă
        alert(message);
        return;
    }

    // Mapare iconițe pentru diferite tipuri de notificări
    const iconMap = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };

    const icon = iconMap[type] || 'info-circle';

    // Setează clasa și conținutul notificării
    notification.removeClass('alert-success alert-danger alert-warning alert-info')
               .addClass('alert-' + type)
               .html('<i class="fas fa-' + icon + ' mr-2"></i>' + message);

    // Afișează containerul de notificări cu animație
    notificationContainer.slideDown(300);

    // Ascunde notificarea după 5 secunde
    setTimeout(function() {
        notificationContainer.slideUp(300);
    }, 5000);

    // Log pentru debugging
    logDebug('Notificare afișată:', { message: message, type: type });
}

/**
 * Inițializează sistemul de notificări pentru pagina de detalii dosar
 */
function initNotificationSystem() {
    logDebug('Inițializare sistem de notificări pentru detalii dosar...');

    // Verifică dacă elementele necesare există
    const notificationContainer = document.getElementById('notificationContainer');
    const notification = document.getElementById('notification');

    if (!notificationContainer || !notification) {
        console.error('Elementele pentru sistemul de notificări nu au fost găsite!');
        return false;
    }

    // Adaugă stiluri responsive pentru notificări pe mobile
    const style = document.createElement('style');
    style.textContent = `
        @media (max-width: 767.98px) {
            #notificationContainer {
                margin: 0.5rem;
            }

            #notification {
                font-size: 0.9rem;
                padding: 0.75rem;
            }

            #notification i {
                margin-right: 0.5rem;
            }
        }

        /* Animații pentru notificări */
        #notificationContainer {
            transition: all 0.3s ease-in-out;
        }

        #notification {
            border-left: 4px solid;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .alert-success {
            border-left-color: #28a745;
        }

        .alert-danger {
            border-left-color: #dc3545;
        }

        .alert-warning {
            border-left-color: #ffc107;
        }

        .alert-info {
            border-left-color: #17a2b8;
        }
    `;

    document.head.appendChild(style);

    logDebug('Sistemul de notificări a fost inițializat cu succes.');
    return true;
}

/**
 * Gestionează loading overlay-ul pentru pagina de detalii dosar
 */
function initPageLoadingOverlay() {
    const loadingOverlay = document.getElementById('pageLoadingOverlay');
    const mainContent = document.getElementById('mainContent');

    if (!loadingOverlay || !mainContent) {
        console.error('Elementele pentru loading overlay nu au fost găsite!');
        return;
    }

    // Funcție pentru ascunderea loading overlay-ului
    function hideLoadingOverlay() {
        // Adăugăm clasa loaded la conținutul principal
        mainContent.classList.add('loaded');

        // Ascundem overlay-ul cu animație
        loadingOverlay.classList.add('fade-out');

        // Eliminăm complet overlay-ul după animație
        setTimeout(() => {
            if (loadingOverlay.parentNode) {
                loadingOverlay.parentNode.removeChild(loadingOverlay);
            }
        }, 500);

        logDebug('Loading overlay ascuns cu succes.');
    }

    // Verificăm dacă pagina are erori sau conținut gol
    const hasError = document.querySelector('.alert-danger');
    const hasContent = document.querySelector('.dosar-header');

    // Calculăm timpul minim de afișare (1-2 secunde conform cerințelor)
    const minDisplayTime = 1000; // 1 secundă
    const startTime = Date.now();

    // Funcție pentru ascunderea cu respectarea timpului minim
    function hideWithMinTime() {
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

        setTimeout(hideLoadingOverlay, remainingTime);
    }

    // Ascundem loading-ul după ce conținutul este gata
    if (hasError || hasContent) {
        // Dacă avem conținut sau eroare, ascundem loading-ul
        hideWithMinTime();
    } else {
        // Fallback: ascundem după 2 secunde maxim
        setTimeout(hideLoadingOverlay, 2000);
    }

    logDebug('Loading overlay inițializat pentru detalii dosar.');
}

/**
 * Actualizează mesajul din loading overlay
 */
function updateLoadingMessage(message, submessage = '') {
    const messageElement = document.querySelector('.page-loading-message');
    const submessageElement = document.querySelector('.page-loading-submessage');

    if (messageElement) {
        messageElement.textContent = message;
    }

    if (submessageElement) {
        submessageElement.textContent = submessage;
    }
}

// Inițializează sistemul de notificări când DOM-ul este încărcat
document.addEventListener('DOMContentLoaded', function() {
    initNotificationSystem();
    initPageLoadingOverlay();
});

// Inițializează loading overlay-ul imediat (înainte de DOMContentLoaded)
// pentru a fi siguri că este vizibil de la început
(function() {
    // Verificăm dacă loading overlay-ul există
    function checkAndInitLoading() {
        const loadingOverlay = document.getElementById('pageLoadingOverlay');
        if (loadingOverlay) {
            // Overlay-ul este deja vizibil prin CSS
            logDebug('Loading overlay detectat și activ.');
        } else {
            // Reîncercăm după un scurt interval
            setTimeout(checkAndInitLoading, 50);
        }
    }

    // Începem verificarea
    if (document.readyState === 'loading') {
        checkAndInitLoading();
    }
})();

/**
 * Text Size Adjustment Feature
 * Allows users to increase/decrease font size for better readability
 */
(function() {
    'use strict';

    // Text size levels and their corresponding classes and labels
    const textSizeLevels = [
        { class: 'text-size-xs', label: 'Foarte mic', size: -2 },
        { class: 'text-size-sm', label: 'Mic', size: -1 },
        { class: '', label: 'Normal', size: 0 },
        { class: 'text-size-lg', label: 'Mare', size: 1 },
        { class: 'text-size-xl', label: 'Foarte mare', size: 2 },
        { class: 'text-size-xxl', label: 'Extra mare', size: 3 }
    ];

    let currentSizeIndex = 2; // Start with normal size (index 2)
    const STORAGE_KEY = 'judicial_portal_text_size';

    // Initialize text size controls
    function initTextSizeControls() {
        const controls = document.getElementById('textSizeControls');
        const decreaseBtn = document.getElementById('decreaseTextSize');
        const increaseBtn = document.getElementById('increaseTextSize');
        const resetBtn = document.getElementById('resetTextSize');
        const indicator = document.getElementById('textSizeIndicator');

        if (!controls || !decreaseBtn || !increaseBtn || !resetBtn || !indicator) {
            console.warn('Text size controls not found');
            return;
        }

        // Load saved preference
        loadSavedTextSize();

        // Event listeners
        decreaseBtn.addEventListener('click', function() {
            changeTextSize(-1);
        });

        increaseBtn.addEventListener('click', function() {
            changeTextSize(1);
        });

        resetBtn.addEventListener('click', function() {
            resetTextSize();
        });

        // Keyboard shortcuts (Ctrl + Plus/Minus)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                if (e.key === '+' || e.key === '=') {
                    e.preventDefault();
                    changeTextSize(1);
                } else if (e.key === '-') {
                    e.preventDefault();
                    changeTextSize(-1);
                } else if (e.key === '0') {
                    e.preventDefault();
                    resetTextSize();
                }
            }
        });

        // Update initial state
        updateTextSize();
        updateControls();
    }

    // Change text size by delta (-1 for decrease, +1 for increase)
    function changeTextSize(delta) {
        const newIndex = currentSizeIndex + delta;

        if (newIndex >= 0 && newIndex < textSizeLevels.length) {
            currentSizeIndex = newIndex;
            updateTextSize();
            updateControls();
            saveTextSizePreference();

            // Show notification
            const level = textSizeLevels[currentSizeIndex];
            showNotification(`Mărimea textului: ${level.label}`, 'info');
        }
    }

    // Reset to normal size
    function resetTextSize() {
        currentSizeIndex = 2; // Normal size
        updateTextSize();
        updateControls();
        saveTextSizePreference();
        showNotification('Mărimea textului resetată la normal', 'success');
    }

    // Apply the current text size to the page
    function updateTextSize() {
        const body = document.body;

        // Remove all existing text size classes
        textSizeLevels.forEach(level => {
            if (level.class) {
                body.classList.remove(level.class);
            }
        });

        // Add the current text size class
        const currentLevel = textSizeLevels[currentSizeIndex];
        if (currentLevel.class) {
            body.classList.add(currentLevel.class);
        }
    }

    // Update control button states and indicator
    function updateControls() {
        const decreaseBtn = document.getElementById('decreaseTextSize');
        const increaseBtn = document.getElementById('increaseTextSize');
        const indicator = document.getElementById('textSizeIndicator');

        if (!decreaseBtn || !increaseBtn || !indicator) return;

        // Update button states
        decreaseBtn.disabled = currentSizeIndex <= 0;
        increaseBtn.disabled = currentSizeIndex >= textSizeLevels.length - 1;

        // Update indicator text
        const currentLevel = textSizeLevels[currentSizeIndex];
        indicator.textContent = currentLevel.label;

        // Update button visual states - integrated design handles disabled state via CSS
        if (decreaseBtn.disabled) {
            decreaseBtn.setAttribute('disabled', 'disabled');
        } else {
            decreaseBtn.removeAttribute('disabled');
        }

        if (increaseBtn.disabled) {
            increaseBtn.setAttribute('disabled', 'disabled');
        } else {
            increaseBtn.removeAttribute('disabled');
        }
    }

    // Save text size preference to localStorage
    function saveTextSizePreference() {
        try {
            localStorage.setItem(STORAGE_KEY, currentSizeIndex.toString());
        } catch (e) {
            console.warn('Could not save text size preference:', e);
        }
    }

    // Load saved text size preference
    function loadSavedTextSize() {
        try {
            const saved = localStorage.getItem(STORAGE_KEY);
            if (saved !== null) {
                const savedIndex = parseInt(saved, 10);
                if (savedIndex >= 0 && savedIndex < textSizeLevels.length) {
                    currentSizeIndex = savedIndex;
                }
            }
        } catch (e) {
            console.warn('Could not load text size preference:', e);
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTextSizeControls);
    } else {
        initTextSizeControls();
    }

    // Enhanced mobile interaction for ultra-discrete controls
    function initMobileTextSizeEnhancements() {
        if (window.innerWidth <= 767.98) {
            const textSizeControls = document.getElementById('textSizeControls');
            if (textSizeControls) {
                let hideTimeout;

                function showControls() {
                    textSizeControls.style.opacity = '0.9';
                    clearTimeout(hideTimeout);
                    hideTimeout = setTimeout(() => {
                        textSizeControls.style.opacity = '0.4';
                    }, 3000);
                }

                function hideControls() {
                    clearTimeout(hideTimeout);
                    hideTimeout = setTimeout(() => {
                        textSizeControls.style.opacity = '0.4';
                    }, 1000);
                }

                // Show on touch/hover
                textSizeControls.addEventListener('touchstart', showControls);
                textSizeControls.addEventListener('mouseenter', showControls);
                textSizeControls.addEventListener('mouseleave', hideControls);

                // Hide when touching elsewhere
                document.addEventListener('touchstart', (e) => {
                    if (!textSizeControls.contains(e.target)) {
                        hideControls();
                    }
                });
            }
        }
    }

    // Initialize mobile enhancements
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMobileTextSizeEnhancements);
    } else {
        initMobileTextSizeEnhancements();
    }
})();

// Social Sharing Functionality
(function() {
    'use strict';

    function initSocialSharing() {
        const currentUrl = window.location.href;
        const dosarNumber = '130/98/2022';
        const institutie = 'CurteadeApelBUCURESTI';

        // Get institution name for sharing
        let institutieNume = '';
                    const institutii = {"InaltaCurtedeCASSATIESIJUSTITIE":"\u00cenalta Curte de Casa\u021bie \u0219i Justi\u021bie","CurteadeApelALBAIULIA":"Curtea de Apel Alba Iulia","CurteadeApelBACU":"Curtea de Apel Bac\u0103u","CurteadeApelBRASSOV":"Curtea de Apel Bra\u0219ov","CurteadeApelBUCURESTI":"Curtea de Apel Bucure\u0219ti","CurteadeApelCLUJ":"Curtea de Apel Cluj","CurteadeApelCONSTANTA":"Curtea de Apel Constan\u021ba","CurteadeApelCRAIOVA":"Curtea de Apel Craiova","CurteadeApelGALATI":"Curtea de Apel Gala\u021bi","CurteadeApelIASI":"Curtea de Apel Ia\u0219i","CurteadeApelORADEA":"Curtea de Apel Oradea","CurteadeApelPITESTI":"Curtea de Apel Pite\u0219ti","CurteadeApelPLOIESTI":"Curtea de Apel Ploie\u0219ti","CurteadeApelSUCEAVA":"Curtea de Apel Suceava","CurteadeApelTARGUMURES":"Curtea de Apel T\u00e2rgu Mure\u0219","CurteadeApelTIMISOARA":"Curtea de Apel Timi\u0219oara","TribunalulALBA":"Tribunalul Alba","TribunalulARAD":"Tribunalul Arad","TribunalulARGES":"Tribunalul Arge\u0219","TribunalulBACU":"Tribunalul Bac\u0103u","TribunalulBIHOR":"Tribunalul Bihor","TribunalulBISTRITANASAUD":"Tribunalul Bistri\u021ba-N\u0103s\u0103ud","TribunalulBOTOSANI":"Tribunalul Boto\u0219ani","TribunalulBRASSOV":"Tribunalul Bra\u0219ov","TribunalulBRASOV":"Tribunalul Bra\u0219ov","TribunalulBRAILA":"Tribunalul Br\u0103ila","TribunalulBUCURESTI":"Tribunalul Bucure\u0219ti","TribunalulBUZAU":"Tribunalul Buz\u0103u","TribunalulCALARASI":"Tribunalul C\u0103l\u0103ra\u0219i","TribunalulCARASSSEVERIN":"Tribunalul Cara\u0219-Severin","TribunalulCLUJ":"Tribunalul Cluj","TribunalulCONSTANTA":"Tribunalul Constan\u021ba","TribunalulCOVASNA":"Tribunalul Covasna","TribunalulDAMBOVITA":"Tribunalul D\u00e2mbovi\u021ba","TribunalulDOLJ":"Tribunalul Dolj","TribunalulGALATI":"Tribunalul Gala\u021bi","TribunalulGIURGIU":"Tribunalul Giurgiu","TribunalulGORJ":"Tribunalul Gorj","TribunalulHARGHITA":"Tribunalul Harghita","TribunalulHUNEDOARA":"Tribunalul Hunedoara","TribunalulIALOMITA":"Tribunalul Ialomi\u021ba","TribunalulIASI":"Tribunalul Ia\u0219i","TribunalulILFOV":"Tribunalul Ilfov","TribunalulMARAMURES":"Tribunalul Maramure\u0219","TribunalulMEHEDINTI":"Tribunalul Mehedin\u021bi","TribunalulMURES":"Tribunalul Mure\u0219","TribunalulNEAMT":"Tribunalul Neam\u021b","TribunalulOLT":"Tribunalul Olt","TribunalulPRAHOVA":"Tribunalul Prahova","TribunalulSALAJ":"Tribunalul S\u0103laj","TribunalulSATUMARE":"Tribunalul Satu Mare","TribunalulSIBIU":"Tribunalul Sibiu","TribunalulSUCEAVA":"Tribunalul Suceava","TribunalulTELEORMAN":"Tribunalul Teleorman","TribunalulTIMIS":"Tribunalul Timi\u0219","TribunalulTULCEA":"Tribunalul Tulcea","TribunalulVALCEA":"Tribunalul V\u00e2lcea","TribunalulVASLUI":"Tribunalul Vaslui","TribunalulVRANCEA":"Tribunalul Vrancea","JudecatoriaSECTORUL1BUCURESTI":"Judec\u0103toria Sectorul 1 Bucure\u0219ti","JudecatoriaSECTORUL2BUCURESTI":"Judec\u0103toria Sectorul 2 Bucure\u0219ti","JudecatoriaSECTORUL3BUCURESTI":"Judec\u0103toria Sectorul 3 Bucure\u0219ti","JudecatoriaSECTORUL4BUCURESTI":"Judec\u0103toria Sectorul 4 Bucure\u0219ti","JudecatoriaSECTORUL5BUCURESTI":"Judec\u0103toria Sectorul 5 Bucure\u0219ti","JudecatoriaSECTORUL6BUCURESTI":"Judec\u0103toria Sectorul 6 Bucure\u0219ti","JudecatoriaAIUD":"Judec\u0103toria Aiud","JudecatoriaALBAIULIA":"Judec\u0103toria Alba Iulia","JudecatoriaALEXANDRIA":"Judec\u0103toria Alexandria","JudecatoriaARAD":"Judec\u0103toria Arad","JudecatoriaBACU":"Judec\u0103toria Bac\u0103u","JudecatoriaBISTRITA":"Judec\u0103toria Bistri\u021ba","JudecatoriaBOTOSANI":"Judec\u0103toria Boto\u0219ani","JudecatoriaBRASSOV":"Judec\u0103toria Bra\u0219ov","JudecatoriaBRAILA":"Judec\u0103toria Br\u0103ila","JudecatoriaBUZAU":"Judec\u0103toria Buz\u0103u","JudecatoriaBUFTEA":"Judec\u0103toria Buftea","JudecatoriaCALARASI":"Judec\u0103toria C\u0103l\u0103ra\u0219i","JudecatoriaCARACAL":"Judec\u0103toria Caracal","JudecatoriaCLUJNAPOCA":"Judec\u0103toria Cluj-Napoca","JudecatoriaCONSTANTA":"Judec\u0103toria Constan\u021ba","JudecatoriaCRAIOVA":"Judec\u0103toria Craiova","JudecatoriaDEVA":"Judec\u0103toria Deva","JudecatoriaFOCSANI":"Judec\u0103toria Foc\u0219ani","JudecatoriaGALATI":"Judec\u0103toria Gala\u021bi","JudecatoriaGIURGIU":"Judec\u0103toria Giurgiu","JudecatoriaIASI":"Judec\u0103toria Ia\u0219i","JudecatoriaORADEA":"Judec\u0103toria Oradea","JudecatoriaPITESTI":"Judec\u0103toria Pite\u0219ti","JudecatoriaPLOIESTI":"Judec\u0103toria Ploie\u0219ti","JudecatoriaREMNICUVILCEA":"Judec\u0103toria R\u00e2mnicu V\u00e2lcea","JudecatoriaSATUMARE":"Judec\u0103toria Satu Mare","JudecatoriaSIBIU":"Judec\u0103toria Sibiu","JudecatoriaSLATINA":"Judec\u0103toria Slatina","JudecatoriaSUCEAVA":"Judec\u0103toria Suceava","JudecatoriaTARGOVISTE":"Judec\u0103toria T\u00e2rgovi\u0219te","JudecatoriaTARGUJIU":"Judec\u0103toria T\u00e2rgu Jiu","JudecatoriaTARGUMURES":"Judec\u0103toria T\u00e2rgu Mure\u0219","JudecatoriaTIMISOARA":"Judec\u0103toria Timi\u0219oara","JudecatoriaTULCEA":"Judec\u0103toria Tulcea","JudecatoriaVASLUI":"Judec\u0103toria Vaslui","JudecatoriaZALAU":"Judec\u0103toria Zal\u0103u","TribunalulComercialBUCURESTI":"Tribunalul Comercial Bucure\u0219ti","TribunalulComercialCLUJ":"Tribunalul Comercial Cluj","TribunalulComercialCONSTANTA":"Tribunalul Comercial Constan\u021ba","TribunalulComercialTIMISOARA":"Tribunalul Comercial Timi\u0219oara","TribunalulMilitarBUCURESTI":"Tribunalul Militar Bucure\u0219ti","TribunalulMilitarCLUJ":"Tribunalul Militar Cluj","TribunalulPentruMinoriSiFamilieBUCURESTI":"Tribunalul pentru Minori \u0219i Familie Bucure\u0219ti","TribunalulPentruMinoriSiFamilieCLUJ":"Tribunalul pentru Minori \u0219i Familie Cluj","CurteadeApelBUCURESTISECTIACOMERCIALA":"Curtea de Apel Bucure\u0219ti - Sec\u021bia Comercial\u0103","CurteadeApelBUCURESTISECTIAPENALA":"Curtea de Apel Bucure\u0219ti - Sec\u021bia Penal\u0103","CurteadeApelBUCURESTISECTIACIVILA":"Curtea de Apel Bucure\u0219ti - Sec\u021bia Civil\u0103","JudecatoriaBUCURESTI":"Judec\u0103toria Bucure\u0219ti","TribunalulMUNICIPIULUIBUCURESTI":"Tribunalul Municipiului Bucure\u0219ti","JudecatoriaCAMPINA":"Judec\u0103toria C\u00e2mpina","JudecatoriaCOMPIEGNE":"Judec\u0103toria Compi\u00e8gne","JudecatoriaDROBETATURNUSEVERIN":"Judec\u0103toria Drobeta-Turnu Severin","JudecatoriaHUNEDOARA":"Judec\u0103toria Hunedoara","JudecatoriaMANGALIA":"Judec\u0103toria Mangalia","JudecatoriaMEDGIDIA":"Judec\u0103toria Medgidia","JudecatoriaNAVODARI":"Judec\u0103toria N\u0103vodari","JudecatoriaOTOPENI":"Judec\u0103toria Otopeni","JudecatoriaPANTELIMON":"Judec\u0103toria Pantelimon","JudecatoriaRAMNICUVALCEA":"Judec\u0103toria R\u00e2mnicu V\u00e2lcea","JudecatoriaROMAN":"Judec\u0103toria Roman","JudecatoriaTURDA":"Judec\u0103toria Turda","JudecatoriaVOLUNTARI":"Judec\u0103toria Voluntari"};
            institutieNume = institutii['CurteadeApelBUCURESTI'] || 'CurteadeApelBUCURESTI';
        
        const shareTitle = `Dosar ${dosarNumber} - Portal Judiciar România`;
        const shareText = `Detalii complete pentru dosarul ${dosarNumber}${institutieNume ? ` de la ${institutieNume}` : ''}`;

        // Copy Link functionality
        const copyLinkBtn = document.getElementById('copyLinkBtn');
        if (copyLinkBtn) {
            copyLinkBtn.addEventListener('click', function() {
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(currentUrl).then(function() {
                        showNotification('Link copiat în clipboard!', 'success');

                        // Visual feedback
                        const originalText = copyLinkBtn.innerHTML;
                        copyLinkBtn.innerHTML = '<i class="fas fa-check me-1"></i>Copiat!';
                        copyLinkBtn.classList.add('btn-success');
                        copyLinkBtn.classList.remove('btn-outline-primary');

                        setTimeout(function() {
                            copyLinkBtn.innerHTML = originalText;
                            copyLinkBtn.classList.remove('btn-success');
                            copyLinkBtn.classList.add('btn-outline-primary');
                        }, 2000);
                    }).catch(function() {
                        fallbackCopyToClipboard(currentUrl);
                    });
                } else {
                    fallbackCopyToClipboard(currentUrl);
                }
            });
        }

        // Facebook Share
        const facebookShareBtn = document.getElementById('facebookShareBtn');
        if (facebookShareBtn) {
            facebookShareBtn.addEventListener('click', function() {
                const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(currentUrl)}`;
                openShareWindow(facebookUrl, 'Facebook');
            });
        }

        // WhatsApp Share
        const whatsappShareBtn = document.getElementById('whatsappShareBtn');
        if (whatsappShareBtn) {
            whatsappShareBtn.addEventListener('click', function() {
                const whatsappText = `${shareText}\n${currentUrl}`;
                const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(whatsappText)}`;

                // On mobile, try to open WhatsApp app, otherwise open in new tab
                if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                    window.location.href = whatsappUrl;
                } else {
                    window.open(whatsappUrl, '_blank');
                }
            });
        }

        // Email Share
        const emailShareBtn = document.getElementById('emailShareBtn');
        if (emailShareBtn) {
            emailShareBtn.addEventListener('click', function() {
                const emailSubject = encodeURIComponent(shareTitle);
                const emailBody = encodeURIComponent(`${shareText}\n\nAccesează dosarul aici: ${currentUrl}`);
                const emailUrl = `mailto:?subject=${emailSubject}&body=${emailBody}`;
                window.location.href = emailUrl;
            });
        }
    }

    // Fallback copy to clipboard for older browsers
    function fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            showNotification('Link copiat în clipboard!', 'success');
        } catch (err) {
            showNotification('Nu s-a putut copia link-ul. Vă rugăm să copiați manual din bara de adrese.', 'warning');
        }

        document.body.removeChild(textArea);
    }

    // Open share window with optimal dimensions
    function openShareWindow(url, platform) {
        const width = 600;
        const height = 400;
        const left = (window.innerWidth - width) / 2;
        const top = (window.innerHeight - height) / 2;

        const features = `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`;
        window.open(url, `share_${platform}`, features);
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSocialSharing);
    } else {
        initSocialSharing();
    }
})();
</script>

    <!-- Footer Section -->
    <footer class="modern-footer" role="contentinfo">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <p class="mb-2">
                        &copy; 2025 <strong>Portal Judiciar România</strong> - DosareJust.ro. Toate drepturile rezervate.
                    </p>
                </div>
                <div class="col-md-4 text-md-right">
                    <div class="footer-links">
                        <a href="index.php" class="footer-link" title="Portal Judiciar România - Pagina principală">Acasă</a>
                        <a href="index.php" class="footer-link" title="Căutare dosare instanțe România">Căutare Dosare</a>
                        <a href="sedinte.php" class="footer-link" title="Ședințe judecătorești România">Ședințe</a>
                        <a href="http://portal.just.ro" target="_blank" rel="noopener" class="footer-link" title="Portal oficial Ministerul Justiției">Portal Just Oficial</a>
                        <a href="contact.php" class="footer-link" title="Contact Portal Judiciar România">Contact</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTopBtn" class="back-to-top" title="Mergi sus" aria-label="Mergi la începutul paginii">
        <i class="fas fa-chevron-up" aria-hidden="true"></i>
    </button>

    <!-- Back to Top Script -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const backToTopBtn = document.getElementById('backToTopBtn');
        let isScrolling = false;

        // Throttled scroll handler for better performance
        function handleScroll() {
            if (!isScrolling) {
                window.requestAnimationFrame(function() {
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                    if (scrollTop > 400) {
                        backToTopBtn.classList.add('visible');
                    } else {
                        backToTopBtn.classList.remove('visible');
                    }

                    isScrolling = false;
                });
                isScrolling = true;
            }
        }

        // Add scroll event listener
        window.addEventListener('scroll', handleScroll, { passive: true });

        // Enhanced click handler with smooth scroll
        backToTopBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // Add active state
            this.style.transform = 'translateY(-1px) scale(0.95)';

            // Smooth scroll to top
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });

            // Reset button state
            setTimeout(() => {
                this.style.transform = '';
            }, 150);

            // Focus management for accessibility
            document.body.focus();
        });

        // Keyboard accessibility
        backToTopBtn.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
    </script>
</body>
</html>