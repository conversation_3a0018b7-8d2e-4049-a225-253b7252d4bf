<?php

/**
 * Portal Judiciar - Pagina de detalii dosar
 */

// Încărcăm bootstrap-ul aplicației
require_once dirname(__DIR__) . '/bootstrap.php';

// Importăm clasele necesare
use App\Helpers\TemplateEngine;
use App\Services\DosarService;

// Inițializăm motorul de șabloane
$templateEngine = new TemplateEngine();

// Obținem lista instanțelor din funcția centralizată
require_once '../includes/functions.php';
$instante = getInstanteList();

// Parametrii pentru detalii dosar
$numarDosar = $_GET['numar'] ?? '';
$institutie = $_GET['institutie'] ?? '';

// Verificăm dacă avem parametrii necesari
if (empty($numarDosar) || empty($institutie)) {
    // Adăugăm un mesaj flash cu eroarea
    TemplateEngine::addFlashMessage('danger', 'Numărul dosarului și instituția sunt obligatorii pentru vizualizarea detaliilor.');
    
    // Redirecționăm către pagina principală
    header('Location: index.php');
    exit;
}

// Detaliile dosarului
$dosar = null;

try {
    // Inițializăm serviciul de dosare
    $dosarService = new DosarService();
    
    // Obținem detaliile dosarului
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    // Verificăm dacă am găsit dosarul
    if (empty((array)$dosar)) {
        // Adăugăm un mesaj flash cu eroarea
        TemplateEngine::addFlashMessage('warning', 'Nu au fost găsite detalii pentru dosarul specificat.');
        
        // Redirecționăm către pagina principală
        header('Location: index.php');
        exit;
    }
} catch (Exception $e) {
    // Adăugăm un mesaj flash cu eroarea
    TemplateEngine::addFlashMessage('danger', 'Eroare la obținerea detaliilor dosarului: ' . $e->getMessage());
    
    // Logăm eroarea
    error_log('Eroare la obținerea detaliilor dosarului: ' . $e->getMessage());
    
    // Redirecționăm către pagina principală
    header('Location: index.php');
    exit;
}

// Datele pentru șablon
$data = [
    'instante' => $instante,
    'dosar' => $dosar
];

// Afișăm șablonul
echo $templateEngine->render('detalii_dosar.twig', $data);
