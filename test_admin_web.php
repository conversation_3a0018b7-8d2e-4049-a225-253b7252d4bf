<?php
/**
 * Test admin web access
 */

// Activez raportarea erorilor
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Test Admin Web Access</h1>";

// Test 1: Bootstrap
echo "<h2>1. Bootstrap Test</h2>";
try {
    require_once __DIR__ . '/bootstrap.php';
    echo "<p style='color: green;'>✅ Bootstrap loaded successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Bootstrap failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}

// Test 2: Database
echo "<h2>2. Database Test</h2>";
try {
    $db = App\Config\Database::getConnection();
    echo "<p style='color: green;'>✅ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 3: Admin Auth Service
echo "<h2>3. Admin Auth Service Test</h2>";
try {
    $authService = new App\Services\AdminAuthService();
    echo "<p style='color: green;'>✅ AdminAuthService loaded</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ AdminAuthService failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 4: Template Engine
echo "<h2>4. Template Engine Test</h2>";
try {
    $templateEngine = new App\Helpers\TemplateEngine();
    echo "<p style='color: green;'>✅ TemplateEngine loaded</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ TemplateEngine failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 5: Session
echo "<h2>5. Session Test</h2>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<p style='color: green;'>✅ Session is active</p>";
    echo "<p>Session ID: " . session_id() . "</p>";
} else {
    echo "<p style='color: red;'>❌ Session is not active</p>";
}

// Test 6: File permissions
echo "<h2>6. File Permissions Test</h2>";
$files = [
    'public/admin/index.php',
    'src/Templates/admin/dashboard.twig',
    'src/Services/AdminAuthService.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<p style='color: green;'>✅ $file is readable</p>";
        } else {
            echo "<p style='color: red;'>❌ $file is not readable</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ $file does not exist</p>";
    }
}

// Test 7: Include admin file directly
echo "<h2>7. Admin File Include Test</h2>";
ob_start();
try {
    // Simulez variabilele necesare
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_SERVER['REQUEST_URI'] = '/just/public/admin/';
    
    include 'public/admin/index.php';
    $output = ob_get_contents();
    echo "<p style='color: green;'>✅ Admin file included successfully</p>";
    echo "<p>Output length: " . strlen($output) . " characters</p>";
    
    if (strlen($output) > 0) {
        echo "<h3>First 500 characters of output:</h3>";
        echo "<pre>" . htmlspecialchars(substr($output, 0, 500)) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Admin file include failed: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (Error $e) {
    echo "<p style='color: red;'>❌ Admin file error: " . htmlspecialchars($e->getMessage()) . "</p>";
} finally {
    ob_end_clean();
}

echo "<h2>Test Complete</h2>";
echo "<p><a href='public/admin/'>Try accessing admin directly</a></p>";
echo "<p><a href='public/monitor.php'>Try accessing monitor</a></p>";
?>
