<?php
/**
 * Test Hybrid Methods
 * Direct testing of hybrid extraction methods to verify they're working
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head>";
echo "<title>Hybrid Methods Test</title>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
.warning { background: #fff3cd; border-left-color: #ffc107; }
.error { background: #f8d7da; border-left-color: #dc3545; }
.success { background: #d4edda; border-left-color: #28a745; }
.debug-info { background: #e3f2fd; border: 1px solid #2196f3; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px; }
.method-test { border: 1px solid #ddd; padding: 10px; margin: 10px 0; }
</style></head><body>";

echo "<h1>🔧 Hybrid Methods Test</h1>";
echo "<p><strong>Objective:</strong> Direct testing of hybrid extraction methods</p>";
echo "<hr>";

// Test case
$numarDosar = '130/98/2022';
$institutie = 'TribunalulIALOMITA';

echo "<div class='section'>";
echo "<h2>📊 Method Testing for Case {$numarDosar}</h2>";

try {
    $dosarService = new DosarService();
    
    // First get the raw SOAP data
    echo "<div class='method-test'>";
    echo "<h3>Step 1: Raw SOAP Data Retrieval</h3>";
    
    $searchParams = [
        'numarDosar' => $numarDosar,
        'institutie' => $institutie,
        'obiectDosar' => '',
        'numeParte' => '',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    // Use reflection to access private method
    $reflection = new ReflectionClass($dosarService);
    $executeSoapMethod = $reflection->getMethod('executeSoapCallWithRetry');
    $executeSoapMethod->setAccessible(true);
    
    $response = $executeSoapMethod->invoke($dosarService, 'CautareDosare2', $searchParams, "Test call");
    
    if (isset($response->CautareDosare2Result->Dosar)) {
        $dosare = $response->CautareDosare2Result->Dosar;
        $rawDosar = is_array($dosare) ? $dosare[0] : $dosare;
        
        echo "<div class='debug-info'>";
        echo "Raw SOAP response retrieved successfully\n";
        echo "Dosar number: " . ($rawDosar->numar ?? 'N/A') . "\n";
        echo "Institution: " . ($rawDosar->institutie ?? 'N/A') . "\n";
        echo "Has parties: " . (isset($rawDosar->parti) ? 'YES' : 'NO') . "\n";
        if (isset($rawDosar->parti) && isset($rawDosar->parti->DosarParte)) {
            $soapParties = $rawDosar->parti->DosarParte;
            $soapCount = is_array($soapParties) ? count($soapParties) : 1;
            echo "SOAP parties count: {$soapCount}\n";
        }
        echo "</div>";
        
        echo "<p>✅ Raw SOAP data retrieved successfully</p>";
    } else {
        echo "<p>❌ Failed to retrieve raw SOAP data</p>";
        exit;
    }
    echo "</div>";
    
    // Test extractPartiesFromDecisionText method
    echo "<div class='method-test'>";
    echo "<h3>Step 2: Test extractPartiesFromDecisionText()</h3>";
    
    $extractMethod = $reflection->getMethod('extractPartiesFromDecisionText');
    $extractMethod->setAccessible(true);
    
    $decisionParties = $extractMethod->invoke($dosarService, $rawDosar);
    
    echo "<div class='debug-info'>";
    echo "Decision text extraction results:\n";
    echo "Parties extracted: " . count($decisionParties) . "\n";
    if (count($decisionParties) > 0) {
        echo "First 5 parties:\n";
        for ($i = 0; $i < min(5, count($decisionParties)); $i++) {
            echo "  " . ($i + 1) . ". " . ($decisionParties[$i]['nume'] ?? 'N/A') . " (source: " . ($decisionParties[$i]['source'] ?? 'unknown') . ")\n";
        }
        if (count($decisionParties) > 5) {
            echo "  ... and " . (count($decisionParties) - 5) . " more\n";
        }
    }
    echo "</div>";
    
    if (count($decisionParties) > 0) {
        echo "<p>✅ Decision text extraction working - found " . count($decisionParties) . " parties</p>";
    } else {
        echo "<p>⚠️ Decision text extraction returned 0 parties</p>";
    }
    echo "</div>";
    
    // Test SOAP party extraction
    echo "<div class='method-test'>";
    echo "<h3>Step 3: Extract SOAP Parties</h3>";
    
    $soapParties = [];
    if (isset($rawDosar->parti) && isset($rawDosar->parti->DosarParte)) {
        $parti = $rawDosar->parti->DosarParte;
        if (is_array($parti)) {
            foreach ($parti as $parte) {
                if (isset($parte->nume)) {
                    $soapParties[] = [
                        'nume' => $parte->nume ?? '',
                        'calitate' => $parte->calitateParte ?? '',
                        'source' => 'soap_api'
                    ];
                }
            }
        } elseif (isset($parti->nume)) {
            $soapParties[] = [
                'nume' => $parti->nume ?? '',
                'calitate' => $parti->calitateParte ?? '',
                'source' => 'soap_api'
            ];
        }
    }
    
    echo "<div class='debug-info'>";
    echo "SOAP parties extraction results:\n";
    echo "Parties extracted: " . count($soapParties) . "\n";
    if (count($soapParties) > 0) {
        echo "First 5 parties:\n";
        for ($i = 0; $i < min(5, count($soapParties)); $i++) {
            echo "  " . ($i + 1) . ". " . ($soapParties[$i]['nume'] ?? 'N/A') . " (source: " . ($soapParties[$i]['source'] ?? 'unknown') . ")\n";
        }
        if (count($soapParties) > 5) {
            echo "  ... and " . (count($soapParties) - 5) . " more\n";
        }
    }
    echo "</div>";
    
    if (count($soapParties) > 0) {
        echo "<p>✅ SOAP party extraction working - found " . count($soapParties) . " parties</p>";
    } else {
        echo "<p>❌ SOAP party extraction failed</p>";
    }
    echo "</div>";
    
    // Test mergeAndDeduplicateParties method
    echo "<div class='method-test'>";
    echo "<h3>Step 4: Test mergeAndDeduplicateParties()</h3>";
    
    $mergeMethod = $reflection->getMethod('mergeAndDeduplicateParties');
    $mergeMethod->setAccessible(true);
    
    $mergedParties = $mergeMethod->invoke($dosarService, $soapParties, $decisionParties);
    
    echo "<div class='debug-info'>";
    echo "Merge and deduplication results:\n";
    echo "SOAP parties input: " . count($soapParties) . "\n";
    echo "Decision parties input: " . count($decisionParties) . "\n";
    echo "Merged parties output: " . count($mergedParties) . "\n";
    echo "Deduplication effectiveness: " . (count($soapParties) + count($decisionParties) - count($mergedParties)) . " duplicates removed\n";
    echo "</div>";
    
    if (count($mergedParties) > count($soapParties)) {
        echo "<p>✅ Merge and deduplication working - total parties increased from " . count($soapParties) . " to " . count($mergedParties) . "</p>";
    } else {
        echo "<p>⚠️ Merge did not increase party count - may indicate decision text extraction issue</p>";
    }
    echo "</div>";
    
    // Test complete mapDosarToObject method
    echo "<div class='method-test'>";
    echo "<h3>Step 5: Test Complete mapDosarToObject()</h3>";
    
    $mapMethod = $reflection->getMethod('mapDosarToObject');
    $mapMethod->setAccessible(true);
    
    $mappedDosar = $mapMethod->invoke($dosarService, $rawDosar);
    
    echo "<div class='debug-info'>";
    echo "Complete mapping results:\n";
    echo "Mapped dosar number: " . ($mappedDosar->numar ?? 'N/A') . "\n";
    echo "Mapped dosar institution: " . ($mappedDosar->institutie ?? 'N/A') . "\n";
    echo "Total parties in mapped dosar: " . count($mappedDosar->parti ?? []) . "\n";
    
    // Analyze sources in final result
    $finalSoapCount = 0;
    $finalDecisionCount = 0;
    $finalUnknownCount = 0;
    
    foreach ($mappedDosar->parti ?? [] as $parte) {
        $source = $parte['source'] ?? 'unknown';
        switch ($source) {
            case 'soap_api': $finalSoapCount++; break;
            case 'decision_text': $finalDecisionCount++; break;
            default: $finalUnknownCount++; break;
        }
    }
    
    echo "Final source distribution:\n";
    echo "  SOAP API: {$finalSoapCount}\n";
    echo "  Decision text: {$finalDecisionCount}\n";
    echo "  Unknown: {$finalUnknownCount}\n";
    echo "</div>";
    
    $totalFinalParties = count($mappedDosar->parti ?? []);
    if ($totalFinalParties > 100) {
        echo "<p>✅ Complete mapping successful - hybrid extraction working with {$totalFinalParties} total parties</p>";
    } else {
        echo "<p>❌ Complete mapping shows only {$totalFinalParties} parties - hybrid extraction not working</p>";
    }
    echo "</div>";
    
    // Compare with public getDetaliiDosar method
    echo "<div class='method-test'>";
    echo "<h3>Step 6: Compare with Public getDetaliiDosar()</h3>";
    
    $publicDosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    $publicParties = count($publicDosar->parti ?? []);
    
    echo "<div class='debug-info'>";
    echo "Public method results:\n";
    echo "Total parties: {$publicParties}\n";
    echo "Matches private method: " . ($publicParties === $totalFinalParties ? 'YES' : 'NO') . "\n";
    echo "</div>";
    
    if ($publicParties === $totalFinalParties) {
        echo "<p>✅ Public method matches private method results</p>";
    } else {
        echo "<p>❌ Inconsistency: Public method returns {$publicParties} parties, private method returns {$totalFinalParties}</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h4>❌ Error during testing</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";

// Summary and diagnosis
echo "<div class='section'>";
echo "<h2>📋 Summary and Diagnosis</h2>";

if (isset($totalFinalParties) && $totalFinalParties > 100) {
    echo "<div class='success'>";
    echo "<h4>✅ Hybrid Extraction System Working</h4>";
    echo "<p>All hybrid extraction methods are functioning correctly for this test case.</p>";
    echo "<ul>";
    echo "<li>SOAP extraction: " . (isset($finalSoapCount) ? $finalSoapCount : 0) . " parties</li>";
    echo "<li>Decision text extraction: " . (isset($finalDecisionCount) ? $finalDecisionCount : 0) . " parties</li>";
    echo "<li>Total after deduplication: {$totalFinalParties} parties</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h4>❌ Hybrid Extraction Issue Detected</h4>";
    echo "<p>The hybrid extraction system is not working correctly for this test case.</p>";
    echo "<p><strong>Possible causes:</strong></p>";
    echo "<ul>";
    echo "<li>Decision text extraction method not finding parties</li>";
    echo "<li>No decision text available for this case</li>";
    echo "<li>Merge and deduplication method not working correctly</li>";
    echo "<li>SOAP data structure different than expected</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h4>Next Steps:</h4>";
echo "<ol>";
echo "<li>If hybrid extraction is working here but not on frontend, the issue is in data transfer</li>";
echo "<li>If hybrid extraction is not working here, the issue is in the methods themselves</li>";
echo "<li>Test with the problematic case (CurteadeApelBUCURESTI) to see if it's institution-specific</li>";
echo "<li>Check error logs for any silent failures in the extraction methods</li>";
echo "</ol>";

echo "</div>";

echo "</body></html>";
?>
