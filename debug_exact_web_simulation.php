<?php
// Simulate the exact web interface behavior to find where asterisk is lost
require_once 'bootstrap.php';

use App\Services\DosarService;

// Simulate the exact POST request
$_POST['bulkSearchTerms'] = "14096/3/2024*";

echo "<h1>🔍 Debug Exact Web Interface Simulation</h1>";

// Include ALL the exact functions from index.php
function parseBulkSearchTerms($input) {
    $input = str_replace(',', "\n", $input);
    $terms = explode("\n", $input);
    $cleanTerms = [];

    foreach ($terms as $term) {
        $term = trim($term);
        if (!empty($term) && strlen($term) >= 2) {
            $cleanTerms[] = [
                'term' => $term,
                'type' => detectSearchType($term)
            ];
        }
    }

    $uniqueTerms = [];
    $seenTerms = [];

    foreach ($cleanTerms as $termData) {
        $termKey = strtolower($termData['term']);
        if (!in_array($termKey, $seenTerms)) {
            $uniqueTerms[] = $termData;
            $seenTerms[] = $termKey;
        }
    }

    return $uniqueTerms;
}

function detectSearchType($term) {
    $cleanTerm = trim($term, '"\'');
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\*$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\/[a-zA-Z0-9]+$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
        return 'numarDosar';
    }
    
    return 'numeParte';
}

function normalizeCaseNumber($caseNumber) {
    $cleanNumber = trim($caseNumber, '"\'');
    $hasWildcard = false;
    $hasSuffix = false;
    $originalSuffix = '';
    $normalizedNumber = $cleanNumber;

    // Detectează și elimină asterisk wildcard
    if (preg_match('/^(.+)\*$/', $cleanNumber, $matches)) {
        $hasWildcard = true;
        $normalizedNumber = $matches[1];
    }

    // Detectează sufixe suplimentare (ex: /a17, /a1, etc.)
    if (preg_match('/^(\d+\/\d+(?:\/\d+)?)\/([a-zA-Z][a-zA-Z0-9]*|[0-9]*[a-zA-Z][a-zA-Z0-9]*)$/', $normalizedNumber, $matches)) {
        $hasSuffix = true;
        $originalSuffix = $matches[2];
        $normalizedNumber = $matches[1];
    }

    // Elimină prefixele (nr., dosar, număr)
    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(.+)$/i', $normalizedNumber, $matches)) {
        $normalizedNumber = $matches[1];
    }

    return [
        'normalized' => $normalizedNumber,
        'original' => $cleanNumber,
        'hasWildcard' => $hasWildcard,
        'hasSuffix' => $hasSuffix,
        'suffix' => $originalSuffix
    ];
}

function performBulkSearchWithFilters($searchTermsData, $filters) {
    $dosarService = new DosarService();
    $results = [];

    foreach ($searchTermsData as $termData) {
        $term = $termData['term'];
        $searchType = $termData['type'];

        echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
        echo "<h4>Processing term: '" . htmlspecialchars($term) . "' (type: $searchType)</h4>";

        try {
            $searchParams = [
                'numarDosar' => '',
                'institutie' => $filters['institutie'] ?? null,
                'numeParte' => '',
                'obiectDosar' => '',
                'dataStart' => '',
                'dataStop' => '',
                'dataUltimaModificareStart' => '',
                'dataUltimaModificareStop' => '',
                'categorieInstanta' => '',
                'categorieCaz' => ''
            ];

            // This is the critical part - simulate the exact logic from index.php
            switch ($searchType) {
                case 'numarDosar':
                    echo "<strong>Case number processing:</strong><br>";
                    echo "Original term: '" . htmlspecialchars($term) . "'<br>";

                    // ENHANCED: Normalizăm numărul de dosar pentru compatibilitate SOAP API
                    $caseNumberInfo = normalizeCaseNumber($term);
                    echo "Normalized: '" . htmlspecialchars($caseNumberInfo['normalized']) . "'<br>";
                    echo "Has wildcard: " . ($caseNumberInfo['hasWildcard'] ? 'YES' : 'NO') . "<br>";
                    echo "Original in info: '" . htmlspecialchars($caseNumberInfo['original']) . "'<br>";

                    // FIXED: Pentru căutări wildcard cu asterisk, păstrăm asterisk-ul în căutare
                    // pentru a găsi și cazurile cu asterisk literal în numărul dosarului
                    if ($caseNumberInfo['hasWildcard']) {
                        $searchParams['numarDosar'] = $caseNumberInfo['original']; // Păstrăm asterisk-ul
                        echo "<strong>WILDCARD SEARCH: Using original term to find literal asterisk cases</strong><br>";
                    } else {
                        $searchParams['numarDosar'] = $caseNumberInfo['normalized'];
                    }

                    // Stocăm informațiile despre wildcard/suffix pentru filtrarea client-side
                    $searchParams['_caseNumberInfo'] = $caseNumberInfo;
                    break;
                case 'numeParte':
                default:
                    $searchParams['numeParte'] = $term;
                    break;
            }

            echo "<strong>Search params:</strong><br>";
            echo "numarDosar: '" . htmlspecialchars($searchParams['numarDosar']) . "'<br>";
            echo "numeParte: '" . htmlspecialchars($searchParams['numeParte']) . "'<br>";

            $termResults = $dosarService->cautareAvansata($searchParams);
            echo "<strong>Results found:</strong> " . count($termResults) . "<br>";

            // Add search metadata to each result
            foreach ($termResults as $dosar) {
                $dosar->searchTerm = $term;  // ← IMPORTANT: This should preserve the original term
                $dosar->searchType = $searchType;
            }

            echo "<strong>Building result array:</strong><br>";
            echo "term: '" . htmlspecialchars($term) . "'<br>";
            echo "type: '" . htmlspecialchars($searchType) . "'<br>";
            echo "count: " . count($termResults) . "<br>";

            $results[] = [
                'term' => $term,  // ← This should be the original term with asterisk
                'type' => $searchType,
                'results' => $termResults ?: [],
                'count' => count($termResults ?: []),
                'error' => null
            ];

        } catch (Exception $e) {
            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => [],
                'count' => 0,
                'error' => $e->getMessage()
            ];
        }
        
        echo "</div>";
    }

    return $results;
}

function generateResultMessage($count, $term) {
    if ($count === 0) {
        return "Nu au fost găsite rezultate pentru termenul '{$term}'";
    } elseif ($count === 1) {
        return "1 rezultat găsit pentru termenul '{$term}'";
    } else {
        return "{$count} rezultate găsite pentru termenul '{$term}'";
    }
}

try {
    $bulkSearchTerms = $_POST['bulkSearchTerms'] ?? '';
    
    if (!empty($bulkSearchTerms)) {
        echo "<h2>Step 1: Parse Search Terms</h2>";
        $searchTermsData = parseBulkSearchTerms($bulkSearchTerms);
        
        echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
        echo "<strong>Parsed terms:</strong><br>";
        foreach ($searchTermsData as $index => $termData) {
            echo "Term #{$index}: '" . htmlspecialchars($termData['term']) . "' (type: {$termData['type']})<br>";
        }
        echo "</div>";
        
        echo "<h2>Step 2: Perform Search (Exact Index.php Logic)</h2>";
        $searchResults = performBulkSearchWithFilters($searchTermsData, []);
        
        echo "<h2>Step 3: Check Final Results</h2>";
        
        foreach ($searchResults as $index => $result) {
            echo "<div style='background: #fff3cd; padding: 15px; margin: 10px 0; border: 1px solid #ffc107;'>";
            echo "<h3>Result #{$index}</h3>";
            echo "<strong>Final result data:</strong><br>";
            echo "result['term'] = '" . htmlspecialchars($result['term']) . "'<br>";
            echo "result['type'] = '" . htmlspecialchars($result['type']) . "'<br>";
            echo "result['count'] = " . $result['count'] . "<br>";
            
            if (strpos($result['term'], '*') !== false) {
                echo "<div style='background: #d4edda; padding: 8px; margin: 5px 0; border: 1px solid #c3e6cb;'>";
                echo "<strong>✅ ASTERISK PRESERVED in final result!</strong>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 8px; margin: 5px 0; border: 1px solid #f5c6cb;'>";
                echo "<strong>❌ ASTERISK LOST in final result!</strong>";
                echo "</div>";
            }
            
            // Test the message generation
            $message = generateResultMessage($result['count'], $result['term']);
            echo "<strong>Generated message:</strong> " . htmlspecialchars($message) . "<br>";
            
            if (strpos($message, '*') !== false) {
                echo "<div style='background: #d4edda; padding: 8px; margin: 5px 0; border: 1px solid #c3e6cb;'>";
                echo "<strong>✅ ASTERISK PRESERVED in message!</strong>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 8px; margin: 5px 0; border: 1px solid #f5c6cb;'>";
                echo "<strong>❌ ASTERISK LOST in message!</strong>";
                echo "</div>";
            }
            
            // Simulate the exact HTML that would be generated
            echo "<h4>HTML that would be generated:</h4>";
            echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>";
            echo "<h6>" . htmlspecialchars($result['term']) . "</h6>";
            echo "<span id='resultMessage{$index}' data-term='" . htmlspecialchars($result['term']) . "' data-original-count='{$result['count']}'>";
            echo $message;
            echo "</span>";
            echo "</div>";
            
            echo "</div>";
        }
        
        echo "<h2>Step 4: Conclusion</h2>";
        
        $allTermsHaveAsterisk = true;
        $allMessagesHaveAsterisk = true;
        
        foreach ($searchResults as $result) {
            if (strpos($result['term'], '*') === false) {
                $allTermsHaveAsterisk = false;
            }
            $message = generateResultMessage($result['count'], $result['term']);
            if (strpos($message, '*') === false) {
                $allMessagesHaveAsterisk = false;
            }
        }
        
        if ($allTermsHaveAsterisk && $allMessagesHaveAsterisk) {
            echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border: 1px solid #c3e6cb;'>";
            echo "<h3>✅ BACKEND IS PERFECT!</h3>";
            echo "<p>All terms and messages preserve the asterisk correctly.</p>";
            echo "<p><strong>The issue must be in the actual web interface:</strong></p>";
            echo "<ul>";
            echo "<li>Check if you're actually searching for '14096/3/2024*' (with asterisk)</li>";
            echo "<li>Check if the browser is modifying the input somehow</li>";
            echo "<li>Check if there's JavaScript that modifies the display</li>";
            echo "<li>Check the actual HTML source in the browser</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb;'>";
            echo "<h3>❌ BACKEND ISSUE FOUND!</h3>";
            echo "<p>The asterisk is being lost in the backend processing.</p>";
            echo "</div>";
        }
        
    } else {
        echo "<p>No search terms provided.</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb;'>";
    echo "<h3>❌ Error:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🎯 Final Debugging Steps</h2>";
echo "<p>If backend preserves asterisk correctly, then:</p>";
echo "<ol>";
echo "<li><strong>Check the actual web search:</strong> Make sure you're typing '14096/3/2024*' with asterisk</li>";
echo "<li><strong>Check browser input:</strong> Some browsers might strip special characters</li>";
echo "<li><strong>Check form submission:</strong> Use browser dev tools to see what's actually sent</li>";
echo "<li><strong>Check HTML source:</strong> View page source to see the actual generated HTML</li>";
echo "<li><strong>Check JavaScript:</strong> Look for any JS that modifies the display</li>";
echo "</ol>";
?>
