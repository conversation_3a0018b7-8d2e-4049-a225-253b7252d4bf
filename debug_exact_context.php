<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 DEBUGGING EXACT CONTEXT FOR: Burduşelu Tudoriţa\n";
echo "===================================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    // Get the solutieSumar content
    $solutieSumarText = '';
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $i => $sedinta) {
            if (!empty($sedinta['solutieSumar'])) {
                $solutieSumarText = $sedinta['solutieSumar'];
                break;
            }
        }
    }
    
    if (empty($solutieSumarText)) {
        echo "❌ No solutieSumar text found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    echo "Text length: " . strlen($solutieSumarText) . " characters\n\n";
    
    // Find the exact position of "Burduşelu Tudoriţa"
    $pos = stripos($solutieSumarText, 'Burduşelu Tudoriţa');
    if ($pos === false) {
        echo "❌ 'Burduşelu Tudoriţa' not found in text\n";
        exit(1);
    }
    
    echo "✅ Found 'Burduşelu Tudoriţa' at position {$pos}\n\n";
    
    // Get the exact context (300 characters before and after)
    $start = max(0, $pos - 300);
    $length = min(600, strlen($solutieSumarText) - $start);
    $context = substr($solutieSumarText, $start, $length);
    
    echo "🔍 EXACT CONTEXT (300 chars before/after):\n";
    echo "==========================================\n";
    echo $context . "\n\n";
    
    // Now let's manually test Pattern 9 (comma extraction) on this context
    echo "🔍 TESTING PATTERN 9 (COMMA EXTRACTION):\n";
    echo "=========================================\n";
    
    // Split by comma around this area
    $contextStart = max(0, $pos - 100);
    $contextEnd = min(strlen($solutieSumarText), $pos + 100);
    $testSegment = substr($solutieSumarText, $contextStart, $contextEnd - $contextStart);
    
    echo "Test segment:\n";
    echo $testSegment . "\n\n";
    
    $commaNames = explode(',', $testSegment);
    echo "Comma-separated segments:\n";
    foreach ($commaNames as $i => $name) {
        $name = trim($name);
        echo "{$i}: '{$name}'\n";
        
        if (stripos($name, 'Tudoriţa') !== false) {
            echo "  *** CONTAINS TUDORIŢA ***\n";
            
            // Test the new regex pattern
            $newPattern = '/^[A-ZĂÂÎȘȚăâîșțţşŞţŢ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+$/u';
            $matches = preg_match($newPattern, $name);
            echo "  New regex match: " . ($matches ? 'PASS' : 'FAIL') . "\n";
            
            if (!$matches) {
                echo "  Length: " . strlen($name) . "\n";
                echo "  First char: '" . substr($name, 0, 1) . "'\n";
                echo "  Last char: '" . substr($name, -1) . "'\n";
                
                // Test character by character
                for ($j = 0; $j < strlen($name); $j++) {
                    $char = substr($name, $j, 1);
                    $charMatch = preg_match('/[A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]/u', $char);
                    if (!$charMatch) {
                        echo "  Problem char at pos {$j}: '{$char}' (ord: " . ord($char) . ")\n";
                    }
                }
            }
        }
    }
    
    echo "\n✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
