<?php

namespace App\Services;

use App\Config\Database;
use Exception;

/**
 * Audit Logger Service
 * 
 * Provides comprehensive audit logging functionality for tracking
 * user actions, data changes, and security events.
 */
class AuditLogger
{
    /**
     * Log a user action to the audit log
     * 
     * @param int|null $userId User ID (null for system actions)
     * @param string $action Action performed (e.g., 'CREATE', 'UPDATE', 'DELETE', 'LOGIN')
     * @param string|null $tableName Table affected (if applicable)
     * @param int|null $recordId Record ID affected (if applicable)
     * @param array|null $oldValues Old values before change
     * @param array|null $newValues New values after change
     * @param string|null $ipAddress IP address of user
     * @param string|null $userAgent User agent string
     * @return bool Success status
     */
    public function log(
        ?int $userId,
        string $action,
        ?string $tableName = null,
        ?int $recordId = null,
        ?array $oldValues = null,
        ?array $newValues = null,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): bool {
        try {
            // Get IP address and user agent if not provided
            if ($ipAddress === null) {
                $ipAddress = $this->getClientIpAddress();
            }
            
            if ($userAgent === null) {
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            }
            
            // Prepare values for JSON storage
            $oldValuesJson = $oldValues ? json_encode($oldValues, JSON_UNESCAPED_UNICODE) : null;
            $newValuesJson = $newValues ? json_encode($newValues, JSON_UNESCAPED_UNICODE) : null;
            
            // Insert audit log entry
            Database::execute(
                "INSERT INTO audit_log (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                [
                    $userId,
                    $action,
                    $tableName,
                    $recordId,
                    $oldValuesJson,
                    $newValuesJson,
                    $ipAddress,
                    $userAgent
                ]
            );
            
            return true;
            
        } catch (Exception $e) {
            // Log error but don't break the application
            error_log("Audit logging failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Log user authentication events
     * 
     * @param int|null $userId User ID (null for failed attempts)
     * @param string $action LOGIN_SUCCESS, LOGIN_FAILED, LOGOUT, etc.
     * @param array $details Additional details (email, reason, etc.)
     * @return bool Success status
     */
    public function logAuth(?int $userId, string $action, array $details = []): bool
    {
        return $this->log(
            $userId,
            $action,
            'users',
            $userId,
            null,
            $details
        );
    }

    /**
     * Log data changes with before/after values
     * 
     * @param int $userId User making the change
     * @param string $tableName Table being modified
     * @param int $recordId Record ID being modified
     * @param array $oldData Data before change
     * @param array $newData Data after change
     * @return bool Success status
     */
    public function logDataChange(int $userId, string $tableName, int $recordId, array $oldData, array $newData): bool
    {
        // Only log fields that actually changed
        $changes = [];
        $oldValues = [];
        $newValues = [];
        
        foreach ($newData as $field => $newValue) {
            $oldValue = $oldData[$field] ?? null;
            if ($oldValue !== $newValue) {
                $oldValues[$field] = $oldValue;
                $newValues[$field] = $newValue;
            }
        }
        
        // Only log if there are actual changes
        if (empty($oldValues) && empty($newValues)) {
            return true; // No changes to log
        }
        
        return $this->log(
            $userId,
            'UPDATE',
            $tableName,
            $recordId,
            $oldValues,
            $newValues
        );
    }

    /**
     * Log record creation
     * 
     * @param int $userId User creating the record
     * @param string $tableName Table name
     * @param int $recordId New record ID
     * @param array $data Record data
     * @return bool Success status
     */
    public function logCreate(int $userId, string $tableName, int $recordId, array $data): bool
    {
        return $this->log(
            $userId,
            'CREATE',
            $tableName,
            $recordId,
            null,
            $data
        );
    }

    /**
     * Log record deletion
     * 
     * @param int $userId User deleting the record
     * @param string $tableName Table name
     * @param int $recordId Record ID being deleted
     * @param array $data Record data before deletion
     * @return bool Success status
     */
    public function logDelete(int $userId, string $tableName, int $recordId, array $data): bool
    {
        return $this->log(
            $userId,
            'DELETE',
            $tableName,
            $recordId,
            $data,
            null
        );
    }

    /**
     * Log security events
     * 
     * @param int|null $userId User ID (if applicable)
     * @param string $event Security event type
     * @param array $details Event details
     * @return bool Success status
     */
    public function logSecurity(?int $userId, string $event, array $details = []): bool
    {
        return $this->log(
            $userId,
            'SECURITY_' . $event,
            null,
            null,
            null,
            $details
        );
    }

    /**
     * Get audit log entries with filtering
     * 
     * @param array $filters Filtering options
     * @param int $limit Number of entries to return
     * @param int $offset Offset for pagination
     * @return array Audit log entries
     */
    public function getAuditLog(array $filters = [], int $limit = 100, int $offset = 0): array
    {
        try {
            $whereConditions = [];
            $params = [];
            
            // Build WHERE conditions based on filters
            if (!empty($filters['user_id'])) {
                $whereConditions[] = "al.user_id = ?";
                $params[] = $filters['user_id'];
            }
            
            if (!empty($filters['action'])) {
                $whereConditions[] = "al.action = ?";
                $params[] = $filters['action'];
            }
            
            if (!empty($filters['table_name'])) {
                $whereConditions[] = "al.table_name = ?";
                $params[] = $filters['table_name'];
            }
            
            if (!empty($filters['date_from'])) {
                $whereConditions[] = "al.created_at >= ?";
                $params[] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $whereConditions[] = "al.created_at <= ?";
                $params[] = $filters['date_to'];
            }
            
            if (!empty($filters['ip_address'])) {
                $whereConditions[] = "al.ip_address = ?";
                $params[] = $filters['ip_address'];
            }
            
            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
            
            // Add limit and offset to params
            $params[] = $limit;
            $params[] = $offset;
            
            $sql = "
                SELECT 
                    al.*,
                    u.email as user_email,
                    u.first_name,
                    u.last_name
                FROM audit_log al
                LEFT JOIN users u ON al.user_id = u.id
                {$whereClause}
                ORDER BY al.created_at DESC
                LIMIT ? OFFSET ?
            ";
            
            return Database::fetchAll($sql, $params);
            
        } catch (Exception $e) {
            error_log("Failed to retrieve audit log: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get audit statistics
     * 
     * @param array $filters Filtering options
     * @return array Statistics
     */
    public function getAuditStats(array $filters = []): array
    {
        try {
            $whereConditions = [];
            $params = [];
            
            // Build WHERE conditions (same as getAuditLog)
            if (!empty($filters['date_from'])) {
                $whereConditions[] = "created_at >= ?";
                $params[] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $whereConditions[] = "created_at <= ?";
                $params[] = $filters['date_to'];
            }
            
            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
            
            $sql = "
                SELECT 
                    COUNT(*) as total_entries,
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(DISTINCT ip_address) as unique_ips,
                    SUM(CASE WHEN action LIKE 'LOGIN%' THEN 1 ELSE 0 END) as login_events,
                    SUM(CASE WHEN action = 'CREATE' THEN 1 ELSE 0 END) as create_events,
                    SUM(CASE WHEN action = 'UPDATE' THEN 1 ELSE 0 END) as update_events,
                    SUM(CASE WHEN action = 'DELETE' THEN 1 ELSE 0 END) as delete_events,
                    SUM(CASE WHEN action LIKE 'SECURITY_%' THEN 1 ELSE 0 END) as security_events
                FROM audit_log
                {$whereClause}
            ";
            
            return Database::fetchOne($sql, $params) ?: [];
            
        } catch (Exception $e) {
            error_log("Failed to get audit stats: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Clean up old audit log entries
     * 
     * @param int $daysToKeep Number of days to keep
     * @return int Number of entries deleted
     */
    public function cleanup(int $daysToKeep = 365): int
    {
        try {
            $deleted = Database::execute(
                "DELETE FROM audit_log WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)",
                [$daysToKeep]
            )->rowCount();
            
            if ($deleted > 0) {
                $this->log(null, 'SYSTEM_CLEANUP', 'audit_log', null, null, [
                    'deleted_entries' => $deleted,
                    'days_kept' => $daysToKeep
                ]);
            }
            
            return $deleted;
            
        } catch (Exception $e) {
            error_log("Audit log cleanup failed: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get client IP address (handles proxies and load balancers)
     * 
     * @return string IP address
     */
    private function getClientIpAddress(): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs (from proxies)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                // Validate IP address
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}
