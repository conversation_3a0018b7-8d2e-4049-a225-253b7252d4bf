<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

echo "🔍 ANALYZING ALL DECISION TEXT PATTERNS\n";
echo "=======================================\n\n";

$dosarService = new DosarService();

try {
    // Get case details for CurteadeApelBUCURESTI
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'CurteadeApelBUCURESTI');
    
    if (!$dosar) {
        echo "❌ Case not found\n";
        exit(1);
    }
    
    echo "✅ Case found\n";
    echo "Current total parties: " . count($dosar->parti) . "\n\n";
    
    // Count decision text parties by quality
    $decisionParties = [];
    foreach ($dosar->parti as $party) {
        $partyArray = (array) $party;
        if (isset($partyArray['source']) && $partyArray['source'] === 'decision_text') {
            $quality = $partyArray['calitate'];
            if (!isset($decisionParties[$quality])) {
                $decisionParties[$quality] = [];
            }
            $decisionParties[$quality][] = $partyArray['nume'];
        }
    }
    
    echo "📊 DECISION TEXT PARTIES BY QUALITY:\n";
    foreach ($decisionParties as $quality => $names) {
        echo "{$quality}: " . count($names) . " parties\n";
        
        // Show first 5 names for each quality
        echo "  Sample names:\n";
        for ($i = 0; $i < min(5, count($names)); $i++) {
            echo "    - " . $names[$i] . "\n";
        }
        echo "\n";
    }
    
    // Get all text sources
    $allTexts = [];
    if (isset($dosar->sedinte) && is_array($dosar->sedinte)) {
        foreach ($dosar->sedinte as $i => $sedinta) {
            if (!empty($sedinta['solutie'])) {
                $allTexts[] = ['source' => 'solutie', 'text' => $sedinta['solutie']];
            }
            if (!empty($sedinta['solutieSumar'])) {
                $allTexts[] = ['source' => 'solutieSumar', 'text' => $sedinta['solutieSumar']];
            }
        }
    }
    
    echo "📄 TEXT SOURCES FOUND:\n";
    foreach ($allTexts as $i => $textSource) {
        echo ($i + 1) . ". {$textSource['source']}: " . strlen($textSource['text']) . " characters\n";
    }
    echo "\n";
    
    // Test all patterns manually on the solutieSumar (the main source)
    $solutieSumarText = '';
    foreach ($allTexts as $textSource) {
        if ($textSource['source'] === 'solutieSumar') {
            $solutieSumarText = $textSource['text'];
            break;
        }
    }
    
    if (empty($solutieSumarText)) {
        echo "❌ No solutieSumar text found\n";
        exit(1);
    }
    
    echo "🔍 TESTING ALL PATTERNS ON SOLUTIESUMAR:\n";
    echo "========================================\n\n";
    
    $allExtractedNames = [];
    
    // Pattern 1: Basic semicolon separation
    echo "Pattern 1: Semicolon separation\n";
    if (preg_match_all('/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)\s*;/', $solutieSumarText, $matches)) {
        $pattern1Names = array_unique($matches[1]);
        echo "Found: " . count($pattern1Names) . " names\n";
        $allExtractedNames = array_merge($allExtractedNames, $pattern1Names);
    } else {
        echo "Found: 0 names\n";
    }
    echo "\n";
    
    // Pattern 2: Comma separation with quality indicators
    echo "Pattern 2: Comma separation with quality\n";
    if (preg_match_all('/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)\s*,\s*(?:în calitate de|ca|fiind)/i', $solutieSumarText, $matches)) {
        $pattern2Names = array_unique($matches[1]);
        echo "Found: " . count($pattern2Names) . " names\n";
        $allExtractedNames = array_merge($allExtractedNames, $pattern2Names);
    } else {
        echo "Found: 0 names\n";
    }
    echo "\n";
    
    // Pattern 3: "împotriva" pattern
    echo "Pattern 3: Împotriva pattern\n";
    if (preg_match_all('/împotriva\s+([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+?)(?:\s*,|\s*şi|\s*$)/i', $solutieSumarText, $matches)) {
        $pattern3Names = array_unique($matches[1]);
        echo "Found: " . count($pattern3Names) . " names\n";
        $allExtractedNames = array_merge($allExtractedNames, $pattern3Names);
    } else {
        echo "Found: 0 names\n";
    }
    echo "\n";
    
    // Pattern 4: "reclamant/pârât" pattern
    echo "Pattern 4: Reclamant/Pârât pattern\n";
    if (preg_match_all('/(?:reclamant|pârât|petent|intimat)(?:ul|a)?\s*:?\s*([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)/i', $solutieSumarText, $matches)) {
        $pattern4Names = array_unique($matches[1]);
        echo "Found: " . count($pattern4Names) . " names\n";
        $allExtractedNames = array_merge($allExtractedNames, $pattern4Names);
    } else {
        echo "Found: 0 names\n";
    }
    echo "\n";
    
    // Pattern 5: "reprezentat de" pattern
    echo "Pattern 5: Reprezentat de pattern\n";
    if (preg_match_all('/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)\s*reprezentat/i', $solutieSumarText, $matches)) {
        $pattern5Names = array_unique($matches[1]);
        echo "Found: " . count($pattern5Names) . " names\n";
        $allExtractedNames = array_merge($allExtractedNames, $pattern5Names);
    } else {
        echo "Found: 0 names\n";
    }
    echo "\n";
    
    // Pattern 6: "cu domiciliul" pattern
    echo "Pattern 6: Cu domiciliul pattern\n";
    if (preg_match_all('/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)\s*cu domiciliul/i', $solutieSumarText, $matches)) {
        $pattern6Names = array_unique($matches[1]);
        echo "Found: " . count($pattern6Names) . " names\n";
        $allExtractedNames = array_merge($allExtractedNames, $pattern6Names);
    } else {
        echo "Found: 0 names\n";
    }
    echo "\n";
    
    // Pattern 7: Enhanced comma separation
    echo "Pattern 7: Enhanced comma separation\n";
    $commaNames = explode(',', $solutieSumarText);
    $pattern7Names = [];
    foreach ($commaNames as $name) {
        $name = trim($name);
        $name = preg_replace('/.*(?:apelanţii|apelan\?ii)\s+/', '', $name);
        $name = preg_replace('/\s*ca\s+(?:netimbrate|nefondate).*$/', '', $name);
        $name = preg_replace('/\s*\(.*?\)/', '', $name);
        $name = preg_replace('/\s*şi\s*$/', '', $name);
        $name = preg_replace('/\..*$/', '', $name);
        $name = trim($name);
        
        if (strlen($name) >= 3 && preg_match('/^[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+$/u', $name)) {
            $pattern7Names[] = $name;
        }
    }
    $pattern7Names = array_unique($pattern7Names);
    echo "Found: " . count($pattern7Names) . " names\n";
    $allExtractedNames = array_merge($allExtractedNames, $pattern7Names);
    echo "\n";
    
    // Pattern 8: Appeal patterns (already tested)
    echo "Pattern 8: Appeal patterns (Anulează + Respinge)\n";
    $pattern8Names = [];
    
    // Anulează
    if (preg_match('/Anulează\s+apelurile\s+formulate\s+de\s+apelanţii\s+([^.]+?)(?:\s*ca\s+(?:netimbrate|nefondate))?\./', $solutieSumarText, $anuleazaMatch)) {
        $apellantsText = $anuleazaMatch[1];
        $apellantNames = explode(',', $apellantsText);
        foreach ($apellantNames as $name) {
            $name = trim($name);
            $name = preg_replace('/\s*şi\s*$/', '', $name);
            $name = preg_replace('/\s*\?.*$/', '', $name);
            $name = trim($name);
            if (strlen($name) >= 3 && preg_match('/^[A-Za-zĂÂÎȘȚăâîșțţ0-9][A-Za-zĂÂÎȘȚăâîșțţ0-9\s\-\.\(\)\/]+$/u', $name)) {
                $pattern8Names[] = $name;
            }
        }
    }
    
    // Respinge
    if (preg_match('/Respinge\s+apelurile\s+formulate\s+de\s+apelan[ţ?]ii\s+(.+?)(?:\s*ca\s+(?:netimbrate|nefondate))?(?:\.\s*Admite|$)/s', $solutieSumarText, $respingeMatch)) {
        $apellantsText = $respingeMatch[1];
        $apellantsText = preg_replace('/\.\s*[A-Z][^,]*(?:Georgeta|Elisabeta|Marioara|Anişoara|Florica|Steliana|Florenţa|Sorin)[^,]*/', '', $apellantsText);
        $apellantNames = explode(',', $apellantsText);
        foreach ($apellantNames as $name) {
            $name = trim($name);
            $name = preg_replace('/\s*şi\s*$/', '', $name);
            $name = preg_replace('/\s*\?.*$/', '', $name);
            $name = trim($name);
            if (strlen($name) >= 3 && preg_match('/^[A-Za-zĂÂÎȘȚăâîșțţ0-9][A-Za-zĂÂÎȘȚăâîșțţ0-9\s\-\.\(\)\/]+$/u', $name)) {
                $pattern8Names[] = $name;
            }
        }
    }
    
    $pattern8Names = array_unique($pattern8Names);
    echo "Found: " . count($pattern8Names) . " names\n";
    $allExtractedNames = array_merge($allExtractedNames, $pattern8Names);
    echo "\n";
    
    // Summary
    $uniqueAllNames = array_unique($allExtractedNames);
    echo "📊 SUMMARY:\n";
    echo "Total names from all patterns: " . count($allExtractedNames) . "\n";
    echo "Unique names from all patterns: " . count($uniqueAllNames) . "\n";
    echo "Current decision text parties: 252\n";
    echo "Difference: " . (count($uniqueAllNames) - 252) . "\n\n";
    
    echo "✅ Analysis complete\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
