<?php
/**
 * Portal Judiciar - Test Simple Mail Function
 * 
 * Test pentru funcția nativă mail() din PHP
 */

// Încărcăm bootstrap-ul aplicației
require_once 'bootstrap.php';

// Inițializăm sesiunea
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$testResult = null;

// Procesăm testul dacă formularul a fost trimis
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_test'])) {
    $testEmail = trim($_POST['test_email']);
    $testSubject = 'Test Email - Portal Judiciar';
    $testMessage = "Acesta este un email de test trimis prin funcția nativă mail() din PHP.\n\n";
    $testMessage .= "Timestamp: " . date('d.m.Y H:i:s') . "\n";
    $testMessage .= "IP: " . $_SERVER['REMOTE_ADDR'] . "\n";
    $testMessage .= "User Agent: " . $_SERVER['HTTP_USER_AGENT'] . "\n\n";
    $testMessage .= "Dacă primești acest email, funcția mail() funcționează corect!";
    
    // Headers pentru email
    $headers = array();
    $headers[] = 'From: Portal Judiciar <noreply@localhost>';
    $headers[] = 'Reply-To: ' . CONTACT_EMAIL;
    $headers[] = 'X-Mailer: PHP/' . phpversion();
    $headers[] = 'Content-Type: text/plain; charset=UTF-8';
    $headers[] = 'Content-Transfer-Encoding: 8bit';
    
    // Trimitem email-ul
    $mailSent = mail($testEmail, $testSubject, $testMessage, implode("\r\n", $headers));
    
    if ($mailSent) {
        $testResult = [
            'success' => true,
            'message' => 'Email-ul de test a fost trimis cu succes prin funcția mail()!'
        ];
    } else {
        $testResult = [
            'success' => false,
            'message' => 'Eroare la trimiterea email-ului prin funcția mail(). Verifică configurația serverului.'
        ];
    }
}

// Verificăm configurația mail din PHP
$mailConfig = [
    'sendmail_path' => ini_get('sendmail_path'),
    'smtp' => ini_get('SMTP'),
    'smtp_port' => ini_get('smtp_port'),
    'sendmail_from' => ini_get('sendmail_from'),
    'mail.add_x_header' => ini_get('mail.add_x_header'),
    'mail.log' => ini_get('mail.log')
];

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Mail - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            padding: 1.5rem;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        .config-item:last-child {
            border-bottom: none;
        }
        .config-value {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.875rem;
        }
        .simple-mail-info {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        .simple-mail-info h5 {
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="text-center mb-4">
            <h1 class="display-5">
                <i class="fas fa-paper-plane me-2"></i>
                Test Simple Mail Function
            </h1>
            <p class="lead">Testare funcție nativă mail() din PHP</p>
        </div>

        <!-- Simple Mail Info -->
        <div class="simple-mail-info">
            <h5><i class="fas fa-info-circle me-2"></i>Despre funcția mail() din PHP</h5>
            <p class="mb-2">
                Funcția <code>mail()</code> este metoda nativă din PHP pentru trimiterea email-urilor.
                Este mai simplă decât SMTP și nu necesită configurarea de credentiale externe.
            </p>
            <p class="mb-0">
                <strong>Avantaje:</strong> Simplă, fără configurare SMTP, funcționează pe majoritatea serverelor<br>
                <strong>Dezavantaje:</strong> Poate fi blocată de unii furnizori, email-urile pot ajunge în spam
            </p>
        </div>

        <!-- Test Result -->
        <?php if ($testResult): ?>
            <div class="test-section">
                <div class="alert alert-<?php echo $testResult['success'] ? 'success' : 'danger'; ?>">
                    <h5>
                        <i class="fas fa-<?php echo $testResult['success'] ? 'check' : 'times'; ?> me-2"></i>
                        <?php echo $testResult['success'] ? 'Test Reușit!' : 'Test Eșuat!'; ?>
                    </h5>
                    <p class="mb-0"><?php echo htmlspecialchars($testResult['message']); ?></p>
                </div>
            </div>
        <?php endif; ?>

        <!-- PHP Mail Configuration -->
        <div class="test-section">
            <h3><i class="fas fa-cog me-2"></i>Configurația PHP Mail</h3>
            <?php foreach ($mailConfig as $key => $value): ?>
                <div class="config-item">
                    <span><?php echo htmlspecialchars($key); ?></span>
                    <span class="config-value">
                        <?php echo $value ? htmlspecialchars($value) : '<em>nesetat</em>'; ?>
                    </span>
                </div>
            <?php endforeach; ?>
            
            <div class="mt-3">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Aceste setări sunt configurate în php.ini și pot varia în funcție de serverul web.
                </small>
            </div>
        </div>

        <!-- Test Form -->
        <div class="test-section">
            <h3><i class="fas fa-envelope me-2"></i>Trimite Email de Test</h3>
            
            <form method="POST" class="mb-3">
                <div class="mb-3">
                    <label for="test_email" class="form-label">Email destinatar:</label>
                    <input type="email" 
                           class="form-control" 
                           id="test_email" 
                           name="test_email" 
                           value="<?php echo htmlspecialchars(CONTACT_EMAIL); ?>" 
                           required>
                    <div class="form-text">Adresa unde va fi trimis email-ul de test</div>
                </div>
                
                <button type="submit" name="send_test" class="btn btn-primary">
                    <i class="fas fa-paper-plane me-2"></i>
                    Trimite Email de Test
                </button>
            </form>
        </div>

        <!-- Comparison -->
        <div class="test-section">
            <h3><i class="fas fa-balance-scale me-2"></i>Comparație: mail() vs SMTP</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-success"><i class="fas fa-paper-plane me-2"></i>Funcția mail()</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Simplă de folosit</li>
                        <li><i class="fas fa-check text-success me-2"></i>Fără configurare SMTP</li>
                        <li><i class="fas fa-check text-success me-2"></i>Fără credentiale externe</li>
                        <li><i class="fas fa-check text-success me-2"></i>Funcționează pe majoritatea serverelor</li>
                        <li><i class="fas fa-times text-danger me-2"></i>Poate fi blocată de hosting</li>
                        <li><i class="fas fa-times text-danger me-2"></i>Email-uri pot ajunge în spam</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5 class="text-primary"><i class="fas fa-server me-2"></i>SMTP</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Livrare mai sigură</li>
                        <li><i class="fas fa-check text-success me-2"></i>Mai puține șanse de spam</li>
                        <li><i class="fas fa-check text-success me-2"></i>Control complet asupra trimiterii</li>
                        <li><i class="fas fa-times text-danger me-2"></i>Necesită configurare complexă</li>
                        <li><i class="fas fa-times text-danger me-2"></i>Credentiale externe necesare</li>
                        <li><i class="fas fa-times text-danger me-2"></i>Poate fi blocat de firewall</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Instructions for WAMP -->
        <div class="test-section">
            <h3><i class="fas fa-server me-2"></i>Configurare pentru WAMP</h3>
            
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-2"></i>Pentru a funcționa pe WAMP:</h6>
                <ol class="mb-0">
                    <li>Deschide <code>php.ini</code> din WAMP</li>
                    <li>Caută secțiunea <code>[mail function]</code></li>
                    <li>Configurează:
                        <ul>
                            <li><code>SMTP = localhost</code></li>
                            <li><code>smtp_port = 25</code></li>
                            <li><code>sendmail_from = noreply@localhost</code></li>
                        </ul>
                    </li>
                    <li>Restart Apache din WAMP</li>
                    <li>Opțional: Instalează un server SMTP local (ex: hMailServer)</li>
                </ol>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="test-section text-center">
            <div class="d-grid gap-2 d-md-block">
                <a href="contact.php" class="btn btn-primary">
                    <i class="fas fa-envelope me-2"></i>
                    Pagina Contact
                </a>
                <a href="debug_contact.php" class="btn btn-secondary">
                    <i class="fas fa-bug me-2"></i>
                    Debug SMTP
                </a>
                <button onclick="location.reload()" class="btn btn-info">
                    <i class="fas fa-sync me-2"></i>
                    Reîncarcă Test
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
