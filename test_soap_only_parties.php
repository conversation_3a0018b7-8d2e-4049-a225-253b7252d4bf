<?php
/**
 * Test SOAP Only Parties
 * Verifică câte părți reale sunt în SOAP API fără extragerea din text
 */

// Include necessary files
require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<!DOCTYPE html>";
echo "<html><head><title>Test SOAP Only Parties</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .info { color: blue; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .highlight { background: yellow; font-weight: bold; }
</style></head><body>";

echo "<h1>🔍 Test SOAP Only Parties</h1>";
echo "<p>Verifică câte părți reale sunt în SOAP API fără extragerea din text</p>";
echo "<hr>";

try {
    $dosarService = new \App\Services\DosarService();
    $dosar = $dosarService->getDetaliiDosar('130/98/2022', 'TribunalulIALOMITA');
    
    if (!$dosar || !isset($dosar->parti)) {
        echo "<p class='error'>❌ Nu s-au putut obține datele dosarului</p>";
        exit;
    }
    
    $totalParties = count($dosar->parti);
    
    echo "<div class='section'>";
    echo "<h2>📊 Analiza Părților (Doar SOAP API)</h2>";
    
    echo "<table>";
    echo "<tr><th>Metric</th><th>Valoare</th><th>Status</th></tr>";
    echo "<tr><td>Total părți afișate</td><td>{$totalParties}</td><td class='" . ($totalParties <= 100 ? 'success' : 'warning') . "'>" . ($totalParties <= 100 ? '✅ Doar părți reale' : '⚠️ Posibil text extras') . "</td></tr>";
    
    // Analizăm sursele părților
    $soapCount = 0;
    $decisionCount = 0;
    $unknownCount = 0;
    
    foreach ($dosar->parti as $party) {
        $source = $party->source ?? 'unknown';
        switch ($source) {
            case 'soap_api': $soapCount++; break;
            case 'decision_text': $decisionCount++; break;
            default: $unknownCount++; break;
        }
    }
    
    echo "<tr><td>Părți din SOAP API</td><td>{$soapCount}</td><td class='success'>✅ Părți reale</td></tr>";
    echo "<tr><td>Părți din text decizie</td><td>{$decisionCount}</td><td class='" . ($decisionCount === 0 ? 'success' : 'error') . "'>" . ($decisionCount === 0 ? '✅ Niciuna (corect)' : '❌ Încă extrage din text') . "</td></tr>";
    echo "<tr><td>Părți sursă necunoscută</td><td>{$unknownCount}</td><td class='info'>ℹ️ Info</td></tr>";
    
    echo "</table>";
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>📋 Lista Părților (Primele 20)</h2>";
    
    echo "<table>";
    echo "<tr><th>#</th><th>Nume</th><th>Calitate</th><th>Sursă</th><th>Tip</th></tr>";
    
    $displayLimit = min(20, $totalParties);
    for ($i = 0; $i < $displayLimit; $i++) {
        $party = $dosar->parti[$i];
        $source = $party->source ?? 'unknown';
        $rowClass = '';
        $tip = '';
        
        // Determinăm tipul părții
        if ($source === 'soap_api') {
            $rowClass = 'style="background-color: #e8f5e8;"';
            $tip = 'Parte reală';
        } elseif ($source === 'decision_text') {
            $rowClass = 'style="background-color: #ffebee;"';
            $tip = 'Extras din text';
        } else {
            $rowClass = 'style="background-color: #fff3e0;"';
            $tip = 'Necunoscut';
        }
        
        echo "<tr {$rowClass}>";
        echo "<td>" . ($i + 1) . "</td>";
        echo "<td>" . htmlspecialchars($party->nume ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($party->calitate ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($source) . "</td>";
        echo "<td>{$tip}</td>";
        echo "</tr>";
    }
    
    if ($totalParties > 20) {
        echo "<tr><td colspan='5' class='info'>... și încă " . ($totalParties - 20) . " părți</td></tr>";
    }
    
    echo "</table>";
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🔍 Verificare SARAGEA TUDORIŢA</h2>";
    
    $saragea_found = false;
    $saragea_position = -1;
    $saragea_source = '';
    
    foreach ($dosar->parti as $index => $party) {
        if (isset($party->nume) && 
            stripos($party->nume, 'SARAGEA') !== false && 
            stripos($party->nume, 'TUDORI') !== false) {
            $saragea_found = true;
            $saragea_position = $index + 1;
            $saragea_source = $party->source ?? 'unknown';
            break;
        }
    }
    
    echo "<table>";
    echo "<tr><th>Test</th><th>Rezultat</th><th>Status</th></tr>";
    echo "<tr><td>SARAGEA TUDORIŢA găsită</td><td>" . ($saragea_found ? "Da" : "Nu") . "</td><td class='" . ($saragea_found ? 'success' : 'error') . "'>" . ($saragea_found ? '✅ Găsită' : '❌ Lipsă') . "</td></tr>";
    
    if ($saragea_found) {
        echo "<tr><td>Poziția în listă</td><td>{$saragea_position}</td><td class='info'>ℹ️ Info</td></tr>";
        echo "<tr><td>Sursă date</td><td>{$saragea_source}</td><td class='info'>ℹ️ Info</td></tr>";
        echo "<tr><td>Este parte reală</td><td>" . ($saragea_source === 'soap_api' ? 'Da' : 'Nu') . "</td><td class='" . ($saragea_source === 'soap_api' ? 'success' : 'warning') . "'>" . ($saragea_source === 'soap_api' ? '✅ Parte reală' : '⚠️ Din text') . "</td></tr>";
    }
    
    echo "</table>";
    
    echo "</div>";
    
    echo "<div class='section'>";
    echo "<h2>🎯 Evaluare Finală</h2>";
    
    $only_real_parties = ($decisionCount === 0);
    $reasonable_count = ($totalParties >= 10 && $totalParties <= 150); // Număr rezonabil de părți
    $saragea_real = ($saragea_found && $saragea_source === 'soap_api');
    
    echo "<table>";
    echo "<tr><th>Criteriu</th><th>Status</th></tr>";
    echo "<tr><td>Doar părți reale (fără text extras)</td><td class='" . ($only_real_parties ? 'success' : 'error') . "'>" . ($only_real_parties ? '✅ Corect' : '❌ Încă extrage din text') . "</td></tr>";
    echo "<tr><td>Număr rezonabil de părți</td><td class='" . ($reasonable_count ? 'success' : 'warning') . "'>" . ($reasonable_count ? '✅ Rezonabil' : '⚠️ Verifică') . "</td></tr>";
    echo "<tr><td>SARAGEA este parte reală</td><td class='" . ($saragea_real ? 'success' : 'warning') . "'>" . ($saragea_real ? '✅ Parte reală' : '⚠️ Verifică') . "</td></tr>";
    echo "</table>";
    
    if ($only_real_parties && $reasonable_count) {
        echo "<p class='success'>🎉 PERFECT! Sistemul afișează doar părți reale din SOAP API!</p>";
        echo "<p class='success'>✅ Nu mai extrage cuvinte din textul deciziei</p>";
        echo "<p class='success'>✅ Numărul de părți este rezonabil ({$totalParties})</p>";
    } elseif ($only_real_parties) {
        echo "<p class='warning'>⚠️ PARȚIAL: Nu mai extrage din text, dar verifică numărul de părți</p>";
    } else {
        echo "<p class='error'>❌ PROBLEMĂ: Încă extrage părți din textul deciziei</p>";
    }
    
    echo "<h3>🔗 Test Links</h3>";
    echo "<ul>";
    echo "<li><a href='detalii_dosar.php?numar=130/98/2022&institutie=TribunalulIALOMITA' target='_blank'>Pagina principală (ar trebui să afișeze doar părți reale)</a></li>";
    echo "<li><a href='detalii_dosar.php?numar=130/98/2022&institutie=TribunalulIALOMITA&debug=1' target='_blank'>Pagina cu debug</a></li>";
    echo "</ul>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<h2 class='error'>❌ Error Occurred</h2>";
    echo "<p class='error'>Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p class='info'>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><em>Test completat la " . date('Y-m-d H:i:s') . "</em></p>";
echo "</body></html>";
?>
