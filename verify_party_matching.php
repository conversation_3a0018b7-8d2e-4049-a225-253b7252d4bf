<?php
/**
 * Comprehensive verification script for enhanced party matching functionality
 */

// Include necessary files
require_once 'bulk_search_functions.php';

echo "<h1>Enhanced Party Matching Verification</h1>";
echo "<style>
    .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .pass { background-color: #d4edda; border-color: #c3e6cb; }
    .fail { background-color: #f8d7da; border-color: #f5c6cb; }
    .exact-match { background: linear-gradient(135deg, rgba(0, 123, 255, 0.15) 0%, rgba(40, 167, 69, 0.1) 100%); border-left: 4px solid #28a745; padding: 3px 6px; border-radius: 4px; }
    .partial-match { background-color: rgba(0, 123, 255, 0.1); border-left: 3px solid #007bff; padding: 3px 6px; border-radius: 4px; }
    .text-primary { color: #007bff !important; font-weight: 600; }
</style>";

// Test data simulating real party structures from DosarService.php
$testCases = [
    [
        'name' => 'MAIER RADU Test Case',
        'parties' => [
            ['nume' => 'IONESCU MARIA', 'calitate' => 'Reclamant'],
            ['nume' => 'MAIER RADU MIHAI', 'calitate' => 'Pârât'],
            ['nume' => 'SC EXEMPLU SRL', 'calitate' => 'Terț']
        ],
        'searchTerm' => 'MAIER RADU',
        'expectedMatch' => 'MAIER RADU MIHAI',
        'expectedType' => 'partial'
    ],
    [
        'name' => 'Exact Match Test',
        'parties' => [
            ['nume' => 'POPESCU ION', 'calitate' => 'Reclamant'],
            ['nume' => 'IONESCU MARIA', 'calitate' => 'Pârât']
        ],
        'searchTerm' => 'POPESCU ION',
        'expectedMatch' => 'POPESCU ION',
        'expectedType' => 'exact'
    ],
    [
        'name' => 'Romanian Diacritics Test',
        'parties' => [
            ['nume' => 'IONESCU MARIA', 'calitate' => 'Reclamant'],
            ['nume' => 'ȘTEFĂNESCU ANA', 'calitate' => 'Pârât'],
            ['nume' => 'ȚĂRANU GHEORGHE', 'calitate' => 'Martor']
        ],
        'searchTerm' => 'Stefanescu',
        'expectedMatch' => 'ȘTEFĂNESCU ANA',
        'expectedType' => 'partial'
    ],
    [
        'name' => 'Partial Match Test',
        'parties' => [
            ['nume' => 'POPESCU ION ALEXANDRU', 'calitate' => 'Reclamant'],
            ['nume' => 'IONESCU MARIA', 'calitate' => 'Pârât']
        ],
        'searchTerm' => 'POPESCU',
        'expectedMatch' => 'POPESCU ION ALEXANDRU',
        'expectedType' => 'partial'
    ],
    [
        'name' => 'No Match Test',
        'parties' => [
            ['nume' => 'IONESCU MARIA', 'calitate' => 'Reclamant'],
            ['nume' => 'POPESCU ION', 'calitate' => 'Pârât']
        ],
        'searchTerm' => 'GEORGESCU',
        'expectedMatch' => null,
        'expectedType' => 'none'
    ]
];

$totalTests = 0;
$passedTests = 0;

foreach ($testCases as $testCase) {
    $totalTests++;
    
    echo "<div class='test-case'>";
    echo "<h3>{$testCase['name']}</h3>";
    
    // Display test setup
    echo "<p><strong>Search Term:</strong> \"{$testCase['searchTerm']}\"</p>";
    echo "<p><strong>Expected Match:</strong> " . ($testCase['expectedMatch'] ?? 'None') . "</p>";
    echo "<p><strong>Expected Type:</strong> {$testCase['expectedType']}</p>";
    
    echo "<h4>Parties in Case:</h4>";
    echo "<ul>";
    foreach ($testCase['parties'] as $party) {
        echo "<li>{$party['nume']} - {$party['calitate']}</li>";
    }
    echo "</ul>";
    
    // Test the matching function
    $matchingParty = findMatchingParty($testCase['parties'], $testCase['searchTerm']);
    
    $actualMatch = null;
    $actualQuality = null;
    if ($matchingParty) {
        $actualMatch = $matchingParty['nume'] ?? '';
        $actualQuality = $matchingParty['calitate'] ?? '';
    }
    
    // Test match type
    $actualType = 'none';
    if ($actualMatch) {
        $actualType = getMatchType($actualMatch, $testCase['searchTerm']);
    }
    
    // Test highlighting
    $highlighted = '';
    if ($actualMatch) {
        $highlighted = highlightMatchingPartyName($actualMatch, $testCase['searchTerm'], 'numeParte');
    }
    
    // Verify results
    $matchCorrect = ($actualMatch === $testCase['expectedMatch']);
    $typeCorrect = ($actualType === $testCase['expectedType']);
    $testPassed = $matchCorrect && $typeCorrect;
    
    if ($testPassed) {
        $passedTests++;
    }
    
    echo "<h4>Results:</h4>";
    echo "<p><strong>Actual Match:</strong> " . ($actualMatch ?? 'None') . " " . ($matchCorrect ? "✅" : "❌") . "</p>";
    echo "<p><strong>Actual Type:</strong> {$actualType} " . ($typeCorrect ? "✅" : "❌") . "</p>";
    echo "<p><strong>Quality:</strong> " . ($actualQuality ?? 'None') . "</p>";
    
    if ($highlighted) {
        echo "<p><strong>Highlighted Result:</strong> {$highlighted}</p>";
    }
    
    echo "<p><strong>Test Status:</strong> " . ($testPassed ? "<span style='color: green; font-weight: bold;'>PASS ✅</span>" : "<span style='color: red; font-weight: bold;'>FAIL ❌</span>") . "</p>";
    
    echo "</div>";
}

// Summary
echo "<div style='margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 5px;'>";
echo "<h2>Test Summary</h2>";
echo "<p><strong>Total Tests:</strong> {$totalTests}</p>";
echo "<p><strong>Passed:</strong> {$passedTests}</p>";
echo "<p><strong>Failed:</strong> " . ($totalTests - $passedTests) . "</p>";
echo "<p><strong>Success Rate:</strong> " . round(($passedTests / $totalTests) * 100, 1) . "%</p>";

if ($passedTests === $totalTests) {
    echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 All tests passed! Enhanced party matching is working correctly.</p>";
} else {
    echo "<p style='color: red; font-weight: bold; font-size: 18px;'>⚠️ Some tests failed. Please review the implementation.</p>";
}
echo "</div>";

// Test the specific MAIER RADU case with real search
echo "<div style='margin-top: 30px; padding: 20px; background-color: #e7f3ff; border-radius: 5px;'>";
echo "<h2>Real Search Test: MAIER RADU</h2>";
echo "<p>To test with real data from the judicial portal:</p>";
echo "<ol>";
echo "<li><a href='bulk_search.php' target='_blank'>Open Bulk Search</a></li>";
echo "<li>Enter 'MAIER RADU' in the search terms</li>";
echo "<li>Select 'Nume parte' as search type</li>";
echo "<li>Execute the search</li>";
echo "<li>Look for case '12748/211/2019' in the results</li>";
echo "<li>Verify that 'MAIER RADU MIHAI' appears in the 'Nume Parte' column with blue highlighting</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><a href='bulk_search.php'>← Back to Bulk Search</a> | <a href='test_maier_radu.php'>Test MAIER RADU Case</a></p>";
?>
