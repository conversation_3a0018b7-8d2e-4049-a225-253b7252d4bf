<?php
require_once 'config/config.php';
require_once 'services/DosarService.php';

// Include necessary functions from index.php
function validateAndMapInstitutionCode($institutionCode) {
    if (empty($institutionCode)) {
        return null;
    }
    return $institutionCode;
}

function validateRomanianDate($dateString) {
    if (empty($dateString)) {
        return ['valid' => false, 'date' => null];
    }
    
    if (preg_match('/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/', $dateString, $matches)) {
        $day = (int)$matches[1];
        $month = (int)$matches[2];
        $year = (int)$matches[3];
        
        if (checkdate($month, $day, $year)) {
            return [
                'valid' => true,
                'date' => sprintf('%04d-%02d-%02d', $year, $month, $day)
            ];
        }
    }
    
    return ['valid' => false, 'date' => null];
}

function parseBulkSearchTerms($bulkSearchTerms) {
    $terms = [];
    $lines = explode("\n", $bulkSearchTerms);
    
    foreach ($lines as $line) {
        $term = trim($line);
        if (!empty($term)) {
            if (preg_match('/^\d+\/\d+\/\d+$/', $term)) {
                $type = 'numarDosar';
            } elseif (preg_match('/^[A-ZĂÂÎȘȚ][A-Za-zĂÂÎȘȚăâîșțţşŞţŢ\s\-\.]+$/', $term)) {
                $type = 'numeParte';
            } else {
                $type = 'obiectDosar';
            }
            
            $terms[] = [
                'term' => $term,
                'type' => $type
            ];
        }
    }
    
    return $terms;
}

function performBulkSearchWithFilters($searchTermsData, $advancedFilters = []) {
    $dosarService = new DosarService();
    $results = [];

    $mappedInstitutionCode = validateAndMapInstitutionCode($advancedFilters['institutie'] ?? null);

    foreach ($searchTermsData as $termData) {
        $term = $termData['term'];
        $searchType = $termData['type'];

        try {
            $dataStart = '';
            $dataStop = '';

            if (!empty($advancedFilters['dataInceput'])) {
                $startValidation = validateRomanianDate($advancedFilters['dataInceput']);
                if ($startValidation['valid']) {
                    $dataStart = $startValidation['date'] . 'T00:00:00';
                }
            }

            if (!empty($advancedFilters['dataSfarsit'])) {
                $endValidation = validateRomanianDate($advancedFilters['dataSfarsit']);
                if ($endValidation['valid']) {
                    $dataStop = $endValidation['date'] . 'T23:59:59';
                }
            }

            $searchParams = [
                'numarDosar' => '',
                'institutie' => $mappedInstitutionCode,
                'numeParte' => '',
                'obiectDosar' => '',
                'dataStart' => $dataStart,
                'dataStop' => $dataStop,
                'dataUltimaModificareStart' => '',
                'dataUltimaModificareStop' => '',
                'categorieInstanta' => $advancedFilters['categorieInstanta'] ?? '',
                'categorieCaz' => $advancedFilters['categorieCaz'] ?? ''
            ];

            switch ($searchType) {
                case 'numarDosar':
                    $searchParams['numarDosar'] = $term;
                    break;
                case 'obiectDosar':
                    $searchParams['obiectDosar'] = $term;
                    break;
                case 'filter_only':
                    break;
                case 'numeParte':
                default:
                    $searchParams['numeParte'] = $term;
                    break;
            }

            $termResults = $dosarService->cautareAvansata($searchParams);

            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => $termResults ?: [],
                'count' => count($termResults ?: []),
                'error' => null,
                'filters' => $advancedFilters,
                'search_params' => $searchParams
            ];

        } catch (Exception $e) {
            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => [],
                'count' => 0,
                'error' => $e->getMessage(),
                'filters' => $advancedFilters,
                'search_params' => $searchParams ?? []
            ];
        }
    }

    return $results;
}

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Afișare Rezultate - Portal Judiciar România</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .debug-section {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: #f8f9fa;
        }
        .result-item {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .party-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            padding: 10px;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 4px; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">
            <i class="fas fa-bug me-2 text-primary"></i>
            Debug Afișare Rezultate
        </h1>
        
        <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
            <?php
            $bulkSearchTerms = $_POST['bulkSearchTerms'] ?? '';
            $advancedFilters = [];
            
            if (!empty($bulkSearchTerms)) {
                $searchTermsData = parseBulkSearchTerms($bulkSearchTerms);
                $searchResults = performBulkSearchWithFilters($searchTermsData, $advancedFilters);
                
                echo "<div class='debug-section'>";
                echo "<h3><i class='fas fa-info-circle me-2'></i>Rezultate Debug</h3>";
                
                foreach ($searchResults as $index => $result) {
                    echo "<div class='result-item'>";
                    echo "<h5>Termen: " . htmlspecialchars($result['term']) . " (Tip: " . $result['type'] . ")</h5>";
                    
                    if (!empty($result['error'])) {
                        echo "<div class='error'><i class='fas fa-exclamation-triangle me-2'></i>Eroare: " . htmlspecialchars($result['error']) . "</div>";
                    } else {
                        echo "<div class='success'><i class='fas fa-check-circle me-2'></i>Rezultate găsite: " . $result['count'] . "</div>";
                        
                        if ($result['count'] > 0) {
                            echo "<h6>Analiza primelor 3 rezultate:</h6>";
                            
                            $displayResults = array_slice($result['results'], 0, 3);
                            foreach ($displayResults as $dosarIndex => $dosar) {
                                echo "<div class='info'>";
                                echo "<strong>Dosar " . ($dosarIndex + 1) . ":</strong> " . htmlspecialchars($dosar->numar ?? 'N/A') . "<br>";
                                echo "<strong>Instanță:</strong> " . htmlspecialchars($dosar->institutie ?? 'N/A') . "<br>";
                                echo "<strong>Obiect:</strong> " . htmlspecialchars(substr($dosar->obiect ?? 'N/A', 0, 100)) . "<br>";
                                
                                $parti = $dosar->parti ?? [];
                                echo "<strong>Părți:</strong> " . count($parti) . " părți<br>";
                                
                                if (count($parti) > 0) {
                                    echo "<strong>Structura părților:</strong><br>";
                                    echo "<div class='party-list'>";
                                    
                                    foreach (array_slice($parti, 0, 5) as $partyIndex => $party) {
                                        echo "Partea " . ($partyIndex + 1) . ":\n";
                                        echo "  Tip: " . gettype($party) . "\n";
                                        
                                        if (is_object($party)) {
                                            echo "  Nume: " . ($party->nume ?? 'N/A') . "\n";
                                            echo "  Calitate: " . ($party->calitate ?? 'N/A') . "\n";
                                            echo "  Source: " . ($party->source ?? 'N/A') . "\n";
                                        } elseif (is_array($party)) {
                                            echo "  Nume: " . ($party['nume'] ?? 'N/A') . "\n";
                                            echo "  Calitate: " . ($party['calitate'] ?? 'N/A') . "\n";
                                            echo "  Source: " . ($party['source'] ?? 'N/A') . "\n";
                                        } else {
                                            echo "  Valoare: " . var_export($party, true) . "\n";
                                        }
                                        echo "\n";
                                    }
                                    
                                    if (count($parti) > 5) {
                                        echo "... și încă " . (count($parti) - 5) . " părți\n";
                                    }
                                    
                                    echo "</div>";
                                }
                                
                                // Test party processing functions
                                if ($result['type'] === 'numeParte' && count($parti) > 0) {
                                    echo "<strong>Test funcții procesare părți:</strong><br>";
                                    
                                    // Test findMatchingParty equivalent
                                    $matchingParty = null;
                                    $searchTerm = $result['term'];
                                    
                                    foreach ($parti as $party) {
                                        $partyName = is_object($party) ? ($party->nume ?? '') : ($party['nume'] ?? '');
                                        if (stripos($partyName, $searchTerm) !== false) {
                                            $matchingParty = $party;
                                            break;
                                        }
                                    }
                                    
                                    if ($matchingParty) {
                                        $matchedName = is_object($matchingParty) ? ($matchingParty->nume ?? '') : ($matchingParty['nume'] ?? '');
                                        $matchedQuality = is_object($matchingParty) ? ($matchingParty->calitate ?? '') : ($matchingParty['calitate'] ?? '');
                                        echo "<div class='success'>Partea potrivită: " . htmlspecialchars($matchedName) . " (" . htmlspecialchars($matchedQuality) . ")</div>";
                                    } else {
                                        echo "<div class='warning'>Nu s-a găsit partea potrivită pentru termenul '" . htmlspecialchars($searchTerm) . "'</div>";
                                    }
                                }
                                
                                echo "</div>";
                            }
                        }
                    }
                    
                    echo "</div>";
                }
                
                echo "</div>";
            }
            ?>
        <?php endif; ?>
        
        <div class="debug-section">
            <h3><i class='fas fa-play me-2'></i>Test Debug</h3>
            <form method="POST">
                <div class="mb-3">
                    <label for="bulkSearchTerms" class="form-label">Termeni de căutare pentru debug:</label>
                    <textarea class="form-control" id="bulkSearchTerms" name="bulkSearchTerms" rows="5" placeholder="POPESCU&#10;IONESCU"><?php echo htmlspecialchars($_POST['bulkSearchTerms'] ?? 'POPESCU
IONESCU'); ?></textarea>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-bug me-2"></i>
                    Rulează Debug
                </button>
            </form>
        </div>
        
        <div class="text-center mt-4">
            <a href="index.php" class="btn btn-success btn-lg">
                <i class="fas fa-home me-2"></i>
                Înapoi la Portal
            </a>
        </div>
    </div>
</body>
</html>
