<?php
/**
 * Test page for PDF and Official Portal button functionality
 * This page tests both buttons with real case data and provides debugging information
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

// Set content type to HTML with UTF-8 encoding
header('Content-Type: text/html; charset=UTF-8');

$testResults = [];
$error = null;

// Test button functionality
if ($_SERVER['REQUEST_METHOD'] === 'POST' || isset($_GET['test'])) {
    $testCaseNumber = $_POST['testCaseNumber'] ?? $_GET['testCaseNumber'] ?? '';
    $testInstitution = $_POST['testInstitution'] ?? $_GET['testInstitution'] ?? '';
    
    if (!empty($testCaseNumber) && !empty($testInstitution)) {
        try {
            // Test case details retrieval
            $dosarService = new DosarService();
            $dosarDetails = $dosarService->getDetaliiDosar($testCaseNumber, $testInstitution);
            
            if ($dosarDetails) {
                $testResults['case_found'] = true;
                $testResults['case_data'] = $dosarDetails;
                $testResults['case_url'] = "detalii_dosar.php?numar=" . urlencode($testCaseNumber) . "&institutie=" . urlencode($testInstitution);
                
                // Test PDF functionality
                $testResults['pdf_tests'] = [
                    'browser_print_available' => true,
                    'server_pdf_url' => "generate_pdf.php?numar=" . urlencode($testCaseNumber) . "&institutie=" . urlencode($testInstitution) . "&disposition=inline",
                    'download_pdf_url' => "generate_pdf.php?numar=" . urlencode($testCaseNumber) . "&institutie=" . urlencode($testInstitution) . "&disposition=attachment"
                ];
                
                // Test Official Portal functionality
                $testResults['portal_tests'] = [
                    'portal_url' => "https://portal.just.ro/?dosar=" . urlencode($testCaseNumber) . "&institutie=" . urlencode($testInstitution),
                    'case_number' => $testCaseNumber,
                    'institution' => $testInstitution
                ];
                
            } else {
                $testResults['case_found'] = false;
                $error = "Nu s-au găsit detalii pentru dosarul specificat.";
            }
        } catch (Exception $e) {
            $error = 'Eroare la testarea funcționalității: ' . $e->getMessage();
        }
    } else {
        $error = "Vă rugăm să specificați numărul dosarului și instituția.";
    }
}

?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Funcționalitate Butoane - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Roboto', sans-serif;
        }
        .test-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .test-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        .test-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px 8px 0 0;
        }
        .test-body {
            padding: 1.5rem;
        }
        .test-form {
            background: #e9ecef;
            border-radius: 6px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .warning-message {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .test-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            text-decoration: none;
            margin: 0.5rem 0.5rem 0.5rem 0;
            transition: all 0.3s ease;
        }
        .test-link:hover {
            background: #0056b3;
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
        }
        .test-link.pdf {
            background: #dc3545;
        }
        .test-link.pdf:hover {
            background: #c82333;
        }
        .test-link.portal {
            background: #ffc107;
            color: #212529;
        }
        .test-link.portal:hover {
            background: #e0a800;
            color: #212529;
        }
        .debug-info {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 1rem;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
            overflow-x: auto;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        .checklist li:last-child {
            border-bottom: none;
        }
        .checklist li::before {
            content: "☐ ";
            color: #007bff;
            font-weight: bold;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <div class="test-header">
                <h1 class="h3 mb-0">
                    <i class="fas fa-bug mr-2"></i>
                    Test Funcționalitate Butoane PDF și Portal Oficial
                </h1>
                <p class="mb-0 mt-2 opacity-75">
                    Testare și debugging pentru butoanele "Salvează PDF" și "Portal Oficial" din pagina de detalii dosar
                </p>
            </div>
            <div class="test-body">
                <div class="test-form">
                    <h4><i class="fas fa-cog mr-2"></i>Parametri de Test</h4>
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="testCaseNumber" class="form-label">Numărul dosarului:</label>
                                <input type="text" class="form-control" id="testCaseNumber" name="testCaseNumber" 
                                       value="<?php echo htmlspecialchars($_POST['testCaseNumber'] ?? ''); ?>"
                                       placeholder="ex: 123/2024" required>
                                <small class="form-text text-muted">Introduceți un număr de dosar valid pentru test</small>
                            </div>
                            <div class="col-md-6">
                                <label for="testInstitution" class="form-label">Instituția:</label>
                                <select class="form-control" id="testInstitution" name="testInstitution" required>
                                    <option value="">-- Selectați instituția --</option>
                                    <?php 
                                    $institutii = getInstanteList();
                                    foreach ($institutii as $cod => $nume): 
                                    ?>
                                        <option value="<?php echo htmlspecialchars($cod); ?>"
                                                <?php echo (isset($_POST['testInstitution']) && $_POST['testInstitution'] === $cod) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($nume); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-play mr-2"></i>Testează Funcționalitatea Butoanelor
                                </button>
                                <a href="?test=1&testCaseNumber=123/2024&testInstitution=TribunalulBUCURESTI" class="btn btn-secondary ml-2">
                                    <i class="fas fa-vial mr-2"></i>Test Rapid (Exemplu)
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <?php if ($error): ?>
                <div class="error-message">
                    <strong>Eroare:</strong> <?php echo htmlspecialchars($error); ?>
                </div>
                <?php endif; ?>

                <?php if (!empty($testResults) && $testResults['case_found']): ?>
                
                <div class="success-message">
                    <strong>✓ Dosarul a fost găsit!</strong> Testarea funcționalității butoanelor poate continua.
                </div>

                <h4><i class="fas fa-clipboard-check mr-2"></i>Rezultate Test Butoane</h4>

                <div class="test-result">
                    <h5>1. Test Buton "Salvează PDF"</h5>
                    <p>Testați funcționalitatea PDF folosind link-urile de mai jos:</p>
                    
                    <div class="mb-3">
                        <a href="<?php echo $testResults['case_url']; ?>" target="_blank" class="test-link">
                            <i class="fas fa-external-link-alt mr-2"></i>Deschide Pagina Detalii Dosar
                        </a>
                        <a href="<?php echo $testResults['pdf_tests']['server_pdf_url']; ?>" target="_blank" class="test-link pdf">
                            <i class="fas fa-eye mr-2"></i>Test PDF Server-side (Inline)
                        </a>
                        <a href="<?php echo $testResults['pdf_tests']['download_pdf_url']; ?>" class="test-link pdf">
                            <i class="fas fa-download mr-2"></i>Test PDF Server-side (Download)
                        </a>
                    </div>

                    <div class="debug-info">
                        <strong>Debug Info - PDF URLs:</strong><br>
                        Case Details: <?php echo $testResults['case_url']; ?><br>
                        PDF Inline: <?php echo $testResults['pdf_tests']['server_pdf_url']; ?><br>
                        PDF Download: <?php echo $testResults['pdf_tests']['download_pdf_url']; ?>
                    </div>

                    <p><strong>Checklist pentru testarea PDF:</strong></p>
                    <ul class="checklist">
                        <li>Deschideți pagina de detalii dosar</li>
                        <li>Faceți click pe butonul "Salvează PDF" din header</li>
                        <li>Verificați că se deschide dialogul de printare (browser print)</li>
                        <li>Testați "Save as PDF" din dialogul de printare</li>
                        <li>Verificați că PDF-ul conține toate informațiile dosarului</li>
                        <li>Confirmați că caracterele românești sunt corecte</li>
                        <li>Testați link-urile directe PDF server-side de mai sus</li>
                    </ul>
                </div>

                <div class="test-result">
                    <h5>2. Test Buton "Portal Oficial"</h5>
                    <p>Testați funcționalitatea Portal Oficial:</p>
                    
                    <div class="mb-3">
                        <a href="<?php echo $testResults['case_url']; ?>" target="_blank" class="test-link">
                            <i class="fas fa-external-link-alt mr-2"></i>Deschide Pagina Detalii Dosar
                        </a>
                        <a href="<?php echo $testResults['portal_tests']['portal_url']; ?>" target="_blank" class="test-link portal">
                            <i class="fas fa-external-link-alt mr-2"></i>Test Direct Portal Oficial
                        </a>
                    </div>

                    <div class="debug-info">
                        <strong>Debug Info - Portal Oficial:</strong><br>
                        Case Number: <?php echo htmlspecialchars($testResults['portal_tests']['case_number']); ?><br>
                        Institution: <?php echo htmlspecialchars($testResults['portal_tests']['institution']); ?><br>
                        Portal URL: <?php echo $testResults['portal_tests']['portal_url']; ?>
                    </div>

                    <p><strong>Checklist pentru testarea Portal Oficial:</strong></p>
                    <ul class="checklist">
                        <li>Deschideți pagina de detalii dosar</li>
                        <li>Verificați că butonul galben "Portal Oficial" este vizibil</li>
                        <li>Faceți click pe butonul "Portal Oficial"</li>
                        <li>Verificați că se afișează notificarea informativă</li>
                        <li>Confirmați că se deschide într-un tab nou</li>
                        <li>Verificați că URL-ul conține parametrii corecți</li>
                        <li>Testați link-ul direct de mai sus pentru comparație</li>
                    </ul>
                </div>

                <div class="test-result">
                    <h5>3. Debugging JavaScript</h5>
                    <p><strong>Pentru a identifica problemele JavaScript:</strong></p>
                    <ol>
                        <li>Deschideți Developer Tools (F12)</li>
                        <li>Mergi la tab-ul "Console"</li>
                        <li>Deschideți pagina de detalii dosar</li>
                        <li>Faceți click pe fiecare buton</li>
                        <li>Verificați erorile în consolă</li>
                    </ol>

                    <div class="debug-info">
                        <strong>Funcții JavaScript de verificat:</strong><br>
                        - initExportFunctions() - trebuie să se execute la încărcarea paginii<br>
                        - printDosar() - trebuie să se execute la click pe "Salvează PDF"<br>
                        - openOfficialPortal() - trebuie să se execute la click pe "Portal Oficial"<br>
                        - showNotification() - trebuie să afișeze notificări<br>
                        <br>
                        <strong>Elemente HTML de verificat:</strong><br>
                        - #printBtnHeader - butonul PDF<br>
                        - #officialPortalBtn - butonul Portal Oficial<br>
                        - #printContent - containerul pentru conținutul PDF<br>
                        - #printVersion - versiunea pentru printare
                    </div>
                </div>

                <div class="test-result">
                    <h5>4. Informații despre Dosar</h5>
                    <p><strong>Datele dosarului testat:</strong></p>
                    <ul>
                        <li><strong>Număr:</strong> <?php echo htmlspecialchars($testResults['case_data']->numar); ?></li>
                        <li><strong>Obiect:</strong> <?php echo htmlspecialchars($testResults['case_data']->obiect ?? 'N/A'); ?></li>
                        <li><strong>Data:</strong> <?php echo htmlspecialchars($testResults['case_data']->data ?? 'N/A'); ?></li>
                        <li><strong>Stadiu:</strong> <?php echo htmlspecialchars($testResults['case_data']->stadiuProcesualNume ?? 'N/A'); ?></li>
                        <li><strong>Părți:</strong> <?php echo !empty($testResults['case_data']->parti) ? count($testResults['case_data']->parti) : '0'; ?> părți</li>
                        <li><strong>Ședințe:</strong> <?php echo !empty($testResults['case_data']->sedinte) ? count($testResults['case_data']->sedinte) : '0'; ?> ședințe</li>
                    </ul>
                </div>

                <?php else: ?>
                <div class="warning-message">
                    <strong>Informație:</strong> Introduceți un număr de dosar și instituție valide pentru a începe testarea.
                </div>
                <?php endif; ?>

                <div class="mt-4">
                    <h4><i class="fas fa-tools mr-2"></i>Soluții Comune pentru Probleme</h4>
                    
                    <div class="test-result">
                        <h6>Probleme PDF:</h6>
                        <ul>
                            <li><strong>Butonul nu răspunde:</strong> Verificați că initExportFunctions() se execută</li>
                            <li><strong>Eroare "printContent nu a fost găsit":</strong> Verificați că elementele HTML există</li>
                            <li><strong>PDF gol:</strong> Verificați că getDosarInfo() returnează date valide</li>
                            <li><strong>Caractere românești greșite:</strong> Verificați encoding-ul UTF-8</li>
                        </ul>
                        
                        <h6>Probleme Portal Oficial:</h6>
                        <ul>
                            <li><strong>Butonul nu apare:</strong> Verificați că numărul dosarului nu este gol</li>
                            <li><strong>URL incorect:</strong> Verificați parametrii data-case-number și data-institution</li>
                            <li><strong>Nu se deschide în tab nou:</strong> Verificați window.open() și popup blockers</li>
                            <li><strong>Notificarea nu apare:</strong> Verificați funcția showNotification()</li>
                        </ul>
                    </div>
                </div>

                <div class="test-result">
                    <h5>5. Test Rapid Funcționalitate</h5>
                    <p><strong>Testați rapid ambele butoane:</strong></p>

                    <div class="mb-3">
                        <button onclick="testPdfButton()" class="btn btn-danger">
                            <i class="fas fa-file-pdf mr-2"></i>Test Rapid PDF
                        </button>
                        <button onclick="testPortalButton()" class="btn btn-warning ml-2">
                            <i class="fas fa-external-link-alt mr-2"></i>Test Rapid Portal
                        </button>
                        <button onclick="runFullDiagnostic()" class="btn btn-info ml-2">
                            <i class="fas fa-stethoscope mr-2"></i>Diagnostic Complet
                        </button>
                    </div>

                    <div id="testResults" class="debug-info" style="display: none;">
                        <strong>Rezultate Test:</strong><br>
                        <div id="testOutput"></div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-3">
                        <a href="detalii_dosar.php" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-folder-open mr-2"></i>Detalii Dosar
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="test_pdf_functionality.php" class="btn btn-outline-danger btn-block">
                            <i class="fas fa-file-pdf mr-2"></i>Test PDF Avansat
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="generate_pdf.php" class="btn btn-outline-success btn-block">
                            <i class="fas fa-cogs mr-2"></i>Test PDF Direct
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="test_design_improvements.php" class="btn btn-outline-secondary btn-block">
                            <i class="fas fa-palette mr-2"></i>Test Design
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test JavaScript functionality
        console.log('Test page loaded - checking JavaScript functions...');

        // Test if notification function exists
        if (typeof showNotification === 'function') {
            console.log('✓ showNotification function is available');
        } else {
            console.error('✗ showNotification function is NOT available');
        }

        // Test button click handlers
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded - testing button functionality...');

            // Simulate button tests
            const testButtons = document.querySelectorAll('.test-link');
            testButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    console.log('Test button clicked:', this.textContent.trim());
                });
            });
        });

        // Rapid test functions
        function testPdfButton() {
            const output = document.getElementById('testOutput');
            const results = document.getElementById('testResults');

            let testResult = 'Test Buton PDF:<br>';

            // Test if we can access the case details page
            const caseNumber = '<?php echo htmlspecialchars($_POST['testCaseNumber'] ?? '123/2024'); ?>';
            const institution = '<?php echo htmlspecialchars($_POST['testInstitution'] ?? 'TribunalulBUCURESTI'); ?>';

            if (caseNumber && institution) {
                const caseUrl = `detalii_dosar.php?numar=${encodeURIComponent(caseNumber)}&institutie=${encodeURIComponent(institution)}`;
                testResult += `✓ URL dosar: ${caseUrl}<br>`;

                // Test PDF URLs
                const pdfInlineUrl = `generate_pdf.php?numar=${encodeURIComponent(caseNumber)}&institutie=${encodeURIComponent(institution)}&disposition=inline`;
                const pdfDownloadUrl = `generate_pdf.php?numar=${encodeURIComponent(caseNumber)}&institutie=${encodeURIComponent(institution)}&disposition=attachment`;

                testResult += `✓ PDF Inline: ${pdfInlineUrl}<br>`;
                testResult += `✓ PDF Download: ${pdfDownloadUrl}<br>`;

                // Open case details page for manual testing
                window.open(caseUrl, '_blank');
                testResult += '✓ Pagina de detalii deschisă pentru test manual<br>';
            } else {
                testResult += '✗ Lipsesc parametrii pentru test<br>';
            }

            output.innerHTML = testResult;
            results.style.display = 'block';
        }

        function testPortalButton() {
            const output = document.getElementById('testOutput');
            const results = document.getElementById('testResults');

            let testResult = 'Test Buton Portal Oficial:<br>';

            const caseNumber = '<?php echo htmlspecialchars($_POST['testCaseNumber'] ?? '123/2024'); ?>';
            const institution = '<?php echo htmlspecialchars($_POST['testInstitution'] ?? 'TribunalulBUCURESTI'); ?>';

            if (caseNumber && institution) {
                // Test portal URL construction
                const portalUrl = `https://portal.just.ro/2/Dosar/Dosar?numarDosar=${encodeURIComponent(caseNumber)}&instanta=${encodeURIComponent(institution)}`;
                testResult += `✓ Portal URL: ${portalUrl}<br>`;

                // Test case details page
                const caseUrl = `detalii_dosar.php?numar=${encodeURIComponent(caseNumber)}&institutie=${encodeURIComponent(institution)}`;
                testResult += `✓ URL dosar: ${caseUrl}<br>`;

                // Open both for comparison
                window.open(caseUrl, '_blank');
                setTimeout(() => {
                    window.open(portalUrl, '_blank');
                }, 1000);

                testResult += '✓ Ambele pagini deschise pentru comparație<br>';
            } else {
                testResult += '✗ Lipsesc parametrii pentru test<br>';
            }

            output.innerHTML = testResult;
            results.style.display = 'block';
        }

        function runFullDiagnostic() {
            const output = document.getElementById('testOutput');
            const results = document.getElementById('testResults');

            let diagnostic = 'Diagnostic Complet:<br><br>';

            // Test browser capabilities
            diagnostic += '<strong>Browser:</strong><br>';
            diagnostic += `- User Agent: ${navigator.userAgent}<br>`;
            diagnostic += `- Cookies enabled: ${navigator.cookieEnabled ? '✓' : '✗'}<br>`;
            diagnostic += `- JavaScript enabled: ✓<br>`;
            diagnostic += `- Local Storage: ${typeof(Storage) !== "undefined" ? '✓' : '✗'}<br><br>`;

            // Test jQuery
            diagnostic += '<strong>Libraries:</strong><br>';
            diagnostic += `- jQuery: ${typeof $ !== 'undefined' ? '✓ v' + ($.fn ? $.fn.jquery : 'unknown') : '✗'}<br>`;
            diagnostic += `- Bootstrap: ${typeof bootstrap !== 'undefined' || typeof $.fn.modal !== 'undefined' ? '✓' : '✗'}<br><br>`;

            // Test window functions
            diagnostic += '<strong>Window Functions:</strong><br>';
            diagnostic += `- window.open: ${typeof window.open === 'function' ? '✓' : '✗'}<br>`;
            diagnostic += `- window.print: ${typeof window.print === 'function' ? '✓' : '✗'}<br><br>`;

            // Test form data
            const caseNumber = '<?php echo htmlspecialchars($_POST['testCaseNumber'] ?? ''); ?>';
            const institution = '<?php echo htmlspecialchars($_POST['testInstitution'] ?? ''); ?>';

            diagnostic += '<strong>Test Data:</strong><br>';
            diagnostic += `- Case Number: ${caseNumber || '✗ Not set'}<br>`;
            diagnostic += `- Institution: ${institution || '✗ Not set'}<br><br>`;

            // Test URLs
            if (caseNumber && institution) {
                diagnostic += '<strong>Generated URLs:</strong><br>';
                diagnostic += `- Case Details: detalii_dosar.php?numar=${encodeURIComponent(caseNumber)}&institutie=${encodeURIComponent(institution)}<br>`;
                diagnostic += `- PDF Inline: generate_pdf.php?numar=${encodeURIComponent(caseNumber)}&institutie=${encodeURIComponent(institution)}&disposition=inline<br>`;
                diagnostic += `- PDF Download: generate_pdf.php?numar=${encodeURIComponent(caseNumber)}&institutie=${encodeURIComponent(institution)}&disposition=attachment<br>`;
                diagnostic += `- Portal Official: https://portal.just.ro/2/Dosar/Dosar?numarDosar=${encodeURIComponent(caseNumber)}&instanta=${encodeURIComponent(institution)}<br>`;
            }

            output.innerHTML = diagnostic;
            results.style.display = 'block';
        }
    </script>
</body>
</html>
