<?php
/**
 * Database Creation Script for Portal Judiciar România
 * 
 * This script helps create the database if it doesn't exist.
 * Run this before running the monitoring system setup.
 */

// Load database configuration
if (file_exists('database_config.php')) {
    require_once 'database_config.php';
} else {
    // Define default values
    define('DB_HOST', 'localhost');
    define('DB_NAME', 'portal_judiciar');
    define('DB_USER', 'root');
    define('DB_PASS', '');
    define('DB_CHARSET', 'utf8mb4');
}

echo "🗄️ DATABASE CREATION SCRIPT\n";
echo "===========================\n\n";

echo "Configuration:\n";
echo "  Host: " . DB_HOST . "\n";
echo "  Database: " . DB_NAME . "\n";
echo "  User: " . DB_USER . "\n";
echo "  Password: " . (empty(DB_PASS) ? '(empty)' : '***') . "\n\n";

try {
    // First, connect without specifying database to check if we can connect to MySQL
    echo "1. Testing MySQL connection...\n";
    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "   ✅ MySQL connection successful\n\n";
    
    // Check if database exists
    echo "2. Checking if database exists...\n";
    $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
    $stmt->execute([DB_NAME]);
    $exists = $stmt->fetch();
    
    if ($exists) {
        echo "   ✅ Database '" . DB_NAME . "' already exists\n\n";
    } else {
        echo "   ⚠️ Database '" . DB_NAME . "' does not exist\n";
        echo "   Creating database...\n";
        
        $sql = "CREATE DATABASE " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        $pdo->exec($sql);
        echo "   ✅ Database '" . DB_NAME . "' created successfully\n\n";
    }
    
    // Test connection to the specific database
    echo "3. Testing connection to database...\n";
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "   ✅ Connection to database '" . DB_NAME . "' successful\n\n";
    
    // Show MySQL version and other info
    echo "4. Database information:\n";
    $version = $pdo->query("SELECT VERSION() as version")->fetch();
    echo "   MySQL Version: " . $version['version'] . "\n";
    
    $charset = $pdo->query("SELECT @@character_set_database as charset")->fetch();
    echo "   Database Charset: " . $charset['charset'] . "\n";
    
    $collation = $pdo->query("SELECT @@collation_database as collation")->fetch();
    echo "   Database Collation: " . $collation['collation'] . "\n\n";
    
    echo "🎉 DATABASE SETUP COMPLETED SUCCESSFULLY!\n\n";
    echo "Next steps:\n";
    echo "1. Run: php setup_monitoring_database.php\n";
    echo "2. Run: php test_monitoring_system.php\n";
    echo "3. Set up cron jobs: php cron/setup_cron.php\n";
    
} catch (PDOException $e) {
    echo "❌ Database setup failed!\n";
    echo "Error: " . $e->getMessage() . "\n\n";
    
    echo "🔧 Troubleshooting:\n";
    echo "1. Make sure MySQL/MariaDB is running in WAMP:\n";
    echo "   - Open WAMP control panel\n";
    echo "   - Check that MySQL service is green/running\n";
    echo "   - If not, click on MySQL to start it\n\n";
    
    echo "2. Check database credentials:\n";
    echo "   - Default WAMP user: root\n";
    echo "   - Default WAMP password: (empty)\n";
    echo "   - Edit database_config.php if needed\n\n";
    
    echo "3. Check MySQL port:\n";
    echo "   - Default port: 3306\n";
    echo "   - Check WAMP settings if using different port\n\n";
    
    echo "4. Test MySQL access:\n";
    echo "   - Open phpMyAdmin in WAMP\n";
    echo "   - Try to login with same credentials\n\n";
    
    exit(1);
}
?>
