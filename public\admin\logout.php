<?php

/**
 * Portal Judiciar România - Admin Logout
 * 
 * Secure logout for administrative users
 */

require_once dirname(__DIR__, 2) . '/bootstrap.php';
require_once dirname(__DIR__, 2) . '/includes/config.php';

use App\Config\Database;

// Start session
session_start();

// Log admin logout if user was logged in
if (isset($_SESSION['user_id'])) {
    $userId = $_SESSION['user_id'];
    $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    try {
        Database::insert('data_processing_logs', [
            'user_id' => $userId,
            'action' => 'admin_logout',
            'ip_address' => $clientIp,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        error_log("Failed to log admin logout: " . $e->getMessage());
    }
}

// Clear all session data
$_SESSION = array();

// Delete session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Destroy session
session_destroy();

// Redirect to login page with success message
header('Location: login.php?message=logout_success');
exit;
