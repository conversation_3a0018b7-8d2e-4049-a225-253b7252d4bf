<?php
// Test the actual HTML generation to see if all 3 rows are created
require_once 'bootstrap.php';

use App\Services\DosarService;

// Simulate the exact POST request
$_POST['bulkSearchTerms'] = "14096/3/2024*";

echo "<h1>🔍 Test Actual HTML Generation</h1>";

// Include all necessary functions from index.php
function parseBulkSearchTerms($input) {
    $input = str_replace(',', "\n", $input);
    $terms = explode("\n", $input);
    $cleanTerms = [];

    foreach ($terms as $term) {
        $term = trim($term);
        if (!empty($term) && strlen($term) >= 2) {
            $cleanTerms[] = [
                'term' => $term,
                'type' => detectSearchType($term)
            ];
        }
    }

    $uniqueTerms = [];
    $seenTerms = [];

    foreach ($cleanTerms as $termData) {
        $termKey = strtolower($termData['term']);
        if (!in_array($termKey, $seenTerms)) {
            $uniqueTerms[] = $termData;
            $seenTerms[] = $termKey;
        }
    }

    return $uniqueTerms;
}

function detectSearchType($term) {
    $cleanTerm = trim($term, '"\'');
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\*$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?\/[a-zA-Z0-9]+$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
        return 'numarDosar';
    }
    
    if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
        return 'numarDosar';
    }
    
    return 'numeParte';
}

function performBulkSearchWithFilters($searchTermsData, $filters) {
    $dosarService = new DosarService();
    $results = [];

    foreach ($searchTermsData as $termData) {
        $term = $termData['term'];
        $searchType = $termData['type'];

        try {
            $searchParams = [
                'numarDosar' => ($searchType === 'numarDosar') ? $term : '',
                'institutie' => $filters['institutie'] ?? null,
                'numeParte' => ($searchType === 'numeParte') ? $term : '',
                'obiectDosar' => '',
                'dataStart' => '',
                'dataStop' => '',
                'dataUltimaModificareStart' => '',
                'dataUltimaModificareStop' => '',
                'categorieInstanta' => '',
                'categorieCaz' => ''
            ];

            $termResults = $dosarService->cautareAvansata($searchParams);

            foreach ($termResults as $dosar) {
                $dosar->searchTerm = $term;
                $dosar->searchType = $searchType;
            }

            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => $termResults,
                'count' => count($termResults),
                'error' => null
            ];

        } catch (Exception $e) {
            $results[] = [
                'term' => $term,
                'type' => $searchType,
                'results' => [],
                'count' => 0,
                'error' => $e->getMessage()
            ];
        }
    }

    return $results;
}

function generateResultMessage($count, $term) {
    if ($count === 0) {
        return "Nu au fost găsite rezultate pentru termenul '{$term}'";
    } elseif ($count === 1) {
        return "1 rezultat găsit pentru termenul '{$term}'";
    } else {
        return "{$count} rezultate găsite pentru termenul '{$term}'";
    }
}

function getRelevantPartyName($parti, $searchTerm, $searchType) {
    if ($searchType !== 'numeParte' || empty($parti)) {
        return '';
    }
    
    foreach ($parti as $parte) {
        if (isset($parte->nume) && stripos($parte->nume, $searchTerm) !== false) {
            return $parte->nume;
        }
    }
    
    return isset($parti[0]->nume) ? $parti[0]->nume : '';
}

function getRelevantPartyQuality($parti, $searchTerm, $searchType) {
    if ($searchType !== 'numeParte' || empty($parti)) {
        return '';
    }
    
    foreach ($parti as $parte) {
        if (isset($parte->nume) && stripos($parte->nume, $searchTerm) !== false) {
            return $parte->calitate ?? '';
        }
    }
    
    return isset($parti[0]->calitate) ? $parti[0]->calitate : '';
}

try {
    $bulkSearchTerms = $_POST['bulkSearchTerms'] ?? '';
    
    if (!empty($bulkSearchTerms)) {
        echo "<h2>Step 1: Perform Search</h2>";
        $searchTermsData = parseBulkSearchTerms($bulkSearchTerms);
        $searchResults = performBulkSearchWithFilters($searchTermsData, []);
        
        echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
        echo "<strong>Search completed:</strong><br>";
        foreach ($searchResults as $result) {
            echo "- Term: '{$result['term']}' → {$result['count']} results<br>";
        }
        echo "</div>";
        
        echo "<h2>Step 2: Generate Actual HTML (Exact Copy from index.php)</h2>";
        
        // Generate the exact HTML that would be produced by index.php
        foreach ($searchResults as $index => $result) {
            echo "<h3>Term #{$index}: {$result['term']}</h3>";
            
            // Result message
            echo "<div style='background: #fff3cd; padding: 10px; margin: 5px 0; border: 1px solid #ffc107;'>";
            echo "<strong>Result Message:</strong><br>";
            echo "<span id='resultMessage{$index}' data-term='" . htmlspecialchars($result['term']) . "' data-original-count='{$result['count']}'>";
            echo generateResultMessage($result['count'], $result['term']);
            echo "</span>";
            echo "</div>";
            
            if ($result['count'] > 0) {
                echo "<div style='background: #f8f9fa; padding: 10px; margin: 5px 0; border: 1px solid #dee2e6;'>";
                echo "<strong>Table Rows Generated:</strong><br>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 5px 0;'>";
                echo "<thead>";
                echo "<tr style='background: #e9ecef;'>";
                echo "<th>Row #</th><th>Case Number</th><th>Institution</th><th>Data Attributes</th><th>Search Type</th>";
                echo "</tr>";
                echo "</thead>";
                echo "<tbody>";
                
                $rowCount = 0;
                foreach ($result['results'] as $dosar) {
                    $rowCount++;
                    
                    // Simulate the exact data preparation from index.php
                    $parti = $dosar->parti ?? [];
                    if (!is_array($parti)) {
                        $parti = [];
                    }
                    $searchTerm = $result['term'] ?? '';
                    $searchType = $result['type'] ?? '';
                    $relevantParty = getRelevantPartyName($parti, $searchTerm, $searchType);
                    $relevantQuality = getRelevantPartyQuality($parti, $searchTerm, $searchType);
                    
                    // Get institution name (simplified)
                    $institutie = $dosar->institutie ?? '';
                    $instituteName = $institutie; // Simplified for testing
                    
                    $caseNumber = htmlspecialchars($dosar->numar ?? '');
                    $hasAsterisk = strpos($dosar->numar, '*') !== false;
                    
                    echo "<tr style='background: " . ($hasAsterisk ? "#fff3cd" : "#f8f9fa") . ";'";
                    echo " data-numar='" . htmlspecialchars($dosar->numar ?? '') . "'";
                    echo " data-instanta='" . htmlspecialchars($instituteName) . "'";
                    echo " data-obiect='" . htmlspecialchars($dosar->obiect ?? '') . "'";
                    echo " data-search-type='" . htmlspecialchars($searchType) . "'>";
                    
                    echo "<td>{$rowCount}</td>";
                    echo "<td>{$caseNumber}" . ($hasAsterisk ? " <strong>(ASTERISK!)</strong>" : "") . "</td>";
                    echo "<td>{$instituteName}</td>";
                    echo "<td>data-numar=\"{$caseNumber}\" data-search-type=\"{$searchType}\"</td>";
                    echo "<td>{$searchType}</td>";
                    echo "</tr>";
                }
                
                echo "</tbody>";
                echo "</table>";
                echo "<strong>Total rows generated: {$rowCount}</strong>";
                echo "</div>";
                
                // Check for issues
                if ($rowCount != $result['count']) {
                    echo "<div style='background: #f8d7da; padding: 10px; margin: 5px 0; border: 1px solid #f5c6cb;'>";
                    echo "<strong>❌ MISMATCH DETECTED!</strong><br>";
                    echo "Expected {$result['count']} rows, but generated {$rowCount} rows<br>";
                    echo "This indicates a problem in the HTML generation loop!";
                    echo "</div>";
                } else {
                    echo "<div style='background: #d4edda; padding: 10px; margin: 5px 0; border: 1px solid #c3e6cb;'>";
                    echo "<strong>✅ HTML Generation OK:</strong> Generated {$rowCount} rows as expected";
                    echo "</div>";
                }
            }
        }
        
        echo "<h2>Step 3: JavaScript Simulation</h2>";
        
        // Simulate what JavaScript would see
        echo "<div style='background: #e7f3ff; padding: 10px; margin: 5px 0; border: 1px solid #007bff;'>";
        echo "<strong>JavaScript would see:</strong><br>";
        echo "<code>document.querySelectorAll('table tbody tr').length</code> = ";
        
        $totalRows = 0;
        foreach ($searchResults as $result) {
            $totalRows += count($result['results']);
        }
        echo "{$totalRows}<br>";
        
        echo "<code>document.querySelectorAll('[data-numar*=\"*\"]').length</code> = ";
        $asteriskRows = 0;
        foreach ($searchResults as $result) {
            foreach ($result['results'] as $dosar) {
                if (strpos($dosar->numar, '*') !== false) {
                    $asteriskRows++;
                }
            }
        }
        echo "{$asteriskRows}<br>";
        echo "</div>";
        
        echo "<h2>Step 4: Final Analysis</h2>";
        
        if ($totalRows == 3 && $asteriskRows == 1) {
            echo "<div style='background: #d4edda; padding: 15px; margin: 10px 0; border: 1px solid #c3e6cb;'>";
            echo "<h3>✅ HTML Generation is Perfect!</h3>";
            echo "<p>All 3 rows are correctly generated, including the asterisk case.</p>";
            echo "<p><strong>The issue must be in:</strong></p>";
            echo "<ul>";
            echo "<li>JavaScript filtering that runs after page load</li>";
            echo "<li>CSS rules that hide elements</li>";
            echo "<li>Browser rendering issues</li>";
            echo "<li>Exact match filter being auto-enabled</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb;'>";
            echo "<h3>❌ HTML Generation Issue!</h3>";
            echo "<p>Expected 3 rows with 1 asterisk case, but got {$totalRows} rows with {$asteriskRows} asterisk cases.</p>";
            echo "</div>";
        }
        
    } else {
        echo "<p>No search terms provided.</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 10px 0; border: 1px solid #f5c6cb;'>";
    echo "<h3>❌ Error:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>🎯 Next Steps</h2>";
echo "<p>If HTML generation is perfect, then check the web interface for:</p>";
echo "<ol>";
echo "<li>Open browser developer tools and check the Console tab for JavaScript errors</li>";
echo "<li>Check if the exact match filter checkbox is checked</li>";
echo "<li>Run <code>document.querySelectorAll('table tbody tr').length</code> in console</li>";
echo "<li>Run <code>document.querySelectorAll('table tbody tr[style*=\"display: none\"]').length</code> to check hidden rows</li>";
echo "<li>Check the result message text to see if it shows '2' or '3'</li>";
echo "</ol>";
?>
