<?php
// Find the exact missing parties to reach 380 total

require_once 'bootstrap.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';

function findMissingParties($caseNumber, $institution, $targetCount = 380) {
    echo "=== FINDING MISSING PARTIES: $caseNumber - $institution ===" . PHP_EOL;
    echo "Target: $targetCount parties" . PHP_EOL . PHP_EOL;
    
    try {
        $dosarService = new DosarService();
        
        // Get current extraction
        $dosar = $dosarService->getDetaliiDosar($caseNumber, $institution);
        $currentCount = count($dosar->parti);
        $missing = $targetCount - $currentCount;
        
        echo "Current parties: $currentCount" . PHP_EOL;
        echo "Missing parties: $missing" . PHP_EOL . PHP_EOL;
        
        // Get raw SOAP data to analyze decision text
        $reflection = new ReflectionClass($dosarService);
        $method = $reflection->getMethod('executeSoapCallWithRetry');
        $method->setAccessible(true);
        
        $searchParams = [
            'numarDosar' => $caseNumber,
            'institutie' => $institution,
            'obiectDosar' => '',
            'numeParte' => '',
            'dataStart' => null,
            'dataStop' => null,
            'dataUltimaModificareStart' => null,
            'dataUltimaModificareStop' => null
        ];
        
        $rawResponse = $method->invoke($dosarService, 'CautareDosare2', $searchParams, "Missing parties analysis");
        
        if (!$rawResponse || !isset($rawResponse->CautareDosare2Result)) {
            echo "❌ No SOAP response" . PHP_EOL;
            return;
        }
        
        $dosare = $rawResponse->CautareDosare2Result->Dosar ?? null;
        if (!$dosare) {
            echo "❌ No Dosar in response" . PHP_EOL;
            return;
        }
        
        if (!is_array($dosare)) {
            $dosare = [$dosare];
        }
        
        $targetDosar = null;
        foreach ($dosare as $d) {
            if (isset($d->numar) && $d->numar === $caseNumber && $d->institutie === $institution) {
                $targetDosar = $d;
                break;
            }
        }
        
        if (!$targetDosar) {
            echo "❌ Case not found" . PHP_EOL;
            return;
        }
        
        // Extract all decision text
        $allDecisionText = '';
        if (isset($targetDosar->sedinte) && isset($targetDosar->sedinte->DosarSedinta)) {
            $sedinte = $targetDosar->sedinte->DosarSedinta;
            if (!is_array($sedinte)) {
                $sedinte = [$sedinte];
            }
            
            foreach ($sedinte as $sedinta) {
                if (isset($sedinta->solutieSumar) && !empty($sedinta->solutieSumar)) {
                    $allDecisionText .= $sedinta->solutieSumar . ' ';
                }
            }
        }
        
        if (empty($allDecisionText)) {
            echo "❌ No decision text found" . PHP_EOL;
            return;
        }
        
        echo "Decision text length: " . strlen($allDecisionText) . " characters" . PHP_EOL . PHP_EOL;
        
        // Get currently extracted party names for comparison
        $currentPartyNames = [];
        foreach ($dosar->parti as $party) {
            $currentPartyNames[] = strtolower(trim($party->nume));
        }
        
        // Find ALL potential party names in decision text
        $allPotentialParties = extractAllPotentialParties($allDecisionText);
        
        // Filter out already extracted parties
        $newParties = [];
        foreach ($allPotentialParties as $potentialParty) {
            $normalizedName = strtolower(trim($potentialParty['nume']));
            if (!in_array($normalizedName, $currentPartyNames)) {
                $newParties[] = $potentialParty;
            }
        }
        
        echo "Potential new parties found: " . count($newParties) . PHP_EOL;
        echo "Needed to reach target: $missing" . PHP_EOL . PHP_EOL;
        
        if (count($newParties) >= $missing) {
            echo "✅ SUCCESS: Found enough parties to reach target!" . PHP_EOL . PHP_EOL;
            echo "Top " . min($missing, 20) . " new parties to add:" . PHP_EOL;
            for ($i = 0; $i < min($missing, count($newParties), 20); $i++) {
                echo "  " . ($i + 1) . ". " . $newParties[$i]['nume'] . " (" . $newParties[$i]['calitate'] . ")" . PHP_EOL;
            }
        } else {
            echo "⚠️ WARNING: Only found " . count($newParties) . " new parties, need $missing" . PHP_EOL;
            echo "All new parties found:" . PHP_EOL;
            foreach ($newParties as $i => $party) {
                echo "  " . ($i + 1) . ". " . $party['nume'] . " (" . $party['calitate'] . ")" . PHP_EOL;
            }
        }
        
        return $newParties;
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . PHP_EOL;
        return [];
    }
}

function extractAllPotentialParties($decisionText) {
    $parties = [];
    
    // Pattern 1: Extract ALL names from semicolon-separated lists (creditors)
    if (preg_match_all('/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+(?:;[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)+)/u', $decisionText, $semiMatches)) {
        foreach ($semiMatches[1] as $match) {
            $names = explode(';', $match);
            foreach ($names as $name) {
                $name = cleanPartyName($name);
                if (isValidNewPartyName($name)) {
                    $parties[] = ['nume' => $name, 'calitate' => 'Creditor'];
                }
            }
        }
    }
    
    // Pattern 2: Extract ALL names from comma-separated lists (appellants)
    if (preg_match_all('/([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+(?:,[A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)+)/u', $decisionText, $commaMatches)) {
        foreach ($commaMatches[1] as $match) {
            $names = explode(',', $match);
            foreach ($names as $name) {
                $name = cleanPartyName($name);
                if (isValidNewPartyName($name)) {
                    $parties[] = ['nume' => $name, 'calitate' => 'Apelant'];
                }
            }
        }
    }
    
    // Pattern 3: Extract individual names with specific legal contexts
    $contexts = [
        '/\bcreditor[ui]?\s+([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)/iu' => 'Creditor',
        '/\bapelan[tţ][ui]?\s+([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)/iu' => 'Apelant',
        '/\breclamant[ui]?\s+([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)/iu' => 'Reclamant',
        '/\bpârât[ui]?\s+([A-ZĂÂÎȘȚŢ][A-Za-zĂÂÎȘȚăâîșțţ\s\-\.]+)/iu' => 'Pârât'
    ];
    
    foreach ($contexts as $pattern => $quality) {
        if (preg_match_all($pattern, $decisionText, $matches)) {
            foreach ($matches[1] as $name) {
                $name = cleanPartyName($name);
                if (isValidNewPartyName($name)) {
                    $parties[] = ['nume' => $name, 'calitate' => $quality];
                }
            }
        }
    }
    
    // Remove duplicates
    $uniqueParties = [];
    $seenNames = [];
    foreach ($parties as $party) {
        $normalizedName = strtolower(trim($party['nume']));
        if (!in_array($normalizedName, $seenNames)) {
            $uniqueParties[] = $party;
            $seenNames[] = $normalizedName;
        }
    }
    
    return $uniqueParties;
}

function cleanPartyName($name) {
    $name = trim($name);
    
    // Remove common prefixes and suffixes
    $name = preg_replace('/^(și\s+|de\s+|către\s+|prin\s+)/i', '', $name);
    $name = preg_replace('/\s*\(.*?\)\s*$/', '', $name); // Remove parenthetical
    $name = preg_replace('/\s*–\s*.*$/', '', $name); // Remove dash content
    $name = preg_replace('/\s*în sumă.*$/i', '', $name); // Remove amount info
    $name = preg_replace('/\s*reprezentând.*$/i', '', $name); // Remove representation info
    
    return trim($name);
}

function isValidNewPartyName($name) {
    // Length check
    if (strlen($name) < 3 || strlen($name) > 150) {
        return false;
    }
    
    // Must start with capital letter
    if (!preg_match('/^[A-ZĂÂÎȘȚŢ]/', $name)) {
        return false;
    }
    
    // Must contain at least one space (first + last name)
    if (!preg_match('/\s/', $name)) {
        return false;
    }
    
    // Must contain only valid characters
    if (!preg_match('/^[A-Za-zĂÂÎȘȚăâîșțţ0-9\s\-\.\(\)\/]+$/u', $name)) {
        return false;
    }
    
    // Exclude obvious legal terms
    $blacklist = [
        'SUMA DE', 'CONFORM', 'ARTICOL', 'CODUL', 'TRIBUNALUL', 'DISPUNE', 'ADMITE', 'RESPINGE',
        'DOSARUL', 'INSTANŢA', 'JUDECĂTOR', 'GREFIER', 'CAMERA DE CONSILIU', 'SECŢIA CIVILĂ'
    ];
    
    foreach ($blacklist as $term) {
        if (stripos($name, $term) !== false) {
            return false;
        }
    }
    
    return true;
}

// Analyze both test cases
echo "🔍 COMPREHENSIVE MISSING PARTY ANALYSIS" . PHP_EOL;
echo "=======================================" . PHP_EOL . PHP_EOL;

$newParties1 = findMissingParties('130/98/2022', 'TribunalulIALOMITA', 380);
echo PHP_EOL . "=======================================" . PHP_EOL . PHP_EOL;
$newParties2 = findMissingParties('130/98/2022', 'CurteadeApelBUCURESTI', 380);

echo PHP_EOL . "✅ Analysis complete!" . PHP_EOL;
?>
